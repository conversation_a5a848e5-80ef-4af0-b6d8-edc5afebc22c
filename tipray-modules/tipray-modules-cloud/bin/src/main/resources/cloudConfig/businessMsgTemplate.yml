# 调取云平台发送消息接口业务模板，字段类型说明：
# template 子级  类型: 列表， 表示发送消息渠道 phone - 短信, wechat - 微信公众号
# name: 模板名（自定义，说明该模板是哪个模板）; use: DLP控制台是否使用该模板（user == true时，会调用云平台同步模板的接口）;
# businessCode: 业务代码（云平台相关，人工跟云平台方确认）;
# params: 模板参数 其中title、remark已不能使用（详情可见 https://www.toutiao.com/article/7229971542267052602）,所以增加detailTemplate用于格式化新模板，再传给云平台，云平台用该内容作为详情。
# detailTemplate: 用于参数发至云平台之前后，对详情进行格式化（用于渠道的查看详情，如公众号的查看详情）
# detailTemplate子级： type 表模板类型 (string, map)， 当类型为map时,占位符可缺失,且map独有split属性，意为分隔符；类型为string时，不可缺失, 可参考code = A0004
cloud:
  message:
    template:
      phone:
        - name: 短信验证码模板（测试）
          use: false
          businessCode: A0001
          params:
            code: 验证码(String)
        - name: DLP登录短信验证码
          use: true
          businessCode: B0005
          params:
            code: 验证码(String,数字)
        - name: DLP忘记密码短信验证码
          use: true
          businessCode: B0006
          params:
            code: 验证码(String,全数字)
      wechat:
        - name: 事件告警消息模板（测试）
          use: false
          businessCode: A0002
          params:
            title: 模板标题
            keyword1: 事件类型
            keyword2: 统计周期
            keyword3: 资产范围
            keyword4: 安全状态
            remark: 备注
        - name: 通用报表模板
          use: false
          businessCode: A0003
          params:
            title: 模板标题
            date: 统计日期
            dept: 部门信息
            scope: 统计范围
            remark: 备注
        - name: 检测报表模板
          use: true
          businessCode: A0004
          params:
            title: 模板标题
            systemName: 系统名称
            date: 日期
            type: 类型
            content: 检测结果
            remark: 备注
          detailTemplate:
            type: map
            template:
              - '<span><strong>系统名称：</strong></span><span>#{systemName}</span>'
              - '<span><strong>日期：</strong></span><span>#{date}</span>'
              - '<span><strong>类型：</strong></span><span>#{type}</span>'
              - '<span><strong>检测结果：</strong></span><span>#{content}</span>'
              - '<span><strong>备注：</strong></span><span>#{remark}</span>'
            split: '<br/>'
#            type: string 仅作类型为字符串时的举例
#            template: '系统名称: #{systemName}\n日期: #{date}\n类型: #{type}检测结果: #{content}备注: #{remark}'
        - name: 通用告警业务消息模板
          use: false
          businessCode: B0001
          params:
            title: 模板标题
            warnObject: 告警对象
            warnType: 告警类型
            warnTime: 告警时间
            warnContent: 告警内容
            remark: 备注
        - name: 设备告警模板
          use: false
          businessCode: B0002
          params:
            title: 模板标题
            device: 设备
            warnContent: 告警内容
            warnTime: 告警时间
            remark: 备注
        - name: 系统安全告警模板
          use: true
          businessCode: B0003
          params:
            title: 模板标题
            device: 设备
            level: 告警级别
            warnTime: 告警时间
            reason: 告警原因
            remark: 备注
          detailTemplate:
            type: map
            detailTemplate:
              - '<fieldset><legend>常规</legend>
                 <span><strong>计算机名</strong></span><span>#{computerName}</span>'
              - '<span><strong>操作员</strong></span><span>#{userName}</span>'
              - '<span><strong>告警来源</strong></span><span>#{alarmTypeName}</span>'
              - '<span><strong>告警类型</strong></span><span>#{sstTypeName}</span>'
              - '<span><strong>告警级别</strong></span><span>#{alarmLevelName}</span>'
              - '<span><strong>执行动作</strong></span><span>#{actionName}</span>'
              - '<span><strong>告警时间</strong></span><span>#{occurDate}</span></fieldset>'
              - '<fieldset><legend>详情</legend>'
              - '<span>#{alarmDetail}</span></fieldset>'
            split: '<br/>'
        - name: 短信告警通知
          use: true
          businessCode: B0004
          params:
            computerName: 计算机名
            userName: 操作员
            alarmTypeName: 告警来源
            sstTypeName: 告警类型
            alarmLevelName: 告警级别
            actionName: 执行动作
            occurDate: 告警时间
            alarmDetail: 详情
