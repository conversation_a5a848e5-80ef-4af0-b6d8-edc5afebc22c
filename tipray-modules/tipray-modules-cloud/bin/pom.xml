<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>tipray-modules</artifactId>
        <groupId>com.tipray</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <description>云平台交互模块，负责与云平台的交互，包括：鉴权等</description>

    <artifactId>tipray-modules-cloud</artifactId>
    <version>1.0</version>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-tools</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-mybatis</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-register</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-modules-base</artifactId>
            <version>1.0</version>
        </dependency>
        <!--算法-->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
        </dependency>
        <!--证书操作、ocsp-->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk18on</artifactId>
        </dependency>
        <!--openpgp、bcpg-->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpg-jdk18on</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>ocsp-commons-client</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/ocsp-commons-client-0.0.1-SNAPSHOT.jar</systemPath>
        </dependency>
    </dependencies>
</project>
