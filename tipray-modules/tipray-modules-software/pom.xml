<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>tipray-modules</artifactId>
        <groupId>com.tipray</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <description>软件中心模块，维护软件库，为终端提供软件管家功能</description>

    <artifactId>tipray-modules-software</artifactId>
    <version>1.0</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-tools</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-mybatis</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-websocket</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-software</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-ftp</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-modules-base</artifactId>
            <version>1.0</version>
        </dependency>
    </dependencies>
</project>