package com.tipray.dlp.software.dao;

import com.tipray.dlp.dao.BaseDao;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import com.tipray.dlp.software.bean.SoftwareAuditLog;
import com.tipray.dlp.software.bean.vo.SoftwareAuditLogVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource
public interface SoftwareAuditLogDao extends BaseDao<SoftwareAuditLog> {

    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(SoftwareAuditLogVO vo);

    /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<SoftwareAuditLog> listByVO(SoftwareAuditLogVO vo);

}
