package com.tipray.dlp.software.bean.dict;

import com.tipray.dlp.ftp.dict.FtpTaskStatus;
import com.tipray.dlp.util.I18nUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 安装包上传到软件中心服务器的状态
 *
 * <AUTHOR>
 * @date 2023/9/1
 */
@Getter
@AllArgsConstructor
public enum SoftwareUploadStatusDict {
    /**
     * 未上传
     */
    NOT_UPLOAD(0, "notUpload"),
    /**
     * 上传中
     */
    UPLOADING(1, "uploading"),
    /**
     * 已上传
     */
    UPLOADED(2, "uploaded");

    private Integer code;
    private String value;

    public String getValue() {
        return I18nUtils.get("pages." + this.value);
    }

    public static boolean isUploading(FtpTaskStatus ftpTaskStatus) {
        return !ftpTaskStatus.isErrorCode() && !ftpTaskStatus.isSuccess();
    }

    public static Integer convertTransferStatus(Integer status) {
        FtpTaskStatus ftpTaskStatus = FtpTaskStatus.of(status);
        if (ftpTaskStatus == null) {
            return SoftwareUploadStatusDict.NOT_UPLOAD.getCode();
        }
        if (ftpTaskStatus.isErrorCode()) {
            return status;
        }
        if (ftpTaskStatus.isSuccess()) {
            return  SoftwareUploadStatusDict.UPLOADED.getCode();
        }
        return SoftwareUploadStatusDict.UPLOADING.getCode();
    }
}
