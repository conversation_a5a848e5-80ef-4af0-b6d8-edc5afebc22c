package com.tipray.dlp.software.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 软件上架策略与软件库关联表
 *
 * <AUTHOR>
 * @date 2023/12/6
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("rel_stg_software_download")
public class RelStgSoftwareDownload {
    /**
     * 软件下载策略ID
     */
    private Long stgId;
    /**
     * ObjType为0 当作软件ID
     * ObjType为1 当作类型ID
     */
    private Long objId;
    /**
     * 对象类型
     * 0 软件ID
     * 1 类型ID
     */
    private Long objType;

    public static RelStgSoftwareDownload from(Long stgId, SoftwareItem softwareItem) {
        RelStgSoftwareDownload rel = new RelStgSoftwareDownload();
        rel.stgId = stgId;
        rel.objId = softwareItem.getId();
        rel.objType = softwareItem.getObjType() == null ? 0L : softwareItem.getObjType();
        return rel;
    }
}
