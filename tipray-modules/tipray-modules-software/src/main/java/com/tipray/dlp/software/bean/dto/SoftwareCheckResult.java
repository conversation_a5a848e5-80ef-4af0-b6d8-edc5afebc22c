package com.tipray.dlp.software.bean.dto;

import com.tipray.dlp.software.bean.SoftwareRepository;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/15
 */
@Getter
@Setter
public class SoftwareCheckResult {
    private int status;
    private long uploadedSize;
    private SoftwareRepository config;

    private static volatile SoftwareCheckResult nonexistentResult;
    private static volatile SoftwareCheckResult uploadedResult;

    /**
     * 不存在安装包
     */
    public static SoftwareCheckResult nonexistent() {
        if (nonexistentResult == null) {
            synchronized (SoftwareCheckResult.class) {
                if (nonexistentResult == null) {
                    nonexistentResult = new SoftwareCheckResult();
                }
            }
        }
        return nonexistentResult;
    }

    /**
     * 安装包正在上传到控制台
     */
    public static SoftwareCheckResult uploading(long uploadedSize) {
        SoftwareCheckResult result = new SoftwareCheckResult();
        result.status = 1;
        result.uploadedSize = uploadedSize;
        return result;
    }

    /**
     * 安装包已上传到控制台
     */
    public static SoftwareCheckResult uploaded() {
        if (uploadedResult == null) {
            synchronized (SoftwareCheckResult.class) {
                if (uploadedResult == null) {
                    uploadedResult = new SoftwareCheckResult();
                    uploadedResult.status = 50;
                }
            }
        }
        return uploadedResult;
    }

    /**
     * 数据库已存档的
     */
    public static SoftwareCheckResult archived(SoftwareRepository config) {
        SoftwareCheckResult result = new SoftwareCheckResult();
        result.status = 100;
        result.config = config;
        return result;
    }

    /**
     * 数据库已删除的
     */
    public static SoftwareCheckResult deleted(SoftwareRepository config, long uploadedSize) {
        SoftwareCheckResult result = new SoftwareCheckResult();
        result.status = 101;
        result.config = config;
        result.uploadedSize = uploadedSize;
        return result;
    }
}
