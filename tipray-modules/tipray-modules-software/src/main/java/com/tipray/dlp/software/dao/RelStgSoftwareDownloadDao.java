package com.tipray.dlp.software.dao;

import com.tipray.dlp.software.bean.RelStgSoftwareDownload;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.stream.Collectors;

public interface RelStgSoftwareDownloadDao {

    default void batchInsert(List<RelStgSoftwareDownload> rels) {
        if (rels == null || rels.isEmpty()) {
            return;
        }
        StringBuilder values = new StringBuilder();
        boolean first = true;
        for (RelStgSoftwareDownload rel : rels) {
            if (first) {
                first = false;
            } else {
                values.append(',');
            }
            values.append('(').append(rel.getStgId()).append(',').append(rel.getObjId()).append(',').append(rel.getObjType()).append(')');
        }
        this.insert(values.toString());
    }

    default void batchUpdate(List<Long> stgIds, List<RelStgSoftwareDownload> rels) {
        this.deleteByStgIds(stgIds);
        if (rels == null || rels.isEmpty()) {
            return;
        }
        this.batchInsert(rels);
    }

    @Insert("INSERT INTO rel_stg_software_download(stg_id, obj_id, obj_type) VALUES ${value}")
    void insert(String value);

    default void deleteByStgIds(List<Long> stgIds) {
        if (stgIds == null || stgIds.isEmpty()) {
            return;
        }
        if (stgIds.size() == 1) {
            this.deleteByStgId(stgIds.get(0));
            return;
        }
        String value = stgIds.stream().map(Long::toUnsignedString).collect(Collectors.joining(","));
        this.delete(value);
    }

    @Delete("DELETE FROM rel_stg_software_download WHERE stg_id = #{stgId}")
    void deleteByStgId(Long stgId);

    @Delete("DELETE FROM rel_mstg_software WHERE obj_id = #{objId}")
    void deleteBySoftId(Long objId);

    @Delete("DELETE FROM rel_stg_software_download WHERE stg_id IN (${value})")
    void delete(String stgIds);

    @Select("SELECT COUNT(*) FROM rel_stg_software_download WHERE obj_id = #{categoryId} AND obj_type = 1")
    Long countByCategoryId(Long categoryId);

    @Select("SELECT s.name FROM rel_stg_software_download r, stg_def s WHERE r.obj_id = #{categoryId} AND r.obj_type = 1 AND r.stg_id = s.id")
    List<String> getStgNamesByCategoryId(Long categoryId);

    @Select("SELECT COUNT(*) FROM rel_stg_software_download WHERE obj_id IN (${value}) AND obj_type = 0")
    Long countBySoftwareIds(String softwareIds);

    @Select("SELECT DISTINCT s.name FROM rel_stg_software_download r, stg_def s WHERE r.obj_id IN (${value}) AND r.obj_type = 0 AND r.stg_id = s.id")
    List<String> getStgNamesBySoftwareIds(String softwareIds);

    @Select("SELECT DISTINCT s.name FROM rel_mstg_software r, mstg_def s WHERE r.obj_id IN (${value}) AND r.stg_id = s.id")
    List<String> getMstgNamesBySoftwareIds(String softwareIds);
}
