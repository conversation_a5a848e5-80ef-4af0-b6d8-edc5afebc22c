package com.tipray.dlp.software.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tipray.dlp.annotation.LogFormat;
import com.tipray.dlp.bean.BaseLog;
import com.tipray.dlp.ftp.dict.FtpTaskStatus;
import com.tipray.dlp.software.bean.dict.SoftwareAuditTypeDict;
import com.tipray.dlp.util.I18nUtils;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 软件管家审计日志
 * <AUTHOR>
 * @date 2023/10/31
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("software_audit_log")
public class SoftwareAuditLog extends BaseLog {
    /**
     * 操作类型{1:下载、2:卸载、3:升级、4:安装、5:手动清空下载的安装包(控制台不显示)}
     */
    @LogFormat(name = "table.operateType", formatClass = SoftwareAuditTypeDict.class)
    private Integer auditType;
    /**
     * 操作结果
     */
    @LogFormat(name = "table.opResult", formatClass = SoftwareAuditLog.class, formatMethod = "formatResult")
    private Integer result;
    /**
     * 软件名称
     */
    @LogFormat(name = "table.softwareName")
    private String softwareName;
    /**
     * 软件版本
     */
    @LogFormat(name = "table.softwareVersion")
    private String softwareVersion;

    private static volatile Map<String, String> opResultMap;

    private static Map<String, String> getOpResultMap() {
        if (opResultMap == null) {
            synchronized (SoftwareAuditLog.class) {
                if (opResultMap == null) {
                    opResultMap = new HashMap<>();
                    // 失败：md5和策略匹配不同
                    opResultMap.put("1.3", "softwareAuditResult1");
                    // 从服务器下载失败
                    opResultMap.put("1.4", "softwareAuditResult2");
                    // 失败：未找到注册表里与策略软件名和版本一致的软件。
                    opResultMap.put("3.3", "softwareAuditResult3");
                    // 失败：外部传的软件名或版本参数为空，无法判断。
                    opResultMap.put("3.4", "softwareAuditResult4");
                }
            }
        }
        return opResultMap;
    }

    public String getOpResult() {
        if (result == null) {
            return null;
        }
        if (result == 1) {
            return I18nUtils.get("text.success", "成功");
        }
        if (result == 2) {
            return I18nUtils.get("text.fail", "失败");
        }
        if (auditType == null) {
            return null;
        }
        if (auditType == SoftwareAuditTypeDict.uninstall.code()) {
            if (result == 3) {
                return I18nUtils.get("pages.softwareAuditResult5", Collections.singletonMap("name", softwareName));
            }
            return SoftwareAuditTypeDict.uninstall.toString() + I18nUtils.get("text.fail") + "(code=" + result + ")";
        }
        if (result == 5) {
            return I18nUtils.get("pages.cancel", "取消") + SoftwareAuditTypeDict.format(auditType);
        }
        String i18nKey = getOpResultMap().get(auditType + "." + result);
        if (i18nKey != null) {
            return I18nUtils.get("pages." + i18nKey);
        }
        FtpTaskStatus status = FtpTaskStatus.of(result);
        if (status == null) {
            return null;
        }
        return I18nUtils.get(status.getInfo());
    }

    public static String formatResult(Map<String, Object> map) {
        Integer result = (Integer) map.get("result");
        Integer auditType = (Integer) map.get("auditType");
        String softwareName = (String) map.get("softwareName");
        if (result == null) {
            return null;
        }
        if (result == 1) {
            return I18nUtils.get("text.success", "成功");
        }
        if (result == 2) {
            return I18nUtils.get("text.fail", "失败");
        }
        if (auditType == null) {
            return null;
        }
        if (auditType == SoftwareAuditTypeDict.uninstall.code()) {
            if (result == 3) {
                return I18nUtils.get("pages.softwareAuditResult5", Collections.singletonMap("name", softwareName));
            }
            return SoftwareAuditTypeDict.uninstall.toString() + I18nUtils.get("text.fail") + "(code=" + result + ")";
        }
        if (result == 5) {
            return I18nUtils.get("pages.cancel", "取消") + SoftwareAuditTypeDict.format(auditType);
        }
        String i18nKey = getOpResultMap().get(auditType + "." + result);
        if (i18nKey != null) {
            return I18nUtils.get("pages." + i18nKey);
        }
        FtpTaskStatus status = FtpTaskStatus.of(result);
        if (status == null) {
            return null;
        }
        return I18nUtils.get(status.getInfo());
    }
}
