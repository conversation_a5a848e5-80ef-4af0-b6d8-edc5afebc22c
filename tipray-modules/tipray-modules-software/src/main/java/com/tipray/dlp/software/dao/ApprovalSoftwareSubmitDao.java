package com.tipray.dlp.software.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tipray.dlp.dao.BaseDao;
import com.tipray.dlp.mybatis.bean.PageVO;
import com.tipray.dlp.software.bean.ApprovalSoftwareSubmit;
import com.tipray.dlp.software.bean.vo.ApprovalSoftwareSubmitVO;
import com.tipray.dlp.util.StringUtil;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ApprovalSoftwareSubmitDao extends BaseDao<ApprovalSoftwareSubmit> {
    Long countByVO(ApprovalSoftwareSubmitVO vo);
    List<ApprovalSoftwareSubmit> listByVO(ApprovalSoftwareSubmitVO vo);

    int deleteById(Integer id);

    @Override
    default ApprovalSoftwareSubmitVO selectPageByVO(PageVO pageVO) {
        if (pageVO instanceof ApprovalSoftwareSubmitVO) {
            ApprovalSoftwareSubmitVO vo = (ApprovalSoftwareSubmitVO) pageVO;
            QueryWrapper<ApprovalSoftwareSubmit> wrapper = new QueryWrapper<>();
            if (StringUtil.isNotBlank(vo.getName())) {
                wrapper.like("name", StringUtil.strip(vo.getName()));
            }
            return this.selectPage(vo, wrapper);
        }
        return new ApprovalSoftwareSubmitVO();
    }
}
