package com.tipray.dlp.software.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tipray.dlp.dao.BaseDao;
import com.tipray.dlp.mybatis.bean.PageVO;
import com.tipray.dlp.software.bean.SoftwareRepository;
import com.tipray.dlp.software.bean.vo.SoftwareRepositoryVO;
import com.tipray.dlp.util.StringUtil;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SoftwareRepositoryDao extends BaseDao<SoftwareRepository> {

    @Select("select id, guid, md5, upload_status from software_repository where guid = #{value} and deleted = 0")
    List<SoftwareRepository> getByGuid(String guid);

    @Select("select * from software_repository where md5 = #{value} order by id desc limit 1")
    SoftwareRepository getByMd5(String md5);

    @Select("select distinct guid from software_repository where (quick_md5 is null or quick_md5 = '') and upload_status = 2 and deleted = 0")
    List<String> listNonQuickMd5SoftwareGuid();

    @Update("update software_repository t1, (${tmpTable}) t2 set t1.quick_md5 = t2.quick_md5 where t1.guid = t2.guid")
    Long batchUpdateQuickMd5s(String tmpTable);

    @Override
    default SoftwareRepositoryVO selectPageByVO(PageVO pageVO) {
        if (pageVO instanceof SoftwareRepositoryVO) {
            SoftwareRepositoryVO vo = (SoftwareRepositoryVO) pageVO;
            QueryWrapper<SoftwareRepository> wrapper = new QueryWrapper<>();
            if (StringUtil.isNotBlank(vo.getName())) {
                wrapper.like("name", StringUtil.strip(vo.getName()));
            }
            if (null != vo.getCategory()) {
                wrapper.eq("category", vo.getCategory());
            }
            if (StringUtil.isNotBlank(vo.getVersion())) {
                wrapper.like("version", vo.getVersion());
            }
            if (null != vo.getUploadStatus()) {
                wrapper.in("upload_status", vo.getUploadStatus());
            }
            String architecture = vo.getArchitecture();
            if (StringUtil.isNotBlank(architecture)) {
                if ("x86".equals(architecture)) {
                    wrapper.in("architecture", 1, 3, 9, 11);
                } else if ("x64".equals(architecture)) {
                    wrapper.in("architecture", 2, 3, 10, 11);
                } else if ("Arm64".equals(architecture)) {
                    wrapper.in("architecture", 8, 9, 10, 11);
                }
            }
            if (StringUtil.isNotBlank(vo.getDescription())) {
                wrapper.like("description", vo.getDescription());
            }
            return this.selectPage(vo, wrapper);
        }
        return new SoftwareRepositoryVO();
    }

}
