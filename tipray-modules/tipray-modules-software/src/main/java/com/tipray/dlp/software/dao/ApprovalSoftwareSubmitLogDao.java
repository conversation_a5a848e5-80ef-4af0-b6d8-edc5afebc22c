package com.tipray.dlp.software.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tipray.dlp.dao.BaseDao;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import com.tipray.dlp.mybatis.bean.PageVO;
import com.tipray.dlp.mybatis.datasource.DataSourceFactory;
import com.tipray.dlp.software.bean.ApprovalSoftwareSubmitLog;
import com.tipray.dlp.software.bean.vo.ApprovalSoftwareSubmitLogVO;
import com.tipray.dlp.util.StringUtil;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource(datasourceId = DataSourceFactory.SHARD_MASTER_DATA_SOURCE)
public interface ApprovalSoftwareSubmitLogDao extends BaseDao<ApprovalSoftwareSubmitLog> {
    Long countByVO(ApprovalSoftwareSubmitLogVO vo);
    List<ApprovalSoftwareSubmitLog> listByVO(ApprovalSoftwareSubmitLogVO vo);


    int deleteById(Integer id);

    @Override
    default ApprovalSoftwareSubmitLogVO selectPageByVO(PageVO pageVO) {
        if (pageVO instanceof ApprovalSoftwareSubmitLogVO) {
            ApprovalSoftwareSubmitLogVO vo = (ApprovalSoftwareSubmitLogVO) pageVO;
            QueryWrapper<ApprovalSoftwareSubmitLog> wrapper = new QueryWrapper<>();
            if (StringUtil.isNotBlank(vo.getName())) {
                wrapper.like("name", StringUtil.strip(vo.getName()));
            }
            return this.selectPage(vo, wrapper);
        }
        return new ApprovalSoftwareSubmitLogVO();
    }
}
