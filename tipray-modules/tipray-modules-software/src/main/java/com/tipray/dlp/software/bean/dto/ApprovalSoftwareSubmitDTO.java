package com.tipray.dlp.software.bean.dto;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tipray.dlp.annotation.LogFormat;
import com.tipray.dlp.bean.BaseBean;
import com.tipray.dlp.bean.dict.IntegerDict;
import com.tipray.dlp.service.UserService;
import com.tipray.dlp.software.bean.ApprovalSoftwareSubmit;
import com.tipray.dlp.software.bean.dict.SoftwareArchitectureDict;
import com.tipray.dlp.software.bean.dict.SoftwareSupportedOsDict;
import com.tipray.dlp.software.service.SoftwareCategoryService;
import com.tipray.dlp.util.FileUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 软件上架申请
 *
 * <AUTHOR>
 * @date 2024/9/23
 */
@Setter
@Getter
@NoArgsConstructor
public class ApprovalSoftwareSubmitDTO extends BaseBean {
    List <ApprovalSoftwareSubmit> approvalSoftwareSubmits;
    /**
     * 审批软件
     */
    @LogFormat(name="pages.approvalSoftWare")
    List<String> softNames;
    @LogFormat(name="pages.approvalOpinion", formatClass = IntegerDict.class, formatMethod = "formatAgree")
    Integer type;
}
