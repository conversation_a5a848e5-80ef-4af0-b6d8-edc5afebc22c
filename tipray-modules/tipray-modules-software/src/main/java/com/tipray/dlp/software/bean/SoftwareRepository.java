package com.tipray.dlp.software.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tipray.dlp.annotation.LogFormat;
import com.tipray.dlp.bean.ModifyVerBean;
import com.tipray.dlp.mybatis.annotation.Unique;
import com.tipray.dlp.software.bean.dict.SoftwareArchitectureDict;
import com.tipray.dlp.software.bean.dict.SoftwareSupportedOsDict;
import com.tipray.dlp.software.service.SoftwareCategoryService;
import com.tipray.dlp.util.FileUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 软件库
 *
 * <AUTHOR>
 * @date 2023/8/9
 */
@Setter
@Getter
@NoArgsConstructor
@TableName("software_repository")
@KeySequence(value = "software_repository_id_seq", dbType = DbType.KINGBASE_ES)
public class SoftwareRepository extends ModifyVerBean {
    /** 发布时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    // @TableField(fill = FieldFill.INSERT) // 需实现 MetaObjectHandler 才行，全局配置可能会影响到其它策略
    private Date publishTime;
    @LogFormat(name = "table.setupFile")
    private String originalFilename;
    @LogFormat(name = "文件名后缀", hidden = true)
    @TableField(exist = false)
    private String suffix;
    @LogFormat(name = "FTP服务器文件GUID", hidden = true)
    private String guid;
    @Unique
    @LogFormat(name = "table.softwareName")
    private String name;
    // @LogFormat(name = "table.softwareIcon")
    private String icon;
    @LogFormat(name = "table.softwareCategory", formatClass = SoftwareCategoryService.class, formatMethod = "getCategoryName", delay = false)
    private Long category;
    @LogFormat(name = "table.softwareSize", formatClass = FileUtil.class, formatMethod = "formatSize")
    private Long size;
    @LogFormat(name = "安装包md5", hidden = true)
    private String md5;
    @LogFormat(name = "安装包快速md5", hidden = true)
    private String quickMd5;
    @LogFormat(name = "table.softwareVersion")
    private String version;
    @LogFormat(name = "table.softwareBits")
    private Integer bits;
    @LogFormat(name = "table.softwareVendor")
    private String manufacturer;
    /**
     * 1：Windows
     * 2：macOS
     * 3：Linux
     */
    @LogFormat(name = "table.supportPlatform", formatClass = SoftwareRepository.class, formatMethod = "formatPlatform")
    private Integer platform;
    /**
     * 按位存储
     * 1 x86
     * 2 x64
     * 3 Arm32
     * 4 Arm64
     */
    @LogFormat(name = "table.softwareArch", formatClass = SoftwareArchitectureDict.class)
    private Integer architecture;
    /**
     * 0：不限定系统
     * 1：winxp
     * 2：win7
     * 3：win8
     * 4：win10
     * 目前先定windows系统上的，MAC，Linux先不定，如果终端判断在最低能使用系统之下，则提示在当前系统无法安装程序
     */
    @LogFormat(name = "table.supportSystem", formatClass = SoftwareSupportedOsDict.class)
    private String minOsVer;
    @LogFormat(name = "table.softwareCopyright")
    private String copyright;
    @LogFormat(name = "table.softwareDescription")
    private String description;
    @LogFormat(name = "table.installGuide")
    private String installGuide;
    @LogFormat(name = "table.installParam")
    private String installParam;
    @LogFormat(name = "table.upgradeParam")
    private String upgradeParam;
    @LogFormat(name = "table.releaseNote")
    private String releaseNote;
    /**
     * 当文件为 7z,zip,rar,iso 等压缩包文件时需要设置解压后执行的主文件程序
     */
    @LogFormat(name = "table.mainExe")
    private String mainExe;
    /**
     * 0:未上传,1:上传中,2:已上传
     */
    @LogFormat(name = "上传状态", hidden = true)
    private Integer uploadStatus;
    @LogFormat(name = "创建者", hidden = true)
    private Long createUser;
    @LogFormat(name = "修改者", hidden = true)
    private Long modifyUser;
    /** 操作用户 */
    @TableField(exist = false)
    private String operateUser;
    @LogFormat(name = "table.remark")
    private String remark;
    @TableField(exist = false)
    private List<Long> uploadServerIds;
    @LogFormat(name = "上传到服务器")
    @TableField(exist = false)
    private String uploadServerNames;

    public static String formatPlatform(Integer platform) {
        if (platform == null) {
            return "";
        }
        switch (platform) {
            case 1:
                return "Windows";
            case 2:
                return "macOS";
            case 3:
                return "Linux";
            default:
                return "";
        }
    }
}
