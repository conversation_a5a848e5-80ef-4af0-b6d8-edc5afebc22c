package com.tipray.dlp.software.bean;

import com.tipray.dlp.annotation.LogFormat;
import com.tipray.dlp.bean.BaseStrategy;
import com.tipray.dlp.software.service.SoftwareCategoryService;
import com.tipray.dlp.software.service.SoftwareRepositoryService;
import com.tipray.dlp.util.BeanUtil;
import com.tipray.dlp.util.CollectionUtil;
import com.tipray.dlp.util.I18nUtils;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.*;
import java.util.function.Function;

/**
 * 软件下载策略
 *
 * <AUTHOR>
 * @date 2023/9/5
 */
@Setter
@Getter
@NoArgsConstructor
public class SoftwareDownloadStrategy extends BaseStrategy {
    @LogFormat(name = "table.stgMessage", formatClass = SoftwareDownloadStrategy.class)
    private List<SoftwareItem> softwareList;

    private String stgMessage;

    public static String format(List<?> softwareList) {
        return formatStgMessage(softwareList, true);
    }

    public static String formatStgMessage(List<?> softwareList, boolean wrapped) {
        if (CollectionUtil.isEmpty(softwareList)) {
            return "";
        }
        List<Long> softwareIds = new ArrayList<>();
        List<Long> categoryIds = new ArrayList<>();
        for (Object softwareItem : softwareList) {
            Long id = getLongValue(softwareItem, "id", SoftwareItem::getId);
            Long objType = getLongValue(softwareItem, "objType", SoftwareItem::getObjType);
            if (objType == null || objType == 0) {
                softwareIds.add(id);
            } else {
                categoryIds.add(id);
            }
        }
        StringBuilder sb = new StringBuilder();
        if (!softwareIds.isEmpty()) {
            Map<Long, String> namesMap = BeanUtil.getBean(SoftwareRepositoryService.class).listNamesByIds(softwareIds);
            if (!namesMap.isEmpty()) {
                if (wrapped) {
                    sb.append('{');
                }
                joinMapValues(namesMap.values(), sb.append(I18nUtils.get("table.softwareName")).append(": "));
            }
        }
        if (!categoryIds.isEmpty()) {
            Map<Long, String> namesMap = BeanUtil.getBean(SoftwareCategoryService.class).listNamesByIds(categoryIds);
            if (!namesMap.isEmpty()) {
                if (sb.length() > 0) {
                    sb.append("; ");
                } else if (wrapped) {
                    sb.append('{');
                }
                joinMapValues(namesMap.values(), sb.append(I18nUtils.get("table.softwareCategory")).append(": "));
            }
        }
        if (wrapped && sb.length() > 0) {
            sb.append('}');
        }
        return sb.toString();
    }

    private static void joinMapValues(Collection<String> values, StringBuilder sb) {
        Iterator<String> it = values.iterator();
        String firstValue = it.next();
        if (!it.hasNext()) {
            sb.append(firstValue);
            return;
        }
        sb.append('[').append(firstValue).append(", ").append(it.next());
        while (it.hasNext()) {
            sb.append(", ").append(it.next());
        }
        sb.append(']');
    }

    private static Long getLongValue(Object obj, String key, Function<SoftwareItem, Long> getter) {
        if (obj instanceof SoftwareItem) {
            return getter.apply((SoftwareItem) obj);
        }
        if (obj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> map = (Map<String, Object>) obj;
            Object value = map.get(key);
            if (value == null) {
                return null;
            }
            if (value instanceof Long) {
                return (Long) value;
            }
            if (value instanceof Number) {
                return ((Number) value).longValue();
            }
            try {
                return Long.valueOf(value.toString());
            } catch (Exception ignored) {}
        }
        return 0L;
    }
}
