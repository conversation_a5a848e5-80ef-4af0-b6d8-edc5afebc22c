package com.tipray.dlp.software.bean;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tipray.dlp.annotation.LogFormat;
import com.tipray.dlp.bean.ModifyVerGroup;
import com.tipray.dlp.mybatis.annotation.Unique;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 软件分类
 * <AUTHOR>
 * @date 2023/8/9
 */
@Setter
@Getter
@NoArgsConstructor
@TableName("software_category")
@KeySequence(value = "software_category_id_seq", dbType = DbType.KINGBASE_ES)
public class SoftwareCategory extends ModifyVerGroup {
    @Unique
    @LogFormat(name = "pages.groupName")
    private String name;
    // @LogFormat(name = "table.icon")
    private String icon;
    @LogFormat(name = "table.remark")
    private String remark;
    private boolean editable;
    @TableField(exist = false)
    private Long parentId;
}
