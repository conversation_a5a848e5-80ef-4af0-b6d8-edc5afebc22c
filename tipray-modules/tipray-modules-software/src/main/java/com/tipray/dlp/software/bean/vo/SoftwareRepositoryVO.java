package com.tipray.dlp.software.bean.vo;

import com.tipray.dlp.annotation.LogFormat;
import com.tipray.dlp.mybatis.bean.PageVO;
import com.tipray.dlp.software.bean.SoftwareRepository;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString
@NoArgsConstructor
public class SoftwareRepositoryVO extends PageVO<SoftwareRepository> {
    private String name;
    @LogFormat(name = "table.name")
    private List<String> names;
    private Long category;
    private String version;
    /**
     * x86, x64, Arm64
     */
    private String architecture;
    private String description;
    // 未勾选的id
    List<Long> backupUnSelectedIds;
    /**
     * 0:未上传,1:上传中,2:已上传
     */
    private Integer uploadStatus;
}
