package com.tipray.dlp.software.bean.dict;

import com.tipray.dlp.util.I18nUtils;
import com.tipray.dlp.util.StringUtil;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 软件支持操作系统字典
 */
public enum SoftwareSupportedOsDict {
    ALL(0, "pages.unlimitedSystem"),
    WIN_XP(1, "Windows XP"),
    WIN_SERVER_2003(2, "Windows Server 2003"),
    WIN_VISTA(3, "Windows Vista"),
    WIN_7(4, "Windows 7"),
    WIN_SERVER_2008(5, "Windows Server 2008"),
    WIN_8(6, "Windows 8"),
    WIN_SERVER_2012(7, "Windows Server 2012"),
    WIN_SERVER_2016(8, "Windows Server 2016"),
    WIN_SERVER_2019(9, "Windows Server 2019"),
    WIN_10(10, "Windows 10"),
    WIN_11(11, "Windows 11"),
    WIN_SERVER_2022(12, "Windows Server 2022");

    private int code;
    private String displayName;

    SoftwareSupportedOsDict(int code, String displayName) {
        this.code = code;
        this.displayName = displayName;
    }

    public int getCode() {
        return code;
    }

    public String getDisplayName() {
        if (displayName.startsWith("pages.")) {
            return I18nUtils.get(displayName);
        }
        return displayName;
    }

    public static String getOsDisplayNameByCode(int code) {
        for (SoftwareSupportedOsDict os : values()) {
            if (os.code == code) {
                return os.getDisplayName();
            }
        }
        return null;
    }

    public static String format(String osVersions) {
        if (StringUtil.isBlank(osVersions)) {
            return "";
        }
        int[] osArr = Arrays.stream(StringUtil.split(osVersions, '|'))
                .mapToInt(value -> NumberUtils.toInt(value, -1))
                .filter(value -> value > -1)
                .distinct()
                .toArray();
        if (ArrayUtils.contains(osArr, 0)) {
            return ALL.getDisplayName();
        }
        return Arrays.stream(osArr)
                .mapToObj(SoftwareSupportedOsDict::getOsDisplayNameByCode)
                .filter(Objects::nonNull)
                .collect(Collectors.joining(","));
    }
}
