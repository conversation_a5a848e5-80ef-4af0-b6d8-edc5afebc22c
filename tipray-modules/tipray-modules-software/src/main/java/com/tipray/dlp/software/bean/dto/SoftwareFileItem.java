package com.tipray.dlp.software.bean.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/1
 */
@Getter
@Setter
public class SoftwareFileItem {
    private String md5;
    private String guid;
    private List<Long> serverDevIds;

    public SoftwareFileItem(String md5, String guid) {
        this.md5 = md5;
        this.guid = guid;
        this.serverDevIds = new ArrayList<>();
    }

    public void addServerDevId(Long serverDevId) {
        if (!this.serverDevIds.contains(serverDevId)) {
            this.serverDevIds.add(serverDevId);
        }
    }

    public void delServerDevId(Long serverDevId) {
        this.serverDevIds.remove(serverDevId);
    }
}
