package com.tipray.dlp.software.bean.vo;

import com.tipray.dlp.annotation.LogFormat;
import com.tipray.dlp.bean.vo.LogVO;
import com.tipray.dlp.software.bean.dict.SoftwareAuditTypeDict;
import com.tipray.dlp.util.LogUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/11/1
 */
@Setter
@Getter
@NoArgsConstructor
@ToString(callSuper = true)
public class SoftwareAuditLogVO extends LogVO {
    /**
     * 操作类型
     */
    @LogFormat(name = "table.operateType", formatClass = SoftwareAuditTypeDict.class)
    private Integer auditType;
    /**
     * 软件名称
     */
    @LogFormat(name = "table.softwareName", formatClass = LogUtil.class, formatMethod = "formatEmptyName", delay = false)
    private String softwareName;
}
