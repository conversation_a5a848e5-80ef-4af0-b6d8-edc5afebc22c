package com.tipray.dlp.software.bean.vo;

import com.tipray.dlp.annotation.LogFormat;
import com.tipray.dlp.bean.vo.LogVO;
import com.tipray.dlp.util.I18nUtils;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@ToString(callSuper = true)
public class ApprovalSoftwareSubmitLogVO extends LogVO {
    @LogFormat(name = "table.softwareName")
    private String name;
    /**
     * 审批结果 审批结果 0:拒绝接入 1：同意接入
     */
    @LogFormat(name="table.approvalResult", formatClass = ApprovalSoftwareSubmitLogVO.class, formatMethod = "formatApprovalResult")
    private String approvalResult;

    private String guids;

    private Integer excludeServerId;
    public static String formatApprovalResult(String approvalResult) {
        return approvalResult == null ? null :
                "0".equals(approvalResult)? I18nUtils.get("pages.refuse","拒绝"):
                        "1".equals(approvalResult)?I18nUtils.get("pages.agree","同意"):null;
    }
}
