package com.tipray.dlp.software.bean.dict;

/**
 * 软件架构字典
 */
public enum SoftwareArchitectureDict {
    x86,
    x64,
    Arm32,
    Arm64;

    public static String format(Integer arch) {
        if (arch == null) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        SoftwareArchitectureDict[] architectures = values();
        int archCode;
        for (int i = 0, n = architectures.length; i < n; i++) {
            archCode = 1 << i;
            if ((arch & archCode) == archCode) {
                if (sb.length() > 0) {
                    sb.append(',');
                }
                sb.append(architectures[i]);
            }
        }
        return sb.toString();
    }
}
