package com.tipray.dlp.software.bean;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/9/5
 */
@Getter
@Setter
public class SoftwareItem {
    /**
     * ObjType为0 当作软件id
     * ObjType为1 当作类型id
     */
    private Long id;
    /**
     * ObjType为0、为1时，都需要填写，
     * 为1时 id 和 type 是一致的
     */
    private Long type;
    /**
     * 对象类型
     * 0 软件id
     * 1 类型id
     */
    private Long objType;
}
