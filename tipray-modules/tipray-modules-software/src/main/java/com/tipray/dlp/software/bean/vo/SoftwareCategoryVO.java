package com.tipray.dlp.software.bean.vo;

import com.tipray.dlp.annotation.LogFormat;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/8/21
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
public class SoftwareCategoryVO {
    /** 分类ID */
    private Long id;
    /**
     * 删除分类时对该分类下软件的处理方式
     * <ol>
     *     <li>下架该分类下的所有软件</li>
     *     <li>将该分类下的所有软件移至分类</li>
     * </ol>
     */
    private Integer deleteType;
    /** 将该分类下的所有软件移至新分类的ID */
    private Long movedId;
    @LogFormat(name = "pages.groupName")
    private String categoryName;

}
