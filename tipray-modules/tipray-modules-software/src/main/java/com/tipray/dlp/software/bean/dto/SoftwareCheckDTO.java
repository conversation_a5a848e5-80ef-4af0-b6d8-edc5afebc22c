package com.tipray.dlp.software.bean.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/11/15
 */
@Getter
@Setter
@ToString
public class SoftwareCheckDTO {
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 安装包MD5
     */
    private String fileMd5;
    /**
     * 文件大小
     */
    private Long fileSize;
    /**
     * 文件格式
     */
    private String fileFormat;
    /**
     * 图标宽度
     */
    private Integer iconWidth;
    /**
     * 图标高度
     */
    private Integer iconHeight;
    /**
     * 当文件为 7z,zip,rar,iso 等压缩包文件时需要设置解压后执行的主文件程序
     */
    private String mainExe;

    private Integer source;
}
