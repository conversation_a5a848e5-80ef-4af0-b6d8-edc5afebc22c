package com.tipray.dlp.software.dao;

import com.tipray.dlp.dao.BaseDao;
import com.tipray.dlp.software.bean.SoftwareCategory;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface SoftwareCategoryDao extends BaseDao<SoftwareCategory> {
    @Select("SELECT name from software_category where id = #{value}")
    String getCategoryName(Long id);
}
