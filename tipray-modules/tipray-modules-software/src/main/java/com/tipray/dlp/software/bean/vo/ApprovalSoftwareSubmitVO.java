package com.tipray.dlp.software.bean.vo;

import com.tipray.dlp.annotation.LogFormat;
import com.tipray.dlp.mybatis.bean.PageVO;
import com.tipray.dlp.software.bean.ApprovalSoftwareSubmit;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@NoArgsConstructor
@ToString(callSuper = true)
public class ApprovalSoftwareSubmitVO extends PageVO<ApprovalSoftwareSubmit> {
    @LogFormat(name = "table.softwareName")
    private String name;
}
