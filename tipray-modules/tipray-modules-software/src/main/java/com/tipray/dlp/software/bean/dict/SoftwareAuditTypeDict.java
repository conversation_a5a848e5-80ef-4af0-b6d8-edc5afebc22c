package com.tipray.dlp.software.bean.dict;

import com.tipray.dlp.util.I18nUtils;

public enum SoftwareAuditTypeDict {
    /**
     * 1 下载
     */
    download,
    /**
     * 2 卸载
     */
    uninstall,
    /**
     * 3 升级
     */
    upgrade,
    /**
     * 4 安装
     */
    install,
    /**
     * 5 手动清空下载的安装包
     */
    clearInstallPkg;

    @Override
    public String toString() {
        return I18nUtils.get("table." + this.name());
    }

    public int code() {
        return this.ordinal() + 1;
    }

    public static String format(Integer auditType) {
        if (auditType == null) {
            return "";
        }
        int code = auditType - 1;
        for (SoftwareAuditTypeDict value : values()) {
            if (value.ordinal() == code) {
                return value.toString();
            }
        }
        return "";
    }
}
