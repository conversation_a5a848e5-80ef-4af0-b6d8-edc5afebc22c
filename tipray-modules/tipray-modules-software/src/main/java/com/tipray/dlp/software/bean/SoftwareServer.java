package com.tipray.dlp.software.bean;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tipray.dlp.annotation.LogFormat;
import com.tipray.dlp.bean.BackupServerBusiness;
import com.tipray.dlp.bean.BackupServerExtData;
import com.tipray.dlp.bean.BaseServer;
import com.tipray.dlp.bean.dict.BackTypeDict;
import com.tipray.dlp.bean.dict.IntegerDict;
import com.tipray.dlp.bean.dict.OsTypeDict;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

/**
 * 软件中心（FTP）服务器
 *
 * <AUTHOR>
 * @date 2023/8/7
 */
@Setter
@Getter
@TableName("dev_server")
public class SoftwareServer extends BaseServer {
    public final static Integer TYPE = 0xAC;

    /** ftp传输用户名*/
    private String userName;
    /** ftp传输密码*/
    private String password;
    /**
     * 备份主路径
     */
    @LogFormat(name = "pages.backupPrimaryPath")
    private String mainPath;
    /**
     * 操作系统{1：windows 2：linux}
     */
    @LogFormat(name="table.osType", formatClass = OsTypeDict.class)
    private Integer osType;
    /**
     * 上传流量限制(kb/s)
     */
    @LogFormat(name="table.uploadSpeedLimit")
    private Integer uploadSpeedLimit;
    /**
     * 下载流量限制(kb/s)
     */
    @LogFormat(name="table.downSpeedLimit")
    private Integer downSpeedLimit;
    /**
     * 数据端口最小值
     */
    @LogFormat(name="table.minDataPort")
    private Integer minDataPort;
    /**
     * 数据端口最大值
     */
    @LogFormat(name="table.maxDataPort")
    private Integer maxDataPort;
    /**
     * 备份文件终端最大并发数
     */
    @LogFormat(name="table.maxOccurs")
    private Integer maxOccurs;
    /**
     * 磁盘写入限制（单位/M）
     */
    @LogFormat(name="table.diskSpaceLimit")
    private Integer diskSpaceLimit;
    /**
     * 磁盘告警（单位/M）
     */
    @LogFormat(name="table.diskShortageAlert")
    private Integer diskShortageAlert;
    /**
     * 是否开启外网Ip
     */
    @LogFormat(name="pages.enableInternetIP", formatClass = IntegerDict.class)
    private Integer enableInternet;
    /**
     * 是否强制返回公网Ip
     */
    @LogFormat(name = "table.enableForceRetIP", formatClass = IntegerDict.class)
    private Integer enableForceRetIP;

    /**
     * 传输方式  是否允许未加密Ftp指令进行文件传输
     */
    @LogFormat(name = "table.transferMode", formatClass = IntegerDict.class)
    private Integer transferMode;


    /** 业务信息列表*/
    private List<BackupServerBusiness> businesses;
    /**
     * 支持的业务类型
     * 软件中心安装包-29
     */
    @LogFormat(name = "table.bussNames")
    private String bussNames = BackTypeDict.BACKUP_TYPE_SOFTWARE_CENTER.getRemark();
    /**
     * 支持的业务类型集合
     */
    private List<Integer> bussTypes = Collections.singletonList(BackTypeDict.BACKUP_TYPE_SOFTWARE_CENTER.getCode());

    /** 同步映射总部ID*/
    private Long refDevId;
    /** 同步开关，按位*/
    private Integer syncType;
    /** 是否是分部服务器*/
    private Boolean isBranch;
    /** 是否是总部服务器*/
    private Boolean isHeadquarters;
    /** 映射关系变更后是否重复上传{0:不重复上传 1:重复上传}*/
    private Integer resyncEnabled;
    /** 同步开关，按位(扩展)*/
    private Integer syncTypeEx;
    /** 总部文件服务器名*/
    private String headquartersName;
    /* 在线状态*/
    @TableField(exist = false)
    private Integer onlineStatus;

    public SoftwareServer() {
        super.setDevType(TYPE);
    }

    public SoftwareServer(Long devId) {
        super.setDevType(TYPE);
        this.setDevId(devId);
    }

    private static final int BUSS_TYPE = 1 << (BackTypeDict.BACKUP_TYPE_SOFTWARE_CENTER.getCode() - 1);

    public int calcBussType() {
        return BUSS_TYPE;
    }

    public void appendExtData(BackupServerExtData data) {
        if (data == null) {
            return;
        }
        BeanUtil.copyProperties(data, this, "id");
    }

    public BackupServerExtData getExtData() {
        BackupServerExtData extData = BeanUtil.copyProperties(this, BackupServerExtData.class);
        extData.setId(this.getDevId());
        return extData;
    }

}
