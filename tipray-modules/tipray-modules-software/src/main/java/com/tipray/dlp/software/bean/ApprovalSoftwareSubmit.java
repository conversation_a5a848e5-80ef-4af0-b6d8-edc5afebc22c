package com.tipray.dlp.software.bean;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tipray.dlp.annotation.LogFormat;
import com.tipray.dlp.bean.BaseBean;
import com.tipray.dlp.bean.dict.IntegerDict;
import com.tipray.dlp.service.UserService;
import com.tipray.dlp.software.bean.dict.SoftwareArchitectureDict;
import com.tipray.dlp.software.bean.dict.SoftwareSupportedOsDict;
import com.tipray.dlp.software.service.SoftwareCategoryService;
import com.tipray.dlp.util.FileUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 软件上架申请
 *
 * <AUTHOR>
 * @date 2024/9/23
 */
@Setter
@Getter
@NoArgsConstructor
@TableName("approval_software_submit")
@KeySequence(value = "approval_software_submit_id_seq", dbType = DbType.KINGBASE_ES)
public class ApprovalSoftwareSubmit extends BaseBean {
    @LogFormat(name = "table.setupFile")
    @TableField("original_file_name")
    private String originalFilename;
    @NotNull
    @LogFormat(name = "FTP服务器文件GUID", hidden = true)
    @TableField("soft_server_id")
    private Integer softServerId;
    @NotBlank
    @LogFormat(name = "FTP服务器文件GUID", hidden = true)
    private String guid;
    private String icon;
    @LogFormat(name = "table.softwareName")
    private String name;
    @LogFormat(name = "table.softwareVersion")
    private String version;
    @LogFormat(name = "table.softwareCategory", formatClass = SoftwareCategoryService.class, formatMethod = "getCategoryName", delay = false)
    @TableField(exist = false)
    private Long category;
    @LogFormat(name = "table.softwareSize", formatClass = FileUtil.class, formatMethod = "formatSize")
    private Long size;
    /**
     * 当文件为 7z,zip,rar,iso 等压缩包文件时需要设置解压后执行的主文件程序
     */
    @LogFormat(name = "table.mainExe")
    private String mainExe;
    /**
     * 1：Windows
     * 2：macOS
     * 3：Linux
     */
    @LogFormat(name = "table.supportPlatform", formatClass = ApprovalSoftwareSubmit.class, formatMethod = "formatPlatform")
    private Integer platform;
    /**
     * 按位存储
     * 1 x86
     * 2 x64
     * 3 Arm32
     * 4 Arm64
     */
    @LogFormat(name = "table.softwareArch", formatClass = SoftwareArchitectureDict.class)
    private Integer architecture;

    /**
     * 0：不限定系统
     * 1：winxp
     * 2：win7
     * 3：win8
     * 4：win10
     * 目前先定windows系统上的，MAC，Linux先不定，如果终端判断在最低能使用系统之下，则提示在当前系统无法安装程序
     */
    @LogFormat(name = "table.supportSystem", formatClass = SoftwareSupportedOsDict.class)
    @TableField("min_os_version")
    private String minOsVer;
    /*@LogFormat(name = "table.softwareBits")
    private Integer bits;*/
    @LogFormat(name = "table.softwareVendor")
    private String manufacturer;
    @LogFormat(name = "table.softwareCopyright")
    @TableField("copy_right")
    private String copyright;
    @LogFormat(name = "table.softwareDescription")
    private String description;
    @LogFormat(name = "table.releaseNote")
    @TableField("release_note")
    private String releaseNote;
    @LogFormat(name = "table.installParam")
    @TableField("install_parameter")
    private String installParam;
    @LogFormat(name = "table.upgradeParam")
    @TableField("upgrade_parameter")
    private String upgradeParam;


    @LogFormat(name = "创建者", hidden = true)
    @TableField(exist = false)
    private Long createUser;
    @LogFormat(name = "修改者", hidden = true)
    @TableField(exist = false)
    private Long modifyUser;
    /** 操作用户 */
    @TableField(exist = false)
    private String operateUser;
    @LogFormat(name = "table.remark")
    private String remark;
    @TableField(exist = false)
    private List<Long> uploadServerIds;
    @LogFormat(name = "上传到服务器")
    @TableField(exist = false)
    private String uploadServerNames;

    @LogFormat(name="table.terminalCode")
    @TableField("term_id")
    private Long terminalId;
    /** 终端操作用户id */
    @LogFormat(name = "pages.userName", formatClass = UserService.class, formatMethod = "getName")
    @TableField("user_id")
    private Long userId;
    @NotBlank
    @LogFormat(name = "安装包md5", hidden = true)
    private String md5;
    @LogFormat(name = "安装包快速md5", hidden = true)
    @TableField("quick_md5")
    private String quickMd5;
    /** 用户名称*/
    @TableField(exist = false)
    private String userName;
    /** 终端名称*/
    @LogFormat(name="table.terminalName")
    @TableField(exist = false)
    private String terminalName;
    @TableField(exist = false)
    private Date createTime;
    @TableField(exist = false)
    private Date modifyTime;

    /**
     * 审批意见
     */
    @LogFormat(name="pages.approvalOpinion", formatClass = IntegerDict.class, formatMethod = "formatAgree")
    @TableField(exist = false)
    private Integer type;

    public static String formatPlatform(Integer platform) {
        if (platform == null) {
            return "";
        }
        switch (platform) {
            case 1:
                return "Windows";
            case 2:
                return "macOS";
            case 3:
                return "Linux";
            default:
                return "";
        }
    }
}
