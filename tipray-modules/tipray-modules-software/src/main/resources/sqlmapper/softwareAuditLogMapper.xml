<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.dlp.software.dao.SoftwareAuditLogDao">

    <resultMap id="SoftwareAuditLogMap" type="com.tipray.dlp.software.bean.SoftwareAuditLog">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="auditType" column="audit_type"/>
        <result property="result" column="result"/>
        <result property="softwareName" column="software_name"/>
        <result property="softwareVersion" column="software_version"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="auditType != null">
                and audit_type = #{auditType}
            </if>
            <if test="softwareName != null and softwareName != ''">
                and software_name like CONCAT('%',#{softwareName},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from software_audit_log
        <include refid="sql_where"></include>
    </select>
    <select id="listByVO" resultMap="SoftwareAuditLogMap">
        select id, record_num, term_id, user_id, create_time, audit_type, result, software_name, software_version, term_group_id, user_group_id
        from software_audit_log
        <include refid="sql_where"></include>
    </select>

</mapper>
