<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.dlp.software.dao.ApprovalSoftwareSubmitLogDao">

    <resultMap id="ApprovalSoftwareSubmitLogMap" type="com.tipray.dlp.software.bean.ApprovalSoftwareSubmitLog">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="originalFilename" column="original_file_name"/>
        <result property="name" column="name"/>
        <result property="softServerId" column="soft_server_id"/>
        <result property="icon" column="icon"/>
        <result property="guid" column="guid"/>
        <result property="version" column="version"/>
        <result property="size" column="size"/>
        <result property="md5" column="md5"/>
        <result property="quickMd5" column="quick_md5"/>
        <result property="mainExe" column="main_exe"/>
        <result property="platform" column="platform"/>
        <result property="architecture" column="architecture"/>
        <result property="minOsVer" column="min_os_version"/>
        <result property="manufacturer" column="manufacturer"/>
        <result property="copyright" column="copy_right"/>
        <result property="description" column="description"/>
        <result property="releaseNote" column="release_note"/>
        <result property="installParam" column="install_parameter"/>
        <result property="upgradeParam" column="upgrade_parameter"/>
        <result property="approvalResult" column="approval_result"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="guids != null and guids != ''">
                and guid in (${guids})
            </if>
            <if test="approvalResult != null and approvalResult != ''">
                and approval_result = #{approvalResult}
            </if>
            <if test="excludeServerId != null">
                and soft_server_id != #{excludeServerId}
            </if>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from approval_software_submit_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="ApprovalSoftwareSubmitLogMap">
        select id, term_id, user_id, create_time, original_file_name, name, soft_server_id, icon, guid, version, size, md5, quick_md5, main_exe, platform, architecture, min_os_version, manufacturer,
            copy_right, description, release_note, install_parameter, upgrade_parameter, approval_result
        from approval_software_submit_log
        <include refid="sql_where"></include>
        order by create_time desc
    </select>
    <delete id="deleteById" parameterType="java.lang.Long">
        delete from approval_software_submit_log where id=#{id}
    </delete>
    <!-- <select id="selectPageByVO" resultMap="ApprovalSoftwareApplyMap">
         select id, term_id, user_id, create_time, original_file_name, soft_server_id, icon, guid, version, size, md5, quick_md5, main_exe, platform, architecture, min_os_version, manufacturer, copy_right, description, release_note, install_parameter, upgrade_parameter, remark
         from approval_software_submit
         <include refid="sql_where"></include>
         order by create_time desc
     </select>-->
</mapper>
