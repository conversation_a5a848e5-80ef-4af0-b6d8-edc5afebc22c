<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>tipray-modules</artifactId>
        <groupId>com.tipray</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <description>基础模块，包括BaseBean、多语言工具、常用工具等</description>

    <artifactId>tipray-modules-base</artifactId>
    <version>1.0</version>

    <dependencies>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-ftp</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-tools</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-mybatis</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-websocket</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-register</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-communication</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-send</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- spring 身份认证 权限授权，使用security 和 JWT-->
        <!--<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
        </dependency>
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
        </dependency>
        <!-- xml bean 对象装换 -->
        <dependency>
            <groupId>org.codehaus.castor</groupId>
            <artifactId>castor-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.castor</groupId>
            <artifactId>castor-xml</artifactId>
        </dependency>
        <!-- 加解密 -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
        </dependency>
        <!-- iText asian/pdf-->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>

        <!-- httpclient config start -->
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents.core5</groupId>
            <artifactId>httpcore5</artifactId>
        </dependency>
        <!-- httpclient config end -->

        <dependency>
            <groupId>org.tmatesoft.svnkit</groupId>
            <artifactId>svnkit</artifactId>
            <optional>true</optional>
            <exclusions>
                <exclusion>
                    <groupId>net.java.dev.jna</groupId>
                    <artifactId>jna</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.java.dev.jna</groupId>
                    <artifactId>platform</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--查找子类或接口的实现类-->
        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
        </dependency>
    </dependencies>
</project>
