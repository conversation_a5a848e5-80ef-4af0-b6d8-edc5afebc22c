<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapping>
<mapping>
    <class name="com.tipray.dlp.bean.dto.XmlRootDTO">
        <map-to xml="root"/>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.dto.PropertyXmlDTO">
            <bind-xml name="property" node="element" />
        </field>
    </class>
    <class name="com.tipray.dlp.bean.dto.PropertyXmlDTO">
        <map-to xml="property"/>
        <field name="code" type="java.lang.String">
            <bind-xml name="code" node="attribute"/>
        </field>
        <field name="value" type="java.lang.String">
            <bind-xml name="value" node="attribute"/>
        </field>
        <field name="type" type="java.lang.String">
            <bind-xml name="type" node="attribute"/>
        </field>
        <field name="remark" type="java.lang.String">
            <bind-xml name="remark" node="attribute"/>
        </field>
        <field name="editable" type="java.lang.Boolean">
            <bind-xml name="editable" node="attribute"/>
        </field>
        <field name="rule" type="com.tipray.dlp.bean.dto.PropertyRuleDTO">
            <bind-xml name="rule" node="element"/>
        </field>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.dto.PropertyXmlDTO">
            <bind-xml name="property" node="element" />
        </field>
    </class>
    <class name="com.tipray.dlp.bean.dto.PropertyRuleDTO">
        <map-to xml="rule"/>
        <field name="type" type="java.lang.String">
            <bind-xml name="type" node="attribute"/>
        </field>
        <field name="format" type="java.lang.String">
            <bind-xml name="format" node="attribute"/>
        </field>
        <field name="min" type="java.lang.Integer">
            <bind-xml name="min" node="attribute"/>
        </field>
        <field name="max" type="java.lang.Integer">
            <bind-xml name="max" node="attribute"/>
        </field>
        <field name="minlength" type="java.lang.Integer">
            <bind-xml name="minlength" node="attribute"/>
        </field>
        <field name="maxlength" type="java.lang.Integer">
            <bind-xml name="maxlength" node="attribute"/>
        </field>
        <field name="multiple" type="java.lang.Boolean">
            <bind-xml name="multiple" node="attribute"/>
        </field>
        <field name="truthy" type="java.lang.String">
            <bind-xml name="truthy" node="attribute"/>
        </field>
        <field name="falsy" type="java.lang.String">
            <bind-xml name="falsy" node="attribute"/>
        </field>
        <field name="items" collection="collection" type="com.tipray.dlp.bean.dto.KeyValueDTO">
            <bind-xml name="option" node="element"/>
        </field>
    </class>
    <class name="com.tipray.dlp.bean.dto.KeyValueDTO">
        <map-to xml="option"/>
        <field name="key" type="java.lang.String">
            <bind-xml name="key" node="attribute"/>
        </field>
        <field name="value" type="java.lang.String">
            <bind-xml name="value" node="attribute"/>
        </field>
    </class>
</mapping>