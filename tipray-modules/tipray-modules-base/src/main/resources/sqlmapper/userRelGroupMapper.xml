<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.UserRelGroupDao">
    <resultMap id="baseResultMap" type="com.tipray.dlp.bean.UserRelGroup">
        <id column="id" property="id" />
        <result column="user_id" property="userId"/>
        <result column="group_id" property="groupId"/>
        <result column="deleted" property="deleted"/>
        <result column="modify_ver" property="modifyVer"/>
    </resultMap>

    <select id="selectObjByUserIds" resultMap="baseResultMap">
        select id, user_id, group_id, modify_ver, deleted from rel_group_user where user_id in (@relatedDataIds)
    </select>

</mapper>
