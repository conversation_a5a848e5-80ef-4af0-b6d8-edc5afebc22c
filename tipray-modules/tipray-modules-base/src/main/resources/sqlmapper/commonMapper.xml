<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.CommonDao">

    <select id="getById" resultType="java.util.HashMap">
        select  * from ${table}
        where id = #{id}
    </select>

    <select id="getByIds" resultType="java.util.HashMap">
        select  * from ${table}
        where id in (${ids})
    </select>

    <select id="getSqlResult" resultType="String">
        ${sql}
    </select>

    <select id="listByIds" resultType="java.util.HashMap">
        select
        <foreach collection="fields" item="field" separator=",">
            ${field}
        </foreach>
        from ${table}
        where id in (${ids})
    </select>

    <select id="list" resultType="java.util.HashMap">
        select *
        from ${table}
    </select>

    <select id="countTable" resultType="java.lang.Integer" parameterType="java.lang.String">
        ${value}
    </select>

    <select id="executeQuery" resultType="java.util.HashMap">
        ${sql}
    </select>
    <select id="executeShardingQuery" resultType="java.util.LinkedHashMap">
        ${sql}
    </select>
    <update id="executeUpdate" parameterType="java.util.List">
        <foreach collection="list" item="sql" separator=";">
            ${sql}
        </foreach>
    </update>
    <insert id="executeInsert" parameterType="java.util.List">
        <foreach collection="list" item="sql" separator=";">
            ${sql}
        </foreach>
    </insert>
</mapper>
