<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ServerDao">

    <resultMap type="com.tipray.dlp.bean.BaseServer" id="resultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="devType" column="dev_type"/>
        <result property="devId" column="dev_id"/>
        <result property="accessPwd" column="access_pwd"/>
        <result property="intranetIp" column="intranet_ip"/>
        <result property="internetIp" column="internet_ip"/>
        <result property="intranetIpv6" column="intranet_ipv6"/>
        <result property="internetIpv6" column="internet_ipv6"/>
        <result property="intranetPort" column="intranet_port"/>
        <result property="internetPort" column="internet_port"/>
        <result property="version" column="version"/>
        <result property="remark" column="remark"/>
        <!--<result property="active" column="active"/>-->
        <result property="groupId" column="group_id"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="select_where_vo">
        <where>
            <if test="devType != null and devType != 0">
                and dev_type = #{devType}
            </if>
            <if test="devTypes != null and devTypes != ''">
                and dev_type in (${devTypes})
            </if>
            <if test="groupId != null">
                and group_id = #{groupId}
            </if>
            <if test="groupIds != null and groupIds != ''">
                and group_id in (${groupIds})
            </if>
            <if test="devIds != null">
                and dev_id in (${devIds})
            </if>
            <if test="exceptServerIds != null">
                and dev_id not in (${exceptServerIds})
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and name like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="name != null and name != ''">
                and name like CONCAT('%',#{name},'%')
            </if>
            <if test="devId != null">
                and dev_id like CONCAT('%',#{devId},'%')
            </if>
            <if test="intranetIp != null and intranetIp != ''">
                and intranet_ip like CONCAT('%',#{intranetIp},'%')
            </if>
            <if test="intranetIpv6 != null and intranetIpv6 != ''">
                and intranet_ipv6 like CONCAT('%',#{intranetIpv6},'%')
            </if>
            <if test="intranetPort != null">
                and intranet_port like CONCAT('%',#{intranetPort},'%')
            </if>

            <if test="internetIp != null and internetIp != ''">
                and internet_ip like CONCAT('%',#{internetIp},'%')
            </if>
            <if test="internetIpv6 != null and internetIpv6 != ''">
                and internet_ipv6 like CONCAT('%',#{internetIpv6},'%')
            </if>
            <if test="internetPort != null">
                and internet_port like CONCAT('%',#{internetPort},'%')
            </if>
            <include refid="commonSQL.andNotDel"></include>
        </where>
    </sql>


    <!--通过主键修改数据-->
    <update id="update" parameterType="com.tipray.dlp.bean.BaseServer">
        update dev_server
        <set>
            <if test="devId != null">
                dev_id = #{devId},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="accessPwd != null and accessPwd != ''">
                access_pwd = #{accessPwd},
            </if>
            intranet_ip = #{intranetIp},
            internet_ip = #{internetIp},
            intranet_ipv6 = #{intranetIpv6},
            internet_ipv6 = #{internetIpv6},
            intranet_port = #{intranetPort},
            internet_port = #{internetPort},
            <if test="intranetSsl != null">
                intranet_ssl = #{intranetSsl},
            </if>
            <if test="internetSsl != null">
                internet_ssl = #{internetSsl},
            </if>
            <if test="version != null and version != ''">
                version = #{version},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="groupId != null">
                group_id = #{groupId},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateModifyTimeByDevId">
        update dev_server
        <set>
            modify_time = #{modifyTime}
        </set>
        where dev_id in (${devIds})
    </update>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.ServerVO" resultMap="resultMap">
        select * from dev_server
        <include refid="select_where_vo"/>
    </select>

    <select id="existGroup" parameterType="java.lang.String" resultType="java.lang.Boolean">
        select count(0) from dev_server where group_id in (${value})
    </select>

    <select id="getMaxDevId" resultType="java.lang.Long">
        select MAX(dev_id) from dev_server
        <where>
            <if test="startDevId != null">
                and dev_id &gt; #{startDevId} - 1
            </if>
            <if test="endDevId != null">
                and dev_id &lt; #{endDevId}
            </if>
        </where>
    </select>

    <select id="listEnable" resultType="long">
        select id from sys_user su where su.active=0 and su.mode = 1
    </select>

    <select id="listFileDevIdByBussId" resultType="java.lang.Long">
        select dev_id from dev_server ds where exists
        (SELECT 1 from rel_backup_server_buss bs WHERE bs.dev_id = ds.dev_id AND bs.enabled = 1 AND bs.buss_id IN (@bussIds))
    </select>

</mapper>
