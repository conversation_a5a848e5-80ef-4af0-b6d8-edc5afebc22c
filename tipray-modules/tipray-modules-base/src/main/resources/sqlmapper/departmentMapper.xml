<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DepartmentDao">
    <resultMap id="pageResultMap" type="com.tipray.dlp.bean.dto.DepartmentDTO">
        <id column="id" property="id" />
        <result column="name" property="name"/>
        <result column="parent_id" property="parentId"/>
        <result column="remark" property="remark"/>
        <result column="manager" property="manager"/>
        <result column="managers" property="managers"/>
        <result column="branch_leader" property="branchLeader"/>
    </resultMap>


    <sql id="vo_where_sql">
        <where>
            <if test="parentId != null">
                and parent_id = #{parentId}
            </if>
            <if test="deptIds != null and deptIds != ''">
                and id in (@deptIds)
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and name like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="ids != null and ids != ''">
                and id in (@ids)
            </if>
            <include refid="commonSQL.andNotDel"></include>
        </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.DeptVO" resultMap="pageResultMap">
        select *,
        (SELECT GROUP_CONCAT(rgp.user_id SEPARATOR ',') FROM rel_group_position rgp WHERE g.id = rgp.group_id and TYPE = 1 and deleted = 0) managers ,
        (SELECT rgp.user_id FROM rel_group_position rgp WHERE g.id = rgp.group_id and TYPE = 2 and deleted = 0 LIMIT 1 ) branch_leader
        from group_info g
        <include refid="vo_where_sql"/>
	</select>

    <select id="selectListByVO" parameterType="com.tipray.dlp.bean.vo.DeptVO" resultMap="pageResultMap">
        select *,
        (SELECT GROUP_CONCAT(rgp.user_id SEPARATOR ',') FROM rel_group_position rgp WHERE g.id = rgp.group_id and TYPE = 1 and deleted = 0) managers ,
        (SELECT rgp.user_id FROM rel_group_position rgp WHERE g.id = rgp.group_id and TYPE = 2 and deleted = 0 LIMIT 1 ) branch_leader
        from group_info g
        <include refid="vo_where_sql"/>
    </select>

    <!-- 批量移动部门 -->
    <update id="moveDept">
        update group_info set parent_id = #{parentId} where id in (@ids)
    </update>

    <update id="updateSortNumById">
        update group_info set sort_num = #{sortNum} where id = #{id}
    </update>

    <select id="getByUserId" resultMap="pageResultMap">
        select g.* from group_info g, rel_group_user rgs where rgs.user_id = #{value} and g.id = rgs.group_id
    </select>

    <select id="getByTermId" resultMap="pageResultMap">
        select g.* from group_info g, rel_group_terminal rgt where rgt.term_id = #{value} and g.id = rgt.group_id
    </select>

    <select id="listByTermId" resultMap="pageResultMap">
        select g.* from group_info g, rel_group_terminal rgt where rgt.term_id in (@termIds) and g.id = rgt.group_id
    </select>

    <select id="listDeleteDeptId" resultType="java.lang.Long">
        select id from group_info where deleted = 1
    </select>

    <insert id="insertById" useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO group_info (id, name,sort_num,parent_id,remark,guid,deleted,create_time,modify_time)
            VALUES (#{id},#{name},#{sortNum},#{parentId},#{remark},#{guid},#{deleted},#{createTime},#{modifyTime})
    </insert>

    <insert id="batchInsert"  useGeneratedKeys="true" keyProperty="id" >
        insert into group_info(name, parent_id, remark, create_time, modify_time, sort_num)
        values
        <foreach collection="addList" item="c" separator=",">
            ( #{c.name}, #{c.parentId}, #{c.remark}, #{c.createTime}, #{c.modifyTime}, #{c.sortNum} )
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        update group_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="name = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    <if test="item.name!=null">
                        when id = #{item.id} then #{item.name}
                    </if>
                </foreach>
            </trim>
            <trim prefix="parent_id = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    <if test="item.parentId != null">
                        when id = #{item.id} then #{item.parentId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    <if test="item.remark!=null">
                        when id = #{item.id} then #{item.remark}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    <if test="item.modifyTime!=null">
                        when id = #{item.id} then #{item.modifyTime}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in (@updateList.id)
    </update>

    <!--通过主键物理删除-->
    <delete id="deletePhysicalByIds">
        delete from group_info where id in (@ids)
    </delete>

     <select id="getMaxIdByTableName" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT MAX(id) FROM ${tableName}
    </select>

    <select id="initSeqVal">
        alter SEQUENCE ${seqName} restart with ${maxId};
    </select>

</mapper>
