<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ServerGroupDao">

    <resultMap type="com.tipray.dlp.bean.dto.ServerGroupDTO" id="resultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>
        <result property="area" column="area"/>
        <result property="size" column="size"/>
        <result property="parentId" column="parent_id"/>
        <result property="loadBanlance" column="load_banlance"/>
        <result property="syncEnable" column="sync_enable"/>
    </resultMap>

    <sql id="select_where_vo">
        <where>
            <if test="searchInfo != null and searchInfo != ''">
                and sg.name like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="parentId != null">
                and parent_id = #{parentId}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.ServerGroupVO" resultType="java.lang.Long">
        select count(0) from dev_server_group sg
        <include refid="select_where_vo"></include>
    </select>
    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.ServerGroupVO" resultMap="resultMap">
        select sg.*, (select count(0) from dev_server s where s.group_id=sg.id) size from dev_server_group sg
        <include refid="select_where_vo"></include>
    </select>

    <select id="existChild" parameterType="java.lang.String" resultType="java.lang.Boolean">
		select count(0) from dev_server_group where parent_id in (${value})
	</select>

    <select id="getIdByParentIdAndName" parameterType="com.tipray.dlp.bean.ServerGroup" resultType="java.lang.Integer">
		select id from dev_server_group where name = #{name} and parent_id = #{parentId}
	</select>
</mapper>
