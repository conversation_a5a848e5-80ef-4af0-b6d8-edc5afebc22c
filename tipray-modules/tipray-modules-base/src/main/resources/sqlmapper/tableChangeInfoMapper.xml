<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.TableChangeInfoDao">

    <select id="list" resultType="java.util.Map">
        select tb_name tableName, modify_ver modifyVer from tb_change_info
    </select>

    <select id="countTable" resultType="java.lang.Integer">
        select count(0) from tb_change_info where tb_name=#{value}
    </select>

    <select id="listTable" resultType="java.lang.String">
        select tb_name from tb_change_info
    </select>

    <insert id="insertModifyVer">
        insert into tb_change_info(tb_name, modify_ver, last_modify_dev_id) values (#{table}, #{modifyVer}, #{modifyDevId})
    </insert>

    <update id="incrementModifyVer">
        update tb_change_info set last_modify_dev_id=#{modifyDevId},modify_ver=modify_ver+1, modify_time=now() where tb_name=#{table}
    </update>
    <select id="getModifyVer" resultType="java.lang.Long">
        select modify_ver from tb_change_info where tb_name=#{table}
    </select>

    <insert id="insertDefaultModifyVerValue" parameterType="java.lang.Integer">
        insert into tb_change_info (tb_name, modify_ver, last_modify_dev_id)
        values
        <foreach collection="tables" item="item" separator=",">
            (#{item}, 1, #{devId})
        </foreach>
    </insert>

</mapper>
