<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.UserTerminalDao">

    <delete id="deleteByIds" parameterType="java.lang.String">
        delete from rel_terminal_user
        <where>
            <if test="ids != null and ids != ''">
                id in (${ids})
            </if>
            <if test="ids == null or ids == ''">
                id = null;
            </if>
        </where>
    </delete>

    <delete id="deleteByDeletedUser">
        delete from rel_terminal_user where user_id in (select id from user where deleted = 1)
    </delete>

    <select id="countByDeletedUser" resultType="java.lang.Integer" parameterType="java.lang.String">
        SELECT count(1) FROM rel_terminal_user where user_id in (select id from user where deleted = 1)
    </select>

    <select id="listIdByTerminalId" resultType="java.lang.Long" parameterType="long">
        select id from rel_terminal_user where term_id = #{terminalId}
    </select>

    <select id="listByTerminalIdNotExtensionDeleted" resultType="com.tipray.dlp.bean.TerminalRelUser" parameterType="long">
        select * from rel_terminal_user where term_id = #{terminalId}
    </select>

</mapper>
