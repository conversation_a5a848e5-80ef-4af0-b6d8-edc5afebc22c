<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ServerGroupRelatedDeviceDao">

    <select id="listWithDeletedByGroupId" parameterType="java.lang.Long" resultType="com.tipray.dlp.bean.ServerGroupRelatedDevice">
        select * from rel_server_group_device where group_id = #{value}
    </select>

    <select id="listWithDeletedByGroupIdAndDevType" resultType="com.tipray.dlp.bean.ServerGroupRelatedDevice">
        select * from rel_server_group_device where group_id = #{groupId} and dev_type = #{devType}
    </select>

</mapper>
