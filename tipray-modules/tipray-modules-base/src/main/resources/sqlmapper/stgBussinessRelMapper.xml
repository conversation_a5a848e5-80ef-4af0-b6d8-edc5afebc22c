<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.StgBusinessRelDao">

    <resultMap type="com.tipray.dlp.bean.StgBusinessRel" id="stgBusinessRelMap">
        <result property="id" column="id"/>
        <result property="stgDefId" column="stg_def_id"/>
        <result property="dataId" column="data_id"/>
        <result property="stgNumber" column="stg_number"/>
        <result property="dataType" column="data_type"/>
        <result property="stgDefName" column="stgDefName"/>
    </resultMap>
    <sql id="vo_where_sql">
        <where>
            <if test="dataType != null">
                and rsb.data_type = #{dataType}
            </if>
            <if test="stgDefIds != null and stgDefIds != ''">
                and rsb.stg_def_id in (${stgDefIds})
            </if>
            <if test="dataIds != null and dataIds != ''">
                and rsb.data_id in (${dataIds})
            </if>
        </where>
    </sql>

    <select id="listRel" parameterType="com.tipray.dlp.bean.vo.StgBusinessRelVO" resultMap="stgBusinessRelMap">
        select rsb.id id, rsb.stg_def_id stg_def_id, rsb.data_id data_id, rsb.stg_number stg_number, rsb.data_type data_type, sd.name stgDefName
        from rel_stg_business rsb
        left join stg_def sd on rsb.stg_def_id = sd.id
        <include refid="vo_where_sql"/>
    </select>

</mapper>
