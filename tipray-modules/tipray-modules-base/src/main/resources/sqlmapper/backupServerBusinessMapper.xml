<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.BackupServerBusinessDao">

    <resultMap type="com.tipray.dlp.bean.BackupServerBusiness" id="resultMap">
        <result property="devId" column="dev_id"/>
        <result property="bussId" column="buss_id"/>
        <result property="bussName" column="buss_name"/>
        <result property="relativePath" column="relative_path"/>
        <result property="enableDelete" column="enable_delete"/>
        <result property="fileRetainDays" column="file_retain_days"/>
        <result property="enabled" column="enabled"/>
    </resultMap>

    <select id="listByDevId" resultMap="resultMap">
        select * from rel_backup_server_buss
        where dev_id = #{devId}
    </select>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.tipray.dlp.bean.BackupServerBusiness">
        insert into rel_backup_server_buss(dev_id, buss_id, buss_name, relative_path, enable_delete, file_retain_days, enabled)
        values (#{devId}, #{bussId}, #{bussName}, #{relativePath}, #{enableDelete}, #{fileRetainDays}, #{enabled})
    </insert>

    <delete id="deleteByDevIds">
        delete from rel_backup_server_buss where dev_id in (${value})
    </delete>

    <select id="countByBussId" resultType="java.util.Map">
        select buss_id bussId,count(0) bussSize from rel_backup_server_buss group by buss_id
    </select>

    <select id="getDevIdsByBussId" resultType="java.lang.Long">
        select DISTINCT dev_id from rel_backup_server_buss WHERE buss_id = #{bussId} AND enabled = 1
    </select>

</mapper>
