<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.AuditLogDao">

    <select id="list" resultType="com.tipray.dlp.bean.AuditLogBean">
        select id,
            user_id,
            term_id,
            user_group_id,
            term_group_id,
            create_time as create_date,
            concat_ws('|',${select}) as content
        from ${table}
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="where != null and where != ''">
                and (${where})
            </if>
            <if test="condition != null and condition != ''">
                and (${condition})
            </if>
            <if test="startDate != null and endDate != null">
                and create_time >= #{startDate} and create_time &lt;= #{endDate}
            </if>
        </where>
    </select>
    <select id="count" resultType="java.lang.Integer">
        select count(*)
        from ${table}
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="where != null and where != ''">
                and (${where})
            </if>
            <if test="condition != null and condition != ''">
                and (${condition})
            </if>
            <if test="startDate != null and endDate != null">
                and create_time >= #{startDate} and create_time &lt;= #{endDate}
            </if>
        </where>
    </select>
</mapper>
