<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.RolePermissionDao">
    <resultMap id="fullResultMap" type="com.tipray.dlp.bean.RolePermission">
        <id column="id" property="id" />
        <result column="role_id" property="roleId"/>
        <result column="type" property="type"/>
        <result column="permission_id" property="permissionId"/>
    </resultMap>

    <select id="listPermissionId" resultType="java.lang.Long">
        select permission_id from role_permission where type=#{type} and role_id in (@roleIds)
    </select>

    <select id="listByPermission" resultMap="fullResultMap">
        select * from role_permission where type=#{type} and permission_id in (@permissionIds)
    </select>

    <select id="listPermissionByRoleId" resultType="java.lang.Long">
        select permission_id from role_permission where type=#{type} and role_id=#{roleId}
    </select>

    <delete id="deleteByParentIdAndRoleCode">
        delete from role_permission
        where id in (
            select * from (
                 select id from role_permission
                 where role_id in (select r.id from role r where r.code like CONCAT(#{roleCode},'%'))
                 and CONCAT(type, '-', permission_id) not in (select CONCAT(type, '-', permission_id) from role_permission where role_id = #{parentId})
             ) a
        )
    </delete>
    <delete id="deleteOtherPermission">
        delete from role_permission
        where role_id in (@roleIds)
        and (
                <foreach collection="paramMap.entrySet()" separator="or" index="key" item="val">
                    type = #{key} and permission_id not in (${val})
                </foreach>
            )
    </delete>
</mapper>