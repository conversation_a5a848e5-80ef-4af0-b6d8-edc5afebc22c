<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.UserDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.dto.UserDTO">
        <id column="id" property="id" />
        <result column="account" property="account"/>
        <result column="name" property="name"/>
        <result column="password" property="password"/>
        <result column="email" property="email"/>
        <result column="phone" property="phone"/>
        <result column="active" property="active"/>
        <result column="flag" property="flag"/>
        <result column="sid" property="sid"/>
        <result column="source" property="source"/>
        <result column="sync_id" property="syncId"/>
        <result column="role_ids" property="roleIds" typeHandler="com.tipray.dlp.bean.handler.StringConvertLongListTypeHandler"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="validStartDate" column="valid_start_date"/>
        <result property="validEndDate" column="valid_end_date"/>
        <result property="immediateSupervisor" column="director_id"/>
        <!--<collection property="roleIds" column="role_id" javaType="java.util.List" ofType="java.lang.Long">
            <result column="role_id" />
        </collection>-->
    </resultMap>

    <sql id="default_select_field">
        id,name,account,password,email,phone,active,flag,sid,source,sync_id,create_time,modify_time
    </sql>

    <sql id="vo_where_sql">
        <where>
            <if test="groupId != null">
                ( exists (select 0 from rel_group_user ug where ug.user_id = u.id and ug.group_id = #{groupId} <include refid="commonSQL.andNotDel"></include>)
                )
            </if>
            <if test="groupIds != null and groupIds != ''">
                ( exists (select 0 from rel_group_user ug where ug.user_id = u.id and ug.group_id in (${groupIds}) <include refid="commonSQL.andNotDel"></include>)
                )
            </if>
            <if test="active != null">
                and active = #{active}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and (account like CONCAT('%',#{searchInfo},'%') or name like CONCAT('%',#{searchInfo},'%'))
            </if>

            <if test="name != null and name != ''">
                and name like CONCAT('%',#{name},'%')
            </if>
            <if test="account != null and account != ''">
                and account like CONCAT('%',#{account},'%')
            </if>
            <if test="email != null and email != ''">
                and email like CONCAT('%',#{email},'%')
            </if>
            <if test="phone != null and phone != ''">
                and phone like CONCAT('%',#{phone},'%')
            </if>
            <if test="ids != null and ids != ''">
                <if test="status == 1 or status == null">
                    and id in (${ids})
                </if>
                <if test="status == 2">
                    and id not in (${ids})
                </if>
            </if>
            <if test="userType != null ">
                <if test="userType == 1">
                    and valid_start_date is null and valid_end_date is null
                </if>
                <if test="userType == 2">
                    and valid_start_date is not null and valid_end_date is not null
                </if>
                <if test="userType == 3 and validStartDate != null">
                    and valid_start_date > #{validStartDate}
                </if>
                <if test="userType == 4 and validEndDate != null">
                    and valid_end_date <![CDATA[<]]> #{validEndDate}
                </if>
                <if test="userType == 5 and validStartDate != null and validEndDate != null">
                    and valid_start_date <![CDATA[<=]]> #{validStartDate} and valid_end_date >= #{validEndDate}
                </if>
            </if>
            <if test="immediateSupervisor != null">
                and director_id = #{immediateSupervisor}
            </if>
            <if test="userRoleIds != null  and userRoleIds.size() > 0">
                and exists (select 1 from rel_user_role rur where rur.user_id = u.id AND rur.deleted = 0 and rur.role_id in (@userRoleIds))
            </if>
            <include refid="commonSQL.andNotDel"></include>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Integer">
        select count(u.id) from user u
        <include refid="vo_where_sql"/>
    </select>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.UserVO" resultMap="resultMap">
        select id,name,account,email,phone,active,flag,sid,source,sync_id, password, valid_start_date, valid_end_date, (select rgu.group_id from rel_group_user rgu where rgu.user_id = u.id limit 1) group_ids,
        director_id, (SELECT GROUP_CONCAT(rur.role_id SEPARATOR ',') FROM rel_user_role rur WHERE rur.user_id = u.id and rur.deleted = 0) role_ids
        from user u
        <include refid="vo_where_sql"/>
    </select>

    <select id="listUserIdAndNameByVO" parameterType="com.tipray.dlp.bean.vo.UserVO" resultMap="resultMap">
        select id,name,account from user u
        <include refid="vo_where_sql"/>
    </select>

    <select id="listDeletedUserId" resultType="java.lang.Long">
        select id from user where deleted = 1
    </select>

    <select id="listSimpleUser" resultType="com.tipray.dlp.bean.User">
        select id, sid, source, name, account, email, phone, deleted, modify_ver from user
        <where>
            <if test="modifyVer != null">
                modify_ver > #{modifyVer} or modify_ver = 0
                <if test="relModifyVer != null">
                    or id in (select user_id from rel_group_user where modify_ver > #{relModifyVer} or modify_ver = 0)
                </if>
            </if>
        </where>
    </select>

    <select id="listAddomianUser" resultMap="resultMap">
        select <include refid="default_select_field"/>
        from user where sid is not null and sid != '' and source = 2
        <include refid="commonSQL.andNotDel"></include>
    </select>

    <select id="listUserLoginTermIds" resultType="java.lang.Long">
        select term_id from terminaluser_login_state
        where last_login_user_id = #{userId}
    </select>

    <!--通过主键物理删除-->
    <delete id="deletePhysicalByIds">
        delete from user where id in (#{ids})
    </delete>

    <!--通过操作员id物理删除-->
    <delete id="deletePhysicalRelByRelatedDataId">
        delete from rel_group_user where user_id in (#{ids})
    </delete>

    <insert id="insertById"  useGeneratedKeys="true" keyProperty="id" >
        insert into user(id, name,account,password,email,phone,active,flag,sid,create_time,modify_time)
        values(#{id},#{name},#{account},#{password},#{email},#{phone},#{active},#{flag},#{sid},#{createTime},#{modifyTime})
    </insert>

    <update id="updateValidTimeByIds">
        update user
        set valid_start_date = #{validStartDate}, valid_end_date = #{validEndDate}
        where id in (${ids})
    </update>

    <insert id="initUserRole">
        INSERT INTO rel_user_role  (user_id, role_id, modify_ver, create_time, modify_time)
        SELECT id,201,#{modifyVer},#{createTime},#{modifyTime} FROM user u WHERE u.deleted = 0 and u.id NOT IN (SELECT DISTINCT r.user_id FROM rel_user_role r WHERE r.deleted = 0)
    </insert>

    <select id="getUserByPhoneAndOrderBySource" resultMap="resultMap">
        select <include refid="default_select_field"/>
        from user WHERE deleted = 0 AND phone = #{phone}
        ORDER BY
                 CASE WHEN sid = #{sid} AND sync_id = #{syncId} AND active = 1  THEN 4
                      WHEN sid = #{sid} AND source = #{sorceType} AND active = 1 THEN 3
                      WHEN source = #{sorceType} AND active = 1 THEN 2
                      WHEN source = #{sorceType} THEN 1
                      ELSE 0
                 END
        DESC
    </select>

    <update id="updateImmediateSupervisorEmptyByIds">
        update user set director_id = null
        where director_id is not null and director_id in (@ids)
    </update>

</mapper>

