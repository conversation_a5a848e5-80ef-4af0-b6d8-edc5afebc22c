<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.OplogDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.Oplog">
        <id column="id" property="id"/>
        <result column="uuid" property="uuid"/>
        <result column="user_id" property="userId"/>
        <result column="role_id" property="roleId"/>
        <result column="ip" property="ip"/>
        <result column="data_class" property="dataClass"/>
        <result column="method" property="method"/>
        <result column="operation_type" property="operationType"/>
        <result column="menu_code" property="menuCode"/>
        <result column="message" property="message"/>
        <result column="data_id" property="dataId"/>
        <result column="data_info" property="dataInfo"/>
        <result column="create_time" property="createTime"/>
    </resultMap>
    <sql id="sql_where">
        <where>
            <if test="startDate != null and endDate != null">
                and a.create_time &gt;= #{startDate} and a.create_time &lt; #{endDate}
            </if>
            <if test="ip != null and ip != ''">
                and a.ip like CONCAT('%',#{ip},'%')
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and message like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="menuCodes != null">
                and menu_code in (@menuCodes)
            </if>
            <if test="operationType != null">
                and operation_type = #{operationType}
            </if>
            <if test="userIds != null">
                and user_id in (@userIds)
            </if>
        </where>
    </sql>
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.tipray.dlp.bean.Oplog">
		insert into oplog(uuid, user_id,ip,data_class,method,operation_type,menu_code,role_id,message,data_id,data_info,create_time)
		values(#{uuid}, #{userId},#{ip},#{dataClass},#{method},#{operationType},#{menuCode},#{roleId}, #{message},#{dataId},#{dataInfo},#{createTime})
	</insert>
    <update id="updateDataInfo">
        update oplog set data_info=#{dataInfo} where id=#{id}
    </update>
    <delete id="deleteByIds" parameterType="java.lang.String">
		delete from oplog where id in (${value})
	</delete>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.OplogVO" resultType="java.lang.Long">
		select count(0) from oplog a
        <include refid="sql_where"></include>
	</select>
    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.OplogVO" resultMap="resultMap">
        select a.* from oplog a
		<include refid="sql_where"></include>
	</select>
</mapper>
