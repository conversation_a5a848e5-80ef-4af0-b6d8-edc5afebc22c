<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.PropertyDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.Property">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId"/>
        <result column="code" property="code"/>
        <result column="value" property="value"/>
        <result column="type" property="type"/>
        <result column="rule" property="rule"/>
        <result column="remark" property="remark"/>
        <result column="editable" property="editable"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.PropertyVo" resultMap="resultMap">
        select id, parent_id, code, value, type, rule, remark, editable, modify_time  from property
        where editable = 1
        <if test="parentIds != null">
            and parent_id in (${parentIds})
        </if>
        <if test="code != null and code != ''">
            and code like CONCAT('%',#{code},'%')
        </if>
        <if test="value != null and value != ''">
            and value like CONCAT('%',#{value},'%')
        </if>
        <if test="remark != null and remark != ''">
            and remark like CONCAT('%',#{remark},'%')
        </if>
        <if test="exceptCode != null and exceptCode != ''">
            and code not like CONCAT('%',#{exceptCode},'%')
        </if>
        <if test="exceptCode1 != null and exceptCode1 != ''">
            and code not like CONCAT('%',#{exceptCode1},'%')
        </if>
        union all
        select id, 0 as parent_id, config_key as code, config_value as value, config_type as type, '' as rule, remark, 1 as editable, now() as modify_time from global_config
        <where>
            <if test="codes != null">
                and config_key in (@codes)
            </if>
            <if test="code != null and code != ''">
                and config_key like CONCAT('%',#{code},'%')
            </if>
        </where>
    </select>
</mapper>
