<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ServerRelatedDeviceDao">

    <select id="listWithDeletedByDevTypeAndDevGroupId" resultType="com.tipray.dlp.bean.ServerRelatedDevice">
        select * from rel_data_server_device where dev_type = #{devType} and dev_group_id = #{devGroupId} and device_id = 0
    </select>
    <select id="listWithDeletedByServerId" parameterType="java.lang.Long"  resultType="com.tipray.dlp.bean.ServerRelatedDevice">
        select * from rel_data_server_device where server_id = #{value}
    </select>
    <select id="listWithDeletedByDevId" parameterType="java.lang.Long"  resultType="com.tipray.dlp.bean.ServerRelatedDevice">
        select * from rel_data_server_device where device_id = #{value}
    </select>

</mapper>
