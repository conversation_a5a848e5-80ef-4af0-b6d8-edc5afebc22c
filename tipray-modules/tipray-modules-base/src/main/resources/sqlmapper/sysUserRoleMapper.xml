<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SysUserRoleDao">

    <select id="listUserIdByRoleCode" resultType="java.lang.String">
        select user_id from sys_user_role sur, role r where sur.role_id = r.id and r.code like CONCAT(#{value},'%')
    </select>

</mapper>