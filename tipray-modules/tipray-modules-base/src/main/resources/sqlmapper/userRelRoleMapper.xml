<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.UserRelRoleDao">

    <resultMap type="com.tipray.dlp.bean.UserRelRole" id="userRelRoleMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="roleId" column="role_id"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="deleted" column="deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <select id="listAllByUserId" resultMap="userRelRoleMap">
        select id, user_id, role_id, modify_time, deleted, create_time, modify_time
        from rel_user_role where user_id in (${userId})
    </select>
    <select id="listAllByRoleId" resultMap="userRelRoleMap">
        select id, user_id, role_id, modify_time, deleted, create_time, modify_time
        from rel_user_role where role_id in (${roleIds})
    </select>
    <select id="selectUserNotRoleList" resultMap="userRelRoleMap">
        SELECT id, user_id, role_id, modify_time, deleted, create_time, modify_time from rel_user_role where role_id = 201 and user_id IN (
            SELECT id FROM user u WHERE u.deleted = 0 and u.id NOT IN (SELECT DISTINCT r.user_id FROM rel_user_role r WHERE r.deleted = 0));
    </select>
</mapper>
