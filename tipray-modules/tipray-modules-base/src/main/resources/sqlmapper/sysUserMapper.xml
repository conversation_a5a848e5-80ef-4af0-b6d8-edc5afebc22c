<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SysUserDao">

    <resultMap type="com.tipray.dlp.bean.SysUser" id="SysUserMap">
        <result property="id" column="id"/>
        <result property="account" column="account"/>
        <result property="name" column="name"/>
        <result property="email" column="email"/>
        <result property="phone" column="phone"/>
        <result property="password" column="password"/>
        <result property="active" column="active"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="upPwdTime" column="up_pwd_time"/>
        <result property="limitCode" column="limit_code"/>
        <result property="validStartDate" column="valid_start_date"/>
        <result property="validEndDate" column="valid_end_date"/>
    </resultMap>

    <sql id="vo_where_sql">
      <where>
          <include refid="commonSQL.andNotDel"></include>
          <if test="active != null">
              active = #{active}
          </if>
          <if test="roleId != null">
            and exists (select sur.id from sys_user_role sur where sur.user_id = u.id and sur.role_id = #{roleId})
          </if>
          <if test="roleIds != null and roleIds != ''">
            and exists (select sur.id from sys_user_role sur where sur.user_id = u.id and sur.role_id in (${roleIds}))
          </if>
          <if test="exceptRoleIds != null and exceptRoleIds != ''">
            and not exists (select sur.id from sys_user_role sur where sur.user_id = u.id and sur.role_id in (${exceptRoleIds}))
          </if>
          <if test="exceptUserIds != null and exceptUserIds != ''">
              and u.id not in (${exceptUserIds})
          </if>
          <if test="ignoreSuper != null and ignoreSuper">
              and u.id not in (1,2,3,4)
          </if>
          <if test="ignoreSuperAdmin != null and ignoreSuperAdmin">
              and u.id != 1
          </if>
          <if test="searchInfo != null and searchInfo != ''">
            and (account like CONCAT('%',#{searchInfo},'%') or name like CONCAT('%',#{searchInfo},'%'))
          </if>
          <if test="ids != null and ids != ''">
            and id in (${ids})
          </if>
          <if test="name != null and name != ''">
              and name like CONCAT('%',#{name},'%')
          </if>
          <if test="account != null and account != ''">
              and account like CONCAT('%',#{account},'%')
          </if>
          <if test="userType != null ">
              <if test="userType == 1">
                  and valid_start_date is null and valid_end_date is null
              </if>
              <if test="userType == 2 and validStartDate != null and validEndDate != null">
                  and valid_start_date is not null and valid_end_date is not null
              </if>
              <if test="userType == 3 and validStartDate != null">
                  and valid_start_date > #{validStartDate}
              </if>
              <if test="userType == 4 and validEndDate != null">
                  and valid_end_date <![CDATA[<]]> #{validEndDate}
              </if>
              <if test="userType == 5 and validStartDate != null and validEndDate != null">
                  and valid_start_date <![CDATA[<=]]> #{validStartDate} and valid_end_date >= #{validEndDate}
              </if>
          </if>
      </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.UserVO" resultMap="SysUserMap">
        <if test="lang == null or lang == '' or lang == 'zh'">
            select id, account, name, email, phone, active, up_pwd_time, limit_code, valid_start_date, valid_end_date,
            case when password is not null and password != '' then 1 else 0 end hasPassword
            from sys_user u
        </if>
        <if test="lang != null and lang != '' and lang != 'zh' and langParams != null">
            select id, account, name, email, phone, active, up_pwd_time, limit_code, valid_start_date, valid_end_date,
            case when password is not null and password != '' then 1 else 0 end hasPassword
            from (
            select sys_user.id id, sys_user.account account, if (sys_user.id > 4, sys_user.name, sys_user_d.name) name, sys_user.email email,
                   sys_user.phone phone, sys_user.active active, sys_user.up_pwd_time up_pwd_time, sys_user.limit_code limit_code,
                   sys_user.password password, sys_user.deleted deleted,
                   sys_user.valid_start_date valid_start_date, sys_user.valid_end_date valid_end_date
            from sys_user left join
            (select #{langParams.id1} id, #{langParams.name1} name
            union select #{langParams.id2} id, #{langParams.name2} name
            union select #{langParams.id3} id, #{langParams.name3} name
            union select #{langParams.id4} id, #{langParams.name4} name)
            AS sys_user_d on sys_user.id = sys_user_d.id) AS u
        </if>
      <include refid="vo_where_sql"/>
    </select>

    <select id="listDisable" resultType="long">
        SELECT id FROM  sys_user su
        WHERE su.active=1 AND EXISTS (SELECT u.user_id FROM role r,sys_user_role u WHERE r.id=u.role_id AND u.user_id= su.id AND r.type &lt; 2)
    </select>

    <select id="listEnable" resultType="long">
        select id from sys_user su where su.active=0 and su.mode = 1
    </select>
    <select id="listThreeUserIntersectCommonUser" resultType="java.lang.Long">
        SELECT DISTINCT user_id FROM
            (SELECT sur.user_id FROM sys_user_role sur WHERE sur.role_id IN (SELECT role.id FROM role WHERE role.type != 0)) AS t1
        INNER JOIN
            (SELECT sur.user_id FROM sys_user_role sur WHERE sur.role_id IN (SELECT role.id FROM role WHERE role.type = 0)) AS t2
        USING (user_id)
    </select>

    <update id="updateValidTimeByIds">
        update sys_user
        set valid_start_date = #{validStartDate}, valid_end_date = #{validEndDate}
        where id in (${ids})
    </update>

</mapper>
