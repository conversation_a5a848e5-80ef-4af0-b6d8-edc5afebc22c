<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.RoleDao">

    <resultMap type="com.tipray.dlp.bean.Role" id="RoleMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="code" column="code"/>
        <result property="parentId" column="parent_id"/>
        <result property="remark" column="remark"/>
        <result property="active" column="active"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="vo_where_sql">
      <where>
        and 1 = 1
        <if test="isThree!=null and isThree">and type IN (2, 3, 4)</if> /* 三员模式，只查询三员管理员 */
        <if test="isThree!=null and !isThree">and type != 1</if> /* 超管模式，查询除了admin */
        <if test="active != null">and active = #{active}</if>
        <if test="parentId != null">and parent_id = #{parentId}</if>
          <if test="code != null and code != ''">
              and code like CONCAT(#{code},'%')
          </if>
        <if test="roleIds != null and roleIds != ''">
            and id in (${roleIds})
        </if>
        <if test="searchInfo != null and searchInfo != ''">
          and name like CONCAT('%',#{searchInfo},'%')
        </if>
      </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.RoleVO" resultMap="RoleMap">
        <if test="lang == null or lang == '' or lang == 'zh'">
            select id, name, code, parent_id, type, remark, active  from role
        </if>
        <if test="lang != null and lang != '' and lang != 'zh' and langParams != null">
            select id, name, code, parent_id, type, remark, active from (
            select role.id id, if (role.id > 4, role.name, role_d.name) name, role.code code, role.parent_id parent_id, role.type type, role.remark remark, role.active active
            from role left join
            (select #{langParams.id1} id, #{langParams.name1} name
            union select #{langParams.id2} id, #{langParams.name2} name
            union select #{langParams.id3} id, #{langParams.name3} name
            union select #{langParams.id4} id, #{langParams.name4} name)
            AS role_d on role.id = role_d.id) AS role
        </if>
        <include refid="vo_where_sql"/>
    </select>
</mapper>
