<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.TerminalExtAssignServerDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.TerminalExtAssignServer">
        <result column="term_id" property="terminalId"/>
        <result column="assign_server_id" property="assignServerId"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <select id="listByTerminalIds" resultMap="resultMap">
        select * from terminal_ext_assign_server
        where term_id in (${value})
    </select>

</mapper>

