<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.TerminalRelGroupDao">
    <resultMap id="baseResultMap" type="com.tipray.dlp.bean.TerminalRelGroup">
        <id column="id" property="id" />
        <result column="term_id" property="termId"/>
        <result column="group_id" property="groupId"/>
        <result column="modify_ver" property="modifyVer"/>
    </resultMap>

    <select id="selectObjByTermIds" resultMap="baseResultMap">
        select id, term_id, group_id, modify_ver from rel_group_terminal where term_id in (@relatedDataIds)
    </select>

</mapper>
