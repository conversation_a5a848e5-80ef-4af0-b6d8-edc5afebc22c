<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.TerminalDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.TerminalExtMobile">
        <result column="term_id" property="terminalId"/>
        <result column="dev_model" property="devModel"/>
        <result column="phone_num" property="phoneNum"/>
        <result column="mobile_type" property="mobileType"/>
        <result column="specify_app" property="specifyApp"/>
    </resultMap>
    <resultMap id="baseResultMap" type="com.tipray.dlp.bean.Terminal">
        <id column="id" property="id" />
        <result column="name" property="name"/>
        <result column="computer_name" property="computerName"/>
        <result column="server_id" property="serverId"/>
        <result column="assign_server_id" property="assignServerId"/>
        <result column="main_ip" property="mainIp"/>
        <result column="main_mac" property="mainMac"/>
        <result column="ip_list" property="ips"/>
        <result column="mac_list" property="macs"/>
        <result column="mcode" property="mcode"/>
        <result column="guid" property="GUID"/>
        <result column="version" property="version"/>
        <result column="auto_login_user_id" property="userId"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="deleted" property="deleted"/>
        <result column="modify_ver" property="modifyVer"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="change_time" property="changeTime"/>
        <result column="last_online_time" property="lastOnlineTime"/>
        <result column="remark" property="remark"/>
        <result column="login_mode" property="loginMode"/>
        <result column="use_type" property="useType"/>
        <result column="name_state" property="nameState"/>
        <association property="mobileExt" resultMap="resultMap"/>
    </resultMap>
    <resultMap id="termResultMap" type="com.tipray.dlp.bean.vo.term.TermVo">
        <result column="uid" property="uid"/>
        <result column="term_name" property="termName"/>
        <result column="term_num" property="termNum"/>
        <result column="term_group" property="termGroup"/>
        <result column="keywords" property="keywords"/>
        <result column="auto_login_user" property="autoLoginUser"/>
        <result column="user_group" property="userGroup"/>
        <result column="user_group_id" property="userGroupId"/>
        <result column="term_group_id" property="termGroupId"/>
    </resultMap>
    <resultMap id="groupRelDTOMap" type="com.tipray.dlp.bean.dto.GroupRelDTO">
        <result column="group_id" property="groupId"/>
    </resultMap>

    <sql id="select_where_vo">
        <where>
            <if test="ids != null and ids != ''">
                and t.id in (@ids)
            </if>
            <if test="assignServerId != null">
                and (
                exists (select 0 from terminal_ext_assign_server ts where ts.term_id = t.id and ts.assign_server_id = #{assignServerId} and ts.deleted = 0)
                )
            </if>
            <if test="groupId != null">
                and (
                exists (select 0 from rel_group_terminal tg where tg.term_id = t.id and tg.group_id = #{groupId})
                )
            </if>
            <if test="groupIds != null and groupIds != ''">
                and (
                exists (select 0 from rel_group_terminal tg where tg.term_id = t.id and tg.group_id in (@groupIds))
                )
            </if>
            <if test="searchInfo != null and searchInfo != '' ">
                and (name like CONCAT('%',#{searchInfo},'%') or computer_name like CONCAT('%',#{searchInfo},'%') or CONCAT(t.id) like CONCAT('%',#{searchInfo},'%'))
            </if>
            <if test="mainIp != null and mainIp != ''">
                and main_ip like CONCAT('%', #{mainIp}, '%')
            </if>
            <if test="ip != null and ip != '' ">
                and ip_list like CONCAT('%',#{ip},'%')
            </if>
            <if test="ipv6 != null and ipv6 != ''">
                and ipv6_list like CONCAT('%', #{ipv6}, '%')
            </if>
            <if test="ipv4Orv6 != null and ipv4Orv6 != ''">
                and (ip_list like CONCAT('%',#{ipv4Orv6},'%') or ipv6_list like CONCAT('%', #{ipv4Orv6}, '%'))
            </if>
            <if test="mac != null and mac != '' ">
                and mac_list like CONCAT('%',#{mac},'%')
            </if>
            <if test="serverId != null">
                and server_id = #{serverId}
            </if>
            <if test="computerName != null and computerName != '' ">
                and computer_name like CONCAT('%',#{computerName},'%')
            </if>
            <if test="name != null and name != '' ">
                and name like CONCAT('%',#{name},'%')
            </if>
            <if test="loginMode != null">
                and login_mode = #{loginMode}
            </if>
            <if test="userId != null">
                and auto_login_user_id = #{userId}
            </if>
            <if test="autoLoginUserIds != null and autoLoginUserIds != ''">
                and auto_login_user_id in (@autoLoginUserIds)
            </if>
            <if test="searchId != null and searchId != '' ">
                and CONCAT(t.id) like CONCAT('%',#{searchId},'%')
            </if>
            <if test="type != null">
                and type =#{type}
            </if>
            <if test="types != null and types != ''">
                and type in (@types)
            </if>
            <if test="filterType != null and filterType != ''">
                and type not in (@filterType)
            </if>
            <if test="endTime != null">
                <![CDATA[ and modify_time <= #{endTime} ]]>
            </if>
            <if test="startTime != null">
                <![CDATA[ and modify_time >= #{startTime} ]]>
            </if>
            <if test="useType != null and useType != ''">
                and use_type in (@useType)
            </if>
            <if test="filterUseType != null and filterUseType != ''">
                and use_type = 0
            </if>
            <if test="offlineTime != null">
                <![CDATA[ and tls.term_last_online_time <= #{offlineTime} ]]>
            </if>
            <if test="filterBoundUTerm">
                and t.id not in (SELECT tt.id from terminal tt WHERE (type &amp; 0xf0) = 32 AND guid != '')
                <if test="occupiedIds != null and occupiedIds != ''">
                    and t.id not in (@occupiedIds)
                </if>
            </if>
            <if test="queryUninstall!=null and queryUninstall">
                and ( t.id in (@uninstallTermIds)
                and t.id not in (SELECT tt.id from terminal tt WHERE (type &amp; 0xf0) = 32 AND guid != '')
                <if test="occupiedIds != null and occupiedIds != ''">
                    and t.id not in (@occupiedIds)
                </if>
                )
            </if>
            <if test="filterEmptyMacOrIp">
                and t.main_ip is not null and t.main_ip != ''
                and t.main_mac is not null and t.main_mac != ''
            </if>
            <if test="version != null and version != ''">
                and version like concat('%', #{version}, '%')
            </if>
            <if test="minVersion != null and minVersion != ''">
                and version > #{minVersion}
            </if>
            <if test="lastLoginUserId != null">
                and tls.last_login_user_id = #{lastLoginUserId}
            </if>
            <if test="searchIdAndName != null and searchIdAndName != '' ">
                and (t.name like CONCAT('%',#{searchIdAndName},'%') or t.id like CONCAT('%',#{searchIdAndName},'%'))
            </if>
            and t.deleted = 0
        </where>
    </sql>

    <select id="listIdByVO" parameterType="com.tipray.dlp.bean.vo.TerminalVO" resultType="java.lang.Long">
        select t.id from terminal t
        <include refid="select_where_vo"></include>
    </select>

    <select id="countByVO" resultType="java.lang.Integer">
        select count(id) from terminal t
        LEFT OUTER JOIN terminaluser_login_state tls on t.id = tls.term_id
        <include refid="select_where_vo"></include>
    </select>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.TerminalVO" resultMap="baseResultMap">
        select t.id, t.name, t.computer_name, t.server_id, t.status, t.main_ip, t.main_mac, t.ip_list, t.mac_list, t.mcode, t.guid, t.version, t.type, t.use_type, t.deleted,
        t.login_mode, t.auto_login_user_id, t.create_time, t.modify_time, tls.term_last_online_time as last_online_time, t.change_time, t.remark, tx.*, txs.assign_server_id, rgt.group_id as group_ids from terminal t
        LEFT OUTER JOIN terminal_ext_mobile tx  ON t.id = tx.term_id
        LEFT OUTER JOIN rel_group_terminal rgt ON rgt.term_id = t.id
        LEFT OUTER JOIN (select ts.term_id, ts.assign_server_id from terminal_ext_assign_server ts where ts.deleted = 0) txs ON t.id = txs.term_id
        LEFT OUTER JOIN terminaluser_login_state tls on t.id = tls.term_id
        <include refid="select_where_vo"></include>
    </select>

    <select id="listOsType" parameterType="com.tipray.dlp.bean.vo.TerminalVO" resultType="java.lang.Integer">
        select t.type from terminal t
        <include refid="select_where_vo"></include>
        group by t.type
    </select>
    <select id="listSimpleTerm" resultMap="baseResultMap">
        select id, name, guid, version, type, use_type, main_ip, main_mac, deleted, modify_ver, login_mode from terminal
        <where>
            <if test="modifyVer != null">
                modify_ver > #{modifyVer}
                <if test="relModifyVer != null">
                    or id in (select term_id from rel_group_terminal where modify_ver > #{relModifyVer})
                </if>
            </if>
        </where>
    </select>

    <select id="listTerminalByUserId" parameterType="java.lang.Long" resultMap="baseResultMap" >
        select t.* from terminal t,rel_terminal_user ut where t.id = ut.term_id and ut.user_id = #{value}
        <include refid="commonSQL.andNotDel"></include>
    </select>

    <select id="statisticGroupByCreateTime" resultType="java.util.Map">
        select count(0) num,date_format(create_time, '%Y-%m-%d') perDay from terminal
        where create_time &gt;= #{startDate} and create_time &lt; #{endDate} GROUP BY perDay ORDER BY perDay
    </select>

    <resultMap id="ngResultMap" type="com.tipray.dlp.bean.dto.TermUserSyncDTO">
        <result column="term_id" property="termId"/>
        <result column="term_name" property="termName"/>
        <result column="termGroupId" property="termGroupId"/>
        <result column="user_id" property="userId"/>
        <result column="account" property="account"/>
        <result column="user_name" property="userName"/>
        <result column="userGroupId" property="userGroupId"/>
    </resultMap>


    <select id="getSameLoginUser" resultMap="ngResultMap">
        select u.id user_id from terminal t , user u, terminaluser_login_state ls
        where t.id = ls.term_id and ls.last_login_user_id = u.id and t.use_type=0 and u.deleted=0 and t.deleted=0  GROUP BY u.id HAVING count(0) >1
    </select>

    <select id="listTermUserSyncByVO" parameterType="com.tipray.dlp.bean.vo.TermUserSyncVo" resultMap="ngResultMap">
        select tu.*, rgt.group_id termGroupId, rgu.group_id userGroupId
        from (
            select t.id term_id, t.name term_name,u.id user_id, u.name user_name, u.account from terminal t , user u, terminaluser_login_state ls
            where t.id = ls.term_id and ls.last_login_user_id = u.id and t.use_type=0 and u.deleted=0 and t.deleted=0
        ) tu,
        rel_group_terminal rgt,
        rel_group_user rgu
        <where>
            rgu.deleted = 0
            and tu.term_id = rgt.term_id
            and tu.user_id = rgu.user_id
            <if test="userIds != null and userIds != ''">
                and tu.user_id not in (@userIds)
            </if>
            <if test="syncGroup != null">
                and rgt.group_id != rgu.group_id
            </if>
            <if test="syncName != null">
                and tu.term_name != tu.user_name
            </if>
            <if test="syncAccount != null">
                and tu.term_name != tu.account
            </if>
            <if test="termName != null">
                and tu.term_name like CONCAT('%',#{termName},'%')
            </if>
            <if test="userName != null">
                and tu.user_name like CONCAT('%',#{userName},'%')
            </if>
            <if test="account != null">
                and tu.account like CONCAT('%',#{account},'%')
            </if>
        </where>
    </select>

    <update id="updateUserNameWithTermName">
        update user
        set name = case
        <foreach collection="list" item="item" index="index">
            when id = #{item.userId} then #{item.termName}
        </foreach>
        end
        where id in (@list.userId)
    </update>

    <update id="updateTermNameWithUserName">
        update terminal
        set name_state = 1, name = case
        <foreach collection="list" item="item" index="index">
            when id = #{item.termId} then #{item.userName}
        </foreach>
        end
        where id in (@list.termId)
    </update>

    <select id="listDeleteTermId" resultType="java.lang.Long">
        select id from terminal where deleted = 1 or use_type != 0
    </select>

    <select id="getDeleteTermId" resultType="java.lang.Long">
        select id from terminal where deleted = 1
    </select>

    <select id="listIsCleanedTermId" resultType="java.lang.Long">
        select id from terminal where use_type != 0
    </select>

    <select id="listUndeletedId" resultType="java.lang.Long">
        select id from terminal where deleted = 0
    </select>

    <select id="existDeletedTerm" parameterType="java.lang.Long" resultType="java.lang.Boolean">
        select count(0) from terminal where  id = #{value} and deleted = 1 or use_type != 0
    </select>
    <update id="updateTermNameWithUserAccount">
        update terminal
        set name = case
        <foreach collection="list" item="item" index="index">
            when id = #{item.termId} then #{item.account}
        </foreach>
        end
        where
        <foreach collection="list" separator="or" item="item" index="index">
            id = #{item.termId}
        </foreach>
    </update>
    <insert id="insertWithId" parameterType="com.tipray.dlp.bean.Terminal">
        INSERT INTO terminal  ( id, guid, mcode, name, computer_name, main_ip, main_mac, ip_list, mac_list, version,  login_mode, auto_login_user_id, type, use_type, deleted, create_time, modify_time )
        VALUES  ( #{id}, #{GUID}, #{mcode}, #{name}, #{computerName}, #{mainIp}, #{mainMac}, #{ips}, #{macs}, #{version},  #{loginMode}, #{userId}, #{type}, #{useType}, #{deleted}, #{createTime}, #{modifyTime} )
    </insert>

    <select id="listGuidMacId" parameterType="java.lang.Integer" resultMap="baseResultMap">
        select guid, main_mac, id from terminal
        <where>
            <if test="deleted != null">
                deleted = #{deleted}
            </if>
        </where>
    </select>

    <select id="getNameById" parameterType="java.lang.Long" resultType="java.lang.String">
        select name from terminal where id = #{value}
    </select>
    <select id="countAll" resultType="java.lang.Long">
        select count(0) from terminal where deleted = 0
    </select>

    <select id="selectAllTerminal" parameterType="com.tipray.dlp.bean.vo.TerminalVO" resultMap="baseResultMap">
        select t.id, t.name  from terminal t
        LEFT OUTER JOIN terminal_ext_mobile tx  ON t.id = tx.term_id
        LEFT OUTER JOIN (select ts.term_id, ts.assign_server_id from terminal_ext_assign_server ts where ts.deleted = 0) txs ON t.id = txs.term_id
        <include refid="select_where_vo"></include>
    </select>

    <select id="countByTerminalVO" parameterType="com.tipray.dlp.bean.vo.TerminalVO" resultType="java.lang.Integer">
        select count(t.id) from terminal t
        LEFT OUTER JOIN terminal_ext_mobile tx  ON t.id = tx.term_id
        LEFT OUTER JOIN (select ts.term_id, ts.assign_server_id from terminal_ext_assign_server ts where ts.deleted = 0) txs ON t.id = txs.term_id
        <include refid="select_where_vo"></include>
    </select>

    <select id="getByGuid" resultMap="baseResultMap">
        select * from terminal where guid = #{guid} order by id desc limit 1
    </select>

    <update id="updateUsbTerm" parameterType="com.tipray.dlp.bean.Terminal">
        update terminal
        set guid = #{GUID}, version = #{version}
        where id = #{id}
    </update>


    <select id="countLongTimeOfflineTerminal" resultType="java.lang.Long">
        select t.id id from terminal t
        join
        (select term_id from terminaluser_login_state
        where term_last_online_time is null or timestampdiff(minute,term_last_online_time,now()) &gt;= #{cleanDuration}) ts
        on t.id = ts.term_id
        where t.use_type = 0 and t.deleted = 0
    </select>
    <select id="countByType" resultType="java.util.Map">
        SELECT type, mobile_type, COUNT(*) num FROM terminal t
        LEFT JOIN terminal_ext_mobile tem on t.id = tem.term_id
        WHERE deleted = 0 GROUP BY type, mobile_type
    </select>

    <select id="getOfflineTermInfo" resultType="com.tipray.dlp.bean.dto.OfflineTermInfo">
        select id, name, type, login_mode, auto_login_user_id
        from terminal where id = #{id} and (type &amp; 0xf0) = 128
    </select>

    <update id="updateTermUseTypeByIds">
        update terminal
        set use_type = 0
        where id in (@ids)
    </update>

    <select id="listFlowTerminalByVO" resultType="com.tipray.dlp.bean.TermFlowInfo">
        select t.id id, t.name termName, t.main_ip ip, t.main_mac macAddress, t.computer_name computerName,
                gi.id groupId, gi.name groupName
        from terminal t
        left join rel_group_terminal rgt on rgt.term_id = t.id
        left join group_info gi on gi.id = rgt.group_id
        <where>
            <if test="ids != null and ids != ''">
                and t.id in (${ids})
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and (t.id like concat('%', #{searchInfo}, '%') or t.name like concat('%', #{searchInfo}, '%') )
            </if>
            <if test="groupIds != null and groupIds.size() > 0">
                and rgt.group_id in
                <foreach collection="groupIds" open="(" item="item" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>

