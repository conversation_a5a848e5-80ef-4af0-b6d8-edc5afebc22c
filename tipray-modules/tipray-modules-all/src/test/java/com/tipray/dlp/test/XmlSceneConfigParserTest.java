package com.tipray.dlp.test;

import com.tipray.dlp.bean.Scene;
import com.tipray.dlp.support.scene.XmlSceneConfigParser;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/18
 */
public class XmlSceneConfigParserTest {
    public static void main(String[] args) {
        XmlSceneConfigParser parser = new XmlSceneConfigParser();
        List<Scene> sceneList = parser.parse();
        sceneList.forEach(System.out::println);
    }
}
