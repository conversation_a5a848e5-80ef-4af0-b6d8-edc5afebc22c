package com.tipray.dlp.test;

import cn.hutool.core.io.FileUtil;
import com.tipray.dlp.socket.Package;
import com.tipray.dlp.socket.utils.PackerUtil;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 服务设备信息测试
 * <AUTHOR>
 * @date 2023/11/06/13:28
 */
@SpringBootTest
public class ServerStatusTest {
    /**
     * 解析协议日志文件并输出服务器状态信息
     * 说明：通信协议日志要开启调试模式，这样才能正常解析
     */
    public static void main(String[] args) {
        List<String> lineList = FileUtil.readLines(new File("D:\\socket.log"), "utf-8");
        Map<String, List<String>> map = new HashMap<>();
        String key = null;
        StringBuilder value = null;
        // 步骤1：解析日志文件，提取0x010191协议数据
        for (int i = 0; i < lineList.size(); i++) {
            String line = lineList.get(i);
            if(line.contains("01 32 30 31 39 10 00 00 00 01 00 00 00 00 01 00 15 18 01 82")){
                key = line.substring(100,111);
                value = new StringBuilder(line);
                String time = lineList.get(i-1).substring(0, 13);
                if(!map.containsKey(key)){
                    map.put(key, new LinkedList<>());
                    map.put(key+"Time", new LinkedList<>());
                }
                List<String> timeList = map.get(key+"Time");
                if(timeList.size() < 2){
                    timeList.add(time);
                } else {
                    timeList.set(1, time);
                }
            } else if (value!=null && line.matches("(\\s[0-9a-fA-F]{2})+")) {
                value.append(line);
            } else {
                if(key != null){
                    map.get(key).add(value.toString());
                }
                key = null;
                value = null;
            }
        }
        if(key != null){
            map.get(key).add(value.toString());
        }
        // 步骤2：将协议数据解析成终端的在线状态信息
        StringBuilder sb = null;
        for (String mapKey : map.keySet()) {
            if(mapKey.endsWith("Time")){
                continue;
            }
            sb = new StringBuilder(mapKey);
            List<String> timeList = map.get(mapKey+"Time");
            sb.append(" ").append(timeList.get(0)).append("~").append(timeList.get(1));
            System.out.println("====================" + sb);
            List<String> termList = new LinkedList<>();
            List<String> pkgList = map.get(mapKey);
            for (String pkgHex : pkgList) {
                Package pkg = PackerUtil.hexLogToPackage(pkgHex);
                if(pkg!=null){
                    byte[] body = pkg.getBody();
                    String jsonStr = new String(body, StandardCharsets.UTF_8);
                    System.out.println(jsonStr);
                }
            }
        }
    }
}
