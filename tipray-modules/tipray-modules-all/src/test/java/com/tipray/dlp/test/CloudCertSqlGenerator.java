package com.tipray.dlp.test;

import com.google.gson.Gson;
import com.tipray.dlp.cloud.bean.CloudOcspPublicKey;
import com.tipray.dlp.cloud.constants.CloudConstant;
import com.tipray.dlp.cloud.util.CustomPropertiesLoader;
import com.tipray.dlp.exception.ServiceException;
import com.tipray.dlp.service.cloud.CipherHelper;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.RandomStringUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 云服务证书sql生成，用于更换证书
 * <AUTHOR>
 * @date 2025/4/10
 */
public class CloudCertSqlGenerator {
    // 随机保护口令的长度
    private static final int LENGTH_PROTECT_PASSWORD = 10;

    private static final String CERT_PATH = "F:\\cloudCert";

    private static final String CLOUD_CERT_TABLE = "cloud_cert_config";

    private static final String KEY_OCSP_PUBLIC_KEY = "ocsp.public-key";
    private static final String KEY_CERT_INFO = "cloud.cert-info";
    private static final String KEY_PASS_CIPHER = "cloud.pass-cipher";
    private static final String KEY_PROTECT_PASSWORD_CIPHER = "cloud.protect-password-cipher";


    private static final List<Info> infos;

    static {
        Info prodConfig = Info.builder()
                .startId(1)
                .subPath("prod")
                .host("yun.tipray.com")
                .tableName(CLOUD_CERT_TABLE)
                .ocspFilename("config.properties")
                .certFileName("<EMAIL>")
                .pass("62529284")
                .build();

        Info testConfig = Info.builder()
                .startId(5)
                .subPath("test")
                .host("**************")
                .tableName(CLOUD_CERT_TABLE)
                .ocspFilename("config.properties")
                .certFileName("<EMAIL>")
                .pass("95873533")
                .build();
        Info devConfig = Info.builder()
                .startId(9)
                .subPath("dev")
                .host("***************")
                .tableName(CLOUD_CERT_TABLE)
                .ocspFilename("config.properties")
                .certFileName("<EMAIL>")
                .pass("62529284")
                .build();

        infos = Arrays.asList(prodConfig, testConfig, devConfig);
    }

    public static void main(String[] args) {

        printReplaceSql();


        // 创建
//        printSql(1);

        // 更新
//        printSql(2);
    }

    private static void printSql(int type) {
        String operator = "";
        operator = type == 1 ? "创建" : "更新";
        List<String> list = type == 1 ? generateAllInsertSql() : generateAllUpdateSql();
        System.out.println("=================以下是sql" + operator + "语句=========================================");
        list.forEach(System.out::println);
        System.out.println("=================sql" + operator + "语句输出结束=========================================");
    }

    private static void printReplaceSql() {
        List<String> list = generateAllReplaceSql();
        System.out.println("=================以下是sql替换语句=========================================");
        list.forEach(System.out::println);
        System.out.println("=================sql替换语句输出结束=========================================");
    }

    private static List<String> generateAllReplaceSql() {
        return infos.stream().flatMap(info -> generateReplaceSql(info).stream()).collect(Collectors.toList());
    }

    private static List<String> generateAllInsertSql() {
        return infos.stream().flatMap(info -> generateInsertSql(info).stream()).collect(Collectors.toList());
    }

    private static List<String> generateAllUpdateSql() {
        return infos.stream().flatMap(info -> generateUpdateSql(info).stream()).collect(Collectors.toList());
    }


    private static List<String> generateReplaceSql(Info info) {
        Integer startId = info.getStartId();
        String sqlFormat = "replace into %s (`id`, `type`, `host`, `setting_key`, `value`, `remark`) values (%s, %s, '%s', '%s', '%s', '%s');";

        Gson gson = new Gson();
        String filePath = CERT_PATH + File.separator + info.subPath + File.separator;
        // 读取 ocsp证书公钥信息
        CloudOcspPublicKey ocspPublicKey = CustomPropertiesLoader.loadFileSystemProperties(filePath + info.getOcspFilename());
        String ocspJson = gson.toJson(ocspPublicKey);

        // 读取 签名私钥证书信息、密码、保护密码等信息
        File file = new File(filePath + info.getCertFileName());
        if (!file.exists()) {
            throw new RuntimeException("证书文件不存在");
        }
        String certInfo = readSecretInfoFromFile(file);
        String protectPassword = generateProtectPassword();
        String protectPasswordCipher = CipherHelper.encryptProtectPassword(protectPassword, CloudConstant.KEY_PROTECT_PASSWORD);
        String passCipher = CipherHelper.encryptPass(info.getPass(), protectPassword);


        String ocspInsertSql = String.format(sqlFormat, info.getTableName(), startId++, 1, info.getHost(), KEY_OCSP_PUBLIC_KEY,  ocspJson, "OCSP公钥证书");
        String certInfoInsertSql = String.format(sqlFormat, info.getTableName(), startId++,  2, info.getHost(), KEY_CERT_INFO,  certInfo, "云服务证书");
        String passCipherInsertSql = String.format(sqlFormat, info.getTableName(), startId++, 2, info.getHost(), KEY_PASS_CIPHER,  passCipher, "密码密文");
        String protectPasswordCipherInsertSql = String.format(sqlFormat, info.getTableName(), startId++, 2, info.getHost(), KEY_PROTECT_PASSWORD_CIPHER,  protectPasswordCipher, "保护口令密文");
        return Arrays.asList(ocspInsertSql, certInfoInsertSql, passCipherInsertSql, protectPasswordCipherInsertSql);
    }

    private static List<String> generateInsertSql(Info info) {
        Integer startId = info.getStartId();
        String sqlFormat = "insert ignore into %s (`id`, `type`, `host`, `setting_key`, `value`, `remark`) values (%s, %s, '%s', '%s', '%s', '%s');";
        String ocspInsertSql = String.format(sqlFormat, info.getTableName(), startId++, 1, info.getHost(), KEY_OCSP_PUBLIC_KEY,  "", "OCSP公钥证书");
        String certInfoInsertSql = String.format(sqlFormat, info.getTableName(), startId++,  2, info.getHost(), KEY_CERT_INFO,  "", "云服务证书");
        String passCipherInsertSql = String.format(sqlFormat, info.getTableName(), startId++, 2, info.getHost(), KEY_PASS_CIPHER,  "", "密码密文");
        String protectPasswordCipherInsertSql = String.format(sqlFormat, info.getTableName(), startId++, 2, info.getHost(), KEY_PROTECT_PASSWORD_CIPHER,  "", "保护口令密文");
        return Arrays.asList(ocspInsertSql, certInfoInsertSql, passCipherInsertSql, protectPasswordCipherInsertSql);
    }

    private static List<String> generateUpdateSql(Info info) {
        Gson gson = new Gson();
        String filePath = CERT_PATH + File.separator + info.subPath + File.separator;
        String sqlFormat = "update %s set value = '%s' where `host` = '%s' and `type` = %s and `setting_key` = '%s';";
        // 读取 ocsp证书公钥信息
        CloudOcspPublicKey ocspPublicKey = CustomPropertiesLoader.loadFileSystemProperties(filePath + info.getOcspFilename());
        String ocspJson = gson.toJson(ocspPublicKey);

        // 读取 签名私钥证书信息、密码、保护密码等信息
        File file = new File(filePath + info.getCertFileName());
        if (!file.exists()) {
            throw new RuntimeException("证书文件不存在");
        }
        String certInfo = readSecretInfoFromFile(file);
        String protectPassword = generateProtectPassword();
        String protectPasswordCipher = CipherHelper.encryptProtectPassword(protectPassword, CloudConstant.KEY_PROTECT_PASSWORD);
        String passCipher = CipherHelper.encryptPass(info.getPass(), protectPassword);

        String ocspUpdateSql = String.format(sqlFormat, info.getTableName(), ocspJson, info.getHost(), 1, KEY_OCSP_PUBLIC_KEY);
        String certInfoUpdateSql = String.format(sqlFormat, info.getTableName(), certInfo, info.getHost(), 2, KEY_CERT_INFO);
        String passCipherUpdateSql = String.format(sqlFormat, info.getTableName(), passCipher, info.getHost(), 2, KEY_PASS_CIPHER);
        String protectPasswordCipherUpdateSql = String.format(sqlFormat, info.getTableName(), protectPasswordCipher, info.getHost(), 2, KEY_PROTECT_PASSWORD_CIPHER);
        return Arrays.asList(ocspUpdateSql, certInfoUpdateSql, passCipherUpdateSql, protectPasswordCipherUpdateSql);
    }


//    public static void main(String[] args) {
//        List<String> sqls = generateSql("<EMAIL>", "62529284");
////        List<String> sqls = generateSql("<EMAIL>", "58143360");
////        List<String> sqls = generateSql("<EMAIL>", "19354024");
//          // 19号过期的证书
////        List<String> sqls = generateSql("<EMAIL>", "99249771");
////        List<String> sqls = generateSql("<EMAIL>", "41746607");
//
//
//
//        System.out.println("=================以下是sql更新语句=========================================");
//        sqls.forEach(System.out::println);
//        System.out.println("=================sql更新语句 end=========================================");
//    }

    // 使用证书和密码明文生成更换证书的sql语句
    public static List<String> generateSql(String certFileName, String pass) {
        String logFormat = "生成SQL语句，证书文件：%s, 导入码：%s";
        System.out.println(String.format(logFormat, certFileName, pass));
        File file = new File(CERT_PATH + File.separator + certFileName);
        if (!file.exists()) {
            throw new RuntimeException("证书文件不存在");
        }
        String certInfo = readSecretInfoFromFile(file);
        String protectPassword = generateProtectPassword();
        String protectPasswordCipher = CipherHelper.encryptProtectPassword(protectPassword, CloudConstant.KEY_PROTECT_PASSWORD);
        String passCipher = CipherHelper.encryptPass(pass, protectPassword);

        System.out.println("证书信息：\n" + certInfo);
        System.out.println("保护密码：\n" + protectPassword);
        System.out.println("保护密码密文：\n" + protectPasswordCipher);
        System.out.println("密码密文：\n" + passCipher);

        // 生成 global_config配置的sql更新语句
        // 保护证书sql
        String certInfoUpdateSqlFormat = "update global_config set config_value = '%s' where config_key = 'cloud.cert-info';";
        // 密码密文
        String passCipherUpdateSqlFormat = "update global_config set config_value = '%s' where config_key = 'cloud.pass-cipher';";
        // 保护口令密文
        String protectPasswordCipherUpdateSqlFormat = "update global_config set config_value = '%s' where config_key = 'cloud.protect-password-cipher';";

        String certInfoUpdateSql = String.format(certInfoUpdateSqlFormat, certInfo);
        String passCipherUpdateSql = String.format(passCipherUpdateSqlFormat, passCipher);
        String protectPasswordCipherUpdateSql = String.format(protectPasswordCipherUpdateSqlFormat, protectPasswordCipher);
        return Arrays.asList(certInfoUpdateSql, passCipherUpdateSql, protectPasswordCipherUpdateSql);
    }

    /**
     * 这里只生成更新语句，根据host生成更新 OCSP公钥证书，签名私钥信息，密码密文，保护口令密文
     * @param host
     */
    public static List<String> generateUpdateSqlByHost(String host, String tableName, String certInfo, String passCipher, String protectPasswordCipher) {
        String ocspUpdateSqlFormat = "update %s set value = %s where host = %s and type = 1;";
        String certInfoUpdateSqlFormat = "update %s set value = %s where host = %s and setting_key = %s and type = 2;";
        String passwordCipherUpdateSqlFormat = "update %s set value = %s where host = %s and setting_key = %s and type = 2;";
        String protectPasswordCipherUpdateSqlFormat = "update %s set value = %s where host = %s and setting_key = %s and type = 2;";

        String certInfoUpdateSql = String.format(certInfoUpdateSqlFormat, tableName, certInfo, host, "cloud.cert-info");
        String passwordCipherUpdateSql = String.format(passwordCipherUpdateSqlFormat, tableName, passCipher, host, "cloud.pass-cipher");
        String protectPasswordCipherUpdateSql = String.format(protectPasswordCipherUpdateSqlFormat, tableName, protectPasswordCipher, host, "protect-password-cipher");
        return Arrays.asList(certInfoUpdateSql, passwordCipherUpdateSql, protectPasswordCipherUpdateSql);
    }

    /**
     * 这里生成insert语句，且提供默认值
     * @return
     */
    public static List<String> generateInsertSqlCloudCert(String host, String tableName) {
        return null;
    }

    // 生成保护密码
    private static String generateProtectPassword() {
        return RandomStringUtils.randomNumeric(LENGTH_PROTECT_PASSWORD);
    }

    private static String readSecretInfoFromFile(File certFile) {
        try {
            StringBuilder contentBuilder = new StringBuilder();
            BufferedReader reader = new BufferedReader(new FileReader(certFile));
            String line = "";
            while ((line = reader.readLine()) != null) {
                contentBuilder.append(line).append("\n");
            }
            return contentBuilder.toString();
        } catch (IOException e) {
            throw new ServiceException("证书读取失败" + e.getLocalizedMessage());
        }
    }

    @Getter
    @Setter
    @ToString
    @Builder
    public static class Info {
        private Integer startId;
        private String subPath;
        private String host;
        private String tableName;
        // ocsp公钥证书名称
        private String ocspFilename;
        // 签名私钥的证书名称
        private String certFileName;
        // 签名私钥的证书密码
        private String pass;
    }
}
