package com.tipray.dlp.test;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import com.tipray.dlp.controller.socket.dto.SysServiceResponseDTO;
import com.tipray.dlp.controller.socket.dto.TaskPlanItemResponseDTOV1;
import com.tipray.dlp.socket.Package;
import com.tipray.dlp.socket.utils.ByteUtil;
import com.tipray.dlp.socket.utils.PackerUtil;
import com.tipray.dlp.socket.utils.SocketUtil;
import com.tipray.dlp.util.StringUtil;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通信组件响应体解析测试
 * <AUTHOR>
 * @date 2023/11/06/13:28
 */
@SpringBootTest
public class SocketRespondTest {
    /**
     * 解析协议日志文件并输出终端的在线状态信息
     * 说明：通信协议日志要开启调试模式，这样才能正常解析
     */
    public static void main(String[] args) {
        List<String> lineList = FileUtil.readLines(new File("C:\\Users\\<USER>\\Desktop\\socket.20240925.16.log"), "utf-8");
        Map<String, List<String>> map = new java.util.HashMap<>();
        String key = null;
        StringBuilder value = null;
        // 步骤1：解析日志文件，提取0x010191协议数据
        for (int i = 0; i < lineList.size(); i++) {
            String line = lineList.get(i);
            if(line.contains("01 32 30 31 39 10 00 00 00 01 00 00 00 00 01 00 1d 01 01 91")){
                key = line.substring(100,111);
                value = new StringBuilder(line);
                String time = lineList.get(i-1).substring(0, 13);
                if(!map.containsKey(key)){
                    map.put(key, new LinkedList<>());
                    map.put(key+"Time", new LinkedList<>());
                }
                List<String> timeList = map.get(key+"Time");
                if(timeList.size() < 2){
                    timeList.add(time);
                } else {
                    timeList.set(1, time);
                }
            } else if (value!=null && line.matches("(\\s[0-9a-fA-F]{2})+")) {
                value.append(line);
            } else {
                if(key != null){
                    map.get(key).add(value.toString());
                }
                key = null;
                value = null;
            }
        }
        if(key != null){
            map.get(key).add(value.toString());
        }
        // 步骤2：将协议数据解析成终端的在线状态信息
        StringBuilder sb = null;
        List<String> statueList = new LinkedList<>();
        for (String mapKey : map.keySet()) {
            if(mapKey.endsWith("Time")){
                continue;
            }
            sb = new StringBuilder(mapKey);
            List<String> timeList = map.get(mapKey+"Time");
            sb.append(" ").append(timeList.get(0)).append("~").append(timeList.get(1));

            List<String> termList = new LinkedList<>();
            List<String> pkgList = map.get(mapKey);
            for (String pkgHex : pkgList) {
                Package pkg = PackerUtil.hexLogToPackage(pkgHex);
                if(pkg!=null){
                    byte[] body = pkg.getBody();
                    // 数据格式 终端个数（short类型） 【终端ID+操作员ID+登录状态】[DWORD+DWORD+BYTE]*N个
                    byte[] bytes = Arrays.copyOfRange(body, 2, body.length);    // 终端状态数据
                    for (int i = 0; i < bytes.length; i += 9) {
                        int terminalId = ByteUtil.toInt(Arrays.copyOfRange(bytes, i, i + 4));
                        int userId = ByteUtil.toInt(Arrays.copyOfRange(bytes, i + 4, i + 8));
                        int status = bytes[i + 8];
                        termList.add(terminalId+"_"+userId+"_"+status);
                    }
                }
            }
            sb.append(" ").append(termList.stream().sorted().collect(Collectors.joining(",")));
            statueList.add(sb.toString());
        }
        // 步骤3：排序后打印输出终端的在线状态信息
        statueList.stream().sorted().forEach(System.out::println);
    }

    //    @Test
    public void testzz() throws FileNotFoundException {
        String bodyStr = IoUtil.readUtf8(new FileInputStream("D:\\body.txt"));
        String[] items = StringUtil.split(bodyStr, ",");
        byte[] body = new byte[items.length];
        for (int i = 0; i < items.length; i++) {
            body[i] = (byte) Integer.parseInt(items[i].trim());
        }
        TaskPlanItemResponseDTOV1 dto = SocketUtil.toObject(body, (short)1, TaskPlanItemResponseDTOV1.class);
        System.out.println(dto);
    }

//    @Test
    public void testserver() throws FileNotFoundException {
        String bodyStr = IoUtil.readUtf8(new FileInputStream("D:\\body-server.txt"));
        String[] items = StringUtil.split(bodyStr, ",");
        byte[] body = new byte[items.length];
        for (int i = 0; i < items.length; i++) {
            body[i] = (byte) Integer.parseInt(items[i].trim());
        }
        SysServiceResponseDTO dto = SocketUtil.toObject(body, SysServiceResponseDTO.class);
        System.out.println(dto);
    }
}
