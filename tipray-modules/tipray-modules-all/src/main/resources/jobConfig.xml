<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE root>
<!--
    job标签说明：定时任务
	    state: job状态,0:未启用,1:启用
	    cron: job定时器的时间表达式
	    refclass: job执行的类名，必须实现JobService
	    exec-now: 是否立即执行一次，默认true
	    i18nKey: 用于控制台定时器监控页面的 name以及remark的多语言， i18nKey 存储在 zh、tw、en.json文件中，在pages.层级下添加,
	             name的多语言key对应i18nKey, remark的多语言key对应 i18nKey + "remark"
 -->
<root>
    <job state="1" cron="0 30 1 * * ?" name="数据清理JOB" refclass="com.tipray.dlp.service.job.ClearJob" remark="用于清除无用的数据、文件等" i18nKey="clearJob"/>
    <job state="1" cron="0/15 * * * * ?" name="设备登录状态JOB" refclass="com.tipray.dlp.service.job.DeviceLoginStatusJob" remark="用于定时获取设备的登录状态" i18nKey="deviceLoginStatusJob"/>
    <job state="1" cron="0/30 * * * * ?" name="表数据变更监听JOB" refclass="com.tipray.dlp.service.job.TableChangeObserveJob"  remark="用于观察表数据的变更" i18nKey="tableChangeObserveJob"/>
    <job state="1" cron="0 0 */24 * * ?" name="更新注册信息JOB" refclass="com.tipray.dlp.service.job.RegisterJob"  remark="用于更新本地缓存的注册信息，判断是否过期" i18nKey="registerJob"/>
    <job state="1" cron="0/30 * * * * ?" name="全盘扫描状态更新JOB" refclass="com.tipray.dlp.service.job.DiskScanStatusObserveJob"  remark="用于更新本地全盘扫描任务的状态" i18nKey="diskScanStatusObserveJob"/>
    <job state="1" cron="0/60 * * * * ?" name="及时消息告警JOB" refclass="com.tipray.dlp.service.job.AlarmJob"  remark="用于执行邮件告警" i18nKey="alarmJob"/>
    <job state="1" cron="0/60 * * * * ?" name="服务器状态告警JOB" refclass="com.tipray.dlp.service.job.ServiceAlarmJob"  remark="用于执行服务器状态告警" i18nKey="serviceAlarmJob"/>
    <job state="1" cron="0 0 1 * * ?" name="周期性采集检测规则JOB" refclass="com.tipray.dlp.service.job.RuleTrainCycleJob"  remark="用于数据库指纹重新采集" i18nKey="ruleCycleTrainJob"/>
    <job state="1" cron="0 */10 * * * ?" name="重新执行采集失败的规则" refclass="com.tipray.dlp.service.job.RuleTrainJob" i18nKey="ruleTrainJob"/>
    <job state="1" cron="0 */1 * * * ?" name="训练服务器启动状态监听JOB" refclass="com.tipray.dlp.service.job.CCBGCollectorStatusJob" remark="用于定时获取设备的登录状态" i18nKey="CCBGCollectorStatusJob"/>
    <job state="1" cron="0 */1 * * * ?" name="定期向消息中心发送消息" refclass="com.tipray.dlp.service.job.MsgCenterJob" i18nKey="msgCenterJob"/>
    <job state="1" cron="*/2 * * * * ?" name="定时获取服务器信息" refclass="com.tipray.dlp.service.job.ServerInfoJob" remark="当服务器状态功能被访问时，每隔几秒从引擎获取一次服务器数据（cpu实时性要求高）" i18nKey="serverInfoJob"/>
<!--    <job state="1" cron="0 */9 * * * ?" name="第三方数据同步JOB" refclass="com.tipray.dlp.service.job.SyncConflictJob" remark="用于处理第三方数据源数据问题"/>-->
    <job state="1" cron="0 0/30 * * * ?" name="系统预警扫描JOB" refclass="com.tipray.dlp.service.job.SysAlarmJob" remark="用于定时计算是否存在触发预警的数据，存在则触发系统告警提醒" i18nKey="sysAlarmJob"/>
    <job state="1" cron="0/30 * * * * ?" name="同步审计日志分组数据" refclass="com.tipray.dlp.service.job.SyncGroupIdToLogJob" remark="用于定时将部门id同步到旧终端产生的审计日志"/>
    <job state="1" cron="0/30 * * * * ?" name="同步审计日志表变更版本" refclass="com.tipray.dlp.service.job.SyncLogTbVerToTableJob" remark="用于定时检测审计日志表是否变更，有变更则更新版本"/>
    <job state="1" cron="0 */10 * * * ?" name="更新Ftp组件传输状态JOB" refclass="com.tipray.dlp.service.job.FtpStatusJob" i18nKey="ftpStatusJob"/>
    <job state="1" cron="0/15 * * * * ?" name="计算机个性化图片上传JOB" refclass="com.tipray.dlp.service.job.PicUploadJob" remark="用于定时将web服务器图片上传到文件服务器" i18nKey="picUploadJob"/>
    <job state="1" cron="*/10 * * * * ?" name="检测文件下载JOB" refclass="com.tipray.dlp.service.job.PatchDetectionJob" remark="用于下载检测文件" i18nKey="patchDetectionJob"/>
    <job state="1" cron="*/10 * * * * ?" name="补丁文件下载JOB" refclass="com.tipray.dlp.service.job.PatchJob" remark="用于下载补丁文件" i18nKey="patchJob"/>
    <job state="1" cron="*/10 * * * * ?" name="补丁文件和检测文件上传JOB" refclass="com.tipray.dlp.service.job.PatchUploadJob" remark="用于上传补丁文件和检测文件" i18nKey="patchUploadJob"/>
    <job state="1" cron="0 30 1 * * ?" name="快速MD5JOB" refclass="com.tipray.dlp.service.job.FileQuickMd5Job"/>
    <job state="1" cron="0 0 0 * * ?" name="任务分发失效任务标记更新JOB" refclass="com.tipray.dlp.service.job.UpdateTaskStatusJob" method="execute" remark="用于将任务分发策略失效的策略标记为失效状态，即active=2" i18nKey="updateActiveJob"/>
    <job state="1" cron="0 */5 * * * ?" name="数据库密码同步" refclass="com.tipray.dlp.service.job.DatabasePasswordSyncJob" method="execute" remark="数据库密码同步，用于校验数据库密码是否为最新密码" i18nKey="databasePasswordSyncJob"/>
    <job state="1" cron="0 * * * * ?" name="文件及任务状态更新JOB" refclass="com.tipray.dlp.service.job.UpdateFileAndTaskStatusJob" method="execute" remark="定时（每分钟）检测任务文件是否已存在，存在则更新文件/任务状态" i18nKey="updateFileAndTaskStatusJob"/>
    <!--<job state="1" cron="0 0 0 * * ?" name="智能备份任务到期后标记为停止任务JOB" refclass="com.tipray.dlp.backup.job.BackupTaskStatusJob" method="execute" remark="智能备份任务到期后标记为停止任务JOB" i18nKey="backupTaskStatusJob"/>-->
    <job state="1" cron="0 * * * * ? " name="软件资产信息定时增量更新" refclass="com.tipray.dlp.service.job.SoftwareInfoSyncJob" remark="定时（每分钟）增量处理（根据同款软件识别配置）软件资产纵表数据转存成横表数据"/>
    <job state="1" cron="0 15 0 * * ? " name="定时更新永久离线终端绑定状态" refclass="com.tipray.dlp.service.job.OfflineTermStatusJob" remark="用于更新永久离线终端绑定状态缓存"/>
</root>
