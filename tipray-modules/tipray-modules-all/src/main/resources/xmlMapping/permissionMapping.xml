<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapping>
<mapping>
    <class name="com.tipray.dlp.bean.dto.XmlRootDTO">
        <map-to xml="root"/>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.dto.PermissionXmlDTO">
            <bind-xml name="permission" node="element" />
        </field>
    </class>
    <class name="com.tipray.dlp.bean.dto.PermissionXmlDTO">
        <map-to xml="permission"/>
        <field name="moduleCode" type="java.lang.String">
            <bind-xml name="mo-code" node="attribute"/>
        </field>
        <field name="menuCode" type="java.lang.String">
            <bind-xml name="me-code" node="attribute"/>
        </field>
        <field name="threeType" type="java.lang.String">
            <bind-xml name="three-type" node="attribute"/>
        </field>
        <field name="remark" type="java.lang.String">
            <bind-xml name="remark" node="attribute"/>
        </field>
        <field name="remarkKey" type="java.lang.String">
            <bind-xml name="i18n-remark" node="attribute"/>
        </field>
        <field name="doTest" type="java.lang.Boolean">
            <bind-xml name="do-test" node="attribute"/>
        </field>
        <field name="isBtn" type="java.lang.Boolean">
            <bind-xml name="is-btn" node="attribute"/>
        </field>
        <field name="include" type="com.tipray.dlp.bean.dto.XmlIncludeDTO">
            <bind-xml name="inclusions" node="element"/>
        </field>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.dto.PermissionXmlDTO">
            <bind-xml name="permission" node="element" />
        </field>
    </class>

    <class name="com.tipray.dlp.bean.dto.XmlIncludeDTO">
        <map-to xml="inclusions"/>
        <field name="children" collection="collection" type="java.lang.String">
            <bind-xml name="me-code" node="element" />
        </field>
    </class>
</mapping>