<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapping>
<mapping>
    <class name="com.tipray.dlp.bean.dto.XmlRootDTO">
        <map-to xml="root"/>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.ReportTextTemp">
            <bind-xml name="textTemp" node="element" />
        </field>
    </class>

    <class name="com.tipray.dlp.bean.ReportTextTemp">
        <map-to xml="textTemp"/>
        <field name="id" type="java.lang.Integer">
            <bind-xml name="id" node="attribute"/>
        </field>
        <field name="meCode" type="java.lang.String">
            <bind-xml name="me-code" node="attribute"/>
        </field>
        <field name="str" type="java.lang.String">
            <bind-xml name="str" node="attribute"/>
        </field>
        <field name="tagName" type="java.lang.String">
            <bind-xml name="tag-name" node="attribute"/>
        </field>
        <field name="fieldName" type="java.lang.String">
            <bind-xml name="field-name" node="attribute"/>
        </field>
        <field name="dataType" type="java.lang.Integer">
            <bind-xml name="data-type" node="attribute"/>
        </field>
        <field name="label" type="java.lang.String">
            <bind-xml name="label" node="attribute"/>
        </field>
    </class>
</mapping>