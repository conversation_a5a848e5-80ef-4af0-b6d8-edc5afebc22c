<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapping>
<mapping>
    <class name="com.tipray.dlp.bean.dto.XmlRootDTO">
        <map-to xml="root"/>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.dto.TableXmlDTO">
            <bind-xml name="table" node="element" />
        </field>
    </class>
    <class name="com.tipray.dlp.bean.dto.TableXmlDTO">
        <map-to xml="table"/>
        <field name="tableName" type="java.lang.String">
            <bind-xml name="t-name" node="attribute"/>
        </field>
        <field name="className" type="java.lang.String">
            <bind-xml name="cl-name" node="attribute"/>
        </field>
        <field name="columnName" type="java.lang.String">
            <bind-xml name="c-name" node="attribute"/>
        </field>
        <field name="filed" type="java.lang.String">
            <bind-xml name="f-name" node="attribute"/>
        </field>
        <field name="type" type="java.lang.String">
            <bind-xml name="type" node="attribute"/>
        </field>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.dto.TableXmlDTO">
            <bind-xml name="table" node="element" />
        </field>
    </class>

</mapping>