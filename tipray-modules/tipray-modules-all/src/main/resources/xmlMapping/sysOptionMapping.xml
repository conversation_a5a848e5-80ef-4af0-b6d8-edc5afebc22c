<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapping>
<mapping>
    <class name="com.tipray.dlp.bean.dto.XmlRootDTO">
        <map-to xml="root"/>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.SysOptions">
            <bind-xml name="option" node="element" />
        </field>
    </class>
    <class name="com.tipray.dlp.bean.SysOptions">
        <map-to xml="option"/>
        <field name="optId" type="java.lang.Long">
            <bind-xml name="code" node="attribute"/>
        </field>
        <field name="optVal" type="java.lang.Long">
            <bind-xml name="value" node="attribute"/>
        </field>
        <field name="optStrVal" type="java.lang.String">
            <bind-xml name="strValue" node="attribute"/>
        </field>
        <field name="optDesp" type="java.lang.String">
            <bind-xml name="remark" node="attribute"/>
        </field>
    </class>
</mapping>