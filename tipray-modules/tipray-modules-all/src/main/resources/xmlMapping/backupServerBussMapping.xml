<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapping>
<mapping>
    <class name="com.tipray.dlp.bean.dto.XmlRootDTO">
        <map-to xml="root"/>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.BackupServerBusiness">
            <bind-xml name="business" node="element" />
        </field>
    </class>
    <class name="com.tipray.dlp.bean.BackupServerBusiness">
        <map-to xml="business"/>
        <field name="bussId" type="java.lang.Integer">
            <bind-xml name="id" node="attribute"/>
        </field>
        <field name="bussName" type="java.lang.String">
            <bind-xml name="name" node="attribute"/>
        </field>
        <field name="relativePath" type="java.lang.String">
            <bind-xml name="path" node="attribute"/>
        </field>
        <field name="enableDelete" type="java.lang.Integer">
            <bind-xml name="deletable" node="attribute"/>
        </field>
        <field name="fileRetainDays" type="java.lang.Integer">
            <bind-xml name="days" node="attribute"/>
        </field>
        <field name="enabled" type="java.lang.Integer">
            <bind-xml name="enabled" node="attribute"/>
        </field>
    </class>
</mapping>