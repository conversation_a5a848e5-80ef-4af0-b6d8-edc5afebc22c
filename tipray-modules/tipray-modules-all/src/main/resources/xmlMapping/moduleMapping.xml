<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapping>
<mapping>
    <class name="com.tipray.dlp.bean.dto.XmlRootDTO">
        <map-to xml="root"/>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.dto.ModuleXmlDTO">
            <bind-xml name="module" node="element" />
        </field>
    </class>
    <class name="com.tipray.dlp.bean.dto.ModuleXmlDTO">
        <map-to xml="module"/>
        <field name="code" type="java.lang.Integer">
            <bind-xml name="code" node="attribute"/>
        </field>
        <field name="name" type="java.lang.String">
            <bind-xml name="name" node="attribute"/>
        </field>
        <field name="type" type="java.lang.Integer">
            <bind-xml name="type" node="attribute"/>
        </field>
        <field name="enable" type="java.lang.Boolean">
            <bind-xml name="enable" node="attribute"/>
        </field>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.dto.ModuleXmlDTO">
            <bind-xml name="module" node="element" />
        </field>
    </class>
</mapping>