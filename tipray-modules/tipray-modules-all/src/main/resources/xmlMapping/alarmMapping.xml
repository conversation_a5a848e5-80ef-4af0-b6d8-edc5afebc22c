<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapping>
<mapping>
    <class name="com.tipray.dlp.bean.dto.XmlRootDTO">
        <map-to xml="root"/>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.dto.AlarmXmlDTO">
            <bind-xml name="alarm" node="element" />
        </field>
    </class>
    <class name="com.tipray.dlp.bean.dto.AlarmXmlDTO">
        <map-to xml="alarm"/>
        <field name="menuCode" type="java.lang.String">
            <bind-xml name="me-code" node="attribute"/>
        </field>
        <field name="strategyGroupType" type="java.lang.Integer">
            <bind-xml name="strategy-group-type" node="attribute"/>
        </field>
        <field name="strategyNumbers" type="java.lang.String">
            <bind-xml name="strategy-numbers" node="attribute"/>
        </field>
        <field name="alarmType" type="java.lang.Integer">
            <bind-xml name="alarm-type" node="attribute"/>
        </field>
        <field name="alarmBus" type="java.lang.Integer">
            <bind-xml name="alarm-bus" node="attribute"/>
        </field>
        <field name="order" type="java.lang.Integer">
            <bind-xml name="order" node="attribute"/>
        </field>
        <field name="i18nDict" type="java.lang.String">
            <bind-xml name="i18n-dict" node="attribute"/>
        </field>
        <field name="remark" type="java.lang.String">
            <bind-xml name="remark" node="attribute"/>
        </field>
    </class>
</mapping>
