<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapping>
<mapping>
    <class name="com.tipray.dlp.bean.dto.XmlRootDTO">
        <map-to xml="root"/>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.ReportTableTemp">
            <bind-xml name="tableTemp" node="element" />
        </field>
    </class>
    <class name="com.tipray.dlp.bean.ReportTableTemp">
        <map-to xml="tableTemp"/>
        <field name="tag" type="java.lang.String">
            <bind-xml name="tag" node="attribute"/>
        </field>
        <field name="colName" type="java.lang.String">
            <bind-xml name="col-name" node="attribute"/>
        </field>
        <field name="colTag" type="java.lang.String">
            <bind-xml name="col-tag" node="attribute"/>
        </field>
        <field name="fieldName" type="java.lang.String">
            <bind-xml name="field-name" node="attribute"/>
        </field>
        <field name="menuCode" type="java.lang.String">
            <bind-xml name="menu-code" node="attribute"/>
        </field>
        <field name="dataType" type="java.lang.Integer">
            <bind-xml name="data-type" node="attribute"/>
        </field>
        <field name="type" type="java.lang.Integer">
            <bind-xml name="type" node="attribute"/>
        </field>
        <field name="sortField" type="java.lang.Integer">
            <bind-xml name="sort-field" node="attribute"/>
        </field>
    </class>
</mapping>