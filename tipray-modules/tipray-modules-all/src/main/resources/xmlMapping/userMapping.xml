<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapping>
<mapping>
    <class name="com.tipray.dlp.bean.dto.XmlRootDTO">
        <map-to xml="root"/>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.dto.RoleXmlDTO">
            <bind-xml name="role" node="element" />
        </field>
    </class>
    <class name="com.tipray.dlp.bean.SysUser">
        <map-to xml="user"/>
        <field name="id" type="java.lang.Long">
            <bind-xml name="id" node="attribute"/>
        </field>
        <field name="account" type="java.lang.String">
            <bind-xml name="account" node="attribute"/>
        </field>
        <field name="name" type="java.lang.String">
            <bind-xml name="name" node="attribute"/>
        </field>
        <field name="password" type="java.lang.String">
            <bind-xml name="password" node="attribute"/>
        </field>
    </class>
    <class name="com.tipray.dlp.bean.dto.RoleXmlDTO">
        <map-to xml="role"/>
        <field name="id" type="java.lang.Long">
            <bind-xml name="id" node="attribute"/>
        </field>
        <field name="type" type="java.lang.Integer">
            <bind-xml name="type" node="attribute"/>
        </field>
        <field name="name" type="java.lang.String">
            <bind-xml name="name" node="attribute"/>
        </field>
        <field name="remark" type="java.lang.String">
            <bind-xml name="remark" node="attribute"/>
        </field>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.dto.RoleXmlDTO">
            <bind-xml name="role" node="element" />
        </field>
        <field name="users" collection="collection" type="com.tipray.dlp.bean.SysUser">
            <bind-xml name="user" node="element" />
        </field>
    </class>
</mapping>