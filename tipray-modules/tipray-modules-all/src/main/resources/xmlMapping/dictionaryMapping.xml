<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapping>
<mapping>
    <class name="com.tipray.dlp.bean.dto.XmlRootDTO">
        <map-to xml="root"/>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.dto.DictionaryXmlDTO">
            <bind-xml name="dictionary" node="element" />
        </field>
    </class>
    <class name="com.tipray.dlp.bean.dto.DictionaryXmlDTO">
        <map-to xml="dictionary"/>
        <field name="code" type="java.lang.String">
            <bind-xml name="code" node="attribute"/>
        </field>
        <field name="value" type="java.lang.Integer">
            <bind-xml name="value" node="attribute"/>
        </field>
        <field name="name" type="java.lang.String">
            <bind-xml name="name" node="attribute"/>
        </field>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.dto.DictionaryXmlDTO">
            <bind-xml name="dictionary" node="element" />
        </field>
    </class>
</mapping>