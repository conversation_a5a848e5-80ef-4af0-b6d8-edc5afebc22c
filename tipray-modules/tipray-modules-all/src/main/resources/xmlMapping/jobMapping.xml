<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapping>
<mapping>
    <class name="com.tipray.dlp.bean.dto.XmlRootDTO">
        <map-to xml="root"/>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.ScheduledTask">
            <bind-xml name="job" node="element" />
        </field>
    </class>
    <class name="com.tipray.dlp.bean.ScheduledTask">
        <map-to xml="job"/>
        <field name="name" type="java.lang.String">
            <bind-xml name="name" node="attribute"/>
        </field>
        <field name="refclass" type="java.lang.String">
            <bind-xml name="refclass" node="attribute"/>
        </field>
        <field name="method" type="java.lang.String">
            <bind-xml name="method" node="attribute"/>
        </field>
        <field name="cron" type="java.lang.String">
            <bind-xml name="cron" node="attribute"/>
        </field>
        <field name="state" type="java.lang.Integer">
            <bind-xml name="state" node="attribute"/>
        </field>
        <field name="remark" type="java.lang.String">
            <bind-xml name="remark" node="attribute"/>
        </field>
        <field name="execNow" type="boolean">
            <bind-xml name="exec-now" node="attribute"/>
        </field>
        <field name="i18nKey" type="java.lang.String">
            <bind-xml name="i18nKey" node="attribute"/>
        </field>
    </class>
</mapping>