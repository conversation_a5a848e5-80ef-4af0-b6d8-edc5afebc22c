<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapping>
<mapping>
    <class name="com.tipray.dlp.bean.dto.XmlRootDTO">
        <map-to xml="root"/>
        <field name="children" collection="collection" type="com.tipray.dlp.bean.InterpretationTemp">
            <bind-xml name="interpretation" node="element" />
        </field>
    </class>
    <class name="com.tipray.dlp.bean.InterpretationTemp">
        <map-to xml="interpretation"/>
        <field name="id" type="java.lang.Integer">
            <bind-xml name="id" node="attribute"/>
        </field>
        <field name="name" type="java.lang.String">
            <bind-xml name="name" node="attribute"/>
        </field>
        <field name="code" type="java.lang.String">
            <bind-xml name="code" node="element"/>
        </field>
        <field name="reportCode" type="java.lang.String">
            <bind-xml name="report-code" node="attribute"/>
        </field>
    </class>
</mapping>