<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE root>
<!--
    tag: 表格标签
    col-name: 列名
    col-tag: 列数据标签 用于模板显示
    filed-name: 对应报表查询结果字段名
    menu-code: 参考ReportDict menu-code
    data-type: 数据类型 0普通 1时长 2文件大小
    type: 二进制 1普通报表 2趋势报表 3对比报表
-->
<root>
    <tableTemp tag="dataSecurity" col-name="countObject" col-tag="countObject" field-name="xAxisLabel" menu-code="E" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="dataSecurity" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="E" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="dataSecurity" col-name="encOrDecSumAll" col-tag="batchEncAndDec" field-name="encOrDecSumAll" menu-code="E" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="dataSecurity" col-name="encSumAll" col-tag="batchEnc" field-name="encSumAll" menu-code="E" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="dataSecurity" col-name="decSumAll" col-tag="batchDec" field-name="decSumAll" menu-code="E" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="dataSecurity" col-name="diskScanSumAll" col-tag="diskScanFile" field-name="diskScanSumAll" menu-code="E" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="dataSecurity" col-name="diskScanSstSumAll" col-tag="diskScanSens" field-name="diskScanSstSumAll" menu-code="E" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="dataSecurity" col-name="diskScanNonsstSumAll" col-tag="diskScanNonSens" field-name="diskScanNonsstSumAll" menu-code="E" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="dataSecurity" col-name="diskScanEncSumAll" col-tag="diskScanEnc" field-name="diskScanEncSumAll" menu-code="E" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="dataSecurity" col-name="diskScanDecSumAll" col-tag="diskScanDec" field-name="diskScanDecSumAll" menu-code="E" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="dataSecurity" col-name="outFileSumAll" col-tag="outboundFile" field-name="outFileSumAll" menu-code="E" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="dataSecurity" col-name="directOutFileSumAll" col-tag="directOutboundFile" field-name="directOutFileSumAll" menu-code="E" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="dataSecurity" col-name="directOutFileSizeSumAll" col-tag="directOutboundFileSize" field-name="directOutFileSizeSumAll" menu-code="E" data-type="2" type="7" sort-field="0"/>
    <tableTemp tag="dataSecurity" col-name="applyOutFileSumAll" col-tag="applyOutboundFile" field-name="applyOutFileSumAll" menu-code="E" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="dataSecurity" col-name="applyOutFileSizeSumAll" col-tag="applyOutboundFileSize" field-name="applyOutFileSizeSumAll" menu-code="E" data-type="2" type="7" sort-field="0"/>
    <tableTemp tag="dataSecurity" col-name="fileBackupSumAll" col-tag="fileBackup" field-name="fileBackupSumAll" menu-code="E" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="dataSecurity" col-name="fileBackupSizeSumAll" col-tag="fileBackupSize" field-name="fileBackupSizeSumAll" menu-code="E" data-type="2" type="7" sort-field="0"/>
    <tableTemp tag="dataSecurity" col-name="encFileSumAll" col-tag="encFile" field-name="encFileSumAll" menu-code="E" data-type="0" type="7" sort-field="1"/>

    <tableTemp tag="printMonitor" col-name="countObject" col-tag="countObject" field-name="xAxisLabel" menu-code="C" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="printMonitor" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="C" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="printMonitor" col-name="printedCopiesSumAll" col-tag="copiesNum" field-name="printedCopiesSumAll" menu-code="C" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="printMonitor" col-name="printedPagesSumAll" col-tag="printedPages" field-name="printedPagesSumAll" menu-code="C" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="printMonitor" col-name="printFileSizeSumAll" col-tag="printFileSize" field-name="fileSizeSumAll" menu-code="C" data-type="2" type="7" sort-field="0"/>

    <tableTemp tag="sensitiveKeywordLog" col-name="countObject" col-tag="countObject" field-name="xAxisLabel" menu-code="F" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="sensitiveKeywordLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="F" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="sensitiveKeywordLog" col-name="sensitiveKeywordTriggerCount" col-tag="triggerNum" field-name="triggerCountSum" menu-code="F" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="sensitiveKeywordLog" col-name="percent" col-tag="keywordPercent" field-name="keywordPercent" menu-code="F" data-type="0" type="1" sort-field="0"/>

    <tableTemp tag="HardwareAsset" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="B" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="HardwareAsset" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="B" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="HardwareAsset" col-name="changeTimesSumAll" col-tag="changeTimes" field-name="changeTimesSumAll" menu-code="B" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="HardwareAsset" col-name="cpuChangeTimesSumAll" col-tag="cpuChangeTimes" field-name="cpuChangeTimesSumAll" menu-code="B" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="HardwareAsset" col-name="moboChangeTimesSumAll" col-tag="moboChangeTimes" field-name="moboChangeTimesSumAll" menu-code="B" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="HardwareAsset" col-name="vcChangeTimesSumAll" col-tag="vcChangeTimes" field-name="vcChangeTimesSumAll" menu-code="B" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="HardwareAsset" col-name="hddChangeTimesSumAll" col-tag="hddChangeTimes" field-name="hddChangeTimesSumAll" menu-code="B" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="HardwareAsset" col-name="ramChangeTimesSumAll" col-tag="ramChangeTimes" field-name="ramChangeTimesSumAll" menu-code="B" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="HardwareAsset" col-name="otherChangeTimesSumAll" col-tag="otherChangeTimes" field-name="otherChangeTimesSumAll" menu-code="B" data-type="0" type="7" sort-field="0"/>

    <tableTemp tag="softwareAsset" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="B" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="softwareAsset" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="B" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="softwareAsset" col-name="changeTimesSumAll" col-tag="changeTimes" field-name="changeTimesSumAll" menu-code="B" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="softwareAsset" col-name="osChangeTimesSumAll" col-tag="osChangeTimes" field-name="osChangeTimesSumAll" menu-code="B" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="softwareAsset" col-name="avChangeTimesSumAll" col-tag="avChangeTimes" field-name="avChangeTimesSumAll" menu-code="B" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="softwareAsset" col-name="spChangeTimesSumAll" col-tag="spChangeTimes" field-name="spChangeTimesSumAll" menu-code="B" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="softwareAsset" col-name="appChangeTimesSumAll" col-tag="appChangeTimes" field-name="appChangeTimesSumAll" menu-code="B" data-type="0" type="7" sort-field="0"/>

    <tableTemp tag="chatLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="D" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="chatLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="D" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="chatLog" col-name="chatRecordNumSumAll" col-tag="chatRecords" field-name="chatRecordNumSumAll" menu-code="D" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="chatLog" col-name="loginNumSumAll" col-tag="loginTimes" field-name="loginNumSumAll" menu-code="D" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="chatLog" col-name="sentenceNumSumAll" col-tag="sentenceTotal" field-name="sentenceNumSumAll" menu-code="D" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="chatLog" col-name="charNumSumAll" col-tag="charTotal" field-name="charNumSumAll" menu-code="D" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="chatLog" col-name="imageNumSumAll" col-tag="imageTotal" field-name="imageNumSumAll" menu-code="D" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="chatLog" col-name="fileNumSumAll" col-tag="fileTotal" field-name="fileNumSumAll" menu-code="D" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="chatLog" col-name="fileSizeSumAll" col-tag="fileSizeTotal" field-name="fileSizeSumAll" menu-code="D" data-type="2" type="7" sort-field="0"/>

    <tableTemp tag="urlsLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="D" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="urlsLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="D" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="urlsLog" col-name="urlNumSumAll" col-tag="pageViewsCount" field-name="urlNumSumAll" menu-code="D" data-type="0" type="7" sort-field="1"/>

    <tableTemp tag="webBrowsingTime" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="D" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="webBrowsingTime" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="D" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="webBrowsingTime" col-name="totalTimeSumAll" col-tag="pageViewDuration" field-name="totalTimeSumAll" menu-code="D" data-type="1" type="7" sort-field="1"/>

    <tableTemp tag="urlsSearch" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="D" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="urlsSearch" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="D" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="urlsSearch" col-name="urlSearchSumAll" col-tag="urlSearchTotal" field-name="searchCountSum" menu-code="D" data-type="0" type="7" sort-field="1"/>

    <tableTemp tag="browserPasteLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="D" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="browserPasteLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="D" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="browserPasteLog" col-name="browserPasteSumAll" col-tag="browserPasteTotal" field-name="pasteCountSum" menu-code="D" data-type="0" type="7" sort-field="1"/>

    <tableTemp tag="trwfeLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="G" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="G" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="fileDecryptSumAll" col-tag="fileDecryptTotal" field-name="fileDecryptSumAll" menu-code="G" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="trwfeLog" col-name="fileDecryptApproveSumAll" col-tag="fileDecApprove" field-name="fileDecryptApproveSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="fileDecryptRejectSumAll" col-tag="fileDecReject" field-name="fileDecryptRejectSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="fileDecryptAutoApproveSumAll" col-tag="fileDecAutoApprove" field-name="fileDecryptAutoApproveSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="outSendSumAll" col-tag="outSendTotal" field-name="outSendSumAll" menu-code="G" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="trwfeLog" col-name="outSendApproveSumAll" col-tag="outSendApprove" field-name="outSendApproveSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="outSendRejectSumAll" col-tag="outSendReject" field-name="outSendRejectSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="outSendAutoApproveSumAll" col-tag="outSendAutoApprove" field-name="outSendAutoApproveSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="changeFileLevelSumAll" col-tag="changeFileLevelTotal" field-name="changeFileLevelSumAll" menu-code="G" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="trwfeLog" col-name="changeFileLevelApproveSumAll" col-tag="changeFileLevelApprove" field-name="changeFileLevelApproveSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="changeFileLevelRejectSumAll" col-tag="changeFileLevelReject" field-name="changeFileLevelRejectSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="changeFileLevelAutoApproveSumAll" col-tag="changeFileLevelAutoApprove" field-name="changeFileLevelAutoApproveSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="offlineSumAll" col-tag="offlineTotal" field-name="offlineSumAll" menu-code="G" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="trwfeLog" col-name="offlineApproveSumAll" col-tag="offlineApprove" field-name="offlineApproveSumAll" menu-code="G" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="trwfeLog" col-name="offlineRejectSumAll" col-tag="offlineReject" field-name="offlineRejectSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="offlineAutoApproveSumAll" col-tag="offlineAutoApprove" field-name="offlineAutoApproveSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="filePrintSumAll" col-tag="filePrintTotal" field-name="filePrintSumAll" menu-code="G" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="trwfeLog" col-name="filePrintApproveSumAll" col-tag="filePrintApprove" field-name="filePrintApproveSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="filePrintRejectSumAll" col-tag="filePrintReject" field-name="filePrintRejectSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="filePrintAutoApproveSumAll" col-tag="filePrintAutoApprove" field-name="filePrintAutoApproveSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="cancelWatermarkSumAll" col-tag="cancelWatermarkTotal" field-name="cancelWatermarkSumAll" menu-code="G" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="trwfeLog" col-name="cancelWatermarkApproveSumAll" col-tag="cancelWatermarkApprove" field-name="cancelWatermarkApproveSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="cancelWatermarkRejectSumAll" col-tag="cancelWatermarkReject" field-name="cancelWatermarkRejectSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="cancelWatermarkAutoApproveSumAll" col-tag="cancelWatermarkAutoApprove" field-name="cancelWatermarkAutoApproveSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="sensitiveFileOutSendSumAll" col-tag="sensitiveFileOutSendTotal" field-name="sensitiveFileOutSendSumAll" menu-code="G" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="trwfeLog" col-name="sensitiveFileOutSendApproveSumAll" col-tag="sensitiveFileOutSendApprove" field-name="sensitiveFileOutSendApproveSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="sensitiveFileOutSendRejectSumAll" col-tag="sensitiveFileOutSendReject" field-name="sensitiveFileOutSendRejectSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="sensitiveFileOutSendAutoApproveSumAll" col-tag="sensitiveFileOutSendAutoApprove" field-name="sensitiveFileOutSendAutoApproveSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="behaviorControlSumAll" col-tag="behaviorControlTotal" field-name="behaviorControlSumAll" menu-code="G" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="trwfeLog" col-name="behaviorControlApproveSumAll" col-tag="behaviorControlApprove" field-name="behaviorControlApproveSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="behaviorControlRejectSumAll" col-tag="behaviorControlReject" field-name="behaviorControlRejectSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="behaviorControlAutoApproveSumAll" col-tag="behaviorControlAutoApprove" field-name="behaviorControlAutoApproveSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="fileRelieveJurisdictionSumAll" col-tag="readPermissionConv" field-name="fileRelieveJurisdictionSumAll" menu-code="G" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="trwfeLog" col-name="fileRelieveJurisdictionApproveSumAll" col-tag="readPermissionConvApprove" field-name="fileRelieveJurisdictionApproveSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="fileRelieveJurisdictionRejectSumAll" col-tag="readPermissionConvReject" field-name="fileRelieveJurisdictionRejectSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="trwfeLog" col-name="fileRelieveJurisdictionAutoApproveSumAll" col-tag="readPermissionConvAutoApprove" field-name="fileRelieveJurisdictionAutoApproveSumAll" menu-code="G" data-type="0" type="7" sort-field="0"/>

    <tableTemp tag="mailLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="D" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="mailLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="D" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="mailLog" col-name="mailNumSumAll" col-tag="mailNumTotal" field-name="mailNumSumAll" menu-code="D" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="mailLog" col-name="mailSendNumSumAll" col-tag="mailSendTotal" field-name="mailSendNumSumAll" menu-code="D" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="mailLog" col-name="mailReceiveNumSumAll" col-tag="mailReceiveTotal" field-name="mailReceiveNumSumAll" menu-code="D" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="mailLog" col-name="mailSizeSumAll" col-tag="fileSizeTotal" field-name="fileSizeSumAll" menu-code="D" data-type="2" type="7" sort-field="0"/>
    <tableTemp tag="mailLog" col-name="attachMailCountSumAll" col-tag="attachMailCount" field-name="attachMailCountSumAll" menu-code="D" data-type="0" type="7" sort-field="0"/>

    <tableTemp tag="netFlowLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="D" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="netFlowLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="D" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="netFlowLog" col-name="totalFlow" col-tag="totalFlow" field-name="totalFlowNumSumAll" menu-code="D" data-type="2" type="7" sort-field="1"/>
    <tableTemp tag="netFlowLog" col-name="receivingFlow" col-tag="receiveFlow" field-name="receiveFlowNumSumAll" menu-code="D" data-type="2" type="7" sort-field="1"/>
    <tableTemp tag="netFlowLog" col-name="sendingFlow" col-tag="sendFlow" field-name="sendFlowNumSumAll" menu-code="D" data-type="2" type="7" sort-field="1"/>

    <tableTemp tag="appUninstall" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="B" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="appUninstall" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="B" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="appUninstall" col-name="fileNameNumSumAll" col-tag="appUninstallTotal" field-name="fileNameNumSumAll" menu-code="B" data-type="0" type="7" sort-field="1"/>

    <tableTemp tag="fileOpLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="C" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="fileOpLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="C" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="fileOpLog" col-name="srcFileNumSumAll" col-tag="srcFileNum" field-name="srcFileNumSumAll" menu-code="C" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="fileOpLog" col-name="create" col-tag="create" field-name="category_1" menu-code="C" data-type="0" type="3" sort-field="0"/>
    <tableTemp tag="fileOpLog" col-name="rename" col-tag="rename" field-name="category_2" menu-code="C" data-type="0" type="3" sort-field="0"/>
    <tableTemp tag="fileOpLog" col-name="delete" col-tag="delete" field-name="category_3" menu-code="C" data-type="0" type="3" sort-field="0"/>
    <tableTemp tag="fileOpLog" col-name="copy" col-tag="copy" field-name="category_4" menu-code="C" data-type="0" type="3" sort-field="0"/>
    <tableTemp tag="fileOpLog" col-name="open" col-tag="open" field-name="category_17" menu-code="C" data-type="0" type="3" sort-field="0"/>
    <tableTemp tag="fileOpLog" col-name="pages.edit" col-tag="edit" field-name="category_18" menu-code="C" data-type="0" type="3" sort-field="0"/>
    <tableTemp tag="fileOpLog" col-name="srcFileSizeSumAll" col-tag="srcFileSizeTotal" field-name="srcFileSizeSumAll" menu-code="C" data-type="2" type="4" sort-field="0"/>

    <tableTemp tag="mobileStorage" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="C" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="mobileStorage" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="C" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="mobileStorage" col-name="deviceInsertTimesSumAll" col-tag="deviceInsertTimes" field-name="deviceInsertTimesSumAll" menu-code="C" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="mobileStorage" col-name="opFileNumSumAll" col-tag="opFileTimes" field-name="fileNumSumAll" menu-code="C" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="mobileStorage" col-name="opFileSizeSumAll" col-tag="opFileSize" field-name="fileSizeSumAll" menu-code="C" data-type="2" type="7" sort-field="0"/>
    <tableTemp tag="mobileStorage" col-name="createFileNumSumAll" col-tag="createFileTotal" field-name="createFileNumSumAll" menu-code="C" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="mobileStorage" col-name="createFileSizeSumAll" col-tag="createFileSize" field-name="createFileSizeSumAll" menu-code="C" data-type="2" type="7" sort-field="0"/>
    <tableTemp tag="mobileStorage" col-name="copyFileNumSumAll" col-tag="copyFileTotal" field-name="copyFileNumSumAll" menu-code="C" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="mobileStorage" col-name="copyFileSizeSumAll" col-tag="copyFileSize" field-name="copyFileSizeSumAll" menu-code="C" data-type="2" type="7" sort-field="0"/>
    <tableTemp tag="mobileStorage" col-name="renameFileNumSumAll" col-tag="renameFileTotal" field-name="renameFileNumSumAll" menu-code="C" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="mobileStorage" col-name="renameFileSizeSumAll" col-tag="renameFileSize" field-name="renameFileSizeSumAll" menu-code="C" data-type="2" type="7" sort-field="0"/>
    <tableTemp tag="mobileStorage" col-name="deleteFileNumSumAll" col-tag="deleteFileTotal" field-name="deleteFileNumSumAll" menu-code="C" data-type="0" type="7" sort-field="0"/>
    <tableTemp tag="mobileStorage" col-name="deleteFileSizeSumAll" col-tag="deleteFileSize" field-name="deleteFileSizeSumAll" menu-code="C" data-type="2" type="7" sort-field="0"/>
    <tableTemp tag="mobileStorage" col-name="openFileNumSumAll" col-tag="openFileTotal" field-name="openFileNumSumAll" menu-code="C" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="mobileStorage" col-name="openFileSizeSumAll" col-tag="openFileSize" field-name="openFileSizeSumAll" menu-code="C" data-type="2" type="7" sort-field="0"/>
    <tableTemp tag="mobileStorage" col-name="updateFileNumSumAll" col-tag="updateFileTotal" field-name="updateFileNumSumAll" menu-code="C" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="mobileStorage" col-name="updateFileSizeSumAll" col-tag="updateFileSize" field-name="updateFileSizeSumAll" menu-code="C" data-type="2" type="7" sort-field="0"/>

    <tableTemp tag="systemLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="C" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="systemLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="C" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="systemLog" col-name="logNumSumAll" col-tag="logTotal" field-name="logNumSumAll" menu-code="C" data-type="0" type="7" sort-field="1"/>

    <tableTemp tag="appRunTime" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="C" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="appRunTime" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="C" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="appRunTime" col-name="totalUptime" col-tag="durationOfStartup" field-name="totalUptime" menu-code="C" data-type="1" type="7" sort-field="1"/>
    <tableTemp tag="appRunTime" col-name="activeUptime" col-tag="durationOfActivity" field-name="activeUptime" menu-code="C" data-type="1" type="7" sort-field="1"/>
    <tableTemp tag="appRunTime" col-name="runTimeSumAll" col-tag="appRunningTime" field-name="runTimeSumAll" menu-code="C" data-type="1" type="7" sort-field="1"/>

    <tableTemp tag="mtpFileLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="C" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="mtpFileLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="C" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="mtpFileLog" col-name="mtpOutgoingFileCountSum" col-tag="outgoingFileTotal" field-name="mtpOutgoingFileCountSum" menu-code="C" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="mtpFileLog" col-name="mtpOutgoingFileSizeSum" col-tag="outgoingFileSize" field-name="mtpOutgoingFileSizeSum" menu-code="C" data-type="2" type="7" sort-field="0"/>

    <tableTemp tag="usbMonitoring" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="C" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="usbMonitoring" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="C" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="usbMonitoring" col-name="usbOutgoingFileCountSum" col-tag="outgoingFileTotal" field-name="usbOutgoingFileCountSum" menu-code="C" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="usbMonitoring" col-name="usbOutgoingFileSizeSum" col-tag="outgoingFileSize" field-name="usbOutgoingFileSizeSum" menu-code="C" data-type="2" type="7" sort-field="0"/>

    <tableTemp tag="btPairLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="C" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="btPairLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="C" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="btPairLog" col-name="bluetoothPairingRecordCountSum" col-tag="BTPairingTotal" field-name="bluetoothPairingRecordCountSum" menu-code="C" data-type="0" type="7" sort-field="1"/>

    <tableTemp tag="btFileLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="C" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="btFileLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="C" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="btFileLog" col-name="bluetoothFileTransferCountSum" col-tag="btFileTransferTotal" field-name="bluetoothFileTransferCountSum" menu-code="C" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="btFileLog" col-name="bluetoothFileTransferSizeSum" col-tag="btFileTransferSize" field-name="bluetoothFileTransferSizeSum" menu-code="C" data-type="2" type="7" sort-field="0"/>

    <tableTemp tag="telnetMonitorLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="D" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="telnetMonitorLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="D" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="telnetMonitorLog" col-name="cmdDataNumSumAll" col-tag="cmdLineTotal" field-name="cmdDataNumSumAll" menu-code="D" data-type="0" type="7" sort-field="1"/>

    <tableTemp tag="netDiskLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="D" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="netDiskLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="D" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="netDiskLog" col-name="fileNum" col-tag="fileTotal" field-name="fileNumSumAll" menu-code="D" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="netDiskLog" col-name="fileSizeSumAll" col-tag="fileSizeTotal" field-name="fileSizeKbSumAll" menu-code="D" data-type="2" type="7" sort-field="0"/>

    <tableTemp tag="forumDataLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="D" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="forumDataLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="D" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="forumDataLog" col-name="postCountSum" col-tag="postTotal" field-name="postCountSum" menu-code="D" data-type="0" type="7" sort-field="1"/>

    <tableTemp tag="ftpMonitorLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="D" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="ftpMonitorLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="D" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="ftpMonitorLog" col-name="ftpFileTransferCountSum" col-tag="ftpFileTransferTotal" field-name="ftpFileTransferCountSum" menu-code="D" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="ftpMonitorLog" col-name="ftpFileTransferSizeSum" col-tag="ftpFileTransferSize" field-name="ftpFileTransferSizeSum" menu-code="D" data-type="2" type="7" sort-field="0"/>

    <tableTemp tag="wifiAlarmLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="D" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="wifiAlarmLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="D" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="wifiAlarmLog" col-name="wifiConnRecordCountSum" col-tag="wifiConnTotal" field-name="wifiConnRecordCountSum" menu-code="D" data-type="0" type="7" sort-field="1"/>

    <tableTemp tag="localFileShareLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="D" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="localFileShareLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="D" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="localFileShareLog" col-name="localFileShareCountSum" col-tag="localFileShareTotal" field-name="localFileShareCountSum" menu-code="D" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="localFileShareLog" col-name="localFileShareSizeSum" col-tag="localFileShareSize" field-name="localFileShareSizeSum" menu-code="D" data-type="2" type="7" sort-field="0"/>

    <tableTemp tag="netFileShareRecord" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="D" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="netFileShareRecord" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="D" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="netFileShareRecord" col-name="netFileShareCountSum" col-tag="netFileShareTotal" field-name="netFileShareCountSum" menu-code="D" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="netFileShareRecord" col-name="netFileShareSizeSum" col-tag="netFileShareSize" field-name="netFileShareSizeSum" menu-code="D" data-type="2" type="7" sort-field="0"/>

    <tableTemp tag="SecLevelTransLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="E" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="SecLevelTransLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="E" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="SecLevelTransLog" col-name="securityLevelConvertCountSum" col-tag="securityLevelConvertTotal" field-name="securityLevelConvertCountSum" menu-code="E" data-type="0" type="7" sort-field="1"/>

    <tableTemp tag="integrateLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="H" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="integrateLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="H" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="integrateLog" col-name="emailNumSumAll" col-tag="emailTotal" field-name="emailNumSumAll" menu-code="H" data-type="0" type="3" sort-field="1"/>
    <tableTemp tag="integrateLog" col-name="emailFileSizeSumAll" col-tag="emailFileSize" field-name="emailFileSizeSumAll" menu-code="H" data-type="2" type="3" sort-field="0"/>
    <tableTemp tag="integrateLog" col-name="printedCopiesSumAll" col-tag="printedCopiesTotal" field-name="printedCopiesSumAll" menu-code="H" data-type="0" type="3" sort-field="1"/>
    <tableTemp tag="integrateLog" col-name="printedPagesSumAll" col-tag="printedPagesTotal" field-name="printedPagesSumAll" menu-code="H" data-type="0" type="3" sort-field="0"/>
    <tableTemp tag="integrateLog" col-name="mobileStorageFileNumSumAll" col-tag="mobileStorageFileTotal" field-name="mobileStorageFileNumSumAll" menu-code="H" data-type="0" type="3" sort-field="1"/>
    <tableTemp tag="integrateLog" col-name="mobileStorageFileSizeSumAll" col-tag="mobileStorageFileSize" field-name="mobileStorageFileSizeSumAll" menu-code="H" data-type="2" type="3" sort-field="0"/>
    <tableTemp tag="integrateLog" col-name="outFileNumSumAll" col-tag="outFileTotal" field-name="outFileNumSumAll" menu-code="H" data-type="0" type="3" sort-field="1"/>
    <tableTemp tag="integrateLog" col-name="outFileSizeSumAll" col-tag="outFileSize" field-name="outFileSizeSumAll" menu-code="H" data-type="2" type="3" sort-field="0"/>
    <tableTemp tag="integrateLog" col-name="browserUploadFileNumSumAll" col-tag="browserUploadFileTotal" field-name="browserUploadFileNumSumAll" menu-code="H" data-type="0" type="3" sort-field="1"/>
    <tableTemp tag="integrateLog" col-name="browserUploadFileSizeSumAll" col-tag="browserUploadFileSize" field-name="browserUploadFileSizeSumAll" menu-code="H" data-type="2" type="3" sort-field="0"/>

    <tableTemp tag="adbMonitorLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="C" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="adbMonitorLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="C" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="adbMonitorLog" col-name="outgoingFileCountSum" col-tag="outgoingFileTotal" field-name="outgoingFileCountSum" menu-code="C" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="adbMonitorLog" col-name="outgoingFileSizeSum" col-tag="outgoingFileSize" field-name="outgoingFileSizeSum" menu-code="C" data-type="2" type="7" sort-field="0"/>

    <tableTemp tag="computerEnergySaving" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="C" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="computerEnergySaving" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="C" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="computerEnergySaving" col-name="computerEnergySavingCountSum" col-tag="computerEnergySavingTotal" field-name="computerEnergySavingCountSum" menu-code="C" data-type="0" type="7" sort-field="1"/>

    <tableTemp tag="cdBurnLog" col-name="dept" col-tag="countObject" field-name="xAxisLabel" menu-code="C" data-type="0" type="1" sort-field="0"/>
    <tableTemp tag="cdBurnLog" col-name="Period" col-tag="Period" field-name="xAxisLabel" menu-code="C" data-type="0" type="2" sort-field="0"/>
    <tableTemp tag="cdBurnLog" col-name="cdBurnRecordCountSum" col-tag="cdBurnRecordTotal" field-name="cdBurnRecordCountSum" menu-code="C" data-type="0" type="7" sort-field="1"/>
    <tableTemp tag="cdBurnLog" col-name="cdBurnFileSizeSum" col-tag="cdBurnFileSize" field-name="cdBurnFileSizeKbSum" menu-code="C" data-type="2" type="7" sort-field="0"/>

    <tableTemp tag="riskReport" col-name="slightSumAll" col-tag="slightTotal" field-name="riskLevel1Sum" menu-code="F" data-type="0" type="4" sort-field="0"/>
    <tableTemp tag="riskReport" col-name="generalSumAll" col-tag="generalTotal" field-name="riskLevel2Sum" menu-code="F" data-type="0" type="4" sort-field="0"/>
    <tableTemp tag="riskReport" col-name="seriousSumAll" col-tag="seriousTotal" field-name="riskLevel3Sum" menu-code="F" data-type="0" type="4" sort-field="0"/>
    <tableTemp tag="riskReport" col-name="esSeriousSumAll" col-tag="esSeriousTotal" field-name="riskLevel4Sum" menu-code="F" data-type="0" type="4" sort-field="0"/>
</root>
