#版本号，控制台通过对比版本号，判断此文件是否发生变更，如果没有变更，则不会执行更新，格式yyyyMMddHHmm
version: 20250729001100
enc:
  denseInfo:
    public: public
    internal: internal
    secret: secret
    confidential: confidential
    topSecret: top secret
alarm:
  param:
    FilePath: file path
    Add: Add to
    AppVersion: App Version
    AssetType: Asset Type
    Count: Count
    DetailInfo: Detail Info
    DeviceInfo: Device Info
    DeviceName: Device Name
    DiskFreeSpace: Disk Free Spac
    DiskName: Disk Name
    FileName: File Name
    Function: Function
    Hour: Hour
    Interval: Interval
    KeyWord: Interval
    LogType: Log Type
    Number: Number'
    OffLineNum: Off Line Numbe
    Optype: Operate Type
    ProcessName: Process Name
    RemoteIP: Remote IP
    RemotePort: Remote Port
    RuleName: Rule Name
    Type: Type'
    VersionRange: Version Range
    WinTitle: Windows Title
    alarmData: alarm Data
    attachment: attachment
    capacity: capacity
    descript: descript
    emailAddr: Email Address
    mailReceiver: Email Receiver
    mailSender: Email Sender
    oldName: old name
    newName: new name
    strategyName: strategy Name
    sub: sub
    usbcode: usb code
    vendor: vendor
    IType: Indicates an interface
    IKey: Indicates the violation type
    IDeviceType: Indicates the device type
    IDeviceKey: Indicates a device
    wifiSsid: WiFi name
    wifiBssid: WiFi mac address
    period: Period
    date: Date
    signName: Sign name
    normalCount: Normal times
    importantCount: important times
    seriousCount: Serious times
    timeDimension: Time dimension
    level: Level
    Address: Address
    CMDData: Command line data
    devType: Server type
    devName: Server name
    diskPartition: Disk partition
    diskUsage: Disk allowance
    diskUsageRate: Disk usage ratio
    threshold: Threshold
    cpuUsage: CPU usage
    memoryUsage: Memory usage
    Tip: Tips
    mstscAddress: Remote connection address
    outFilePath: Path of the outgoing file
    ServerName: Server name
    DeviceInstanceId: Instance path
    LabelName: Tag content
    LabelLevel: Tag grade
    UpFlow: Up flow
    DownFlow: Download flow
    TotalFlow: Total flow
    SoftwareName: Software Name
    AIName: AI Name
  type:
    appBlock: Application access limit
    appVer: Software version limit
    asset: Asset change
    bluetooth: Bluetooth file control
    hardAsset: Hardware asset change alarm
    install: Software installation/uninstallation limit
    netIsolation: Network isolation settings
    print: Printing permission control
    softAsset: Software asset change alarm
    softInstalled: Software must be installed
    softLimit: Software blacklist and whitelist
    softRunning: Software must be running
    sstBatchEnc: Batch encryption and decryption
    sstBluetooth: Send file by bluetooth
    sstBurn: Burn file to disc
    sstCloud: Cloud transmission
    sstCopy: Copy file to USB
    sstCut: Cut file to USB
    sstDrip: Sporadic detection
    sstFtp: Upload file to FTP
    sstImFile: Send IM file
    sstIm: IM message
    sstImDownload: Download IM file
    sstLocShare: Local shared file
    sstMailFile: Email attachment content
    sstMail: Email transmission content
    sstNetDiskDownload: Download network disk file
    sstNetDiskUpload: Upload network disk file
    sstNetShare: Network shared file
    sstPost: Forum posting content
    sstPrint: File printing content
    sstSaveAs: Save as file to USB
    sstScan: Full disk scan
    sstSmartEnc: Smart encryption
    sstTelnet: Telnet transmission
    sstWebFile: Upload file to the webpage
    sstWebPaste: Paste content to the webpage
    sstWebPrint: Webpage printing
    sstWeb: Access webpage text content
    sstMTP: Send file by MTP
    store: Storage device usage control
    winTitle: Window title limit
    workMode: Working mode switching
    sysDiskAlarmConfig: System disk usage
    magneticDiskAlarmConfig: Total disk usage
    cpuAlarmConfig: CPU occupied
    memoryAlarmConfig: Memory usage
    ipAlarmConfig: IP address change
    macAlarmConfig: MAC address change
    computerNameAlarmConfig: Computer name change
    wifiBlock: WiFi connection limit
    ftpTool: FTP tool transmission
    hardDriveTime: Hard drive used time
    hardDriveTimes: Hard disk usage
    mailReceiverWhiteList: Email whitelist violation alarm
    mailSenderWhiteList: Email whitelist violation alarm
    offline: Offline screen lock alarm
    emailContent: Email Content Limit
    ADBController: ADB control
    sstABDUpload: Send file by ADB
    diskBackupSpace: Backup disk space alarm
    emailAttachFile: Email attachment limit
    netShareFile: Network shared file outgoing alarm
    USBSendOutFile: USB file outgoing alarm
    burnCD: Burner usage control
    webUpload: Webpage upload file control
    chatSendFile: Outgoing control of chat files
    MTP: MTP control
    appVerLimit: Application version limit
    formatStore: Illegal formatting of USB
    usbInterface: USB interface Control
    netInterface: Network Interface Control
    batchDecLimit: Decryption quantity limit
    signReportPush: Symptom alarm push
    termScan: Terminal security detection
    sstBurnDownload: Download CD file
    telnetControl: Telnet control
    serverDataDiskUsage: Data Disk Space Insufficient Alert
    serverAnyDiskUsage: System Disk Space Insufficient Alert
    serverProgramDiskUsage: Program Disk Space Insufficient Alert
    serverCpuUsage: Server CPU usage alarm
    serverMemoryUsage: Server memory usage alarm
    remoteDesktop: Remote desktop control
    sstRemoteDesktop: Send file by Remote Desktop
    httpWhiteList: Server whitelist
    sstWebDownloadFile: Download webpage file
    otherDeviceLimit: Device usage control
    softBlack: High risk software restrictions
    label: Outgoing tag permission control
    labelUsbCopy: Copy file to USB
    labelUsbCut: Cut file to USB
    labelNetShare: Network shared file
    labelImFile: Send IM file
    labelBurn: Burn file to disc
    labelWebFile: Upload file to the webpage
    labelMailFile: Email attachment content
    labelBluetooth: Send file by bluetooth
    labelMTP: Send file by MTP
    labelRemoteDesktop: Send file by Remote Desktop
    labelFTP: Upload file to FTP
    numFTP: FTP File Upload
    numUsbCopy: USB File Copy
    numUsbCut: USB File Cut
    numNetShare: Remote File Sharing
    numImFile: Instant Messaging File Send
    numBurn: CD/DVD File Burning
    numWebFile: Web File Upload
    numMailFile: Email Attachment Content
    numBluetooth: Bluetooth File Transfer
    numABDUpload: ADB File Upload
    numMTP: MTP File Transfer
    numNetDiskUpload: Cloud Disk File Upload
    numRemoteDesktop: Remote Desktop File Transfer
    flowThresholdLimit: Flow Threshold Limit
    remoteUploadTool: Remote Tool File Upload
    sstRemoteUploadTool: Remote Tool File Upload
    AISendFile: AI File Upload Control
    sstAISendMsg: AI Text Content Transmission
    sstAISendFile: AI File Upload
    softWareCharge: Software copyright control and management
    AISendFileMsg: AI Content Sending Limitation
    AIModel: AI Model Limitation
  template:
    print: 'You trigger a print violation event, Print file path: {{FilePath}}, Print process name: {{ProcessName}}'
    install: 'Your {{Type}} event is blocked, {{Type}} package path: {{FilePath}}'
    winTitle: 'You have opened an illegal window title "{{WinTitle}}" containing the Keyword "{{KeyWord}}"'
    appVer: '{{FilePath}}, {{Type}}'
    appBlock: '{{FilePath}}, application limit'
    store: 'Violation of used: {{DeviceName}}, Device coding: {{usbcode}}, Brand: {{vendor}}, Capacity: {{capacity}}'
    bluetooth: 'Violation of file transfer through Bluetooth, File path: {{FilePath}}'
    netIsolation: 'Violation of access to the network, Network address: {{RemoteIP}}: {{RemotePort}}'
    sstMail: 'The content of the email sent involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}'
    sstCopy: 'The content copied to the mobile storage device involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}, File path: {{FilePath}}'
    sstCut: 'The content cut to the mobile storage device involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}, File path: {{FilePath}}'
    sstSaveAs: 'The content saved to the mobile storage device involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}, File path: {{FilePath}}'
    sstPrint: 'The content of the printed file involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}, File path: {{FilePath}}'
    sstNetShare: 'Accessing or modifying network share involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}, File path: {{FilePath}}'
    sstIm: 'The IM involves violation. The details are as follows: Strategy name: {{strategyName}} Rule name: {{RuleName}}'
    sstImFile: 'The IM file to be sent involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}, File path: {{FilePath}}'
    sstImDownload: 'The IM file to be download involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}, File path: {{FilePath}}'
    sstBurn: 'The file burned from the CD involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}, File path: {{FilePath}}'
    sstWebFile: 'The file sent to the webpage involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}, File path: {{FilePath}}'
    sstWebPaste: 'The content pasted to the webpage involves violation. The details of the violation are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}'
    sstLocShare: 'Operations on local shared file involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}, File path: {{FilePath}}'
    sstMailFile: 'The email attachment to be sent involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}, File path: {{FilePath}}'
    sstBluetooth: 'The file transmitted by Bluetooth involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}, File path: {{FilePath}}'
    sstDrip: 'Sporadic detection involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}'
    sstPost: 'The content of posts involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}'
    sstWeb: 'The webpage information browsed involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}'
    asset: '{{AssetType}} asset information {{LogType}}, details are as follows: {{DetailInfo}}'
    workMode: 'Stop running process during work mode switch: {{ProcessName}}'
    sstNetDiskUpload: 'The content of file upload to network disk involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}, File path: {{FilePath}}'
    sstNetDiskDownload: 'The content of the file downloaded from web disk involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}, File path: {{FilePath}}'
    sstSysDiskUsed: 'The usage of system disk is {{Function}}% which exceeds the threshold of {{number}}% '
    sstMagneticDiskUsed: 'The usage of all disks is {{Function}}% which exceeds the threshold of {{number}}%'
    sstCpuUsed: 'The usage of CPU is {{Function}}% which exceeds the threshold of {{number}}%'
    sstMemoryUsed: 'The usage of memory is {{Function}}% which exceeds the threshold of {{number}}%'
    sstIpChanged: 'The IP address is changed. The missing IP address is {{sub}}, and the new IP address is {{Add}}.'
    sstMacChanged: 'The MAC is changed. The missing MAC is {{sub}}, and the new MAC is {{Add}}.'
    sstComputerNameChanged: 'Computer name has been modified, Original name: {{oldName}}, Now name: {{newName}}'
    softLimit: 'You have opened an unauthorized software {{FilePath}} {{descript}}'
    softInstalled: '{{FilePath}} must be installed'
    softRunning: '{{FilePath}} must be running '
    softAsset: '{{AssetType}} Asset information {{LogType}}, details are as follows: {{DetailInfo}}'
    hardAsset: '{{AssetType}} Asset information {{LogType}}, details are as follows: {{DetailInfo}}'
    formatStore: 'Illegal use special USB disk: {{DeviceName}}, Serial number: {{usbcode}}, Brand: {{vendor}}, Capacity: {{capacity}}'
    wifiBlock: 'You illegally connected to WiFi: {{wifiSsid}}, MAC address: {{wifiBssid}}'
    sstFtp: 'The content of FTP upload file involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}, File path: {{FilePath}}'
    ftpTool: 'You illegally use FTP tools: {{ProcessName}}, {{Optype}} file, File path: {{FilePath}}'
    hardDriveTime: 'Your hard disk {{DiskName}} has been used for {{Hour}} hours and has exceeded the set threshold of ({{Number}}) hours'
    hardDriveTimes: 'Your hard disk {{DiskName}} has been used {{Count}} times and has exceeded the set threshold ({{Number}}) times'
    mailReceiverWhiteList: 'The email attachment you sent has been decrypted, The email address for receiving the decrypted attachment: \r\n {{emailAddr}} the attachment name: {{attachment}}'
    mailSenderWhiteList: 'The email attachment you sent has been decrypted, The email address for receiving the decrypted attachment: \r\n {{emailAddr}} the attachment name: {{attachment}}'
    offline: 'Your terminal has been disconnected from the server for {{OffLineNum}} minutes and is offline. The screen will be locked after {{Interval}} minutes. Please connect to the server as soon as possible or contact the administrator.'
    emailContent: 'The message you sent triggered email content limit Strategy with the following violation: \r\n Keyword: {{KeyWord}} Recipient: {{mailReceiver}} Sender: {{mailSender}}'
    ADBController: 'You illegally use the ADB debug tool to transfer files: {{FileName}}'
    sstABDUpload: 'ADB transfer file content involves violation, The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}, File path: {{FilePath}}'
    diskBackupSpace: 'The remaining space of the disk where the current backup directory resides is less than {{DiskFreeSpace}}GB. Clear the disk in time to avoid failure to back up files'
    emailAttachFile: 'You are violating regulations by email to send attachments \r\n Outgoing attachment Name: \r\n {{alarmData}} Recipient: {{mailReceiver}} Sender: {{mailSender}}'
    netShareFile: 'Illegally use of network shared to send files, File path: {{alarmData}}'
    USBSendOutFile: 'Illegally use of USB to send file, File path: {{alarmData}}'
    burnCD: 'You illegally use CD/DVD burner: {{DeviceInfo}}, Burn files: {{FilePath}}'
    webUpload: 'You triggered the event of illegally upload webpage file, Process name: {{ProcessName}}, File path: {{FilePath}}'
    chatSendFile: 'You trigger the event of sending out illegal chat files. Process name: {{ProcessName}}, File path: {{FilePath}}'
    sstMTP: 'The file Outgoing by MTP involves violation. The details are as follows: Strategy name: {{strategyName}}, Rule name: {{RuleName}}, File path: {{FilePath}}'
    MTP: 'Illegally sent file by MTP, File path: {{FilePath}}'
    appVerLimit: '{{FilePath}}, applications whose product version is <{{AppVersion}}> are not supported. \r\n limits the following versions: {{VersionRange}}'
    usbInterface: 'Your USB interface {{InterfaceKey}} is illegally connected to {{InterfaceDeviceType}} device {{InterfaceDeviceKey}}'
    netInterface: 'Your network interface {{InterfaceKey}} is illegally connected to {{InterfaceDeviceType}} device {{InterfaceDeviceKey}}'
    batchDecLimit: 'You have reached the maximum number of decrypted files per {{period}}'
    signReportPush: '{{date}} {{timeDimension}} {{signName}}Symptoms:<br> Number of serious violations: {{seriousCount}} times, <br> Number of important violations: {{importantCount}} times, <br> > Number of general violation levels: {{normalCount}} times'
    termScan: 'The risk level of the terminal security detection result is {{level}}, {{SucNumber}} items are successfully scanned, {{ErrNumber}} items fail'
    sstBurnDownload: 'Download CD file involves a violation. The details of the violation are as follows: Strategy Name: {{strategyName}}; Rule Name: {{RuleName}}; File path: {{FilePath}}'
    telnetControl: 'You are illegally using the telnet communication tool, process name :{{ProcessName}}, server Address :{{Address}} Command :{{CMDData}}'
    serverDataDiskUsage: '{{devType}} [{{devId}}] Drive letter ({{disk}}) Remaining available space is {{value}}, lower than the threshold {{threshold}} GB'
    serverAnyDiskUsage: '{{devType}} [{{devId}}] Drive letter ({{disk}}) Remaining available space is {{value}}, lower than the threshold {{threshold}} GB'
    serverProgramDiskUsage: '{{devType}} [{{devId}}] Drive letter ({{disk}}) Remaining available space is {{value}}, lower than the threshold {{threshold}} GB'
    serverCpuUsage: '{{devType}} [{{devId}}] The CPU usage is {{value}}%, which exceeds the set threshold {{threshold}}%'
    serverMemoryUsage: '{{devType}} [{{devId}}] Memory usage is {{value}}%, which exceeds the specified threshold {{threshold}}%'
    remoteDesktop: 'You illegally use Remote Desktop Connection tool,remote connection address:{{address}},file path:{{filePath}}'
    sstRemoteDesktop: 'The file sent by the remote desktop involves a violation. The violation details are as follows: Policy name: {{strategyName}}; Rule name: {{RuleName}}; File path: {{FilePath}}'
    processHttpWhiteList: '{{ProcessName}} Access to whitelisted website ({{ServerName}}) failed! Cause: If a non-special process accesses a whitelisted website, the process can be reported to the administrator.'
    strategyHttpWhiteList: '{{ProcessName}} Failed to access the whitelist website ({{ServerName}})! Cause: same browser opens whitelisted websites with different encryption policies'
    otherDeviceLimit: 'Your device is disabled. Device name: {{DeviceName}}, device instance path: {{DeviceInstanceId}}'
    sstWebDownloadFile: 'The file downloaded from web page to the local computer violates the rules. The rules are as follows: Policy name: {{strategyName}}; Rule name: {{RuleName}}; File path: {{FilePath}}'
    softBlack: 'You opened an illegal program {{FilePath}}'
    label: 'The current outgoing file: {{FilePath}} indicates a violation'
    labelUsbCopy: 'The content copied to the mobile storage device is in violation. The violation details are as follows: The tag content {{LabelName}} is in violation or the document level is higher than the specified level {{LabelLevel}}; File path: {{FilePath}}'
    labelUsbCut: 'The content cut to the mobile storage device is illegal. The details of the violation are as follows: The tag content {{LabelName}} is illegal or the document level is higher than the specified level {{LabelLevel}}; File path: {{FilePath}}'
    labelNetShare: 'A violation occurred when accessing/modifying a network share. The details are as follows: The tag content {{LabelName}} is illegal or the document level is higher than the specified level {{LabelLevel}}; File path: {{FilePath}}'
    labelImFile: 'The IM file sent involves a violation. The violation details are as follows: The tag content {{LabelName}} is illegal or the document level is higher than the specified level {{LabelLevel}}; File path: {{FilePath}}'
    labelBurn: 'The disc is burned with a file that violates the rules as follows: The tag content {{LabelName}} is violated or the "document level" is higher than the specified level {{LabelLevel}}; File path: {{FilePath}}'
    labelWebFile: 'The file sent to the web page involves a violation, the details of the violation are as follows: The tag content {{LabelName}} is illegal or the "document level" is higher than the specified level {{LabelLevel}}; File path: {{FilePath}}'
    labelMailFile: 'Sent email attachment involved violation, violation details are as follows: tag content {{LabelName}} violation or "document level" higher than the specified level {{LabelLevel}}; File path: {{FilePath}}'
    labelBluetooth: 'The file transmitted by Bluetooth involves a violation. The details of the violation are as follows: The tag content {{LabelName}} is illegal or the document level is higher than the specified level {{LabelLevel}}; File path: {{FilePath}}'
    labelMTP: 'The files sent by MTP are illegal. The details are as follows: The tag content {{LabelName}} is illegal or the document level is higher than the specified level {{LabelLevel}}. File path: {{FilePath}}'
    labelRemoteDesktop: 'The file sent by the remote desktop is in violation. The details of the violation are as follows: The tag content {{LabelName}} is in violation or the document level is higher than the specified level {{LabelLevel}}; File path: {{FilePath}}'
    labelFTP: 'The content of the FTP file is illegal. The details of the violation are as follows: The tag content {{LabelName}} is illegal or the document level is higher than the specified level {{LabelLevel}}; File path: {{FilePath}}'
    numFTP: 'The content of the FTP uploaded file involves violations. The details of the violations are as follows: The file size or the cumulative number of files transmitted externally is in violation; File path: {{FilePath}}'
    numUsbCopy: 'The content copied to the mobile storage device involves violations. The details of the violations are as follows: The file size or the cumulative number of files transmitted externally is in violation; File path: {{FilePath}}'
    numUsbCut: 'The content cut to the mobile storage device involves violations. The details of the violations are as follows: The file size or the cumulative number of files transmitted externally is in violation; File path: {{FilePath}}'
    numNetShare: 'The access or modification of network shared files involves violations. The details of the violations are as follows: The file size or the cumulative number of files transmitted externally is in violation; File path: {{FilePath}}'
    numImFile: 'The instant messaging file sent involves violations. The details of the violations are as follows: The file size or the cumulative number of files transmitted externally is in violation; File path: {{FilePath}}'
    numBurn: 'The files burned to the CD involve violations. The details of the violations are as follows: The file size or the cumulative number of files transmitted externally is in violation; File path: {{FilePath}}'
    numWebFile: 'The file sent to the web involves violations. The details of the violations are as follows: The file size or the cumulative number of files transmitted externally is in violation; File path: {{FilePath}}'
    numMailFile: 'The email attachment sent involves violations. The details of the violations are as follows: The file size or the cumulative number of files transmitted externally is in violation; File path: {{FilePath}}'
    numBluetooth: 'The file transferred via Bluetooth involves violations. The details of the violations are as follows: The file size or the cumulative number of files transmitted externally is in violation; File path: {{FilePath}}'
    numABDUpload: 'The content of the file transferred via ADB involves violations. The details of the violations are as follows: The file size or the cumulative number of files transmitted externally is in violation; File path: {{FilePath}}'
    numMTP: 'The file sent via MTP involves violations. The details of the violations are as follows: The file size or the cumulative number of files transmitted externally is in violation; File path: {{FilePath}}'
    numNetDiskUpload: 'The content of the file uploaded to your network disk involves violations. The details of the violations are as follows: The file size or the cumulative number of files transmitted externally is in violation; File path: {{FilePath}}'
    numRemoteDesktop: 'The file sent via remote desktop involves violations. The details of the violations are as follows: The file size or the cumulative number of files transmitted externally is in violation; File path: {{FilePath}}'
    flowThresholdLimit: 'The {{ProcessName}} triggered a traffic threshold speed limit event, where the upload traffic statistics: {{UpFlow}} MB; download traffic statistics: {{DownFlow}} MB; total traffic statistics: {{TotalFlow}} MB.'
    remoteUploadTool: 'You have triggered an event of uploading files via a remote tool in violation. The process name is {{ProcessName}}; the file path is {{FilePath}}.'
    sstRemoteUploadTool: 'The file uploaded via a remote tool involves violations. The details of the violations are as follows: Policy name: {{strategyName}}; Rule name: {{RuleName}}; File path: {{FilePath}}'
    AISendFile: 'You have triggered an event of uploading an AI file that violates the rules. Process name: {{ProcessName}}, File path: {{FilePath}}'
    sstAISendMsg: 'The AI text content sent involves a violation. The details of the violation are as follows: Policy name: {{strategyName}}; Rule name: {{RuleName}}'
    sstAISendFile: 'The file sent to AI involves a violation. The details of the violation are as follows: Policy name: {{strategyName}}; Rule name: {{RuleName}}; File path: {{FilePath}}'
    softWareCharge: 'You have installed unauthorized software {{SoftwareName}} in violation of regulations. Please contact the administrator for authorization.'
    AISendFileMsg: 'You have triggered the AI content sending restriction policy while interacting with AI. The offending content is as follows:\r\nKeyword: {{KeyWord}}\r\nAI Name: {{AIName}}\r\nProcess Name: {{ProcessName}}'
    AIModel: 'You have violated the rules by using the AI model tool. The details are as follows:\r\nAI Name: {{AIName}}\r\nProcess Name: {{ProcessName}}'
  lossType:
    sstDrip: Sporadic detection
    sstIm: Send IM message
    sstImFile: Send IM file
    sstImReceive: Receive IM message
    sstImDownload: Download IM file
    sstCopy: Copy file to USB
    sstCut: Cut file to USB
    sstSaveAs: Save as file to USB
    sstWeb: Access webpage text content
    sstWebFile: Upload file to the webpage
    sstWebPaste: Paste content to the webpage
    sstPost: Forum posting content
    sstMail: Email transmission text content
    sstMailFile: Email attachment content
    sstPrint: File printing content
    sstLocShare: Local shared file
    sstNetShare: Network shared file
    sstNetDiskUpload: Upload network disk file
    sstNetDiskDownload: Download network disk file
    sstBurn: Burn file to disc
    sstFtp: Upload file to FTP
    sstBluetooth: Send file by bluetooth
    sstABDUpload: Send file by ADB
    sstMTP: Send file by MTP
    sstBurnDownload: Download CD file
    sstRemoteDesktop: Send file by Remote Desktop
    sstWebDownloadFile: Download webpage file
    sstRemoteUploadTool: Remote tool for file upload
    sstAISendMsg: AI for transmitting text content
    sstAISendFile: AI for file upload
  level:
    level0: 'Notification'
    level1: 'Warn'
    level2: 'Secondary'
    level3: 'Importance'
    level4: 'Exigency'
assets:
  computer: Computer
  custom: Custom
  hardware: Hardware
  software: Software
  CPU: CPU
  motherboard: Motherboard
  BIOS: BIOS
  networkAdapter: Network card
  graphicsCard: Graphics card
  memory: Memory
  hardDiskDrive: Hard disk drive
  otherDevice: Other devices
  keyboard: Keyboard
  USBDevice: USB device
  1394ControlCard: 1394 Control Card
  tapeDrive: Tape drive
  diskPartition: Disk partition
  floppyDrive: Floppy drive
  soundCard: Sound card
  bus: Bus
  infraredDevice: Infrared device
  cache: Cache
  display: Display
  modem: Modem
  systemSlot: System slot
  opticalDiskDrive: Optical disk drive
  PCMCIAController: PCMCIA controller
  UPSDevice: UPS devices
  SCSIController: SCSI controller
  serialPortDevice: Serial device
  operatingSystem: Operating system
  usbPort: USB Port
  netCard: NET CARD
  antivirusSoftware: Antivirus
  systemPatch: System patch
  applicationSoftware: Application software
  name: Name
  specifications: Specifications
  numberOfLogicalProcesses: Nnumber of logical processes
  maximumClockFrequency: Maximum clock frequency
  currentClockFrequency: Current clock frequency
  addressWidth: Address width
  dataWidth: Data width
  socketIdentifier: Socket flag
  processorID: Processor ID
  manufacturer: Manufacturer
  serialNumber: Serial number
  version: Version
  productModel: Product model
  releaseDate: Release date
  MACAddress: MAC address
  iPv4Address: IPv4 address
  defaultGatewayIPv4Address: Default gateway IPv4 address
  serviceName: Service name
  DNSServer: DNS server
  localDNSHostName: Local DNS hostname
  subnetMask: Subnet mask
  autoGetIP: Get IP automatically
  iPv6Address: IPv6 address
  defaultGatewayIPv6Address: Default gateway IPv6 address
  driverVersion: Driver version
  modeOfOperation: Working mode
  bitsPerPixel: Bits per pixel
  refreshFrequency: Refresh frequency
  horizontalResolution: Horizontal resolution
  verticalResolution: Vertical resolution
  size: Size
  frequency: Frequency
  slot: Slot
  diskSize: Disk size
  diskType: Disk type
  interfaceType: Interface type
  deviceID: Device ID
  numberOfPartitions: Number of partitions
  totalHeads: Total number of magnetic heads
  totalCylinders: Total number of cylinders
  tracksPerColumn: Number of tracks per column
  sectorsPerTrack: Number of sectors per track
  bytesPerSector: Bytes per sector
  totalTracks: Total number of tracks
  totalSectors: Total number of sectors
  title: Title
  description: Description
  mode: Mode
  usage: Usage
  deviceName: Device name
  keyboardName: Keyboard name
  USBName: USB name
  summary: Summary
  quantity: Quantity
  label: Volume tag
  totalSize: Total size
  freeSize: Free size
  fileSystem: File system
  seriesNumber: Serial number
  dimension: Size
  productionDayDate: Manufacture Date
  deviceMark: Device flag
  media: Media
  driveLetter: Drive letter
  UPSPort: UPS port
  maximumTransferRate: Maximum transfer rate
  type: Type
  systemCatalog: System catalog
  installationTime: Installation time
  lastStartupTime: Last startup time
  servicePatch: Service patch
  registeredUser: Registered user
  organization: Organization
  companyName: Company name
  path: Path
  installationDate: Installation Date
  developer: Developer
  lastUsedTime: Last used time
  active: Active
  computerType: Computer type
stgBaseConfig:
  printerSet: Print permission control
  waterMarkStrategy: Print watermark control
  imToolStrategy: Communication tool whitelist
  imFileStrategy: Communication Tool Control Settings
  appBlockStrategy: Application access limit
  winTitleBlockStrategy: Window title limit
  appVersionLimitStrategy: Application version limit
  installStrategy: Software installation/uninstallation limit -- Installation limit
  uninstallStrategy: Software installation/uninstallation limit -- Uninstallation limit
  specialPathStrategy: Software installation/uninstallation limit -- Special directory release
  urlStrategy: Webpage browsing limit
  webPostStrategy: Webpage sending content limit
  webPortStrategy: Network port limit
  webFlowStrategy: Flow limit
  netCtrlStrategy: Storage device usage control
  emailKeywordBlockStrategy: Email content limit
  burnConfigStrategy: Burner usage control
  videoStrategy: Screen video settings
  dataDisclosureStrategy: Sensitive content strategy
  effectiveFunction: Sensitive outgoing detection configuration
  dataDisclosureStg: Content detection strategy
  dripDataDisclosureStg: Sporadic detection strategy
  appInfoMd5: Controlled application strategy library -- Application limit anti-counterfeiting fingerprint table
  url: Base URL data
  timeInfo: Time period base data
  softProcessMd5: Controlled application strategy library -- Application install and uninstall anti-counterfeiting fingerprint table
  mailTable: Built-in email keyword
  keyTable: Built-in forum web protocol
  osProcess: Built-in system process
  specialProcess: Built-in special process
  screenWaterMarkStrategy: Screen watermark Settings
  fileFilterSetup: File monitoring filtering
  shareConfig: Shared file control
  blueToothConfig: Bluetooth file control
  driverStrategy: Storage device usage limit
  logFilter: Computer windows log filtering
  contentScan: Sensitive content scan strategy
  scanTask: Sensitive content scann task
  fileDistribute: File distribution task
  autoPatchStrategy: Automatic patch strategy
  serverLibrary: Server information library
  processLibrary: Process information library
  mailLibrary: Email information library
  mailWhiteListStgConfig: Email whitelist settings -- System settings
  mailWhiteListStrategy: Email whitelist settings -- Recipient whitelist
  encryptSpecialPath: Special directory settings
  httpWhiteListStrategy: Server whitelist Settings
  browserFileStrategy: Webpage upload file control
  denseInfo: Classification base Settings
  denseSet: Classification icon and parameter Settings
  chatWhiteList: Communication account library list
  sysUser: Administrator information
  clipboardStrategy: Clipboard control
  screenshotStrategy: Screenshot Settings
  otherDeviceLimit: Device usage control
  deviceInfo: Other device limit base data
  workMode: Working mode settings
  processStrategy: Transparent encryption
  specialSuffixStrategy: Special file suffixes
  lanSegment: LAN segment settings base data
  ipAndMacBind: IP/MAC binding
  commonConfig: Installation package limit Base data table -- Common configuration table
  applySecurityAccess: Application security access server
  processStgLibrary: Controlled application strategy library
  processStgChild: Controlled application strategy library -- Child process strategy
  processStgNet: Controlled application strategy library -- Network limit strategy
  processFileMd5: Controlled application strategy library -- Process anti-counterfeiting strategy
  processStgConfig: Controlled application strategy library -- Advanced configuration
  httpWhiteListProcessFilter: Server white list -- Filtering process
  workModeProcessMd5: Process anti-counterfeiting of working mode switching
  processConfigStg: Advanced configuration of transparent encryption
  readPermission: Read permission Settings
  fileGateConfig: Old server whitelist configuration table
  assetProp: Software and hardware asset attribute information table
  assetDB: Information about software and hardware asset collection items
  assetAlarmSetup: Asset change alarm Settings
  processCollectRule: Process fingerprint collection rule
  processCollectRuleStrategy: Application collection
  diskScan: Full disk scan
  smartEncStrategy: Intelligent encryption
  translucentEncStrategy: Semi-transparent encryption
  softWareTaskStrategy: Software uninstall
  pKeyInfo: Enterprise key
  outgoingCodeWhiteList: Outgoing machine code whitelist
  outgoingScreenWaterMark: Outgoing screen watermark
  outgoingPrintWaterMark: Outgoing printing watermark
  outgoingConfig: Outgoing advanced configuration
  terminalConfig: Terminal Settings
  userDense: Operator classification settings
  webSite: Network Verification Address
  stgFileData: File data associated with strategy
  mobileWPSConfig: Mobile terminal WPS configuration
  mobileWPSConfigStrategy: Mobile terminal WPS configuration Strategy
  batchEncOrDec: Batch encryption and decryption
  backUpConfig: Encryption file backup Settings
  terminalMenuStrategy: Terminal menu management
  shortOfflineStrategy: Short-term offline strategy settings
  terminalUpgrade: Terminal online upgrade
  user: Operator information
  groupInfo: Department information
  relGroupUser: Information about operator's department
  sysWebPortStrategy: Built-in port limit strategy
  exceptStrategy: Sensitive content detection configuration -- Exception Settings
  shareStgConfig: Sensitive content detection configuration -- Share management Settings
  usbDevice: USB device library
  unconventionalSensitive: Full disk scan content detection strategy
  violationResponseRule: Violation response rule
  hotkey: Hotkey Settings
  defaultMsgParamSetup: Default parameter of terminal popup
  processMonitor: Application action monitoring
  alarmTypeParam: Alarm source parameters
  alarmTemplate: Alarm message template
  alarmTemplateParam: Alarm message template parameter
  effectiveFuncConfig: Sensitive detection advanced configuration
  netDisk: Network disk transmission backup limit
  sysBaseConfig: Computer Settings
  outgoingTemplate: Direct outgoing template library
  outgoingTemplateStrategy: Direct outgoing template Settings
  outgoingProcess: Software whitelist
  baseFunction: Terminal base function module
  globalConfig: Global configuration distribute table
  violationRespRuleExtConfig: Violation response rule configuration extension table
  appReportDefault: Application runtime time built-in table
  appReportSet: Application running time statistics settings
  alarmBaseStrategy: Base strategy -- Alarm configuration table
  sensitiveBackupConfig: Sensitive content file backup configuration
  EnDeFileScan: Encrypted file monitoring and filtering
  enDeFileDetail: Built-in encrypted file monitoring and filtering
  processInject: Controlled injection Settings
  processInjectDefault: Controlled injection default settings
  operatorConfig: Encryption parameter Settings
  officeWaterMark: Office document watermark settings
  softwareLimitStrategy: Software blacklist and whitelist
  softRequiredInstallStrategy: Software must be installed
  softRequiredRunStrategy: Software must be running
  softwareAssetAlarmSetup: Software asset change alarm Settings
  hardwareAssetAlarmSetup: Hardware asset change alarm Settings
  groupPolicyFunction: Group strategy function information
  groupPolicyStrategy: Group strategy configuration
  appLogConfig: Application usage record Settings
  appLogOption: Application usage monitor advanced configuration
  signatureData: Feature code library
  alarmActionParam: Alarm message template parameter
  sysAlarmConfig: Computer alarm Settings
  sensitiveFileLossType: Permissible outgoing route
  sensitiveFileOutSendConfig: Sensitive files outgoing parameters configuration
  i18nDict: International dictionary
  usbFileConfig: USB outgoing file Settings
  wifiBlockStrategy: WiFi connection limit
  role: Administrator role
  competitorInfo: Competitive product information
  seamlessReplace: Compatible with other product Settings
  ftpControllerConfig: FTP file management
  forumURLFilter: Forum posts URL filtering
  webBrowseURLFilter: Webpage browsing URL filtering strategy
  mailCarbonCopyStrategy: Email CC
  mailSenderStrategy: Email sender whitelist
  offlineLockScreen: Offline lock screen
  personalizePolicy: Computer personalization
  blindWatermark: Image blind watermark
  documentTrack: Document tracking
  iconRefreshDir: Encryption icon assisted refresh settings
  lossTypeParam: Sensitive disclosure type
  documentTrackGlobal: Document tracking -- Advanced configuration
  adbFileSuffixLibrary: ADB file suffix library
  adbLimitStrategy: ADB management
  sysUserPermission: Administrator permissions
  qrLoginStrategy: Terminal scanning login strategy
  emailAttachFileStrategy: Email attachment limit
  mobileOpenSuffixStrategy: Mobile suffixes configuration
  diskScanSelfCheck: Sensitive file self-check strategy
  diskScanSelfCheckSensitive: Sensitive file self-checking content detection strategy
  userExtInfo: Operator extended data
  enterpriseMailConfig: Email whitelist Settings -- SSL enterprise email Settings
  mobileTerminalUpgradeStrategy: Mobile terminal to upgrade
  mtpConfig: MTP control
  wifiCollectStrategy: WiFi information collection
  backupRule: Backup filtering rule
  directOutgingWhiteList: Direct outgoing application whitelist
  computerEnergySaving: Computer energy saving settings
  appOpenSuffixStrategy: Mobile Terminal Document Reading Settings
  webpagePasteAudit: Web Paste URL Filtering
  softwareDownloadStrategy: Software Publish
  webpageBrowseAudit: Web Browsing URL Filtering
  usbInterfaceLimitStrategy: USB Interface Control
  netInterfaceLimitStrategy: Network Interface Controls
  termSecurityDetectionStrategy: Terminal security detection
  patchCheckStrategy: Patch strategy - Parameter settings
  patchInstallationStrategy: Patch strategy - Installation strategy
  mstscControl: Remote Desktop Control
  browserFileDownloadStrategy: Download webpage file Control
  forumPostingRecordLimit: Forum Posting Record Limit
  emailRecordLimit: Email Record Limit
  telnetCommControl: Telnet Communication Control
  patchAutoInstallStrategy: Patch strategy - Auto installation strategy
  decToolLimitStg: High risk software restrictions strategy
  decToolLimitInfo: High risk software library
  timelyBackupStg: Timely backup
  fullDiskScanBackupStg: Full disk scan backup
  serverBackupConfigStg: Server storage config
  manualLabelStrategy: Manually Tagging
  landLabelingStrategy: Landing Tagging
  labelPermissionControlStrategy: Tagged Document Outbound Control
  softwareBlacklist: Software blacklist program
  autoLabelingStrategy: Tag Base Library
officeWaterMark:
  officeMarkWay1: Download file
  officeMarkWay2: Opens the document
  officeMarkWay4: Decrypt the file
  officeMarkWay8: Manually add a watermark
  officeMarkWay16: Copy files to mobile disk
  officeMarkWay32: Copies files to a shared directory
  officeMarkWay64: The communication tool sends out files
  officeMarkWay128: Outgoing mail file
  officeMarkWay256: Web upload file
  officeMarkWay512: Upload files from a web disk
label:
  grade:
    0: No Rank
    1: General Data
    2: Important Data
    3: Core Data
