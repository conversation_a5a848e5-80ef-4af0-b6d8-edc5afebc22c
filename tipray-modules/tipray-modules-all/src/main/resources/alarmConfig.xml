<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE root>
<!--
	me-code：菜单编码
	strategy-number：策略编号
	alarm-type：告警类型
	alarm-bus：普通告警类型
	order: 排序
	editable：备注
 -->
<root>
    <alarm me-code="C51" strategy-group-type="2" strategy-numbers="1" alarm-type="1" alarm-bus="0" order="1"  i18n-dict="route.PrintSet" remark = "打印权限控制"/>
    <alarm me-code="C24" strategy-group-type="2" strategy-numbers="9,10" alarm-type="2" alarm-bus="0" order="2" i18n-dict="route.installPackage" remark = "软件安装/卸载限制"/>
    <alarm me-code="C22" strategy-group-type="2" strategy-numbers="7" alarm-type="3" alarm-bus="0" order="3" i18n-dict="route.WinTitle" remark = "窗口标题限制"/>
<!--    <alarm me-code="C23" strategy-group-type="2" strategy-numbers="8" alarm-type="4" i18n-dict="软件版本限制" />-->
    <alarm me-code="C21" strategy-group-type="2" strategy-numbers="6" alarm-type="5" alarm-bus="0" order="4"  i18n-dict="route.AppBlock" remark = "程序访问限制"/>
    <alarm me-code="C71" strategy-group-type="2" strategy-numbers="50" alarm-type="6" alarm-bus="0" order="5" i18n-dict="route.Driver" remark = "存储设备使用控制"/>
    <alarm me-code="C61" strategy-group-type="2" strategy-numbers="48" alarm-type="7" alarm-bus="0" order="6" i18n-dict="route.BlueTooth" remark = "蓝牙文件管控"/>
    <alarm me-code="D42" strategy-group-type="3" strategy-numbers="16" alarm-type="8" alarm-bus="0" order="7" i18n-dict="pages.msgModule1" remark = "网络隔离"/>
    <alarm me-code="B41" strategy-group-type="0" strategy-numbers="92" alarm-type="9" alarm-bus="0" order="8" i18n-dict="pages.msgModule2" remark = "资产变更"/>
    <alarm me-code="E3A" strategy-group-type="1" strategy-numbers="72" alarm-type="10" alarm-bus="0" order="9" i18n-dict="pages.msgModule3" remark = "工作模式切换"/>
    <alarm me-code="B4C" strategy-group-type="2" strategy-numbers="152" alarm-type="11" alarm-bus="0" order="10" i18n-dict="route.softwareLimit" remark = "软件黑白名单"/>
    <alarm me-code="B4D" strategy-group-type="2" strategy-numbers="153" alarm-type="12" alarm-bus="0" order="11" i18n-dict="route.requireInstall" remark = "必须安装软件"/>
    <alarm me-code="B4E" strategy-group-type="2" strategy-numbers="154" alarm-type="13" alarm-bus="0" order="12" i18n-dict="route.requireRun" remark = "必须运行软件"/>
    <alarm me-code="B4F" strategy-group-type="0" strategy-numbers="155" alarm-type="14" alarm-bus="0" order="13" i18n-dict="pages.msgModule4" remark = "软件资产变更"/>
    <alarm me-code="B41" strategy-group-type="0" strategy-numbers="156" alarm-type="15" alarm-bus="0" order="14" i18n-dict="pages.msgModule5" remark = "硬件资产变更"/>
    <alarm me-code="C71" strategy-group-type="1" strategy-numbers="50" alarm-type="16" alarm-bus="0" order="15" i18n-dict="pages.msgModule6" remark = "U盘违规格式化"/>
    <alarm me-code="CA3" strategy-group-type="2" strategy-numbers="163" alarm-type="18" alarm-bus="0" order="16" i18n-dict="pages.msgModule7" remark = "系统盘占用"/>
    <alarm me-code="CA3" strategy-group-type="2" strategy-numbers="163" alarm-type="19" alarm-bus="0" order="17" i18n-dict="pages.msgModule8" remark = "磁盘总体占用"/>
    <alarm me-code="CA3" strategy-group-type="2" strategy-numbers="163" alarm-type="20" alarm-bus="0" order="18" i18n-dict="pages.msgModule9" remark = "CPU占用"/>
    <alarm me-code="CA3" strategy-group-type="2" strategy-numbers="163" alarm-type="21" alarm-bus="0" order="19" i18n-dict="pages.msgModule10" remark = "内存占用"/>
    <alarm me-code="CA3" strategy-group-type="2" strategy-numbers="163" alarm-type="22" alarm-bus="0" order="20" i18n-dict="pages.msgModule11" remark = "IP地址变更"/>
    <alarm me-code="CA3" strategy-group-type="2" strategy-numbers="163" alarm-type="23" alarm-bus="0" order="21" i18n-dict="pages.msgModule12" remark = "MAC地址变更"/>
    <alarm me-code="CA3" strategy-group-type="2" strategy-numbers="163" alarm-type="24" alarm-bus="0" order="22" i18n-dict="pages.msgModule13" remark = "计算机名称变更"/>
    <alarm me-code="D44" strategy-group-type="4" strategy-numbers="201" alarm-type="17"  alarm-bus="0" order="23" i18n-dict="route.WifiBlock" remark = "WiFi连接限制"/>
    <alarm me-code="D83" strategy-group-type="2" strategy-numbers="205" alarm-type="25" alarm-bus="0" order="24" i18n-dict="pages.msgModule14" remark = "FTP工具传输"/>
    <alarm me-code="CA3" strategy-group-type="2" strategy-numbers="163" alarm-type="26" alarm-bus="0" order="25" i18n-dict="pages.msgModule15" remark = "硬盘使用时间"/>
    <alarm me-code="CA3" strategy-group-type="2" strategy-numbers="163" alarm-type="27" alarm-bus="0" order="26" i18n-dict="pages.diskUsage" remark = "硬盘使用次数"/>
<!--    <alarm me-code="E61" strategy-group-type="1" strategy-numbers="60" alarm-type="28" i18n-dict="邮件收件人白名单违规告警" />-->
    <alarm me-code="E61" strategy-group-type="1" strategy-numbers="209" alarm-type="29" alarm-bus="0" order="27" i18n-dict="pages.msgModule16" remark = "邮件白名单违规告警"/>
    <alarm me-code="B2H" strategy-group-type="1" strategy-numbers="210" alarm-type="30" alarm-bus="0" order="28" i18n-dict="pages.msgModule17" remark = "离线锁屏告警"/>
    <alarm me-code="D61" strategy-group-type="1" strategy-numbers="210" alarm-type="31" alarm-bus="0" order="29" i18n-dict="pages.msgModule23" remark = "邮件外发管控"/>
    <!--    菜单编码等需要修改-->
    <alarm me-code="C65" strategy-group-type="1" strategy-numbers="218" alarm-type="32" alarm-bus="0" order="30" i18n-dict="route.AdbLimit" remark = "ADB管控"/>
    <alarm me-code="A65" strategy-group-type="5" strategy-numbers="106" alarm-type="33" alarm-bus="0" order="31" i18n-dict="pages.msgModule18" remark = "备份磁盘空间告警"/>
    <alarm me-code="D66" strategy-group-type="4" strategy-numbers="217" alarm-type="34" alarm-bus="0" order="32" i18n-dict="pages.msgModule23" remark = "邮件外发管控"/>
    <alarm me-code="D75" strategy-group-type="4" strategy-numbers="46" alarm-type="35" alarm-bus="0" order="33" i18n-dict="pages.msgModule19" remark = "网络共享文件外传告警"/>
    <alarm me-code="C74" strategy-group-type="2" strategy-numbers="200" alarm-type="36" alarm-bus="0" order="34" i18n-dict="pages.msgModule20" remark = "USB外传文件告警"/>
    <alarm me-code="C81" strategy-group-type="2" strategy-numbers="18" alarm-type="37" alarm-bus="0" order="35" i18n-dict="pages.burnConfig" remark = "刻录机使用管控"/>
    <alarm me-code="D2B" strategy-group-type="4" strategy-numbers="63" alarm-type="38" alarm-bus="0" order="36" i18n-dict="route.BrowserFileStrategy" remark = "网页上传文件管控"/>
    <alarm me-code="D32" strategy-group-type="4" strategy-numbers="5" alarm-type="39" alarm-bus="0" order="37" i18n-dict="pages.msgModule21" remark = "聊天文件外传管控"/>
    <alarm me-code="C68" strategy-group-type="2" strategy-numbers="228" alarm-type="43" alarm-bus="0" order="38" i18n-dict="pages.msgModule22" remark = "MTP外发管控"/>
    <alarm me-code="C22" strategy-group-type="2" strategy-numbers="229" alarm-type="42" alarm-bus="0" order="39" i18n-dict="route.AppVersion" remark = "程序版本限制"/>
    <alarm me-code="C93" strategy-group-type="2" strategy-numbers="240" alarm-type="44" alarm-bus="0" order="40" i18n-dict="route.usbInterfaceConfig" remark="USB接口管控" />
    <alarm me-code="C94" strategy-group-type="2" strategy-numbers="245" alarm-type="45" alarm-bus="0" order="41" i18n-dict="route.netInterfaceConfig" remark="网络接口管控" />
    <alarm me-code="E25" strategy-group-type="1" strategy-numbers="113" alarm-type="46" alarm-bus="0" order="42" i18n-dict="pages.msgModule24" remark="批量解密数量限制" />
    <alarm me-code="B64" strategy-group-type="5" strategy-numbers="246" alarm-type="48" alarm-bus="0" order="43" i18n-dict="route.termSecurityDetection" remark="终端安全检测" />
	<alarm me-code="H55" strategy-group-type="4" strategy-numbers="252" alarm-type="49" alarm-bus="0" order="44" i18n-dict="route.telnetCommControl" remark="Telnet通讯管控" />
    <alarm me-code="H57" strategy-group-type="1" strategy-numbers="254" alarm-type="52" alarm-bus="0" order="45" i18n-dict="route.remoteDesktopControl" remark="远程桌面管控" />
    <alarm me-code="C91" strategy-group-type="2" strategy-numbers="70" alarm-type="53"  alarm-bus="0" order="46" i18n-dict="route.OtherDeviceLimit" remark="设备使用控制" />
    <alarm me-code="138" strategy-group-type="6" strategy-numbers="265" alarm-type="54" alarm-bus="0" order="47" i18n-dict="route.softwareBlackList" remark="高危软件限制" />
    <alarm me-code="D51" strategy-group-type="4" strategy-numbers="15" alarm-type="56" alarm-bus="0" order="48" i18n-dict="pages.flowThresholdLimit" remark="流量阈值限制" />
    <alarm me-code="H59" strategy-group-type="1" strategy-numbers="284" alarm-type="57" alarm-bus="0" order="49" i18n-dict="route.softwareBlackList" remark="远程工具上传文件" />
    <alarm me-code="D86" strategy-group-type="4" strategy-numbers="286" alarm-type="58" alarm-bus="0" order="50" i18n-dict="route.softwareBlackList" remark="AI上传文件管控" />
    <alarm me-code="D85" strategy-group-type="4" strategy-numbers="285" alarm-type="60" alarm-bus="0" order="51" i18n-dict="pages.aiSendContentLimit" remark="AI发送内容限制" />
    <alarm me-code="D89" strategy-group-type="4" strategy-numbers="287" alarm-type="61" alarm-bus="0" order="52" i18n-dict="pages.aiModelLimit" remark="AI模型限制" />
    <alarm me-code="B43" strategy-group-type="5" strategy-numbers="289" alarm-type="59" alarm-bus="0" order="53" i18n-dict="route.softwareBlackList" remark="软件资产管控" />
<!--    <alarm me-code="E62" strategy-group-type="1" strategy-numbers="62" alarm-type="50" i18n-dict="pages.httpWhiteList" remark="服务器白名单" />-->
<!--    <alarm me-code="E62" strategy-group-type="1" strategy-numbers="62" alarm-type="51" i18n-dict="pages.httpWhiteList" remark="服务器白名单" />-->
    <alarm me-code="C71" strategy-group-type="2" strategy-numbers="50" alarm-type="1001" alarm-bus="1" order="201" i18n-dict="pages.labelUsbCopy" remark = "USB复制文件"/>
    <alarm me-code="C71" strategy-group-type="2" strategy-numbers="50" alarm-type="1002" alarm-bus="1" order="202" i18n-dict="pages.labelUsbCut" remark = "USB剪切文件"/>
    <alarm me-code="H57" strategy-group-type="1" strategy-numbers="254" alarm-type="1003" alarm-bus="1" order="203" i18n-dict="route.stgLabelRemoteShareFile" remark="远程共享文件外传" />
    <alarm me-code="D32" strategy-group-type="4" strategy-numbers="5" alarm-type="1004" alarm-bus="1" order="204" i18n-dict="pages.stgLabelImSendFile" remark = "即时通讯发送文件"/>
    <alarm me-code="C81" strategy-group-type="2" strategy-numbers="18" alarm-type="1005" alarm-bus="1" order="205" i18n-dict="pages.stgLabelCdBurnFile" remark = "光盘刻录文件"/>
    <alarm me-code="D2B" strategy-group-type="4" strategy-numbers="63" alarm-type="1006" alarm-bus="1" order="206" i18n-dict="pages.stgLabelWebUploadFile" remark = "网页上传文件"/>
    <alarm me-code="D61" strategy-group-type="1" strategy-numbers="210" alarm-type="1007" alarm-bus="1" order="207" i18n-dict="pages.stgLabelMail" remark = "邮件附件内容"/>
    <alarm me-code="C61" strategy-group-type="2" strategy-numbers="48" alarm-type="1008" alarm-bus="1" order="208" i18n-dict="pages.stgLabelBluetooth" remark = "蓝牙外传文件"/>
    <alarm me-code="C68" strategy-group-type="2" strategy-numbers="228" alarm-type="1009" alarm-bus="1" order="209" i18n-dict="pages.stgLabelMtpSendFile" remark = "MTP外传文件"/>
    <alarm me-code="H57" strategy-group-type="1" strategy-numbers="254" alarm-type="1010" alarm-bus="1" order="210" i18n-dict="route.stgLabelRemoteOutgoingFile" remark="远程桌面外传文件" />
    <alarm me-code="D83" strategy-group-type="2" strategy-numbers="205" alarm-type="1011" alarm-bus="1" order="211" i18n-dict="pages.stgLabelFTP" remark = "FTP文件上传"/>
    <alarm me-code="D83" strategy-group-type="2" strategy-numbers="205" alarm-type="2001" alarm-bus="2" order="301" i18n-dict="pages.stgLabelFTP" remark = "FTP上传文件"/>
    <alarm me-code="C71" strategy-group-type="2" strategy-numbers="50" alarm-type="2002" alarm-bus="2" order="302" i18n-dict="pages.labelUsbCopy" remark = "USB复制文件"/>
    <alarm me-code="C71" strategy-group-type="2" strategy-numbers="50" alarm-type="2003" alarm-bus="2" order="303" i18n-dict="pages.labelUsbCut" remark = "USB剪切文件"/>
    <alarm me-code="H57" strategy-group-type="2" strategy-numbers="254" alarm-type="2004" alarm-bus="2" order="304" i18n-dict="pages.stgLabelRemoteShareFile" remark = "远程共享文件外传"/>
    <alarm me-code="D32" strategy-group-type="4" strategy-numbers="5" alarm-type="2005" alarm-bus="2" order="305" i18n-dict="pages.stgLabelImSendFile" remark = "即时通讯发送文件"/>
    <alarm me-code="C81" strategy-group-type="2" strategy-numbers="18" alarm-type="2006" alarm-bus="2" order="306" i18n-dict="pages.stgLabelCdBurnFile" remark = "光盘刻录文件"/>
    <alarm me-code="D2B" strategy-group-type="4" strategy-numbers="63" alarm-type="2007" alarm-bus="2" order="307" i18n-dict="pages.stgLabelWebUploadFile" remark = "网页上传文件"/>
    <alarm me-code="D61" strategy-group-type="1" strategy-numbers="210" alarm-type="2008" alarm-bus="2" order="308" i18n-dict="pages.stgLabelMail" remark = "邮件附件内容"/>
    <alarm me-code="C61" strategy-group-type="2" strategy-numbers="48" alarm-type="2009" alarm-bus="2" order="309" i18n-dict="pages.stgLabelBluetooth" remark = "蓝牙外传文件"/>
    <alarm me-code="C65" strategy-group-type="1" strategy-numbers="218" alarm-type="2010" alarm-bus="2" order="310" i18n-dict="pages.adbUpload" remark = "ADB上传文件"/>
    <alarm me-code="C68" strategy-group-type="2" strategy-numbers="228" alarm-type="2011" alarm-bus="2" order="311" i18n-dict="pages.stgLabelMtpSendFile" remark = "MTP外传文件"/>
    <alarm me-code="D81" strategy-group-type="4" strategy-numbers="134" alarm-type="2012" alarm-bus="2" order="312" i18n-dict="pages.stgLabelWebDiskUploadFile" remark = "网盘上传文件"/>
    <alarm me-code="H57" strategy-group-type="1" strategy-numbers="254" alarm-type="2013" alarm-bus="2" order="313" i18n-dict="route.stgLabelRemoteOutgoingFile" remark="远程桌面外传文件" />

</root>
