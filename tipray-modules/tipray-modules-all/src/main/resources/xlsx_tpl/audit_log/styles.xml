<?xml version="1.0" encoding="UTF-8"?>
<styleSheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main"><numFmts count="0"/><fonts count="3"><font><sz val="11.0"/><color indexed="8"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font><font><name val="Arial"/><sz val="12.0"/><color indexed="9"/><b val="true"/></font><font><name val="Arial"/><sz val="10.0"/><color indexed="8"/></font></fonts><fills count="5"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="darkGray"/></fill><fill><patternFill patternType="none"><fgColor indexed="48"/></patternFill></fill><fill><patternFill patternType="none"><fgColor indexed="48"/><bgColor indexed="48"/></patternFill></fill><fill><patternFill patternType="solid"><fgColor indexed="48"/><bgColor indexed="48"/></patternFill></fill></fills><borders count="9"><border><left/><right/><top/><bottom/><diagonal/></border><border><top style="thin"/></border><border><right style="thin"/><top style="thin"/></border><border><right style="thin"/><top style="thin"/><bottom style="thin"/></border><border><left style="thin"/><right style="thin"/><top style="thin"/><bottom style="thin"/></border><border><left style="thin"/><right style="thin"/><top style="thin"><color indexed="8"/></top><bottom style="thin"/></border><border><left style="thin"/><right style="thin"><color indexed="8"/></right><top style="thin"><color indexed="8"/></top><bottom style="thin"/></border><border><left style="thin"/><right style="thin"><color indexed="8"/></right><top style="thin"><color indexed="8"/></top><bottom style="thin"><color indexed="8"/></bottom></border><border><left style="thin"><color indexed="8"/></left><right style="thin"><color indexed="8"/></right><top style="thin"><color indexed="8"/></top><bottom style="thin"><color indexed="8"/></bottom></border></borders><cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs><cellXfs count="3"><xf numFmtId="0" fontId="0" fillId="0" borderId="0" xfId="0"/><xf numFmtId="0" fontId="1" fillId="4" borderId="8" xfId="0" applyFont="true" applyFill="true" applyBorder="true"><alignment horizontal="left" vertical="center" wrapText="false"/></xf><xf numFmtId="0" fontId="2" fillId="0" borderId="0" xfId="0" applyFont="true"/></cellXfs></styleSheet>