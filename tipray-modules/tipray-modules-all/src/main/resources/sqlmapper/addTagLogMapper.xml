<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.AddTagLogDao">

    <resultMap type="com.tipray.dlp.bean.AddTagLog" id="AddTagLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="trank" column="trank"/>
        <result property="trankContent" column="trank_content"/>
        <result property="recordGuid" column="record_guid"/>
        <result property="fileName" column="file_name"/>
        <result property="filePath" column="file_path"/>
        <result property="realSuffix" column="real_suffix"/>
        <result property="encryFlag" column="encry_flag"/>
        <result property="processName" column="process_name"/>
        <result property="addType" column="add_type"/>
        <result property="tagContent" column="tag_content"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="clearFlag" column="clear_flag"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="addType != null and addType != ''">
                and add_type = #{addType}
            </if>
            <if test="addType == 0">
                and add_type = #{addType}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name like CONCAT('%',#{fileName},'%')
            </if>
            <if test="trankContent != null and trankContent != ''">
                and (trank_content like CONCAT('%',#{trankContent},'%')
                    <if test="clearFlag != null"> or clear_flag = #{clearFlag} </if>
                )
            </if>
            <if test="tagContent != null and tagContent != ''">
                and (tag_content like CONCAT('%',#{tagContent},'%')
                    <if test="clearFlag != null"> or clear_flag = #{clearFlag} </if>
                )
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from add_document_tag_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="AddTagLogMap">
        select id, user_id,term_id, trank, trank_content, file_name, record_guid, file_path, real_suffix, encry_flag, process_name,
        add_type, tag_content, create_time, term_group_id, user_group_id, clear_flag
        from add_document_tag_log
        <include refid="sql_where"></include>
    </select>

</mapper>
