<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.cloud.offlinestrategy.dao.OfflineStrategyExtendConfigDao">

    <!-- 查询最近删除的配置ID列表 -->
    <select id="selectRecentlyDeletedConfigIds" resultType="java.lang.Long">
        SELECT id
        FROM ose_offline_strategy_extend_config
        WHERE deleted = 1
          AND modify_time >= #{timeWindow}
        ORDER BY modify_time ASC
    </select>

</mapper>
