<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DiskScanEncOrDecLogDao">

    <resultMap type="com.tipray.dlp.bean.DiskScanEncOrDecLog" id="DiskScanLogMap">
        <result property="id" column="id"/>
        <result property="guid" column="guid"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="opType" column="op_type"/>
        <result property="result" column="result"/>
        <result property="fileName" column="file_name"/>
        <result property="filePath" column="file_path"/>
        <result property="sensGuid" column="content_aware_guid"/>
        <result property="createTime" column="create_time"/>
        <result property="runGuid" column="run_guid"/>
        <result property="batchNo" column="batch_no"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>
    <sql id="full_fields_sql">
        id,term_id,user_id,create_time,content_aware_guid,guid,op_type,file_name,file_path,result,run_guid,batch_no,term_group_id,user_group_id
    </sql>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="opType != null">
                and op_type = #{opType}
            </if>
            <if test="opTypes != null and opTypes != ''">
                and op_type in (${opTypes})
            </if>
            <if test="result != null and result != ''">
                and result in (${result})
            </if>
            <if test="guid != null and guid != ''">
                and guid like CONCAT('%',#{guid},'%')
            </if>
            <if test="guids != null and guids != ''">
                and guid in (${guids})
            </if>
            <if test="rangeId != null">
                and id > #{rangeId}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.DiskScanLogVO" resultType="java.lang.Long">
      select count(0) from disk_scan_des_detail_log
      <include refid="sql_where"/>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.DiskScanLogVO" resultMap="DiskScanLogMap">
      select <include refid="full_fields_sql"/>
      from disk_scan_des_detail_log
      <include refid="sql_where"/>
    </select>

    <select id="listGuidByVO" parameterType="com.tipray.dlp.bean.vo.DiskScanLogVO" resultType="com.tipray.dlp.bean.DiskScanEncOrDecLog">
        select id, guid as file_guid from disk_scan_des_detail_log <include refid="sql_where"/>
    </select>

</mapper>
