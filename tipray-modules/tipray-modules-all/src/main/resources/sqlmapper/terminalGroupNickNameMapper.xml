<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.TerminalGroupNickNameDao">
    <resultMap id="baseResultMap" type="com.tipray.dlp.bean.dto.TerminalGrpNickNameDTO">
        <id column="id" property="termId" />
        <result column="status" property="status"/>
        <result column="type" property="type"/>
        <result column="version" property="version"/>
        <result column="name" property="name"/>
        <result column="group_name" property="groupName"/>
        <result column="new_group_id" property="newGroupId"/>
        <result column="new_group_name" property="newGroupName"/>
        <result column="nick_name" property="nickName"/>
        <result column="new_nick_name" property="newNickName"/>
        <result column="create_time" property="createTime"/>
        <result column="user_modify_group_id" property="userModifyGroupId"/>
        <result column="guid" property="guid"/>

    </resultMap>
    <resultMap id="baseTgMap" type="com.tipray.dlp.bean.TerminalGrpNickAcquisition">
        <id column="term_id" property="termId" />
        <result column="status" property="status"/>
        <result column="group_name" property="groupName"/>
        <result column="new_group_id" property="newGroupId"/>
        <result column="new_group_name" property="newGroupName"/>
        <result column="nick_name" property="nickName"/>
        <result column="new_nick_name" property="newNickName"/>
        <result column="create_time" property="createTime"/>
        <result column="user_modify_group_id" property="userModifyGroupId"/>
    </resultMap>

    <sql id="select_where_vo">
        <where>
            <if test="ids != null and ids != ''">
                and t.id in (${ids})
            </if>
            <if test="groupId != null">
                and (
                exists (select 0 from rel_group_terminal tg where tg.term_id = t.id and tg.group_id = #{groupId})
                )
            </if>
            <if test="groupIds != null and groupIds != ''">
                and (
                exists (select 0 from rel_group_terminal tg where tg.term_id = t.id and tg.group_id in (${groupIds}))
                )
            </if>
            <if test="searchInfo != null and searchInfo != '' ">
                and (t.name like CONCAT('%',#{searchInfo},'%') or t.id like CONCAT('%',#{searchInfo},'%'))
            </if>
            <if test="name != null and name != '' ">
                and t.name like CONCAT('%',#{name},'%')
            </if>
            <if test="searchId != null and searchId != '' ">
                and id like CONCAT('%',#{searchId},'%')
            </if>
            <if test="type != null">
                and t.type =#{type}
            </if>
            <if test="types != null and types != ''">
                and t.type in (${types})
            </if>
            <if test="status != null and status != '' and status != 1">
                and tgna.status = ${status}
            </if>
            <if test="status != null and status != '' and status == 1">
                and (tgna.status = 1 or tgna.status is null)
            </if>
            <if test="termIds != null and termIds != ''">
                and tgna.term_id in (${termIds})
            </if>
            <if test="filterUseType != null and filterUseType != ''">
                and use_type = 0
            </if>
            <if test="taskSendStatus != null and taskSendStatus">
                and t.version > '5.01.250317.SC'
            </if>
            <if test="taskSendStatus != null and !taskSendStatus">
                <![CDATA[ and t.version < '5.01.250317.SC' ]]>
            </if>
            <include refid="commonSQL.andNotDel"></include>
        </where>
    </sql>

    <select id="terminalGrpNickAcquisitionListByVO" parameterType="com.tipray.dlp.bean.vo.TerminalGrpNickAcquisitionVO" resultMap="baseTgMap" >
        select tgna.term_id ,tgna.status,tgna.group_name ,tgna.new_group_id ,tgna.new_group_name,
        tgna.nick_name ,tgna.new_nick_name ,
        tgna.create_time,tgna.user_modify_group_id
        from terminal_grp_nick_acquisition tgna
        <where>
            <if test="status != null and status != ''">
                and tgna.status = #{status}
            </if>
            <if test="termId != null and termId != ''">
                and tgna.term_id = #{termId}
            </if>
        </where>
    </select>

    <select id="getTaskPage" parameterType="com.tipray.dlp.bean.vo.TerminalGrpNickAcquisitionVO" resultMap="baseResultMap">
        select t.id, t.name, t.type type, t.version,
        tgna.status , tgna.group_name, tgna.new_group_id, tgna.new_group_name,
        tgna.nick_name, tgna.new_nick_name, tgna.create_time, tgna.user_modify_group_id,t.guid
        from terminal t left JOIN terminal_grp_nick_acquisition tgna  ON t.id = tgna.term_id
        <include refid="select_where_vo"/>
    </select>

    <select id="countByVO" resultType="java.lang.Long" >
        select count(0)
        from terminal_grp_nick_acquisition tgna
        LEFT JOIN
        (select t.id id,gi.id group_id,gi.name,t.name oldNickName from terminal t
        LEFT JOIN rel_group_terminal rgt on t.id=rgt.term_id
        LEFT JOIN group_info gi on gi.id = rgt.group_id) tt
        on tgna.term_id = tt.id
        <where>
            <if test="status != null and status != ''">
                and tgna.status = ${status}
            </if>
            <if test="termId != null and termId != ''">
                and tgna.term_id = ${termId}
            </if>
        </where>
    </select>
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into terminal_grp_nick_acquisition(term_id, status, create_time)
        values (#{termId}, #{status}, #{createTime})
    </insert>

    <update id="updateByTermId" parameterType="com.tipray.dlp.bean.TerminalGrpNickAcquisition">
        update terminal_grp_nick_acquisition
        <set>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="groupName != null and groupName != ''">
                group_name = #{groupName},
            </if>
            <if test="newGroupId != null">
                new_group_id = #{newGroupId},
            </if>
            <if test="newGroupName != null ">
                new_group_name = #{newGroupName},
            </if>
            <if test="nickName != null and nickName != ''">
                nick_name = #{nickName},
            </if>
            <if test="newNickName != null and newNickName != ''">
                new_nick_name = #{newNickName},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="userModifyGroupId != null">
                user_modify_group_id = #{userModifyGroupId},
            </if>
        </set>
        where term_id=#{termId}
    </update>

    <!--删除被卸载的终端数据-->
    <delete id="deleteByTerminalDeleted">
        delete t1 from terminal_grp_nick_acquisition t1,(select id from terminal where deleted =1) t2 where t1.term_id =t2.id
    </delete>
</mapper>

