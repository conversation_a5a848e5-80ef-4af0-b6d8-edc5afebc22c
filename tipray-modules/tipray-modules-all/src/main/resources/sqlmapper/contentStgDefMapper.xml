<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ContentStgDefDao">

    <resultMap type="com.tipray.dlp.bean.ContentStgDef" id="DefMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="stgTypeNumber" column="stg_type_number"/>
        <result property="strategyDefType" column="stg_def_type"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="active" column="active"/>
        <result property="remark" column="remark"/>
        <result property="dripScan" column="drip_scan"/>
        <result property="severity" column="severity"/>
        <result property="ruleGroupIds" column="rule_group_ids"/>
        <result property="respondRuleIds" column="respond_rule_ids"/>
        <result property="type" column="type"/>
        <result property="value" column="value"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <if test="active != null">
                and d.active = #{active}
            </if>
            <if test="objectType != null and objectId != null">
                and (
                    o.object_type = #{objectType} and o.object_id = #{objectId}
                <if test="parentObjectType != null and parentObjectIds != null and parentObjectIds != ''">
                  or (o.object_type = #{parentObjectType} and o.object_id in (@parentObjectIds) and d.active = true)
                </if>
                )
            </if>
            <if test="objectType != null and objectIds != null and objectIds != ''">
                and o.object_type = #{objectType} and o.object_id in (@objectIds)
            </if>
            <if test="strategyId != null">
                and d.id = #{strategyId}
            </if>
            <if test="strategyIds != null and strategyIds != ''">
                and d.id in (@strategyIds)
            </if>
            <if test="dripScan != null">
                and (d.drip_scan = #{dripScan} or d.active = 1)
            </if>
            <if test="stgTypeNumber != null">
                and d.stg_type_number = #{stgTypeNumber}
            </if>
            <if test="stgTypeNumbers != null and stgTypeNumbers != ''">
                and d.stg_type_number in (@stgTypeNumbers)
            </if>
            <if test="strategyDefType != null">
                and d.stg_def_type = #{strategyDefType}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and d.name like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="ids != null and ids != ''">
                and d.id in (@ids)
            </if>
            <if test="objectType == null and objectId == null and permissions != null and permissions.size() > 0">
                and (o.object_id is null or
                <foreach collection="permissions" index="item" item="item" open="(" close=")" separator="or">
                    (o.object_type = #{item.objectType} and o.object_id in (${item.objectIds}))
                </foreach>
                )
            </if>
        </where>
    </sql>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.StrategyVO" resultMap="DefMap">
      select distinct d.id, d.name, d.stg_type_number, d.stg_def_type, d.active, d.remark, d.severity, d.drip_scan, d.rule_group_ids, d.respond_rule_ids, d.type, d.value
      from content_stg_def d  left join stg_special_obj o on d.id = o.stg_id and d.stg_type_number = o.stg_code
      <include refid="vo_where_sql"></include>
    </select>

    <select id="listByRespondRuleId" resultMap="DefMap">
        select id, name, stg_type_number, active,respond_rule_ids, type, value from content_stg_def where
        <foreach collection="ruleIds" item="ruleId" separator="or" open="(" close=")">
            respond_rule_ids = '${ruleId}' or respond_rule_ids like '${ruleId},%' or respond_rule_ids like '%,${ruleId},%' or respond_rule_ids like '%,${ruleId}'
        </foreach>
    </select>
    <select id="listByRuleGroupId" resultMap="DefMap">
        select id, name, stg_type_number, active,rule_group_ids, type, value from content_stg_def where
        <foreach collection="ruleGroupIds" item="ruleId" separator="or" open="(" close=")">
            rule_group_ids = '${ruleId}' or rule_group_ids like '${ruleId},%' or rule_group_ids like '%,${ruleId},%' or rule_group_ids like '%,${ruleId}'
        </foreach>
    </select>

    <select id="contentGroupByStgTypeNumber" resultType="java.util.Map">
        select count(0) num,stg_type_number stgCode from content_stg_def where active = 1 and stg_def_type = 0 GROUP BY stg_type_number
    </select>
</mapper>
