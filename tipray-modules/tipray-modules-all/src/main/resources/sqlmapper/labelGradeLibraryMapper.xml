<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.LabelGradeLibraryDao">

    <resultMap type="com.tipray.dlp.bean.LabelGradeLibrary" id="resultMap">
        <result property="id" column="id"/>
        <result property="langKey" column="lang_key"/>
        <result property="grade" column="grade"/>
        <result property="status" column="status"/>
        <result property="active" column="active"/>
        <result property="content" column="content"/>
        <result property="version" column="version"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="vo_where_sql">
      <where>
          status = 0
          <if test="ids != null and ids != ''">
              and id in (${ids})
          </if>
          <if test="searchInfo != null and searchInfo != ''">
              and content like CONCAT('%',#{searchInfo},'%')
          </if>
        <include refid="commonSQL.andNotDel"></include>
      </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.LabelGradeLibraryVO" resultMap="resultMap">
        select * from label_grade_library
        <include refid="vo_where_sql"/>
    </select>

    <select id="getMaxVersion" resultType="java.lang.Long">
        select max(version) + 1 from label_grade_library
    </select>

    <update id="updateVersion">
        update label_grade_library set version = #{version}, modify_time = now()
    </update>

    <update id="updateGrade">
        update label_grade_library set grade = #{targetGrade}, lang_key = #{targetLangKey}, status = 1, modify_time = now() where id in (${ids})
    </update>

    <update id="updateActive">
        update label_grade_library set active = #{active}, modify_time = now() where id in (${ids})
    </update>
</mapper>
