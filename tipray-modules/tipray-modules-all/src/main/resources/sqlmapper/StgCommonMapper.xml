<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.dlp.dao.StgCommonDao">
    <select id="countSameNameStg" parameterType="com.tipray.dlp.bean.StgDef" resultType="java.lang.Integer">
        SELECT COUNT(id) FROM stg_def
        WHERE name = #{name} AND stg_type_number = #{stgTypeNumber}
    </select>

    <select id="getUsedScopeByStgTypeNumber" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT used_scope FROM stg_base_config
        WHERE number = #{stgTypeNumber}
    </select>


    <select id="countAllTypeByVo" parameterType="com.tipray.dlp.bean.vo.StrategyExportVo" resultType="java.lang.Long">
        select count(*) from (
        <if test="stgDefStgTypeNumbers != null and stgDefStgTypeNumbers != ''">
            (select id, name, stg_type_number, strategy_def_type, active, remark, os_type, strategy_info
            from stg_def
            <where>
                <if test="strategyDefType != null">
                    and strategy_def_type = #{strategyDefType}
                </if>
                <include refid="stg_def_where_sql"/>
            </where> )
        </if>
        <if test="stgDefStgTypeNumbers != null and stgDefStgTypeNumbers != '' and contentStgDefStgTypeNumbers != null and contentStgDefStgTypeNumbers != ''">
            union all
        </if>
        <if test="contentStgDefStgTypeNumbers != null and contentStgDefStgTypeNumbers != ''">
            (select id, name, stg_type_number, stg_def_type AS strategy_def_type, active, remark, null os_type, null strategy_info
            from content_stg_def <include refid="content_stg_where_sql"></include>)
        </if>
        <if test="diScanDefStgTypeNumbers != null and diScanDefStgTypeNumbers != ''
            and ((stgDefStgTypeNumbers != null and stgDefStgTypeNumbers != '') or
             (contentStgDefStgTypeNumbers != null and contentStgDefStgTypeNumbers != ''))">
            union all
        </if>
        <if test="diScanDefStgTypeNumbers != null and diScanDefStgTypeNumbers != ''">
            (select id, name, 95 AS stg_type_number, null strategy_def_type, null active, remark, os_type, null strategy_info
            from disk_scan_def
            <include refid="discan_vo_where_sql"></include>)
        </if>
        ) AS a
    </select>

    <select id="listAllTypeByVo" parameterType="com.tipray.dlp.bean.vo.StrategyExportVo" resultType="com.tipray.dlp.bean.StgDef">
        select a.*, sbc.strategy_key strategy_type, sbc.name stg_type_name from (
            <if test="stgDefStgTypeNumbers != null and stgDefStgTypeNumbers != ''">
                (select id, name, stg_type_number, strategy_def_type, active, remark, os_type, strategy_info
                from stg_def
                <where>
                    <if test="strategyDefType != null">
                        and strategy_def_type = #{strategyDefType}
                    </if>
                    <include refid="stg_def_where_sql"/>
                </where> )
            </if>
            <if test="stgDefStgTypeNumbers != null and stgDefStgTypeNumbers != '' and contentStgDefStgTypeNumbers != null and contentStgDefStgTypeNumbers != ''">
                union all
            </if>
            <if test="contentStgDefStgTypeNumbers != null and contentStgDefStgTypeNumbers != ''">
                (select id, name, stg_type_number, stg_def_type AS strategy_def_type, active, remark, null os_type, null strategy_info
                from content_stg_def <include refid="content_stg_where_sql"></include>)
            </if>
            <if test="diScanDefStgTypeNumbers != null and diScanDefStgTypeNumbers != ''
            and ((stgDefStgTypeNumbers != null and stgDefStgTypeNumbers != '') or
             (contentStgDefStgTypeNumbers != null and contentStgDefStgTypeNumbers != ''))">
                union all
            </if>
        <if test="diScanDefStgTypeNumbers != null and diScanDefStgTypeNumbers != ''">
                (select id, name, 95 AS stg_type_number, null strategy_def_type, null active, remark, os_type, null strategy_info
                from disk_scan_def
                <include refid="discan_vo_where_sql"></include>)
        </if>
        ) AS a
        LEFT JOIN stg_base_config sbc ON a.stg_type_number = sbc.number

    </select>

    <sql id="stg_def_where_sql">
        <if test="strategyId != null">
            and id = #{strategyId}
        </if>
        <if test="strategyIds != null and strategyIds != ''">
            and id in (${strategyIds})
        </if>
        <if test="stgDefStgTypeNumbers != null and stgDefStgTypeNumbers != ''">
            and stg_type_number in (${stgDefStgTypeNumbers})
        </if>
        <if test="stgTypeNumber != null">
            and stg_type_number = #{stgTypeNumber}
        </if>
        <if test="searchInfo != null and searchInfo != ''">
            and name like CONCAT('%',#{searchInfo},'%')
        </if>
        <if test="equalsRemark != null and equalsRemark != ''">
            and remark = #{equalsRemark}
        </if>
        <if test="likeRemark != null and likeRemark != ''">
            and remark like CONCAT('%',#{likeRemark},'%')
        </if>
        <if test="active != null">
            and active = #{active}
        </if>
    </sql>
    <sql id="content_stg_where_sql">
        <where>
            <if test="strategyId != null">
                and id = #{strategyId}
            </if>
            <if test="strategyIds != null and strategyIds != ''">
                and id in (${strategyIds})
            </if>
            <if test="dripScan != null">
                and (drip_scan = #{dripScan} or active = 1)
            </if>
            <if test="stgTypeNumber != null">
                and stg_type_number = #{stgTypeNumber}
            </if>
            <if test="contentStgDefStgTypeNumbers != null and contentStgDefStgTypeNumbers != ''">
                and stg_type_number in (${contentStgDefStgTypeNumbers})
            </if>
            <if test="strategyDefType != null">
                and stg_def_type = #{strategyDefType}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and name like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="ids != null and ids != ''">
                and id in (${ids})
            </if>
        </where>
    </sql>

    <sql id="discan_vo_where_sql">
        <where>
            <if test="searchInfo != null and searchInfo != ''">
                and name like CONCAT('%',#{searchInfo},'%')
            </if>
        </where>
    </sql>
</mapper>
