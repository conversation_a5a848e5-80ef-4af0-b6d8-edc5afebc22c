<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ReportPanelDao">

    <resultMap type="com.tipray.dlp.bean.ReportPanel" id="ReportPanelMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="reportNum" column="report_num" jdbcType="VARCHAR"/>
        <result property="panel" column="panel" jdbcType="VARCHAR"/>
        <result property="queryCondition" column="query_condition" jdbcType="VARCHAR"/>
        <result property="isStay" column="is_stay" jdbcType="INTEGER"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>


    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="ReportPanelMap">
        select id, user_id, name, report_num, panel, is_stay, query_condition
        from report_panel where deleted = 0
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="userId != null">
            and user_id = #{userId}
        </if>
        <if test="name != null and name != ''">
            and name = #{name}
        </if>
        <if test="reportNum != null and reportNum != ''">
            and report_num = #{reportNum}
        </if>
        <if test="panel != null and panel != ''">
            and panel = #{panel}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
    </select>

    <!--通过用户id修改数据-->
    <update id="updateIsStay">
        update report_panel
        set is_stay = 0
        where deleted = 0 and  user_id = #{userId}
    </update>

     <!--根据条件删除数据-->
    <delete id="deleteByCondition">
        delete from report_panel
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="reportNum != null and reportNum != ''">
                and report_num = #{reportNum}
            </if>
            <if test="panel != null and panel != ''">
                and panel = #{panel}
            </if>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
            <if test="isStay != null">
                and is_stay = #{isStay}
            </if>
        </where>
    </delete>
    
</mapper>
