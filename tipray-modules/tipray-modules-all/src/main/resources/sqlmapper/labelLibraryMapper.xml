<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.LabelLibraryDao">

    <resultMap type="com.tipray.dlp.bean.LabelLibrary" id="resultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="groupId" column="group_id"/>
    </resultMap>

    <sql id="vo_where_sql">
      <where>
        status = 0 and id > 3
        <if test="groupId != null and groupId != 0">
            and group_id = #{groupId}
        </if>
        <if test="searchInfo != null and searchInfo != ''">
            and name like CONCAT('%',#{searchInfo},'%')
        </if>
          <if test="ids != null and ids != ''">
              and id in (${ids})
          </if>
        <include refid="commonSQL.andNotDel"></include>
      </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.LabelLibraryVO" resultMap="resultMap">
        select id, name, remark, group_id, modify_time from label_library
        <include refid="vo_where_sql"/>
    </select>

    <select id="getMaxVersion" resultType="java.lang.Long">
        select max(version) + 1 from label_library
    </select>

    <select id="getMaxId" resultType="java.lang.Long">
        select max(id) from label_library
    </select>

    <update id="updateVersion" >
        update label_library set version = #{version} where id in (${ids})
    </update>

    <update id="updateStatus">
        update label_library set status = 1 where id in (${ids})
    </update>
</mapper>
