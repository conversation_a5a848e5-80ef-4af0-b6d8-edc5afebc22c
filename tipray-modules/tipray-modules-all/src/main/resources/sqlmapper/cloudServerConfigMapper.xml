<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.cloud.dao.CloudServerConfigDao">

    <resultMap type="com.tipray.dlp.cloud.bean.CloudServerConfig" id="resultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="host" column="host"/>
        <result property="port" column="port"/>
        <result property="protocol" column="protocol"/>
        <result property="addressType" column="address_type"/>
        <result property="remark" column="remark"/>
        <result property="proxyHost" column="proxy_host"/>
        <result property="proxyPort" column="proxy_port"/>
        <result property="proxyType" column="proxy_type"/>
        <result property="proxyUsername" column="proxy_username"/>
        <result property="proxyPassword" column="proxy_password"/>
        <result property="proxyStatus" column="proxy_status"/>
        <result property="modifyVer" column="modify_ver"/>
        <result property="deleted" column="deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <!-- 获取启用的云服务配置 -->
    <select id="getActiveConfig" resultMap="resultMap">
        SELECT * FROM cloud_server_config
        WHERE (deleted = 0 OR deleted IS NULL)
        ORDER BY id DESC
        LIMIT 1
    </select>

    <!-- 根据名称获取配置 -->
    <select id="getByName" resultMap="resultMap">
        SELECT * FROM cloud_server_config
        WHERE name = #{name} AND (deleted = 0 OR deleted IS NULL)
        LIMIT 1
    </select>

</mapper>
