<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.InstallPkgMakeLogDao">

    <resultMap type="com.tipray.dlp.bean.InstallPkgMakeLog" id="resultMap">
        <result property="id" column="id"/>
        <result property="fileName" column="file_name"/>
        <result property="code" column="code"/>
        <result property="beginTime" column="begin_time" javaType="java.util.Date"/>
        <result property="endTime" column="end_time" javaType="java.util.Date"/>
        <result property="isPermanent" column="is_permanent"/>
        <result property="type" column="type"/>
        <result property="createTime" column="create_time" javaType="java.util.Date"/>
    </resultMap>


   <select id="countCodeCount" resultType="com.tipray.dlp.bean.dto.InstallPkgCount">
       select code, count(0) as num
       from dlp_datamgr.install_pkg_make_log
       where code in (${codes})
       group by code
   </select>

    <insert id="insertLog" useGeneratedKeys="true" keyProperty="id">
        insert into dlp_datamgr.install_pkg_make_log (file_name, code, begin_time, end_time, is_permanent, type, create_time)
        values (#{fileName}, #{code}, #{beginTime}, #{endTime}, #{isPermanent}, #{type}, #{createTime})
    </insert>
</mapper>
