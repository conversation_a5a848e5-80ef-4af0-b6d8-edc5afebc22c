<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.CommonRelatedDataDao">

    <resultMap type="com.tipray.dlp.bean.BaseRelated" id="resultMap">
        <result property="id" column="id"/>
        <result property="dataId" column="data_id"/>
        <result property="bizId" column="biz_id"/>
        <result property="bizType" column="biz_type"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="list" resultMap="resultMap">
        select id, data_id, biz_id, biz_type from ${table}
    </select>

    <select id="countByData" resultType="java.lang.Long">
        select count(0) from ${table} where data_id = #{dataId}
    </select>

    <select id="listByData" resultMap="resultMap">
        select id, data_id, biz_id, biz_type from ${table} where data_id = #{dataId}
    </select>
    <select id="listByDatas" resultMap="resultMap">
        select id, data_id, biz_id, biz_type from ${table} where data_id in (@dataIds)
    </select>

    <select id="listByBiz" resultMap="resultMap">
        select id, data_id, biz_id, biz_type from ${table} where biz_type = #{bizType} and biz_id = #{bizId}
    </select>
    <select id="listDataByBiz" resultType="java.lang.Long">
        select data_id from ${table} where biz_type = #{bizType} and biz_id = #{bizId}
    </select>
    <select id="listBizByData" resultType="java.lang.Long">
        select biz_id from ${table} where biz_type = #{bizType} and data_id in (@dataId)
    </select>

    <insert id="insert">
        insert into ${table}(data_id, biz_id, biz_type) values
        <foreach collection="beans" item="item" index="index" separator=",">
            (#{item.dataId},
            #{item.bizId},
            #{item.bizType})
        </foreach>
    </insert>

    <update id="updateById">
        <foreach collection="beans" item="item" index="index" separator=";">
            update ${table}
            <set>
                data_id = #{item.dataId},
                biz_id = #{item.bizId},
                biz_type = #{item.bizType}
            </set>
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteByIds">
        delete from ${table} where id in (@ids)
    </delete>
    <delete id="deleteByData">
        delete from ${table} where data_id in (@dataId)
    </delete>
    <delete id="deleteByBiz">
        delete from ${table} where biz_type = #{bizType} and biz_id in (@bizId)
    </delete>
</mapper>
