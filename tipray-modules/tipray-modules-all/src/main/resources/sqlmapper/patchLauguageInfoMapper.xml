<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.PatchLauguageInfoDao">
    <resultMap type="com.tipray.dlp.bean.PatchLauguageInfo" id="PatchLauguageInfoMap">
        <result property="id" column="id"/>
        <result property="guid" column="guid"/>
        <result property="updateId" column="update_id"/>
        <result property="lauguage" column="lauguage"/>
        <result property="patchTitle" column="patch_title"/>
        <result property="patchDescribe" column="patch_describe"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>
    <sql id="vo_where_sql">
        <where>
            <if test="guid != null and guid != ''">
                and guid = ${guid}
            </if>
            <if test="updateId != null and updateId != ''">
                and update_id = ${updateId}
            </if>
            <if test="lauguage != null and lauguage != ''">
                and lauguage = #{lauguage}
            </if>
            <if test="patchTitle != null and patchTitle != ''">
                and patch_title like CONCAT('%',#{patchTitle},'%')
            </if>
            <if test="patchDescribe != null and patchDescribe != ''">
                and patch_describe like CONCAT('%',#{patchDescribe},'%')
            </if>
            <if test='guidList != null and guidList.size>0'>
                and guid in (@guidList)
            </if>
        </where>
    </sql>
    <select id="selectPageByVO"  parameterType="com.tipray.dlp.bean.vo.PatchVO" resultMap="PatchLauguageInfoMap">
        select id, guid,update_id,lauguage, patch_title, patch_describe, create_time, modify_time
        from patch_lauguage_info p
        <include refid="vo_where_sql"/>
    </select>
    <select id="getPatchLauguageInfoByGuid"  resultMap="PatchLauguageInfoMap">
        select id, guid,update_id,lauguage, patch_title, patch_describe, create_time, modify_time
        from patch_lauguage_info
        where guid = #{guid}
    </select>

</mapper>