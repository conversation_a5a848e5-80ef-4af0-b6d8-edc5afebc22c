<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DkeyAuthInfoDao">

    <resultMap type="com.tipray.dlp.bean.DkeyAuthInfo" id="DkeyAuthInfoMap">
        <result property="nickName" column="nick_name"/>
        <result property="deviceSerial" column="device_serial"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!--新增所有列-->
    <insert id="insert">
        insert into dkey_auth_info(nick_name, device_serial, start_time, end_time, remark)
        values (#{nickName}, #{deviceSerial}, #{startTime}, #{endTime}, #{remark})
    </insert>

    <update id="update" parameterType="com.tipray.dlp.bean.DkeyAuthInfo">
        update dkey_auth_info set
        nick_name = #{nickName}, start_time = #{startTime}, end_time = #{endTime}, remark = #{remark}
        where device_serial = #{deviceSerial}
    </update>

    <select id="list" resultMap="DkeyAuthInfoMap">
        select nick_name, device_serial, start_time, end_time, remark from dkey_auth_info
    </select>

    <delete id="deleteByDeviceSerial" parameterType="java.lang.String">
        delete from dkey_auth_info where device_serial = #{value}
    </delete>

    <select id="getByDeviceSerial" resultMap="DkeyAuthInfoMap">
        select nick_name, device_serial, start_time, end_time, remark from dkey_auth_info
        where device_serial = #{value}
    </select>

</mapper>
