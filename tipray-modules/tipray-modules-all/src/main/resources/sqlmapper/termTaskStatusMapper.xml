<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.TermTaskStatusDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.TermTaskStatus">
        <id column="id" property="id" />
        <result property="terminalId" column="term_id"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="taskName" column="task_name"/>
        <result property="taskId" column="task_id"/>
        <result property="state" column="state"/>
        <result property="taskFailCode" column="task_fail_code"/>
        <result property="opTime" column="op_time"/>
    </resultMap>

    <sql id="sql_where">
        <where>
<!--            <include refid="commonSQL.andObjectTypeAndObjectId"/>-->
            <if test="termId != null and termId != ''">
                and term_id = #{termId}
            </if>
            <if test="termIds != null and termIds != ''">
                and term_id in (${termIds})
            </if>
            <if test="taskId != null and taskId != ''">
                and task_id = #{taskId}
            </if>
            <if test="taskIds != null and taskIds != ''">
                and task_id in (${taskIds})
            </if>
            <if test="state != null and state != ''">
                and state = #{state}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from term_task_status
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="resultMap">
        select id, term_id, term_group_id, task_name, task_id, state, task_fail_code, op_time from term_task_status
        <include refid="sql_where"></include>
    </select>

    <select id="listTermTaskStatus" resultMap="resultMap">
        select id, term_id, task_id, task_name, state, task_fail_code, op_time from term_task_status
        <include refid="sql_where"></include>
    </select>
</mapper>
