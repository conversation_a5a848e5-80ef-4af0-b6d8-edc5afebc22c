<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.LuaRuleDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.LuaRule">
        <id column="id" property="id" />
        <result column="name" property="name"/>
        <result column="regular" property="regular"/>
        <result column="lua" property="lua"/>
    </resultMap>

    <select id="listAll" resultMap="resultMap">
        select * from rule_lua
        where 1 = 1
        <include refid="commonSQL.andNotDel"></include>
    </select>


</mapper>
