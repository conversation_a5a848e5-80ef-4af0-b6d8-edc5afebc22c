<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DriverBlockDao">
    <insert id="insert">
        INSERT INTO driver_block (driver_type, control_code, time_id, entity_type, entity_id, create_time, modify_time)
        VALUES (#{driverType}, #{controlCode}, #{timeId}, #{entityType}, #{entityId}, #{createTime}, #{modifyTime})
    </insert>

    <update id="update">
        UPDATE driver_block
        SET
          driver_type = #{driverType},
          control_code = #{controlCode},
          time_id = #{timeId},
          modify_time = #{modifyTime}
        WHERE entity_id=#{entityId} and entity_type = #{entityType}
    </update>
    <delete id="deleteByEntity">
        delete from driver_block where driver_type=#{driverType} and entity_type=#{entityType} and entity_id =#{entityId}
    </delete>
    <select id="getByEntity" resultType="com.tipray.dlp.bean.DriverBlock">
        SELECT
        id, driver_type driverType, control_code controlCode, time_id timeId, entity_type entityType, entity_id entityId,
        create_time createTime,modify_time modifyTime
        FROM driver_block
        where entity_id=#{entityId} and entity_type = #{entityType}
        and driver_type = #{driverType}
    </select>
</mapper>
