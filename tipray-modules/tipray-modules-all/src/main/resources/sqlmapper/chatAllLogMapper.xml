<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ChatAllLogDao">

    <resultMap type="com.tipray.dlp.bean.ChatAllLog" id="ChatAllLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="senderAccount" column="sender_account"/>
        <result property="senderName" column="sender_name"/>
        <result property="chatSessionInfo" column="chat_session_info"/>
        <result property="chatSessionAccount" column="chat_session_account"/>
        <result property="localUserAccount" column="local_user_account"/>
        <result property="localUserName" column="local_user_name"/>
        <result property="chatType" column="chat_type"/>
        <result property="msgType" column="msg_type"/>
        <result property="msgText" column="msg_text"/>
        <result property="fileSize" column="file_size"/>
        <result property="localFilePath" column="local_file_path"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="actionType" column="action_type"/>
        <result property="actionType" column="action_type"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt;= #{endDate}
            </if>
            <if test="keyword != null and keyword != ''">
                and msg_text like CONCAT('%',#{keyword},'%')
            </if>
            <if test="chatSessionInfo != null and chatSessionInfo != ''">
                and chat_session_info = #{chatSessionInfo}
            </if>
            <if test="chatSessionAccount != null"><!--可能存在空值-->
                and chat_session_account = #{chatSessionAccount}
            </if>
            <if test="chatType != null">
                and chat_type = #{chatType}
            </if>
            <if test="localUserAccount != null"><!--可能存在空值-->
                and local_user_account = #{localUserAccount}
            </if>
            <if test="localUserName != null and localUserName != ''">
                and local_user_name = #{localUserName}
            </if>
            <if test="msgType != null">
                and msg_type = #{msgType}
            </if>
            <if test="actionType != null">
                and action_type = #{actionType}
            </if>
            <if test="msgTypes != null and msgTypes != ''">
                and msg_type in (${msgTypes})
            </if>
        </where>
    </sql>



    <select id="listByVO" resultMap="ChatAllLogMap">
        select id, user_id, term_id, create_time, sender_account, sender_name, chat_session_info,chat_session_account,local_user_account,local_user_name,
        chat_type,msg_type,msg_text,file_size,local_file_path,backup_server_id,upload_file_guid,term_group_id,user_group_id,action_type
        from chat_msg_log a
        <include refid="sql_where"></include>
    </select>
    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from chat_msg_log a
        <include refid="sql_where"></include>
    </select>

    <select id="getChatSessionInfo" resultMap="ChatAllLogMap">
        select term_id,chat_type,local_user_account,chat_session_info,chat_session_account,local_user_name,max(term_group_id) term_group_id,max(user_group_id) user_group_id
        from chat_msg_log a
        <include refid="sql_where"></include>
        and term_id = #{termId}
        group by term_id,chat_type,local_user_account,chat_session_info,chat_session_account,local_user_name
    </select>


    <select id="getChatTypeInfo" resultType="java.lang.Integer">
        select chat_type from chat_msg_log a
        <include refid="sql_where"></include>
        group by chat_type
    </select>
    <select id="getTerminalInfo" resultType="java.lang.Long">
        select term_id from chat_msg_log a
        <include refid="sql_where"></include>
        <if test="notInKey != null and notInKey != ''">
            and term_id not in (${notInKey})
        </if>
        group by term_id
    </select>
    <select id="getAccountInfo" resultType="com.tipray.dlp.bean.ChatAllLog">
        select local_user_account,local_user_name,max(create_time) create_time from chat_msg_log a
        <include refid="sql_where"></include>
        and term_id = #{termId}
        <if test="notInKey != null and notInKey != '' or notInName != null and notInName != ''">
            and (
            <if test="notInKey != null and notInKey != ''">
                local_user_account not in (${notInKey})
            </if>
            <if test="notInKey != null and notInKey != '' and notInName != null and notInName != ''">
                or
            </if>
            <if test="notInName != null and notInName != ''">
                local_user_account = '' and local_user_name not in (${notInName})
            </if>
            )
        </if>
        group by local_user_account, local_user_name
    </select>
    <select id="getSessionInfo" resultType="com.tipray.dlp.bean.ChatAllLog">
        select chat_session_account,chat_session_info,max(create_time) create_time from chat_msg_log a
        <include refid="sql_where"></include>
        and term_id = #{termId}
        <if test="notInKey != null and notInKey != '' or notInName != null and notInName != ''">
            and (
            <if test="notInKey != null and notInKey != ''">
                chat_session_account not in (${notInKey})
            </if>
            <if test="notInKey != null and notInKey != '' and notInName != null and notInName != ''">
                or
            </if>
            <if test="notInName != null and notInName != ''">
                chat_session_account = '' and chat_session_info not in (${notInName})
            </if>
            )
        </if>
        group by chat_session_account,chat_session_info
    </select>

</mapper>
