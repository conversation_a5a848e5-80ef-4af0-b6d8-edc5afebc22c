<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ChatToolDownloadLogDao">

    <resultMap type="com.tipray.dlp.bean.ChatToolDownloadLog" id="ChatToolDownloadLogMap">
        <result property="id" column="id"/>
        <result property="recordNum" column="record_num"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="processName" column="process_name"/>
        <result property="accountId" column="account_id"/>
        <result property="accountNick" column="account_nick"/>
        <result property="conversationTitle" column="conversation_title"/>
        <result property="softType" column="soft_type"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileName" column="file_name"/>
        <result property="localFilePath" column="local_file_path"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="actionType" column="action_type"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="chatType != null">
                and soft_type = #{chatType}
            </if>
            <if test="actionType != null">
                and action_type = #{actionType}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from chattool_download_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="ChatToolDownloadLogMap">
        select id, record_num, term_id, user_id, create_time, soft_type, process_name, account_id, account_nick, conversation_title, file_size, file_name, local_file_path, backup_server_id, upload_file_guid, term_group_id, user_group_id,action_type
        from chattool_download_log a
        <include refid="sql_where"></include>
    </select>

</mapper>
