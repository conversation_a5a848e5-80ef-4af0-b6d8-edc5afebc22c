<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.UserRoleDao">

    <resultMap type="com.tipray.dlp.bean.UserRole" id="resultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="remark" column="remark"/>
        <result property="groupId" column="group_id"/>
        <result property="nameKey" column="name_key"/>
    </resultMap>

    <sql id="vo_where_sql">
      <where>
        <if test="groupId != null and groupId != 0">
            and group_id = #{groupId}
        </if>
        <if test="searchInfo != null and searchInfo != ''">
            and name like CONCAT('%',#{searchInfo},'%')
        </if>
          <if test="ids != null and ids != ''">
              and id in (${ids})
          </if>
        <include refid="commonSQL.andNotDel"></include>
      </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.UserRoleVO" resultMap="resultMap">
        select id, name, remark, group_id, modify_time, name_key from user_role
        <include refid="vo_where_sql"/>
    </select>

</mapper>
