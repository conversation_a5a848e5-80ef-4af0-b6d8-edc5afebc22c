<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.BrowserDownloadLogDao">

    <resultMap type="com.tipray.dlp.bean.BrowserDownloadLog" id="BrowserDownloadLogMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="browserType" column="browser_type"/>
        <result property="needBackup" column="need_backup"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileName" column="file_name"/>
        <result property="localFilePath" column="local_file_path"/>
        <result property="filePath" column="file_path"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="actionType" column="action_type"/>
        <result property="browserUrl" column="browser_url"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="browserType != null and browserType != ''">
                and browser_type = #{browserType}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name like CONCAT('%',#{fileName},'%')
            </if>
            <if test="actionType != null">
                and action_type = #{actionType}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from browser_download_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="BrowserDownloadLogMap">
        select id,term_id,user_id,create_time, browser_type,need_backup, file_size, file_name,local_file_path, file_path, backup_server_id, upload_file_guid, term_group_id, user_group_id,action_type, browser_url
        from browser_download_log a
        <include refid="sql_where"></include>
    </select>

</mapper>
