<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.UrlDao">

    <resultMap type="com.tipray.dlp.bean.Url" id="UrlMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="address" column="address"/>
        <result property="remark" column="remark"/>
        <result property="groupId" column="group_id"/>
    </resultMap>

    <sql id="vo_where_sql">
      <where>
          <include refid="commonSQL.andNotDel"></include>
          <if test="groupId != null and groupId != 0">
              and group_id = #{groupId}
          </if>
          <if test="searchInfo != null and searchInfo != ''">
              and (name like CONCAT('%',#{searchInfo},'%') or address like CONCAT('%',#{searchInfo},'%'))
          </if>
          <if test="ids != null and ids != ''">
              and id in (${ids})
          </if>
      </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.UrlVO" resultMap="UrlMap">
        select * from url
        <include refid="vo_where_sql"/>
    </select>

</mapper>
