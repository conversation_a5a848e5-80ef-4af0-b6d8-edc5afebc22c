<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ClipboardLogDao">

    <resultMap type="com.tipray.dlp.bean.ClipboardLog" id="ClipboardLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="clipboardDataSize" column="clipboard_data_size"/>
        <result property="clipboardDataStream" column="clipboard_data_stream"/>
        <result property="srcProcName" column="src_proc_name"/>
        <result property="dstProcName" column="dst_proc_name"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="clipboardDataStream != null and clipboardDataStream != ''">
                and clipboard_data_stream like CONCAT('%',#{clipboardDataStream},'%')
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and (src_proc_name like CONCAT('%',#{searchInfo},'%') or dst_proc_name like CONCAT('%',#{searchInfo},'%'))
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from clipboard_info
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="ClipboardLogMap">
        select *
        from clipboard_info
        <include refid="sql_where"></include>
    </select>

</mapper>
