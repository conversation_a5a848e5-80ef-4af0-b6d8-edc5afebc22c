<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ChatLoginLogDao">

    <resultMap type="com.tipray.dlp.bean.ChatLoginLog" id="UrlsSearchMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="chatType" column="chat_type"/>
        <result property="loginAccount" column="login_account"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="chatType != null and chatType != ''">
                and chat_type = #{chatType}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from chat_login_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="UrlsSearchMap">
        select id, user_id, term_id, create_time, chat_type, login_account, term_group_id, user_group_id
        from chat_login_log a
        <include refid="sql_where"></include>
    </select>

</mapper>
