<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.AssetChangeDao">

    <resultMap type="com.tipray.dlp.bean.AssetChange" id="resultMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="devId" column="server_id"/>
        <result property="assetId" column="asset_id"/>
        <result property="assetType" column="asset_type"/>
        <result property="modifyVer" column="modify_ver"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <if test="assetType != null">
                and asset_type = #{assetType}
            </if>
            <if test="modifyVer != null">
                and modify_ver &gt; #{modifyVer}
            </if>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
        </where>
    </sql>

    <select id="getMaxModifyVer" parameterType="java.lang.Integer" resultType="java.lang.Long">
        select max(modify_ver) from asset_change where asset_type = #{value}
    </select>

    <select id="getMaxTimestamp" parameterType="java.lang.Integer" resultType="java.lang.Long">
        select max(timestamp) from asset_change where asset_type = #{value}
        group by asset_type
    </select>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.AssetChangeVO" resultType="java.lang.Long">
        select count(id) from asset_change
        <include refid="vo_where_sql"/>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.AssetChangeVO" resultMap="resultMap">
        select id, term_id, asset_id, modify_ver from asset_change
        <include refid="vo_where_sql"/>
    </select>

</mapper>