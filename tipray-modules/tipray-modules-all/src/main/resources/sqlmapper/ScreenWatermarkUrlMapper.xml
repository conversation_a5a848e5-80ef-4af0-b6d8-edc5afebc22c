<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ScreenWatermarkUrlDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.ScreenWatermarkUrl">
        <id property="id" column="id"/>
        <result property="urlKeyword" column="url_keyword"/>
        <result property="urlRemark" column="url_remark"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <if test="searchInfo != null and searchInfo != ''">
                url_keyword like CONCAT('%',#{searchInfo},'%')
            </if>
        </where>
    </sql>

    <select id="selectPageByVO" resultMap="resultMap">
        select * from screen_watermark_url
        <include refid="sql_where"></include>
    </select>
</mapper>