<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.TelnetMonitorLogDao">

    <resultMap type="com.tipray.dlp.bean.TelnetMonitorLog" id="TelnetMonitorLogMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="cmdType" column="cmd_type"/>
        <result property="ipArr" column="ip_arr"/>
        <result property="cmdData" column="cmd_data"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="loginUser" column="login_user"/>
        <result property="actionType" column="action_type"/>
        <result property="processName" column="process_name"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="ipArr != null and ipArr != ''">
                and ip_arr like CONCAT('%',#{ipArr},'%')
            </if>
            <if test="cmdData != null and cmdData != ''">
                and cmd_data like CONCAT('%',#{cmdData},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.TelnetLogVO" resultType="java.lang.Long">
      select count(0) from telnet_monitor_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.TelnetLogVO" resultMap="TelnetMonitorLogMap">
      select id, term_id, user_id, create_time, cmd_type, ip_arr, cmd_data, term_group_id, user_group_id, login_user, action_type, process_name from telnet_monitor_log
        <include refid="sql_where"></include>
    </select>

</mapper>
