<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ComputerAppLogDao">

    <resultMap type="com.tipray.dlp.bean.ComputerAppLog" id="ComputerAppLogMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="sysLogSrc" column="sys_log_src"/>
        <result property="eventId" column="event_id"/>
        <result property="recordNum" column="record_num"/>
        <result property="recordId" column="record_id"/>
        <result property="logType" column="log_type"/>
        <result property="logLevel" column="log_level"/>
        <result property="logDesc" column="log_desc"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time >= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="logLevel != null">
                and log_level = #{logLevel}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.SystemLogVo" resultType="java.lang.Long">
        select count(0) from computer_app_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.SystemLogVo" resultMap="ComputerAppLogMap">
        select id, record_num, term_id, user_id, create_time, event_id, record_id, log_type, log_level, sys_log_src, log_desc, term_group_id, user_group_id
        from computer_app_log
        <include refid="sql_where"></include>
    </select>

</mapper>
