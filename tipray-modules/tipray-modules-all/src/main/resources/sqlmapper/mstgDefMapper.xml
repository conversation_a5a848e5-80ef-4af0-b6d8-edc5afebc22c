<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.MstgDefDao">

    <resultMap type="com.tipray.dlp.bean.MstgDef" id="MstgDefMap">
        <result property="id" column="id"/>
        <result property="stgTypeNumber" column="stg_type_number"/>
        <result property="name" column="name"/>
        <result property="stgInfo" column="stg_info"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="active" column="active"/>
        <result property="remark" column="remark"/>
        <result property="osType" column="os_type"/>
        <result property="deleted" column="deleted"/>
    </resultMap>
    <sql id="select_field">
        d.id, d.name, d.stg_type_number, d.stg_info, d.active, d.remark, d.os_type, d.create_time, d.modify_time
    </sql>

    <sql id="stg_obj_table_out">
        (
        select object_type, object_id, mstg_def_id from mstg_obj_def
        )
    </sql>

    <sql id="vo_common_where_sql">
        <if test="strategyId != null">
            and d.id = #{strategyId}
        </if>
        <if test="strategyIds != null and strategyIds != ''">
            and d.id in (${strategyIds})
        </if>
        <if test="stgTypeNumbers != null and stgTypeNumbers != ''">
            and d.stg_type_number in (${stgTypeNumbers})
        </if>
        <if test="stgTypeNumber != null">
            and d.stg_type_number = #{stgTypeNumber}
        </if>
        <if test="searchInfo != null and searchInfo != ''">
            and d.name like CONCAT('%',#{searchInfo},'%')
        </if>
        <if test="equalsRemark != null and equalsRemark != ''">
            and d.remark = #{equalsRemark}
        </if>
        <if test="likeRemark != null and likeRemark != ''">
            and d.remark like CONCAT('%',#{likeRemark},'%')
        </if>
        <if test="remark != null and remark != ''">
            and d.remark in (${remark})
        </if>
        <if test="active != null">
            and d.active = #{active}
        </if>
        <if test="osType != null">
            and d.os_type = #{osType}
        </if>
        and deleted = 0
    </sql>

    <sql id="vo_where_sql">
        <where>
            <if test="notNullObjectId != null and notNullObjectId">
                and o.object_id > -1
            </if>
            <if test="objectMap != null">
                and (
                <foreach collection="objectMap.entrySet()" separator="or" index="key" item="val">
                    o.object_type = #{key} and o.object_id in (${val})
                </foreach>
                )
            </if>
            <if test="objectType != null and objectId != null">
                and (
                  o.object_type = #{objectType} and o.object_id = #{objectId}
                <if test="parentObjectType != null and parentObjectIds != null and parentObjectIds != ''">
                  or (o.object_type = #{parentObjectType} and o.object_id in (${parentObjectIds}) and d.active = true)
                </if>
                )
            </if>
            <if test="objectType != null and objectIds != null and objectIds != ''">
                and o.object_type = #{objectType} and o.object_id in (${objectIds})
            </if>
            <if test="permissions != null">
                and ( o.object_id is null or
                    <foreach collection="permissions" separator="or" item="obj">
                        ( o.object_type = #{obj.objectType} and o.object_id in (${obj.objectIds}) )
                    </foreach>
                )
            </if>
            <if test="strategyDefType != null">
                and (strategy_def_type = #{strategyDefType}
                    <if test="includeActivePre != null and includeActivePre">
                        or o.stg_group_id > 0 and o.active = true
                    </if>
                )
            </if>
            <include refid="vo_common_where_sql"/>
        </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.StrategyVO" resultMap="MstgDefMap">
        select <include refid="select_field"/>
        from mstg_def d left join <include refid="stg_obj_table_out"/> o
        on d.id = o.mstg_def_id
        <include refid="vo_where_sql"></include>
    </select>
    <select id="selectPageByVOWithoutObject" parameterType="com.tipray.dlp.bean.vo.StrategyVO" resultMap="MstgDefMap">
        select <include refid="select_field"/>
        from mstg_def d
        <where>
            <include refid="vo_common_where_sql"/>
        </where>
    </select>

    <delete id="deleteByIds">
        delete from mstg_def where id in (@ids)
    </delete>
</mapper>
