<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.WifiDao">

    <resultMap type="com.tipray.dlp.bean.Wifi" id="resultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="macAddress" column="mac_address"/>
        <result property="remark" column="remark"/>
        <result property="groupId" column="group_id"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="macAddress != null and macAddress != ''">
                and mac_address = #{macAddress}
            </if>
            <if test="groupId != null and groupId != 0">
                and group_id = #{groupId}
            </if>
            <if test="groupIds != null and groupIds != ''">
                and group_id in (${groupIds})
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and (name like CONCAT('%', #{searchInfo}, '%') or mac_address like CONCAT('%', #{searchInfo}, '%'))
            </if>
            <if test="ids != null and ids != ''">
                and id in (${ids})
            </if>
        </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.WifiVO" resultMap="resultMap">
        select * from wifi_lib
        <include refid="vo_where_sql"/>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.WifiVO" resultMap="resultMap">
        select * from wifi_lib
        <include refid="vo_where_sql"/>
    </select>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.WifiVO" resultType="java.lang.Long">
        select count(0) from wifi_lib
        <include refid="vo_where_sql"/>
    </select>

</mapper>
