<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.AlarmMsgDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.AlarmMsg">
        <id column="id" property="id"/>
        <result column="computer_name" property="computerName"/>
        <result column="user_name" property="userName"/>
        <result column="term_id" property="terminalId"/>
        <result column="user_id" property="userId"/>
        <result column="create_time" property="createTime"/>
        <result column="event_guid" property="guid"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="alarm_limit" property="alarmLimit"/>
        <result column="alarm_bus" property="alarmBus"/>
        <result column="action" property="action"/>
        <result column="sst_type" property="sstType"/>
        <result column="file_path" property="filePath"/>
        <result column="event_desc" property="eventDesc"/>
        <result column="event_param" property="eventParam"/>
        <result column="hit_stg" property="hitStg"/>
        <result column="content" property="content"/>
        <result column="stg_id" property="ruleId"/>
        <result column="deal_status" property="dealStatus"/>
        <result column="is_complete" property="complete"/>
        <result column="task_guid" property="taskGuid"/>
    </resultMap>

    <sql id="full_fields_sql">
        id, computer_name, user_name, term_id, user_id, create_time, event_guid, alarm_type, alarm_level, alarm_limit, alarm_bus, action, sst_type, file_path, event_desc, event_param, hit_stg, content, stg_id, deal_status, is_complete, task_guid
    </sql>

    <sql id="vo_where_sql">
        <where>
            <if test="alarmLimit != null">
                <![CDATA[ (alarm_limit & ${alarmLimit}) = ${alarmLimit}  and (deal_status & ${alarmLimit}) = 0 ]]>
            </if>
            <if test="ruleId != null and ruleId != ''">
                and (
                    sst_type = 0 and stg_id in (${ruleId})
                    <if test="sstRuleId != null and sstRuleId != ''">
                        or sst_type > 0 and stg_id in (${sstRuleId})
                    </if>
                )
            </if>
            <if test="deleted != null">
                and deleted = ${deleted}
            </if>
            <if test="taskGuid != null and taskGuid != ''">
                and task_guid = #{taskGuid}
            </if>
            <if test="complete != null and complete > 0">
                and (is_complete = ${complete} or task_guid is null)
            </if>
            <if test="complete != null and complete == 0">
                and is_complete = ${complete} and task_guid is not null
            </if>
        </where>
    </sql>

    <sql id="alarm_where_sql">
        <where>
            <![CDATA[ (alarm_limit & ${alarmLimit}) = ${alarmLimit}  and (deal_status & ${alarmLimit}) = 0 and deleted = 0 ]]>
            <if test="ids != null and ids != ''">
                and id in (${ids})
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.AlarmLogVO" resultType="java.lang.Long">
        select count(0) from ins_alarm_msg
        <include refid="vo_where_sql"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.AlarmLogVO" resultMap="resultMap">
        select <include refid="full_fields_sql"></include>
        from ins_alarm_msg
        <include refid="vo_where_sql"></include>
    </select>

    <select id="listIdsByVO" parameterType="com.tipray.dlp.bean.vo.AlarmLogVO"  resultMap="resultMap">
        select <include refid="full_fields_sql"></include>
        from ins_alarm_msg
        <include refid="vo_where_sql"></include>
    </select>

    <select id="list" parameterType="com.tipray.dlp.bean.vo.AlarmLogVO" resultMap="resultMap">
        select <include refid="full_fields_sql"></include>
        from ins_alarm_msg <include refid="alarm_where_sql"></include>
	</select>

    <select id="listByRuleId" resultMap="resultMap">
        <![CDATA[ select id,alarm_limit,deal_status from ins_alarm_msg where stg_id in (${ruleIds})  and (alarm_limit & ${dealStatus}) = ${dealStatus}  and (deal_status & ${dealStatus}) = 0 and deleted = 0 ]]>
	</select>

    <select id="countAlarmMsgList" parameterType="com.tipray.dlp.bean.vo.AlarmLogVO" resultType="java.lang.Long">
        select count(0) from ins_alarm_msg where sst_type = #{sstType} and task_guid = #{taskGuid} and is_complete = 0
	</select>

    <select id="getAlarmMsgList" parameterType="com.tipray.dlp.bean.vo.AlarmLogVO" resultMap="resultMap">
        select <include refid="full_fields_sql"></include>
        from ins_alarm_msg where sst_type = #{sstType} and task_guid = #{taskGuid} and is_complete = 0
	</select>

    <update id="updateDealStatus">
        <![CDATA[ update ins_alarm_msg set deal_status = deal_status + #{dealStatus} where id in (${ids}) and (deal_status & ${dealStatus}) = 0 ]]>
    </update>

    <delete id="deleteByIds" parameterType="java.lang.String">
        delete from ins_alarm_msg where id in (${value})
    </delete>

    <update id="fakeDeleteByIds" parameterType="java.lang.String">
        update ins_alarm_msg set deleted = 1 where id in (${value})
    </update>

    <delete id="deleteBeforeDate" parameterType="java.util.Date">
        <![CDATA[ delete from ins_alarm_msg where create_time < #{date} or create_time < #{deletedDate} and deleted = 1 ]]>
    </delete>

    <select id="listEndMarkAlarmMsg" parameterType="java.lang.String"  resultMap="resultMap">
        select <include refid="full_fields_sql"></include>
        from ins_alarm_msg where task_guid in (${taskGuids}) and is_complete != 0
    </select>

    <select id="listAlarmMsg" resultMap="resultMap">
        select <include refid="full_fields_sql"></include>
        from ins_alarm_msg where sst_type = #{sstType} and task_guid = #{taskGuid}
    </select>

</mapper>
