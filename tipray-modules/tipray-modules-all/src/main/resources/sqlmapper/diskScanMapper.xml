<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DiskScanDao">

    <resultMap type="com.tipray.dlp.bean.DiskScan" id="baseResultMap">
        <result property="id" column="id"/>
        <result property="defId" column="def_id"/>
        <result property="opType" column="op_type"/>
        <result property="scanDir" column="scan_dir"/>
        <result property="exceptDir" column="except_dir"/>
        <result property="suffix" column="suffix"/>
        <result property="penetrateSuffix" column="penetrate_suffix"/>
        <result property="scanMode" column="scan_mode"/>
        <result property="cpuMem" column="cpu_mem"/>
        <result property="endDate" column="end_date"/>
        <result property="status" column="status"/>
        <result property="autoShutdown" column="auto_shutdown"/>
        <result property="name" column="name"/>
        <result property="remark" column="remark"/>
        <result property="contentStgId" column="content_stg_id"/>
        <result property="guid" column="guid"/>
        <result property="startTime" column="start_time"/>
        <result property="stopScanTermSize" column="stop_scan_term_size"/>
        <result property="osType" column="os_type"/>
        <result property="showProgress" column="show_progress"/>
    </resultMap>

    <select id="listByContentStgId" parameterType="java.lang.Long" resultMap="baseResultMap">
        select * from disk_scan_def where content_stg_id = #{value}
    </select>
    <select id="countByContentStgId" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(0) from disk_scan_def where content_stg_id in (@contentStgId) and op_type > 2
    </select>

    <select id="getById" parameterType="java.lang.Long" resultMap="baseResultMap">
        select * from disk_scan_def where id=#{value}
    </select>

    <select id="listById" parameterType="java.lang.String" resultMap="baseResultMap">
        select * from disk_scan_def where id in (@ids)
    </select>

    <select id="listByName" parameterType="java.lang.String" resultMap="baseResultMap">
        select dt.*,d.* from disk_scan_def d
        LEFT JOIN
        (select t.def_id,t.guid,t.status,t.stop_scan_term_size from disk_scan_task t where t.id = (select max(id) from disk_scan_task where t.def_id = def_id)) dt
        on dt.def_id = d.id
        where d.name=#{value}
    </select>
    <sql id="vo_where_sql">
        <where>
            <if test="objectType != null and objectId != null">
                and (
                o.object_type = #{objectType} and o.object_id = #{objectId}
                <if test="parentObjectType != null and parentObjectIds != null and parentObjectIds != ''">
                    or (o.object_type = #{parentObjectType} and o.object_id in (@parentObjectIds) and dt.status = 0)
                </if>
                )
            </if>
            <if test="objectType != null and objectIds != null and objectIds != ''">
                and o.object_type = #{objectType} and o.object_id in (@objectIds)
            </if>
            <if test="permissions != null">
                and
                <foreach collection="permissions" separator="or" open="(" close=")" item="obj">
                    ( o.object_type = #{obj.objectType} and o.object_id in (${obj.objectIds}) )
                </foreach>
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and d.name like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="strategyIds!=null and strategyIds!= ''">
                and dt.guid = #{strategyIds}
            </if>
        </where>
    </sql>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.StrategyVO" resultMap="baseResultMap">
        select distinct dt.*,d.* from disk_scan_def d
        left join stg_special_obj o on d.id = o.stg_id and 95 = o.stg_code
        LEFT JOIN
        (select t.def_id,t.guid,t.status,t.stop_scan_term_size from disk_scan_task t where t.id = (select max(id) from disk_scan_task where t.def_id = def_id)) dt
        on dt.def_id = d.id
        <include refid="vo_where_sql"></include>
    </select>
</mapper>

