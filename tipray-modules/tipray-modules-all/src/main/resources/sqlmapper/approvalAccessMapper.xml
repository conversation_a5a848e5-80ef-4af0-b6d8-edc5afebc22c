<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ApprovalAccessDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.ApprovalAccess">
        <id column="id" property="id" />
        <result column="computer_name" property="computerName"/>
        <result column="term_id" property="termId"/>
        <result column="term_type" property="termType"/>
        <result column="group_id" property="groupId"/>
        <result column="auto_login_user_id" property="userId"/>
        <result column="dept_name" property="deptName"/>
        <!--<result column="user_name" property="userName"/>-->
        <result column="term_name" property="termName"/>
        <result column="version" property="version"/>
        <result column="guid" property="guid"/>
        <result column="mcode" property="mcode"/>
        <result column="main_ip" property="mainIp"/>
        <result column="main_mac" property="mainMac"/>
        <result column="ip_list" property="ips"/>
        <result column="ipv6_list" property="ipv6s"/>
        <result column="mac_list" property="macs"/>
        <result column="result" property="result"/>
        <result column="create_time" property="createTime"/>
        <result column="phone" property="phone"/>
        <result column="install_code" property="installCode"/>
    </resultMap>

    <update id="update">
        update approval_access set
        <if test="groupId != null ">
            group_id=#{groupId},
        </if>
        <if test="userId != null ">
            auto_login_user_id=#{userId},
        </if>
        <if test="deptName != null and deptName != '' ">
            dept_name=#{deptName},
        </if>
        <if test="termName != null and termName != '' ">
            term_name=#{termName},
        </if>
        <if test="termId != null ">
            term_id=#{termId},
        </if>
        result=#{result}
        where id = #{id}
    </update>

    <update id="updateByVO" parameterType="com.tipray.dlp.bean.vo.ApprovalAccessVO">
        update approval_access set
        <if test="groupId != null ">
            group_id=#{groupId},
        </if>
        <if test="userId != null ">
            auto_login_user_id=#{userId},
        </if>
        <!--<if test="userName != null and userName != '' ">
            user_name=#{userName},
        </if>-->
        <if test="deptName != null and deptName != '' ">
            dept_name=#{deptName},
        </if>
        <if test="termName != null and termName != '' ">
            term_name=#{termName},
        </if>
        <if test="termId != null ">
            term_id=#{termId},
        </if>
        result=#{result}
        where id in (${ids})
    </update>

    <sql id="select_where_vo">
        <if test="result != null ">
            and result = #{result}
        </if>
        <if test="termTypes != null and termTypes != '' ">
            and term_type in (${termTypes})
        </if>
        <if test="installCodes != null and installCodes != ''">
            and install_code in (${installCodes})
        </if>
        <if test="computerName != null and computerName != '' ">
            and computer_name like CONCAT('%',#{computerName},'%')
        </if>
        <if test="deptName != null and deptName != '' ">
            and dept_name like CONCAT('%',#{deptName},'%')
        </if>
        <if test="termName != null and termName != '' ">
            and term_name like CONCAT('%',#{termName},'%')
        </if>
        <if test="startDate != null and endDate != null">
            and create_time >= #{startDate} and create_time &lt; #{endDate}
        </if>
        <if test="guid != null and guid != ''">
            and guid like CONCAT('%',#{guid},'%')
        </if>
        <if test="version != null and version != ''">
            and version like CONCAT('%',#{version},'%')
        </if>
        <if test="ips != null and ips != '' ">
            and (ip_list like CONCAT('%',#{ips},'%') or ipv6_list like CONCAT('%',#{ips},'%'))
        </if>
        <if test="macs != null and macs != ''">
            and mac_list like CONCAT('%',#{macs},'%')
        </if>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.ApprovalAccessVO" resultType="java.lang.Long">
        select count(0) from approval_access where 1= 1
        <include refid="select_where_vo"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.ApprovalAccessVO" resultMap="resultMap">
        select * from approval_access where 1=1
        <include refid="select_where_vo"></include>
    </select>

    <select id="listTermName" resultType="java.lang.String">
        select term_name from approval_access
    </select>

    <delete id="deleteAll">
        delete from approval_access
    </delete>

</mapper>
