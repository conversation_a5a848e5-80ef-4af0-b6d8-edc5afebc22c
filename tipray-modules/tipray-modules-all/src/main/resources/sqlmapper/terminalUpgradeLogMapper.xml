<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.TerminalUpgradeLogDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.TerminalUpgradeLog">
        <id column="id" property="id" />
        <result property="terminalId" column="term_id"/>
        <result property="packageType" column="package_type"/>
        <result property="operationType" column="operation_type"/>
        <result property="packageVer" column="package_ver"/>
        <result property="terminalVer" column="terminal_ver"/>
        <result property="fileGuid" column="file_guid"/>
        <result property="fileName" column="file_name"/>
        <result property="taskGuid" column="task_guid"/>
        <result property="step" column="step"/>
        <result property="result" column="result"/>
        <result property="createTime" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="taskGuid != null and taskGuid != ''">
                and task_guid = #{taskGuid}
            </if>
            <if test="step != null and step != ''">
                and step = #{step}
            </if>
            <if test="result != null and result != ''">
                and result = #{result}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from terminal_upgrade_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="resultMap">
        select id, term_id, package_type, package_ver, terminal_ver, file_guid, file_name, task_guid, step, result, create_time, term_group_id from terminal_upgrade_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByTaskGuid" resultMap="resultMap">
        select a.id, a.term_id, a.package_type, a.operation_type, a.package_ver, a.terminal_ver, a.file_guid, a.file_name, a.task_guid, a.step, a.result, a.create_time, a.term_group_id
        from terminal_upgrade_log a
        JOIN (select max(id) id, max(create_time) create_time from terminal_upgrade_log where task_guid = #{taskGuid}) b
        on a.id = b.id where a.task_guid = #{taskGuid}
    </select>

    <select id="listNewestStatusByTermId" resultMap="resultMap">
        select a.id, a.term_id, a.package_type, a.operation_type, a.package_ver, a.terminal_ver, a.file_guid, a.file_name, a.task_guid, a.step, a.result, a.create_time, a.term_group_id
        from terminal_upgrade_log a
        join (select max(id) id from terminal_upgrade_log where term_id in (@termIdList) group by term_id) b
        on a.id = b.id
        <where>
            <if test="guidList != null">
                a.task_guid in (@guidList)
            </if>
        </where>
    </select>
</mapper>
