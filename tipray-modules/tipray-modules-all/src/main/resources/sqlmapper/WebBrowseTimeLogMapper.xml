<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.WebBrowseTimeLogDao">
    <resultMap type="com.tipray.dlp.bean.WebBrowseTimeLog" id="resultMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="host" column="host"/>
        <result property="totalTime" column="total_time"/>
        <result property="createTime" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="accessTime" column="access_time"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <if test="hostGroupId != null">
                and host in (@hostList)
            </if>
            <if test="host != null and host != ''">
                and host like CONCAT('%',#{host},'%')
            </if>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
        </where>
    </sql>

    <!--<select id="countByVO" resultType="java.lang.Long">
        select count(1) from (
            select term_id, user_id from web_browsing_time
            <include refid="sql_where"></include>
            group by term_id, user_id
        ) t
    </select>-->

    <select id="listByVO" resultMap="resultMap">
        select max(id) id, user_id, term_id, host, SUM(total_time) total_time, max(create_time) create_time, min(access_time) access_time,
        DATE_FORMAT(create_time, '%Y-%m-%d') as createTimeFormat
        from web_browsing_time
        <include refid="sql_where"></include>
        group by term_id, user_id, host, createTimeFormat
    </select>

</mapper>
