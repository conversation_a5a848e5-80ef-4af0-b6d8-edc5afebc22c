<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.OutgoingMachineCodeDao">

    <resultMap type="com.tipray.dlp.bean.OutgoingMachineCode" id="OutgoingMachineCodeMap">
        <result property="id" column="id"/>
        <result property="machineCode" column="machine_code" />
        <result property="customer" column="customer"/>
        <result column="remark" property="remark"/>
        <result property="classId" column="class_id"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="where_sql">
        <if test="classId != null">
            and class_id = #{classId}
        </if>
        <if test="machineCode != null and machineCode != ''">
            and machine_code like CONCAT('%',#{machineCode},'%')
        </if>
        <if test="customer != null and customer != ''">
            and customer like CONCAT('%',#{customer},'%')
        </if>
        <if test="ids != null and ids != ''">
            and id in (${ids})
        </if>
        <if test="classIds != null and classIds != ''">
            and class_id in (${classIds})
        </if>
        <if test="searchInfo != null and searchInfo != ''">
            and (machine_code like CONCAT('%',#{searchInfo},'%') or customer like CONCAT('%',#{searchInfo},'%'))
        </if>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from outgoing_machine_code
        <where>
            <include refid="where_sql"/>
        </where>
    </select>

    <select id="list" resultMap="OutgoingMachineCodeMap">
        select * from outgoing_machine_code where 1=1
        <include refid="where_sql"/>
    </select>

    <select id="listByVO" resultMap="OutgoingMachineCodeMap">
        select *
        from outgoing_machine_code
        <where>
            <include refid="where_sql"/>
        </where>
    </select>

    <delete id="deleteByIds">
        delete from outgoing_machine_code where id in (${value})
    </delete>

</mapper>
