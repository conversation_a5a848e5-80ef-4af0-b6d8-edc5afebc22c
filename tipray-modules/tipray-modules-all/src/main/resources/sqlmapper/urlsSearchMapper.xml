<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.UrlsSearchDao">

    <resultMap type="com.tipray.dlp.bean.UrlsSearch" id="UrlsSearchMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="url" column="url"/>
        <result property="host" column="host"/>
        <result property="webTitle" column="web_title"/>
        <result property="searchWord" column="search_word"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="webTitle != null and webTitle != ''">
                and web_title like CONCAT('%',#{webTitle},'%')
            </if>
            <if test="searchWord != null and searchWord != ''">
                and search_word like CONCAT('%',#{searchWord},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from urls_search a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="UrlsSearchMap">
        select id, user_id, term_id, create_time, url, host, web_title, search_word, term_group_id, user_group_id
        from urls_search a
        <include refid="sql_where"></include>
    </select>

</mapper>
