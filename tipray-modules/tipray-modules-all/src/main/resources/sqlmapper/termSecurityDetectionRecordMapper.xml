<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.TermSecurityDetectionRecordDao">

    <resultMap type="com.tipray.dlp.bean.TermSecurityDetectionRecord" id="TermSecurityDetectionRecordMap">
        <result property="id" column="id"/>
        <result property="recordNum" column="record_num"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="risk" column="risk"/>
        <result property="resultJson" column="result_json"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="timestamp" column="timestamp"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="risk != null">
                and risk = #{risk}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from term_scan_audit
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="TermSecurityDetectionRecordMap">
        select id, user_id, term_id, record_num, create_time, risk, term_group_id, user_group_id, result_json, timestamp
        from term_scan_audit
        <include refid="sql_where"></include>
    </select>

    <select id="getById" resultType="com.tipray.dlp.bean.TermSecurityDetectionRecord">
        select id, user_id, term_id, record_num, create_time, risk, result_json, term_group_id, user_group_id, timestamp
        from term_scan_audit
        <where>
            create_time &gt;= #{startDate} and create_time &lt; #{endDate} and id = #{id} and timestamp = #{timestamp}
            <if test="termIds != null and termIds != ''">
                and term_id in (${termIds})
            </if>
        </where>
    </select>
</mapper>
