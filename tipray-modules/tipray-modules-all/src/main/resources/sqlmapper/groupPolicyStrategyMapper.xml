<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.GroupPolicyStrategyDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.GroupPolicyStg">
        <id column="id" property="id" />
        <result column="name" property="name"/>
        <result column="funcId" property="func_id"/>
        <result column="settingOption" property="setting_option"/>
        <result column="settingValue" property="setting_value"/>
        <result column="regKey" property="reg_key"/>
        <result column="active" property="active"/>
        <result column="sstType" property="sst_type"/>
        <result column="alarmType" property="alarm_Type"/>
        <result column="ruleId" property="rule_id"/>
        <result column="remark" property="remark"/>
        <result column="entityType" property="object_type"/>
        <result column="entityId" property="object_id"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <select id="listByEntityId" resultMap="resultMap">
        select * from group_policy_stg where object_id = #{value}
    </select>

    <select id="getByVO" resultMap="resultMap">
        select * from group_policy_stg where object_id = #{terminalId} and func_id = #{funcId}
    </select>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into group_policy_stg(name, func_id, setting_option, setting_value, reg_key, active, sst_type, alarm_Type, rule_id, remark, object_type, object_id)
        values (#{name}, #{funcId}, #{settingOption}, #{settingValue}, #{regKey}, #{active}, #{sstType}, #{alarmType}, #{ruleId}, #{remark}, #{entityType}, #{entityId})
    </insert>

    <update id="update">
        update group_policy_stg
        <set>
            setting_option = #{settingOption},
            setting_value = #{settingValue},
            reg_key = #{regKey}
            <if test="sstType != null">
                sst_type = #{sstType},
            </if>
            <if test="alarmType != null">
                alarm_Type = #{alarmType},
            </if>
            <if test="ruleId != null">
                rule_id = #{ruleId},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>
