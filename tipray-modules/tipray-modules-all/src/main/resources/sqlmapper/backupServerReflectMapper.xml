<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.BackupServerReflectDao">

    <select id="listServerIds" resultType="java.lang.Long">
        select server_id from ref_backup_server
        where ref_server_id = #{value} and deleted = 0
    </select>

    <select id="getRefServerIdByServerId" resultType="java.lang.Long">
        select ref_server_id from ref_backup_server
        where server_id = #{value} and deleted = 0
    </select>

    <insert id="insert">
        INSERT INTO ref_backup_server(server_id, ref_server_id, resync_enabled, sync_type, sync_type_ex, create_time)
        VALUES (#{serverId}, #{refServerId}, #{resyncEnabled}, #{syncType}, #{syncTypeEx}, #{createTime})
    </insert>

    <update id="updateNoDel">
        update ref_backup_server set deleted = 0,resync_enabled=#{resyncEnabled},sync_type=#{syncType},sync_type_ex=#{syncTypeEx}, create_time = #{createTime}
        where ref_server_id = #{refServerId} and server_id = #{serverId}
    </update>

    <update id="deleteByRefServerId">
        update ref_backup_server set deleted = 1 where ref_server_id = #{value}
    </update>

    <update id="deleteByRefServerIds">
        update ref_backup_server set deleted = 1 where ref_server_id in (${value})
    </update>

    <update id="deleteByServerIds">
        update ref_backup_server set deleted = 1 where server_id in (${value})
    </update>

    <select id="listAllNotDeleted" resultType="java.util.Map">
        select server_id serverId, ref_server_id refServerId from ref_backup_server where deleted = 0
    </select>
    <select id="getMapByServerId" resultType="com.tipray.dlp.bean.BackupServer">
        select server_id devId, ref_server_id refDevId, resync_enabled resyncEnabled, sync_type syncType, sync_type_ex syncTypeEx from ref_backup_server
        where server_id = #{value} and deleted = 0
    </select>
    <select id="listByRefServerId" resultType="com.tipray.dlp.bean.BackupServer">
        select server_id devId, ref_server_id refDevId, resync_enabled resyncEnabled, sync_type syncType, sync_type_ex syncTypeEx from ref_backup_server
        where ref_server_id = #{value} and deleted = 0
    </select>

    <select id="listAllRefServerId" resultType="java.lang.Long">
        select distinct ref_server_id from ref_backup_server where deleted = 0
    </select>

    <select id="listAllServerIds" resultType="java.lang.Long">
        select server_id from ref_backup_server where deleted = 0
    </select>

    <select id="listAllServerIdByRefServerId" resultType="java.lang.Long">
        select server_id from ref_backup_server where ref_server_id = #{value}
    </select>

    <select id="listServerIdByRefServerId" resultType="java.lang.Long">
        select server_id from ref_backup_server where ref_server_id = #{value} and deleted = 0
    </select>

    <select id="listServerIdByRefServerIds" resultType="java.lang.Long">
        select server_id from ref_backup_server where ref_server_id in (${value}) and deleted = 0
    </select>

    <select id="getRelatedCreateTime" resultType="java.util.Date">
        select create_time from ref_backup_server
        where ref_server_id = #{refServerId} and server_id = #{serverId}
    </select>

    <select id="getLastRelatedServerId" resultType="java.lang.Long">
        select ref_server_id from
        (
            select ref_server_id, ABS(#{serverId} - create_time) diffTime
            from ref_backup_server
            where create_time &lt; #{serverId}
            order by diffTime asc
            limit 1
        )
    </select>

</mapper>
