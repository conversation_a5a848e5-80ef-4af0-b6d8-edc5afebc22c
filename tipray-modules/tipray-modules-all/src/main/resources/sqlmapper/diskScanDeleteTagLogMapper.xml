<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DiskScanDeleteTagLogDao">

    <resultMap type="com.tipray.dlp.bean.DiskScanDeleteTagLog" id="DiskScanDeleteTagLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="opType" column="op_type"/>
        <result property="result" column="result"/>
        <result property="guid" column="guid"/>
        <result property="batchNo" column="batch_no"/>
        <result property="runGuid" column="run_guid"/>
        <result property="fileName" column="file_name"/>
        <result property="filePath" column="file_path"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="opType != null and opType != null">
                and op_type = #{opType}
            </if>
            <if test="guid != null and guid != null">
                and guid like CONCAT('%',#{guid},'%')
            </if>
            <if test="result != null and result != ''">
                and result in (${result})
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from diskscan_del_tag_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="DiskScanDeleteTagLogMap">
        select id, user_id,term_id, op_type, file_name, guid, batch_no, run_guid, file_path, result, create_time, term_group_id, user_group_id
        from diskscan_del_tag_log
        <include refid="sql_where"></include>
    </select>

</mapper>
