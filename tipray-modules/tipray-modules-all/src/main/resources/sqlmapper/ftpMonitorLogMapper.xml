<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.FtpMonitorLogDao">

    <resultMap type="com.tipray.dlp.bean.FtpMonitorLog" id="FtpMonitorLogMap">
        <result property="id" column="id"/>
        <result property="recordNum" column="record_num"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="cmdType" column="cmd_type"/>
        <result property="host" column="host"/>
        <result property="fileName" column="file_name"/>
        <result property="fileSize" column="file_size"/>
        <result property="filePath" column="file_path"/>
        <result property="backupFilePath" column="backup_file_path"/>
        <result property="devId" column="backup_server_id"/>
        <result property="serverFilePath" column="server_file_path"/>
        <result property="monitorType" column="monitor_type"/>
        <result property="processName" column="process_name"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="actionType" column="action_type"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name like CONCAT('%',#{fileName},'%')
            </if>
            <if test="cmdType != null">
                and cmd_type = #{cmdType}
            </if>
            <if test="actionType != null">
                and action_type = #{actionType}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from ftp_monitor_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="FtpMonitorLogMap">
        select id, record_num, user_id,term_id, cmd_type, host, file_name, file_size, file_path, backup_file_path, backup_server_id, upload_file_guid,
               server_file_path, monitor_type, process_name, create_time, term_group_id, user_group_id,action_type
        from ftp_monitor_log
        <include refid="sql_where"></include>
    </select>

</mapper>
