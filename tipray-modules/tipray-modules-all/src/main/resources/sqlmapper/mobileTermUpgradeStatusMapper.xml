<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.MobileTermUpgradeStatusLogDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.MobileTermUpgradeStatusLog">
        <id column="id" property="id" />
        <result column="term_id" property="terminalId"/>
        <result column="package_name" property="packageName"/>
        <result column="mobile_type" property="mobileType"/>
        <result column="up_status" property="upStatus"/>
        <result column="version_num" property="versionNum"/>
        <result column="install_version" property="installVersion"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result property="termGroupId" column="term_group_id"/>
    </resultMap>

    <sql id="full_fields_sql">
        id,term_id,package_name,mobile_type,up_status,version_num,install_version,create_time,modify_time,term_group_id
    </sql>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="keyword1 != null and keyword1 != ''">
                and up_status = #{keyword1}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from mobile_upgrade_log a
        <include refid="sql_where"/>
    </select>

    <select id="listByVO" resultMap="resultMap">
        select <include refid="full_fields_sql"/>
        from mobile_upgrade_log
        <include refid="sql_where"/>
    </select>

    <select id="countNewStatusByVO" resultType="java.lang.Long">
        select count(0) from mobile_upgrade_log a join
        (select max(id) id,max(create_time) create_time from mobile_upgrade_log group by term_id) b
        on a.id = b.id
        <include refid="sql_where"/>
    </select>

    <select id="listNewStatusByVO" resultMap="resultMap">
        select * from mobile_upgrade_log where id in (SELECT max(id) id FROM mobile_upgrade_log GROUP BY term_id)
        <if test="objectIds != null and objectIds != ''">
            and term_id in (${objectIds})
        </if>
        <if test="keyword1 != null and keyword1 != ''">
            and up_status = #{keyword1}
        </if>
    </select>

</mapper>
