<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DenseTransLogDao">

    <resultMap type="com.tipray.dlp.bean.DenseTransLog" id="DenseTransLogMap">
        <result property="id" column="id"/>
        <result property="recordNum" column="record_num"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="guid" column="guid"/>
        <result property="fileTotal" column="file_total"/>
        <result property="successCount" column="success_count"/>
        <result property="errorCount" column="error_count"/>
        <result property="usedTime" column="used_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.DenseTransLogDetailVO" resultType="java.lang.Long">
      select count(0) from dense_trans_log
      <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.DenseTransLogDetailVO" resultMap="DenseTransLogMap">
      select id, record_num,term_id, user_id, create_time,guid,file_total, success_count, error_count, used_time, term_group_id, user_group_id
      from dense_trans_log
      <include refid="sql_where"></include>
    </select>

</mapper>
