<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.EmailLogDao">

    <resultMap type="com.tipray.dlp.bean.EmailLog" id="EmailLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="cmdType" column="cmd_type"/>
        <result property="mailTitle" column="mail_title"/>
        <result property="sender" column="sender"/>
        <result property="recver" column="receiver"/>
        <result property="attachCount" column="attach_count"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileName" column="file_name"/>
        <result property="readFlag" column="read_flag"/>
        <result property="localFilePath" column="local_file_path"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="webMailGuid" column="web_mail_guid"/>
        <result property="mailGuid" column="mail_guid"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="mailTitle != null and mailTitle != ''">
                and mail_title like CONCAT('%',#{mailTitle},'%')
            </if>
            <if test="keyword2 != null and keyword2 != ''">
                and file_name like CONCAT('%',#{keyword2},'%')
            </if>
            <if test="cmdType != null and cmdType != ''">
                and cmd_type = #{cmdType}
            </if>
            <if test="keyword4 != null and keyword4 != ''">
                and cmd_type in (@keyword4)
            </if>
            <if test="attachCount!=null">
                and attach_count = #{attachCount}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from mail_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="EmailLogMap">
        select id, user_id, term_id, create_time, cmd_type, mail_title, sender, receiver, attach_count, file_size,
        file_name, read_flag, local_file_path, backup_server_id, upload_file_guid, term_group_id, user_group_id,
        web_mail_guid, mail_guid
        from mail_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listRelWebGuid" resultType="com.tipray.dlp.bean.AssociateWebmailAndAttachment">
        select term_id, web_mail_guid, attachment_guid
        from associate_webmail_and_attachment
        <where>
            <foreach collection="queryInfos" item="item" separator="or" index="index">
                <if test="item.webMailGuids != null and item.webMailGuids.size() > 0">
                    ( term_id = #{item.terminalId} and web_mail_guid in
                    <foreach collection="item.webMailGuids" item="guid" separator="," open="(" close=")">
                        #{guid}
                    </foreach>
                    )
                </if>
            </foreach>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
        </where>
    </select>

</mapper>
