<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.WebSiteDao">

    <resultMap type="com.tipray.dlp.bean.WebSite" id="WebSiteMap">
        <result property="id" column="id"/>
        <result property="webSite" column="web_site"/>
        <result property="priority" column="priority"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>
    <sql id="vo_where_sql">
        <where>
            <include refid="commonSQL.andNotDel"></include>
            <if test="searchInfo != null and searchInfo != ''">
                and web_site like CONCAT('%',#{searchInfo},'%')
            </if>
        </where>
    </sql>

    <select id="selectPageByVO" resultMap="WebSiteMap">
      select id, web_site, priority, remark, create_time, modify_time
      from web_site
      <include refid="vo_where_sql"></include>
    </select>

</mapper>
