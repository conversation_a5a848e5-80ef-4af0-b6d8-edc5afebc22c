<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.EmailDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.Email">
        <id column="id" property="id" />
        <result column="host" property="host"/>
        <result column="port" property="port"/>
        <result column="use_ssl" property="useSSL"/>
        <result column="account" property="account"/>
        <result column="password" property="password"/>
        <result column="name" property="name"/>
    </resultMap>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.tipray.dlp.bean.Email">
		insert into email(host,port,use_ssl,account,password,name)
		values(#{host},#{port},#{useSSL},#{account},#{password},#{name})
	</insert>

    <update id="update" parameterType="com.tipray.dlp.bean.Email">
        update email set
        host=#{host},port=#{port},use_ssl=#{useSSL},account=#{account},password=#{password},name=#{name}
        where id=#{id}
    </update>

    <delete id="deleteByIds" parameterType="java.lang.String">
		delete from email where id in (${value})
	</delete>

    <select id="getByAccount" parameterType="java.lang.String" resultMap="resultMap">
		select * from email where account=#{value}
	</select>

    <select id="getById" parameterType="java.lang.Long" resultMap="resultMap">
		select * from email where id=#{value}
	</select>

    <select id="list" resultMap="resultMap">
		select * from email
	</select>
</mapper>