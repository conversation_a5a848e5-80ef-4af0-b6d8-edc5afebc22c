<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.RuleGroupDao">

    <resultMap type="com.tipray.dlp.bean.RuleGroup" id="RuleGroupMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="groupId" column="group_id"/>
        <result property="json" column="json"/>
        <result property="ruleIds" column="rule_id"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <!--查询单个-->
    <select id="getById" resultMap="RuleGroupMap">
        select id, name, group_id, json, rule_id, remark    from rule_group
        where id = #{id}
    </select>

    <select id="listNameById" resultType="java.lang.String">
        select name from rule_group where id in (@ids)
    </select>

    <select id="getByName" resultMap="RuleGroupMap">
        select id, name, group_id, json, rule_id, remark    from rule_group
        where name = #{name}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="list" resultMap="RuleGroupMap">
        select id, name, group_id, json, rule_id, remark from rule_group
    </select>

    <select id="listByIds" resultMap="RuleGroupMap">
        select id, name, group_id, json, rule_id, remark from rule_group where id in (${ruleGroupIds})
    </select>

    <select id="listByRule" resultMap="RuleGroupMap">
        select id, name, group_id, json, rule_id, remark from rule_group where
        <foreach collection="ruleIds" item="ruleId" separator="or" open="(" close=")">
            rule_id = '${ruleId}' or rule_id like '${ruleId},%' or rule_id like '%,${ruleId},%' or rule_id like '%,${ruleId}'
        </foreach>
    </select>

    <select id="listIdByRule" resultType="java.lang.Long">
        select id from rule_group where
        <foreach collection="ruleIds" item="ruleId" separator="or" open="(" close=")">
            rule_id = '${ruleId}' or rule_id like '${ruleId},%' or rule_id like '%,${ruleId},%' or rule_id like '%,${ruleId}'
        </foreach>
    </select>

    <sql id="vo_where_sql">
        <where>
            <if test="searchInfo != null and searchInfo != ''">
                and name like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="groupId != null  and groupId != 0">
                and group_id = #{groupId}
            </if>
        </where>
    </sql>
    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.RuleVO" resultType="java.lang.Long">
        select count(0) from rule_group
        <include refid="vo_where_sql"/>
    </select>
    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.RuleVO" resultMap="RuleGroupMap">
        select id, name, group_id, json, rule_id, remark from rule_group
        <include refid="vo_where_sql"/>
    </select>

    <!--通过主键修改数据-->
    <!--<update id="update">
        update rule_group
        <set>
            modify_time = #{modifyTime}
            <if test="name != null and name != ''">
                ,name = #{name}
            </if>
            <if test="groupId != null">
                ,group_id = #{groupId}
            </if>
            <if test="json != null and json != ''">
                ,json = #{json}
            </if>
            <if test="ruleIds != null and ruleIds != ''">
                ,rule_id = #{ruleIds}
            </if>
            <if test="remark != null">
                ,remark = #{remark}
            </if>
            <if test="checkExpr != null and checkExpr != ''">
                ,check_expr = #{checkExpr}
            </if>
            <if test="exceptExpr != null and exceptExpr != ''">
                ,except_expr = #{exceptExpr}
            </if>
        </set>
        where id = #{id}
    </update>-->

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from rule_group where id = #{value}
    </delete>

    <delete id="deleteByIds">
        delete from rule_group where id in (${ids})
    </delete>

</mapper>
