<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.LossTypeParamDao">

    <resultMap type="com.tipray.dlp.bean.LossTypeParam" id="LossTypeParamMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="lossType" column="loss_type" jdbcType="INTEGER"/>
        <result property="alarmType" column="alarm_type" jdbcType="INTEGER"/>
        <result property="lossDesc" column="loss_desc" jdbcType="VARCHAR"/>
        <result property="lossDescKey" column="loss_desc_key" jdbcType="VARCHAR"/>
        <result property="sortNum" column="sort_num" jdbcType="INTEGER"/>
        <result property="moduleId" column="module_id" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="LossTypeParamMap">
        select
          id, loss_type, alarm_type, loss_desc, loss_desc_key, sort_num, module_id
        from loss_type_param
        where id = #{id}
    </select>    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="LossTypeParamMap">
        select
        id, loss_type, alarm_type, loss_desc, loss_desc_key, sort_num, module_id
        from loss_type_param
        where
        deleted = 0
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="lossType != null">
                and loss_type = #{lossType}
            </if>
            <if test="lossDesc != null and lossDesc != ''">
                and loss_desc = #{lossDesc}
            </if>
        order by id asc
    </select>
    
</mapper>
