<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.dlp.dao.AiChatLogDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.AiChatLog">
        <id column="id" property="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="aiChatTime" column="ai_chat_time"/>
        <result property="sensitiveFlag" column="sens_flag"/>
        <result property="actionType" column="action_type"/>
        <result property="processName" column="proc_name"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileName" column="file_name"/>
        <result property="fileExt" column="file_ext"/>
        <result property="aiName" column="ai_name"/>
        <result property="aiUrl" column="ai_url"/>
        <result property="aiSessionId" column="ai_session_id"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="aiChatGuid" column="ai_chat_guid"/>
        <result property="content" column="content"/>
        <result property="contentLength" column="content_len"/>
        <result property="uploadFileCount" column="upload_file_cnt"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="aiChatGuid != null and aiChatGuid != ''">
                and ai_chat_guid = #{aiChatGuid}
            </if>
            <if test="aiSessionId != null and aiSessionId != ''">
                and ai_session_id like CONCAT('%',#{aiSessionId},'%')
            </if>
            <if test="aiName != null">
                and ai_name = #{aiName}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from ai_chat_record a
        <include refid="sql_where"></include>
    </select>
    <select id="listByVO" resultMap="resultMap">
        select id, record_num, term_id, user_id, create_time, ai_chat_time,
        file_name, file_ext, file_size, backup_server_id, upload_file_guid,
        term_group_id, user_group_id,
        ai_name, proc_name, ai_url, ai_session_id, ai_chat_guid, content, content_len, upload_file_cnt, sens_flag, action_type
        from ai_chat_record a
        <include refid="sql_where"></include>
    </select>
</mapper>