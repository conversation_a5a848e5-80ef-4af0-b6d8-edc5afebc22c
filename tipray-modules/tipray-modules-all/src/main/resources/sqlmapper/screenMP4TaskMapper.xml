<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ScreenMP4TaskDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.ScreenMP4Task">
        <id column="id" property="id" />
        <result column="recorder_id" property="recorderId"/>
        <result column="term_id" property="terminalId"/>
        <result column="user_id" property="userId"/>
        <result column="create_time" property="createTime"/>
        <result column="recorder_file_name" property="recorderFileName"/>
        <result column="index_server_id" property="indexServerId"/>
        <result column="index_upload_file_guid" property="indexUploadFileGuid"/>
        <result column="data_server_id" property="dataServerId"/>
        <result column="data_upload_file_guid" property="dataUploadFileGuid"/>
        <result column="status" property="status"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <insert id="insert" parameterType="com.tipray.dlp.bean.ScreenMP4Task">
		insert into screen_mp4_task(recorder_id, term_id, term_group_id, user_id, user_group_id, create_time, recorder_time, recorder_file_name,
		index_server_id, index_upload_file_guid, data_server_id, data_upload_file_guid, status)
		values(#{recorderId}, #{terminalId}, #{termGroupId}, #{userId}, #{userGroupId}, #{createTime}, #{recorderTime}, #{recorderFileName},
		#{indexServerId}, #{indexUploadFileGuid}, #{dataServerId}, #{dataUploadFileGuid}, #{status})
	</insert>

    <update id="updateStatus">
        update screen_mp4_task set status = #{status} where data_upload_file_guid = #{indexUploadFileGuid}
    </update>

    <delete id="deleteByDataFileGuid" parameterType="java.lang.String">
		delete from screen_mp4_task where data_upload_file_guid in (${value})
	</delete>

    <delete id="delete" parameterType="java.lang.String">
		delete from screen_mp4_task where id in (${value})
	</delete>

    <select id="listByIds" parameterType="java.lang.String" resultMap="resultMap">
        select * from screen_mp4_task
		where id in (${value})
    </select>

    <select id="listByDataFileGuids" parameterType="java.lang.String" resultMap="resultMap">
		select * from screen_mp4_task
		where data_upload_file_guid in (${value})
	</select>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and recorder_time &gt;= #{startDate} and recorder_time &lt; #{endDate}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.VideoRecorderVO" resultType="java.lang.Long">
        select count(0) from screen_mp4_task
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.VideoRecorderVO" resultMap="resultMap">
        select * from screen_mp4_task
        <include refid="sql_where"></include>
    </select>

    <select id="getById" parameterType="java.lang.Long" resultMap="resultMap">
        select * from screen_mp4_task where id = #{value}
    </select>

    <update id="updateFailStatus">
        update screen_mp4_task set status = 1 where status = 0
    </update>

    <select id="getByDataFileGuid" parameterType="java.lang.String" resultMap="resultMap">
        select * from screen_mp4_task where data_upload_file_guid = #{value}
    </select>
</mapper>
