<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.IdentifierRuleDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.IdentifierRule">
        <id column="id" property="id" />
        <result column="name" property="name"/>
        <result column="regular" property="regular"/>
        <result column="check_times" property="checkTimes"/>
        <result column="modify_ver" property="modifyVer" />
        <result column="deleted" property="deleted" />
    </resultMap>
    <insert id="insertEntity" parameterType="com.tipray.dlp.bean.IdentifierRule" useGeneratedKeys="true" keyProperty="id">
        insert into rule_identifier(name, regular) values (#{name}, #{regular})
    </insert>

    <select id="listAll" resultMap="resultMap">
        select * from rule_identifier
        where 1 = 1
        <include refid="commonSQL.andNotDel"></include>
    </select>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into rule_identifier(name, regular, check_times, deleted)
        values (#{name}, #{regular}, #{checkTimes}, #{deleted})
    </insert>

    <update id="deleteIdentifierRule">
        update rule_identifier set deleted = 1
        where id in (${ids})
    </update>

    <select id="listByName" resultType="com.tipray.dlp.bean.IdentifierRule">
        select * from rule_identifier
        where name = #{name}
        <include refid="commonSQL.andNotDel"></include>
    </select>

</mapper>
