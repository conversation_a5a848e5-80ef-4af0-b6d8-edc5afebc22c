<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SoftwareAssetAlarmSetupDao">

    <resultMap type="com.tipray.dlp.bean.SoftwareAssetAlarmSetup" id="AssetAlarmSetupMap">
        <result property="id" column="id"/>
        <result property="softwareAlarm" column="software_alarm"/>
        <result property="softwareOperateType" column="software_operate_type"/>
        <result property="softwareAssetsType" column="software_assets_type"/>
        <result property="softwareRecord" column="software_record"/>
        <result property="softwareAssetsTypeRecord" column="software_assets_type_record"/>
        <result property="softwareOperateTypeRecord" column="software_operate_type_record"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="list" resultMap="AssetAlarmSetupMap">
        select id, software_alarm, software_operate_type, software_assets_type, software_record, software_assets_type_record, software_operate_type_record
        from software_asset_alarm_setup
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into software_asset_alarm_setup(software_alarm, software_operate_type, software_assets_type, software_record, software_assets_type_record, software_operate_type_record)
        values (#{softwareAlarm}, #{softwareOperateType}, #{softwareAssetsType},  #{softwareRecord},  #{softwareAssetsTypeRecord}, #{softwareOperateTypeRecord})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update software_asset_alarm_setup
        <set>
            <if test="softwareAlarm != null">
                software_alarm = #{softwareAlarm},
            </if>
            <if test="softwareOperateType != null">
                software_operate_type = #{softwareOperateType},
            </if>
            <if test="softwareAssetsType != null">
                software_assets_type = #{softwareAssetsType},
            </if>
            <if test="softwareRecord != null">
                software_record = #{softwareRecord},
            </if>
            <if test="softwareAssetsTypeRecord != null">
                software_assets_type_record = #{softwareAssetsTypeRecord},
            </if>
            <if test="softwareOperateTypeRecord != null">
                software_operate_type_record = #{softwareOperateTypeRecord},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>
