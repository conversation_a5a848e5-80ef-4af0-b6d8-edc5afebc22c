<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.MailAttachInfoLogDao">

    <resultMap type="com.tipray.dlp.bean.MailAttachInfoLog" id="resMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="createTime" column="create_time"/>
        <result property="mailGuid" column="mail_guid"/>
        <result property="fileName" column="file_name"/>
        <result property="exeName" column="exe_name"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="mailGuid != null and mailGuid != ''">
                and mail_guid = #{mailGuid}
            </if>
        </where>
    </sql>

    <select id="listByVO" resultMap="resMap">
        select id, user_id, term_id, term_group_id, user_group_id, create_time, mail_guid, file_name, exe_name
        from mail_attach_info_record a
        <include refid="sql_where"></include>
    </select>

</mapper>
