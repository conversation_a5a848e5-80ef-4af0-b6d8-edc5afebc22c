<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DevServerAccessApproveDao">

    <resultMap type="com.tipray.dlp.bean.DevServerAccessApprove" id="resultMap">
        <result property="id" column="id"/>
        <result property="devId" column="dev_id"/>
        <result property="devGuid" column="dev_guid"/>
        <result property="devName" column="dev_name"/>
        <result property="devType" column="dev_type"/>
        <result property="devVersion" column="dev_version"/>
        <result property="showVersion" column="show_version"/>
        <result property="groupId" column="group_id"/>
        <result property="intranetPort" column="intranet_port"/>
        <result property="intranetIpv4" column="intranet_ipv4"/>
        <result property="intranetIpv6" column="intranet_ipv6"/>
        <result property="intranetType" column="intranet_type"/>
        <result property="devMac" column="dev_mac"/>
        <result property="osType" column="os_type"/>
        <result property="sysInfo" column="sys_info"/>
        <result property="optInfo" column="opt_info"/>
        <result property="approveResult" column="approve_result"/>
        <result property="authorizedStatus" column="authorized_status"/>
        <result property="approveType" column="approve_type"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="pkgType" column="pkg_type"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <if test="devGuid != null and devGuid != ''">
                and dev_guid = #{devGuid}
            </if>
            <if test="devGuids != null and devGuids != ''">
                and dev_guid in (${devGuids})
            </if>
            <if test="devId != null">
                and dev_id = #{devId}
            </if>
            <if test="devTypes != null and devTypes != ''">
                and dev_type in (${devTypes})
            </if>
            <if test="devGuidLike != null and devGuidLike != ''">
                and dev_guid like CONCAT('%', #{devGuidLike}, '%')
            </if>
            <if test="approveResults != null and approveResults != ''">
                and approve_result in (${approveResults})
            </if>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="ip != null and ip != ''">
                and (intranet_ipv4 like concat('%', #{ip}, '%') or intranet_ipv6 like concat('%', #{ip}, '%'))
            </if>
            <if test="devMac != null and devMac != ''">
                and dev_mac like concat('%', #{devMac}, '%')
            </if>
            <if test="version != null and version != ''">
            and (
                (show_version IS NULL AND dev_version LIKE CONCAT('%', #{version}, '%'))
                OR
                (show_version IS NOT NULL AND (show_version LIKE CONCAT('%', #{version}, '%') OR dev_version LIKE CONCAT('%', #{version}, '%')))
                )
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="Long">
        select count(*)
        from dev_server_access_approve
        <include refid="sql_where"/>
    </select>

    <select id="listByVO" resultMap="resultMap">
        select id, dev_id, dev_guid, dev_name, dev_type, dev_version, show_version, group_id, intranet_port, intranet_ipv4, intranet_ipv6, intranet_type, dev_mac, os_type, sys_info,
        opt_info, approve_result, authorized_status, approve_type, remark, create_time, modify_time, pkg_type
        from dev_server_access_approve
        <include refid="sql_where"/>
    </select>

    <sql id="update_set">
        <set>
            <if test="devId != null">
                dev_id = #{devId},
            </if>
            <if test="devName != null and devName != ''">
                dev_name = #{devName},
            </if>
            <if test="groupId != null">
                group_id = #{groupId},
            </if>
            <if test="intranetPort != null">
                intranet_port = #{intranetPort},
            </if>
            <if test="intranetIpv4 != null">
                intranet_ipv4 = #{intranetIpv4},
            </if>
            <if test="intranetIpv6 != null">
                intranet_ipv6 = #{intranetIpv6},
            </if>
            <if test="approveResult != null">
                approve_result = #{approveResult},
            </if>
            <if test="authorizedStatus != null">
                authorized_status = #{authorizedStatus},
            </if>
            <if test="intranetType != null">
                intranet_type = #{intranetType},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="sysInfo != null and sysInfo != ''">
                sys_info = #{sysInfo},
            </if>
            <if test="optInfo != null and optInfo != ''">
                opt_info = #{optInfo},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
        </set>
    </sql>

    <!--通过主键修改数据-->
    <update id="updateDataById">
        update dev_server_access_approve
        <include refid="update_set"/>
        where id = #{id}
    </update>

    <update id="updateDataByDevGuids">
        update dev_server_access_approve
        <include refid="update_set"/>
        where dev_guid in ${devGuids}
    </update>

</mapper>
