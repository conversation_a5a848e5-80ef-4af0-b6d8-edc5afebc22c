<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ForumdataLogDao">

    <resultMap type="com.tipray.dlp.bean.ForumdataLog" id="ForumdataLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="author" column="author"/>
        <result property="title" column="title"/>
        <result property="url" column="url"/>
        <result property="backupFileName" column="backup_file_name"/>
        <result property="serverFileName" column="server_file_name"/>
        <result property="localFilePath" column="local_file_path"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="title != null and title != ''">
                and title like CONCAT('%',#{title},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
      select count(0) from forum_data_log a <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="ForumdataLogMap">
      select id, user_id, term_id, create_time, author, title, url, backup_file_name, server_file_name, local_file_path, backup_server_id, upload_file_guid, term_group_id, user_group_id
      from forum_data_log a
      <include refid="sql_where"></include>
    </select>

</mapper>
