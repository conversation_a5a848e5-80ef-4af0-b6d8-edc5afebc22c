<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.CurTaskInfoLogDao">

    <resultMap type="com.tipray.dlp.bean.CurTaskInfoLog" id="dataMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="taskStatus" column="task_status"/>
        <result property="fileName" column="file_name"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="softName != null and softName != ''">
                and file_name like CONCAT('%',#{softName},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from cur_task_info a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="dataMap">
        select id, user_id, term_id, create_time, task_status, file_name, term_group_id, user_group_id
        from cur_task_info a
        <include refid="sql_where"></include>
    </select>
</mapper>
