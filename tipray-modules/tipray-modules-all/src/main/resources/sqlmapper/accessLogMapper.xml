<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.AccessLogDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.AccessLog">
        <id column="id" property="id" />
        <result column="term_name" property="termName"/>
        <result column="version" property="version"/>
        <result column="guid" property="guid"/>
        <result column="mcode" property="mcode"/>
        <result column="main_ip" property="mainIp"/>
        <result column="main_mac" property="mainMac"/>
        <result column="ip_list" property="ips"/>
        <result column="mac_list" property="macs"/>
        <result column="term_id" property="termId"/>
        <result column="group_id" property="groupId"/>
        <result column="auto_login_user_id" property="userId"/>
        <result column="access_code" property="accessCode"/>
        <result column="term_type" property="termType"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and term_name like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="accessCode != null and accessCode != ''">
                and access_code = #{accessCode}
            </if>
            <if test="ip != null and ip != ''">
                and main_ip like CONCAT('%',#{ip},'%')
            </if>
            <if test="termType != null">
                and term_type = #{termType}
            </if>
            <if test="objectType == 1">
                <if test="objectId != null">
                    and term_id = #{objectId}
                </if>
                <if test="objectIds != null and objectIds != ''">
                    and term_id in (${objectIds})
                </if>
            </if>
            <if test="objectType == 3">
                <if test="objectId != null">
                    and group_id = #{objectId}
                </if>
                <if test="objectIds != null and objectIds != ''">
                    and group_id in (${objectIds})
                </if>
            </if>
        </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.AccessLogVO" resultMap="resultMap">
		select * from access_log
        <include refid="sql_where"></include>
	</select>
    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from access_log
        <include refid="sql_where"/>
    </select>
    <select id="statisticGroupByCreateTime" resultType="java.util.Map">
        select count(0) num,date_format(create_time, '%Y-%m-%d') perDay from access_log
        where create_time &gt;= #{startDate} and create_time &lt; #{endDate} GROUP BY perDay ORDER BY perDay
    </select>

</mapper>
