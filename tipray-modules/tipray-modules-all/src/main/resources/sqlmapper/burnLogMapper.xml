<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.BurnLogDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.BurnLog">
        <id column="id" property="id" />
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="terminalName" column="terminalName"/>
        <result property="burnSize" column="burn_size"/>
        <result property="burnId" column="burn_id"/>
        <result property="burner" column="device_info"/>
        <result property="cd" column="burn_cd_info"/>
        <result property="createTime" column="create_time"/>
        <result property="ftpId" column="ftp_id"/>
        <result property="localIp" column="local_ip"/>
        <result property="hostName" column="host_name"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="actionType" column="action_type"/>
    </resultMap>
    <sql id="full_fields_sql">
        id,term_id,user_id,create_time,burn_id,device_info,burn_cd_info,burn_size,is_sensitive,local_ip,host_name,term_group_id,user_group_id,action_type
    </sql>

    <sql id="select_where_vo">
        <include refid="commonSQL.andObjectTypeAndObjectId"/>
        <if test="startDate != null and endDate != null">
            and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
        </if>
        <if test="burnId != null and burnId != ''">
            and burn_id = #{burnId}
        </if>
        <if test="burnName != null and burnName != ''">
            and device_info like CONCAT('%',#{burnName},'%')
        </if>
        <if test="actionType != null">
            and action_type = #{actionType}
        </if>
    </sql>

    <delete id="deleteById" parameterType="java.lang.Long">
		delete from cd_burn_log where id=#{value}
	</delete>

    <delete id="deleteByIds" parameterType="java.lang.String">
		delete from cd_burn_log where id in (${value})
	</delete>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.BurnLogVO" resultType="java.lang.Long">
		select count(0) from cd_burn_log where 1=1
        <include refid="select_where_vo"/>
	</select>
    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.BurnLogVO" resultMap="resultMap">
		select <include refid="full_fields_sql"/>
		from cd_burn_log where 1=1
        <include refid="select_where_vo"/>
	</select>

    <select id="getBurnIdsByIds" parameterType="java.lang.String" resultType="java.lang.String">
        select burn_id from cd_burn_log where id in (${value})
    </select>
</mapper>
