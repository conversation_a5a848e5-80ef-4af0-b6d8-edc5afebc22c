<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.MailWhiteListSenderDao">

    <resultMap type="com.tipray.dlp.bean.MailWhiteListSender" id="resultMap">
        <result property="id" column="id"/>
        <result property="userName" column="user_name"/>
        <result property="mailAddress" column="mail_address"/>
        <result property="office" column="office"/>
        <result property="depart" column="depart"/>
        <result property="state" column="state"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="vo_where_sql">
        <if test="searchInfo != null and searchInfo != ''">
            and user_name like CONCAT('%',#{searchInfo},'%')
        </if>
    </sql>

    <select id="getByMailAddress" parameterType="java.lang.String" resultMap="resultMap">
		select * from mail_white_list_sender where mail_address=#{value}
	</select>

    <!--查询单个-->
    <select id="getById" resultMap="resultMap">
        select * from mail_white_list_sender where id = #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="list" resultMap="resultMap">
        select * from mail_white_list_sender
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into mail_white_list_sender(user_name, mail_address, office, depart, state, remark)
        values(#{userName}, #{mailAddress}, #{office}, #{depart}, #{state}, #{remark})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update mail_white_list_sender
        <set>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="mailAddress != null and mailAddress != ''">
                mail_address = #{mailAddress},
            </if>
            <if test="office != null and office != ''">
                office = #{office},
            </if>
            <if test="depart != null and depart != ''">
                depart = #{depart},
            </if>
            <if test="state != null">
                state = #{state},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from mail_white_list_sender where id = #{value}
    </delete>

    <delete id="deleteByIds">
        delete from mail_white_list_sender where id in (${value})
    </delete>

    <select id="countByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultType="java.lang.Long">
        select count(0) from mail_white_list_sender where 1 = 1
        <include refid="vo_where_sql"/>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultMap="resultMap">
        select * from mail_white_list_sender where 1 = 1
        <include refid="vo_where_sql"/>
    </select>

</mapper>
