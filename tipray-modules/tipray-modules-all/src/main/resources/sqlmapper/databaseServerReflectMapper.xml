<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DatabaseServerReflectDao">

    <select id="listServerIds" resultType="java.lang.Long">
        select db_id from ref_db_server where ref_db_id = #{value}
    </select>

    <select id="getRefServerIdByServerId" resultType="java.lang.Long">
        select ref_db_id from ref_db_server where db_id = #{value}
    </select>

    <!--新增所有列-->
    <insert id="batchInsert">
        INSERT INTO ref_db_server(server_id, ref_db_id)
        VALUES
        <foreach collection="serverIds" item="serverId" separator=",">
            (#{serverId},#{refServerId}
        </foreach>
    </insert>

    <insert id="insert">
        insert into ref_db_server(db_id, ref_db_id)
        values (#{serverId},#{refServerId})
    </insert>

    <delete id="deleteByRefServerIds">
        delete from ref_db_server where ref_db_id in (${value})
    </delete>

    <delete id="deleteByRefServerId">
        delete from ref_db_server where ref_db_id = #{value}
    </delete>

    <delete id="deleteByServerId">
        delete from ref_db_server where db_id = #{serverId}
    </delete>

    <delete id="deleteByServerIdOrRefServerId">
        delete from ref_db_server where db_id in (@serverIds) or ref_db_id in (@serverIds)
    </delete>

    <delete id="deleteByServerIds">
        delete from ref_db_server where db_id in (${value})
    </delete>

    <select id="listAll" resultType="java.util.Map">
        select db_id serverId,ref_db_id refServerId from ref_db_server
    </select>

    <select id="listAllRefServerId" resultType="java.lang.Long">
        select distinct ref_db_id from ref_db_server
    </select>

    <select id="listAllServerId" resultType="java.lang.Long">
        select distinct db_id from ref_db_server
    </select>

    <select id="selectByExitsDevId">
        select db_id dbId, ref_db_id refDbId from ref_db_server where db_id = #{dbId} or ref_db_id = #{dbId}
    </select>
</mapper>
