<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.AlarmTemplateDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.AlarmTemplate">
        <id column="id" property="id"/>
        <result column="sst_type" property="sstType"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="alarm_bus" property="alarmBus"/>
        <result column="msg_template" property="content"/>
        <result column="msg_template_key" property="contentKey"/>
        <result column="sys_version" property="version"/>
        <result column="group_id" property="groupId"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="remark" property="remark"/>
        <result column="lang" property="lang"/>
        <result column="type" property="type"/>
        <result column="name" property="name"/>
        <result column="biz_type" property="bizType"/>
        <result column="table_type" property="tableType"/>
    </resultMap>

    <resultMap id="paramResultMap" type="com.tipray.dlp.bean.AlarmTemplateParam">
        <id column="id" property="id"/>
        <result column="sst_type" property="sstType"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="alarm_bus" property="alarmBus"/>
        <result column="param_name" property="paramName"/>
        <result column="param_no" property="paramNo"/>
    </resultMap>

    <sql id="vo_where_sql">
        <if test="groupId != null and groupId != 0 and groupId != -1">
            and group_id = #{groupId}
        </if>
        <if test="groupId != null and groupId == -1">
            and group_id  is null
        </if>
        <if test="searchInfo != null and searchInfo != ''">
            and (name like concat('%', #{searchInfo}, '%') or msg_template like concat('%', #{searchInfo}, '%'))
        </if>
    </sql>

    <sql id="select_field">
        <choose>
            <when test="tableType != null and tableType == 2">
                id, biz_type, 0 sst_type, alarm_type, 0 alarm_bus, null alarm_level, msg_template, msg_template_key, sys_version, group_id, deleted, 2 table_type
            </when>
            <otherwise>
                id, null biz_type, sst_type, alarm_type, alarm_bus, alarm_level, msg_template, msg_template_key, sys_version, group_id, deleted, 1 table_type
            </otherwise>
        </choose>
    </sql>

    <sql id="select_table">
        <choose>
            <when test="tableType != null and tableType == 2">
                service_alarm_template
            </when>
            <otherwise>
                alarm_template
            </otherwise>
        </choose>
    </sql>

    <update id="updateAlarmTemplate">
        update alarm_template
        set group_id = #{groupId}, alarm_level = #{alarmLevel}
        where id = #{id}
    </update>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.MailLibraryVO" resultMap="resultMap">
        select * from alarm_template where deleted = 0
        <include refid="vo_where_sql"></include>
    </select>

    <select id="listTemplate" resultMap="resultMap">
        select * from alarm_template where deleted = 0
	</select>

    <select id="listParam" resultMap="paramResultMap">
        select * from alarm_template_param where deleted = 0
	</select>

    <select id="selectAllTemplate" resultMap="resultMap">
        select id, null biz_type, sst_type, alarm_type, alarm_bus, alarm_level, msg_template, msg_template_key, sys_version, group_id, deleted, 1 table_type from alarm_template
        union all
        select id, biz_type, 0 sst_type, alarm_type, 0 alarm_bus, null alarm_level, msg_template, msg_template_key, sys_version, group_id, deleted, 2 table_type from service_alarm_template
    </select>

    <select id="selectByIdWithTableType" resultMap="resultMap">
        select <include refid="select_field"/>
        from <include refid="select_table"/>
        where id = #{id}
    </select>

    <!-- 服务告警模板表 -->

    <select id="getServiceTemplateByType" resultMap="resultMap">
        select * from service_alarm_template where biz_type = #{bizType} and alarm_type = #{alarmType}
    </select>
    
    <select id="getServiceTemplateByKey" resultMap="resultMap">
        select * from service_alarm_template where msg_template_key = #{contentKey};
    </select>

</mapper>
