<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.TimeInfoDao">

    <resultMap type="com.tipray.dlp.bean.TimeInfo" id="TimeInfoMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="mon" column="mon"/>
        <result property="tue" column="tue"/>
        <result property="wed" column="web"/>
        <result property="thu" column="thu"/>
        <result property="fri" column="fri"/>
        <result property="sat" column="sat"/>
        <result property="sun" column="sun"/>
    </resultMap>

    <sql id="vo_where_sql">
        <include refid="commonSQL.andNotDel"></include>
        <if test="searchInfo != null and searchInfo != ''">
            and name like CONCAT('%',#{searchInfo},'%')
        </if>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.TimeInfoVO" resultMap="TimeInfoMap">
        <if test="lang == null or lang == 'zh'">
            select id, name, mon, tue, web, thu, fri, sat, sun, modify_ver, deleted, create_time, modify_time from time_info
        </if>

        <if test="lang != null and lang != '' and lang != 'zh' and langParams != null">
            select id, name, mon, tue, web, thu, fri, sat, sun, modify_ver, deleted, create_time, modify_time from
            (select time_info.id, if (time_info.id > 1, time_info.name, time_info_d.name) name, time_info.mon mon, time_info.tue tue,
            time_info.web web, time_info.thu thu, time_info.fri fri, time_info.sat sat, time_info.sun sun, time_info.modify_ver modify_ver,
            time_info.deleted deleted, time_info.create_time create_time, time_info.modify_time modify_time
            from time_info left join (select #{langParams.id1} id, #{langParams.name1} name)
            AS time_info_d on time_info.id = time_info.id) AS time_info
        </if>
        where 1 = 1
        <include refid="vo_where_sql"/>
    </select>

</mapper>
