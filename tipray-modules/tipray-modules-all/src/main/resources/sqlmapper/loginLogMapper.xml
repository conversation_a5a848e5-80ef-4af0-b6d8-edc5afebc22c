<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.LoginLogDao">

    <resultMap type="com.tipray.dlp.bean.LoginLog" id="LoginLogMap">
        <result property="id" column="id"/>
        <result property="recordNum" column="record_num"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="logSource" column="log_src"/>
        <result property="eventType" column="event_id"/>
        <result property="recordId" column="record_id"/>
        <result property="loginUser" column="login_user"/>
        <result property="loginDomain" column="login_domain"/>
        <result property="loginType" column="login_type"/>
        <result property="remark" column="log_desc"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="eventType != null">
                and event_id = #{eventType}
            </if>
            <if test="loginType != null">
                and login_type = #{loginType}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultType="java.lang.Long">
        select count(0) from login_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultMap="LoginLogMap">
        select id, record_num, term_id, user_id, create_time, log_src, event_id, record_id, login_user, login_domain, login_type, log_desc, term_group_id, user_group_id from login_log
        <include refid="sql_where"></include>
    </select>

</mapper>
