<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SystemBackupLogDao">

    <resultMap type="com.tipray.dlp.bean.SystemBackupLog" id="dataMap">
        <result property="id" column="id"/>
        <result property="backupName" column="backup_name"/>
        <result property="backupPosition" column="backup_position"/>
        <result property="backupSize" column="backup_size"/>
        <result property="backupTime" column="backup_time"/>
        <result property="operateStatus" column="operate_status"/>
        <result property="operateFailMsg" column="operate_fail_msg"/>
        <result property="localPath" column="local_path"/>
        <result property="encrypType" column="encryp_type"/>
        <result property="cloudId" column="cloud_id"/>
        <result property="recordTime" column="record_time"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <if test="operateStatus != null">
                and operate_status = #{operateStatus}
            </if>
            <if test="backupPosition != null">
                and backup_position = #{backupPosition}
            </if>
            <if test="ids != null and ids != ''">
                and id in ${ids}
            </if>
            <if test="recordStartTime != null and recordEndTime != null">
                and record_time >= #{recordStartTime} and record_time &lt;= #{recordEndTime}
            </if>
            <if test="backupStartTime != null and backupEndTime != null">
                and backup_time >= #{backupStartTime} and backup_time &lt;= #{backupEndTime}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and backup_name like CONCAT(#{searchInfo},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from dlp_datamgr.db_backup_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="dataMap">
        select id, backup_name, backup_position, backup_size, backup_time, operate_status, operate_fail_msg, local_path, encryp_type, cloud_id, record_time
        from dlp_datamgr.db_backup_log
        <include refid="sql_where"></include>
    </select>
</mapper>
