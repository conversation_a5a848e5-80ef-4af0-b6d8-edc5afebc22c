<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DbAccountDao">
    <resultMap id="DbAccountMap" type="com.tipray.dlp.bean.DbAccount">
        <result column="dev_type" property="devType"/>
        <result column="db_user" property="dbUser"/>
    </resultMap>

    <select id="listDbAccount" resultMap="DbAccountMap">
        select dev_type, db_user
        from db_acct
    </select>
</mapper>
