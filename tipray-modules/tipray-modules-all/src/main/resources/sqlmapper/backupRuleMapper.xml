<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.BackupRuleDao">
    <resultMap id="BackupRuleMap" type="com.tipray.dlp.bean.BackupRule">
        <id column="id" property="id" />
        <result column="name" property="name"/>
        <result column="file_ext_setup" property="fileExtSetup"/>
        <result column="file_ext" property="fileExt"/>
    </resultMap>

    <sql id="sql_where">
        where deleted = 0
        <if test="ids != null and ids != ''">
            and r.id in (${ids})
        </if>
        <if test="searchInfo != null and searchInfo != '' ">
            and name like CONCAT('%',#{searchInfo},'%')
        </if>
        <if test="name != null and name != '' ">
            and name like CONCAT('%',#{name},'%')
        </if>
        <if test="fileExtSetup != null">
            and file_ext_setup = #{fileExtSetup}
        </if>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.BackupRuleVo" resultMap="BackupRuleMap">
        select * from backup_rule
        <include refid="sql_where"></include>
    </select>

</mapper>