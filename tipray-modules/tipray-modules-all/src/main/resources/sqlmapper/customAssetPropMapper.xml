<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.CustomAssetPropDao">

    <resultMap type="com.tipray.dlp.bean.CustomAssetProp" id="CustomAssetPropMap">
        <result property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="name" column="name"/>
        <result property="remark" column="remark"/>
        <result property="type" column="type"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <if test="parentId != null">
                and parent_id = #{parentId}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
        </where>
    </sql>

    <!--查询资产属性名称-->
    <select id="getNameById" resultType="java.lang.String">
        select name from custom_asset_prop where id = #{id}
    </select>

    <!--查询单个-->
    <select id="getById" resultMap="CustomAssetPropMap">
        select id,  parent_id, name, remark, type
        from custom_asset_prop
        where id = #{id}
    </select>

    <!--<select id="countByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultType="java.lang.Long">
      select count(0) from custom_asset_prop
    </select>-->

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.CustomAssetPropVO" resultMap="CustomAssetPropMap">
        select id,  parent_id, name, remark, type from custom_asset_prop
        <include refid="vo_where_sql"/>
    </select>

    <select id="listByParentIds" parameterType="java.lang.String" resultMap="CustomAssetPropMap">
        select id,  parent_id, name, remark, type from custom_asset_prop
        where parent_id in (${parentIds})
        <if test="userId != null">
            and id not in (select prop_id from asset_prop_user where user_id = #{userId})
        </if>
    </select>

    <select id="listByParentId" parameterType="java.lang.Long" resultMap="CustomAssetPropMap">
        select id,  parent_id, name, remark, type from custom_asset_prop where parent_id = #{parentId}
    </select>

    <select id="listByIds" resultMap="CustomAssetPropMap">
        select id,  parent_id, name, remark, type from custom_asset_prop where id in (${ids})
    </select>
</mapper>
