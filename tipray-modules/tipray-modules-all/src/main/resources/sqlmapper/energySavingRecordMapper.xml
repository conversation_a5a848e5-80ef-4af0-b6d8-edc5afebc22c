<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.EnergySavingRecordDao">

    <resultMap type="com.tipray.dlp.bean.EnergySavingRecord" id="EnergySavingRecordMap">
        <result property="id" column="id"/>
        <result property="recordNum" column="record_num"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="execType" column="exec_type"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="execType != null and execType != ''">
                and exec_type = #{execType}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from energy_saving_record
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="EnergySavingRecordMap">
        select id, user_id, term_id, record_num, create_time, exec_type, term_group_id, user_group_id
        from energy_saving_record
        <include refid="sql_where"></include>
    </select>

</mapper>
