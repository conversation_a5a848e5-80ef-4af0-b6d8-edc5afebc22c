<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DbBackupNoticeMsgDao">

    <resultMap type="com.tipray.dlp.bean.DbBackupNoticeMsg" id="resultMap">
        <result property="id" column="id"/>
        <result property="content" column="content"/>
        <result property="type" column="type"/>
        <result property="createTime" column="create_time" javaType="java.util.Date"/>
    </resultMap>


    <select id="listByVo" parameterType="com.tipray.dlp.bean.vo.DbBackupNoticeMsgVO" resultMap="resultMap">
        select id, content, type, create_time
        from dlp_datamgr.db_backup_notice_msg
        <where>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="greaterEqualDate != null">
                and create_time >= #{greaterEqualDate}
            </if>
            <if test="lessDate != null">
                and create_time &lt; #{lessDate}
            </if>
        </where>
    </select>

    <select id="countByVo"  parameterType="com.tipray.dlp.bean.vo.DbBackupNoticeMsgVO" resultType="java.lang.Long">
        select count(0)
        from dlp_datamgr.db_backup_notice_msg
        <where>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="greaterEqualDate != null">
                and create_time >= #{greaterEqualDate}
            </if>
            <if test="lessDate != null">
                and create_time &lt; #{lessDate}
            </if>
        </where>
    </select>

    <delete id="deleteByVo" parameterType="com.tipray.dlp.bean.vo.DbBackupNoticeMsgVO">
        delete from db_backup_notice_msg
        <where>
            <if test="ids != null and ids != ''">
                and id in (${ids})
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="greaterEqualDate != null">
                and create_time >= #{greaterEqualDate}
            </if>
            <if test="lessDate != null">
                and create_time &lt; #{lessDate}
            </if>
        </where>
    </delete>
</mapper>
