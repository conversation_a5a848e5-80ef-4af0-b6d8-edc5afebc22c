<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ModuleSubFilterDao">
    <resultMap type="com.tipray.dlp.bean.ModuleSubFilter" id="resultMap">
        <id column="id" property="id" />
        <result property="objectId" column="object_id"/>
        <result property="objectType" column="object_type"/>
        <result property="filterSubModules" column="filter_submodules"/>
    </resultMap>

    <update id="update">
        update mod_sub_module_filter
        set filter_submodules = #{filterSubModules}
        <include refid="where_sql"></include>
    </update>

    <sql id="where_sql">
        <where>
            <if test="objectType != null and objectId != null">
                and object_type = #{objectType} and object_id = #{objectId}
            </if>
        </where>
    </sql>

    <select id="listFilterSubModulesByVO" resultMap="resultMap">
		select * from mod_sub_module_filter
        <include refid="where_sql"></include>
	</select>

    <select id="selectPageByVO" resultMap="resultMap">
        select * from mod_sub_module_filter
        <include refid="where_sql"></include>
    </select>

    <select id="listByGroup" resultMap="resultMap">
		select * from mod_sub_module_filter where object_type = 3 and object_id in (${groupIds})
	</select>


</mapper>
