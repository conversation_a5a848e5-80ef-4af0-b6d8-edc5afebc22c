<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ProcessStgChildDao">

    <resultMap type="com.tipray.dlp.bean.ProcessStgChild" id="ProcessStgChildMap">
        <result property="id" column="id"/>
        <result property="processStgId" column="process_stg_id"/>
        <result property="processName" column="process_name"/>
        <result property="decReadSfx" column="dec_read_sfx"/>
        <result property="encWriteSfx" column="enc_write_sfx"/>
        <result property="encOpenSfx" column="enc_open_sfx"/>
        <result property="code" column="code"/>
    </resultMap>
    <sql id="vo_where_sql">
        <where>
            <if test="groupId != null">
                and process_stg_id = #{groupId}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and process_name like CONCAT('%',#{searchInfo},'%')
            </if>
            <include refid="commonSQL.andNotDel"></include>
        </where>
    </sql>

    <select id="getByName" resultMap="ProcessStgChildMap">
        select id, process_stg_id, process_name, dec_read_sfx, enc_write_sfx, enc_open_sfx, code        from process_stg_child
        <where>
            process_name = #{value}
            <include refid="commonSQL.andNotDel"></include>
        </where>
        limit 1
    </select>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultMap="ProcessStgChildMap">
      select id, process_stg_id, process_name, dec_read_sfx, enc_write_sfx, enc_open_sfx, code from process_stg_child
      <include refid="vo_where_sql"></include>
    </select>

</mapper>
