<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SofewareAppRelaDataDao">

    <select id="listSoftwareAppIds" resultType="java.lang.Long">
        select relevance_id from rel_software_app_info where software_name = #{softwareName}
    </select>

    <insert id="insert">
        insert into rel_software_app_info (software_name, relevance_id, md5_level )
        values
        <foreach collection="appInfoIds" item="appInfoId" separator=",">
            (#{softwareName}, #{appInfoId}, #{md5Level})
        </foreach>
    </insert>

    <delete id="delete">
        delete from rel_software_app_info where software_name = #{softwareName}
    </delete>

</mapper>
