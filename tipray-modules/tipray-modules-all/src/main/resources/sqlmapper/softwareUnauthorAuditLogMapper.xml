<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SoftwareUnauthorAuditLogDao">

    <resultMap type="com.tipray.dlp.bean.SoftwareUnauthorAuditLog" id="SoftwareUnauthorAuditLogMap">
        <result property="id" column="id"/>
        <result property="recordNum" column="record_num"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="softwareName" column="software_name"/>
        <result property="softwareVersion" column="software_ver"/>
        <result property="uninstallResult" column="uninstall_ret"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="uninstallResult != null">
                and uninstall_ret = #{uninstallResult}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from software_unauthor_audit_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="SoftwareUnauthorAuditLogMap">
        select id, user_id, term_id, create_time, term_group_id, user_group_id, software_name, software_ver, uninstall_ret
        from software_unauthor_audit_log
        <include refid="sql_where"></include>
    </select>


</mapper>
