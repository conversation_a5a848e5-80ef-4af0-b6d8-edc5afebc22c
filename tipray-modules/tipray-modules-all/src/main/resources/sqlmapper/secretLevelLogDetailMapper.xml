<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SecretLevelLogDetailDao">

    <resultMap type="com.tipray.dlp.bean.SecretLevelLogDetail" id="SecretLevelLogDetailMap">
        <result property="id" column="id"/>
        <result property="mainGuid" column="main_guid"/>
        <result property="srcLevel" column="src_level"/>
        <result property="desLevel" column="des_level"/>
        <result property="opResult" column="op_result"/>
        <result property="fileName" column="file_name"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <if test="mainGuid != null">
                and main_guid = #{mainGuid}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.SecretLevelLogDetailVO" resultType="java.lang.Long">
      select count(0) from secret_level_log_detail
        <include refid="sql_where"></include>
    </select>
    
    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.SecretLevelLogDetailVO" resultMap="SecretLevelLogDetailMap">
      select id, main_guid, src_level, des_level, op_result, file_name, create_time from secret_level_log_detail
        <include refid="sql_where"></include>
    </select>

    <delete id="deleteByIds">
        delete from secret_level_log_detail where id in (${value})
    </delete>

</mapper>