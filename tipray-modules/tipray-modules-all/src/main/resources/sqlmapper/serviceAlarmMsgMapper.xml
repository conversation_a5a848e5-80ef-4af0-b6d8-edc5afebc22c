<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ServiceAlarmMsgDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.ServiceAlarmMsg">
        <id column="id" property="id"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="timestamp" property="timestamp"/>
        <result column="dev_id" property="devId"/>
        <result column="dev_type" property="devType"/>
        <result column="stg_id" property="stgId"/>
        <result column="alarm_limit" property="alarmLimit"/>
        <result column="deal_status" property="dealStatus"/>
        <result column="notify_group_id" property="notifyGroupId"/>
        <result column="event_param" property="eventParam"/>
    </resultMap>

    <sql id="alarm_where_sql">
        <where>
            <if test="ids != null and ids != ''">
                and id in (@ids)
            </if>
            <if test="alarmTypes != null and alarmTypes != ''">
                and alarm_type in (@alarmTypes)
            </if>
            <if test="devType != null">
                and dev_type = #{devType}
            </if>
            <if test="devId != null">
                and dev_id = #{devId}
            </if>
            <if test="startTimestamp != null and endTimestamp != null">
                and timestamp &gt;= #{startTimestamp} and timestamp &lt; #{endTimestamp}
            </if>
            <if test="dealStatus != null">
                and deal_status = #{dealStatus}
            </if>
        </where>
    </sql>
    <update id="updateMsgDealStatus">
        <![CDATA[ update dlp_datamgr.service_alarm_msg set deal_status = deal_status + ${dealStatus} where id in (@ids) and (deal_status & ${dealStatus}) = 0 ]]>
    </update>
    <update id="updateMsgAllDeal">
        <![CDATA[ update dlp_datamgr.service_alarm_msg set deal_status = alarm_limit where id in (@ids) ]]>
    </update>
    <update id="updateByConsumerMsgs">
        update dlp_datamgr.service_alarm_msg set alarm_objects = case id
        <foreach collection="msgs" item="item">
             when #{item.id} then #{item.alarmObjects}
        </foreach>
        end, deal_status = alarm_limit where id in (@msgs.id)
    </update>


    <delete id="deleteByIds">
        delete from dlp_datamgr.service_alarm_msg where id in (@ids)
    </delete>

    <select id="listByVo" resultMap="resultMap">
        select * from dlp_datamgr.service_alarm_msg
        <include refid="alarm_where_sql"/>
    </select>

</mapper>