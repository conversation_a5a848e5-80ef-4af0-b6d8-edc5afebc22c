<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ModuleSubDao">
    <resultMap type="com.tipray.dlp.bean.ModuleSub" id="resultMap">
        <result property="productId" column="product_id"/>
        <result property="moduleId" column="module_id"/>
        <result property="subModuleId" column="sub_module_id"/>
        <result property="subModuleDesc" column="sub_module_desc"/>
    </resultMap>

    <select id="list" resultMap="resultMap">
		select * from mod_sub_module_info
	</select>
    <select id="getTermSubModule" resultType="com.tipray.dlp.bean.TermModuleSub">
        select id,term_id termId,submodules,version from mod_terminal_sub_module where term_id = #{termId}
    </select>

</mapper>
