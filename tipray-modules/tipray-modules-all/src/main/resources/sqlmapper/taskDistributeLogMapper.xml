<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.TaskDistributeLogDao">

    <resultMap type="com.tipray.dlp.bean.TaskDistributeLog" id="taskDistributeLogMap">
        <result property="id" column="id"/>
        <result property="recordNum" column="record_num"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="taskId" column="task_id"/>
        <result property="taskName" column="task_name"/>
        <result property="fileName" column="file_name"/>
        <result property="state" column="state"/>
        <result property="taskFailCode" column="task_fail_code"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="taskName != null and taskName != ''">
                and task_name like CONCAT('%',#{taskName},'%')
            </if>
            <if test="state != null and state != ''">
                and state = #{state}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from task_assign_audit
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="taskDistributeLogMap">
        select id, record_num, user_id,term_id, create_time, task_id, task_name, file_name, state, task_fail_code, term_group_id, user_group_id
        from task_assign_audit
        <include refid="sql_where"></include>
    </select>

</mapper>
