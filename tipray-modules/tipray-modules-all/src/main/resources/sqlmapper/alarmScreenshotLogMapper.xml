<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.AlarmScreenshotLogDao">

    <resultMap type="com.tipray.dlp.bean.AlarmScreenshotLog" id="AlarmScreenshotLogMap">
        <result property="id" column="id"/>
        <result property="guid" column="guid"/>
        <result property="createTime" column="create_time"/>
        <result property="backupServerId" column="backup_server_id"/>
        <result property="uploadFileGuid" column="upload_file_guid"/>
        <result property="fileIndex" column="file_index"/>
        <result property="localfilepath" column="local_file_path"/>
        <result property="capscreenGuid" column="cap_screen_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="capscreenGuid != null and capscreenGuid != ''">
                and cap_screen_id = #{capscreenGuid}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.AlarmScreenshotLogVO" resultType="java.lang.Long">
      select count(0) from alarm_screenshot_log
      <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.AlarmScreenshotLogVO" resultMap="AlarmScreenshotLogMap">
      select id, guid, create_time, backup_server_id, upload_file_guid, file_index, local_file_path, cap_screen_id from alarm_screenshot_log
      <include refid="sql_where"></include>
    </select>

</mapper>
