<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.NetFileShareRecordDao">

    <resultMap type="com.tipray.dlp.bean.NetFileShareRecord" id="NetFileShareRecordMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="fileName" column="file_name"/>
        <result property="localFilePath" column="local_file_path"/>
        <result property="fileSize" column="file_size"/>
        <result property="createTime" column="create_time"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="fileOpType" column="file_op_type"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="actionType" column="action_type"/>
        <result property="srcFilePath" column="src_file_path"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name like CONCAT('%',#{fileName},'%')
            </if>
            <if test="fileOpType != null and fileOpType != ''">
                and file_op_type = #{fileOpType}
            </if>
            <if test="actionType != null">
                and action_type = #{actionType}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.NetFileShareLogVO" resultType="java.lang.Long">
      select count(0) from net_file_share_record
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.NetFileShareLogVO" resultMap="NetFileShareRecordMap">
      select id, term_id, user_id, file_name, file_size, create_time,backup_server_id,local_file_path,upload_file_guid,file_op_type,term_group_id,user_group_id,action_type, src_file_path
      from net_file_share_record
      <include refid="sql_where"></include>
    </select>

</mapper>
