<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SoftwareRelaStgDao">

    <select id="listStgId" resultType="java.lang.Long">
        select relevance_id from rel_software_strategy where software_name = #{value}
    </select>

    <select id="listSoftwareName" resultType="java.lang.String">
        select software_name from rel_software_strategy where relevance_id = #{value}
    </select>

    <insert id="insert">
        insert into rel_software_strategy(relevance_id, software_name)
        value (#{stgId}, #{softwareName})
    </insert>

    <delete id="deleteByStgId">
       delete from rel_software_strategy where relevance_id = #{value}
    </delete>

    <delete id="deleteByStgIds">
       delete from rel_software_strategy where relevance_id in (${value})
    </delete>
</mapper>