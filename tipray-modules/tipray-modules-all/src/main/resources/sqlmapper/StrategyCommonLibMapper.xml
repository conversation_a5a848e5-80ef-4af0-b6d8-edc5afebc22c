<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.StrategyCommonLibDao">


    <select id="listStgLibInfo" resultType="java.util.Map">
        select rs.lib_type libType, rs.lib_id libId, su.url_keyword url, sd.name strategyName from rel_stg_common_lib  rs
        left join screen_watermark_url su on rs.lib_id = su.id
        left join stg_def sd on rs.stg_id = sd.id
        where rs.lib_type = #{libType} and rs.lib_id in (${libIds})
    </select>
</mapper>