<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ProcessLibraryDao">

    <resultMap type="com.tipray.dlp.bean.ProcessLibrary" id="resultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
    </resultMap>

    <sql id="vo_where_sql">
        <if test="searchInfo != null and searchInfo != ''">
            and name like CONCAT('%',#{searchInfo},'%')
        </if>
        <include refid="commonSQL.andNotDel"></include>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultMap="resultMap">
        select * from http_white_list_process where 1 = 1
        <include refid="vo_where_sql"/>
    </select>

</mapper>
