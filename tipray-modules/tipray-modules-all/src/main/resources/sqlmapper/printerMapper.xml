<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.PrinterDao">

    <resultMap type="com.tipray.dlp.bean.Printer" id="PrinterMap">
        <result property="id" column="id"/>
        <result property="printName" column="print_name"/>
        <result property="driverName" column="driver_name"/>
        <result property="groupId" column="group_id"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <include refid="commonSQL.andNotDel"></include>
            <if test="groupId != null and groupId != 0">
                and group_id = #{groupId}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and (print_name like CONCAT('%',#{searchInfo},'%') or driver_name like CONCAT('%',#{searchInfo},'%'))
            </if>
            <if test="ids != null and ids != ''">
                and id in (${ids})
            </if>
        </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.PrinterVO" resultMap="PrinterMap">
        select * from printer
        <include refid="vo_where_sql"/>
    </select>

</mapper>
