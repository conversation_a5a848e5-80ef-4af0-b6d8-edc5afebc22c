<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.FilePermissionControlDataDao">

    <resultMap type="com.tipray.dlp.bean.FilePermissionControlData" id="resultMap">
        <result property="id" column="id"/>
        <result property="groupId" column="group_id"/>
        <result property="type" column="type"/>
        <result property="value" column="value"/>
        <result property="modifyVer" column="modify_ver"/>
        <result property="deleted" column="deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <include refid="commonSQL.andNotDel"></include>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="groupId != null">
                and group_id = #{groupId}
            </if>
            <if test="value != null and value != ''">
                and value like CONCAT('%',#{value},'%')
            </if>
        </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.FilePermissionControlDataVO" resultMap="resultMap">
        select * from file_permission_control_data
        <include refid="vo_where_sql"/>
    </select>

</mapper>
