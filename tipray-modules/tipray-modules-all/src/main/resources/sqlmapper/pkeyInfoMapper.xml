<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.PkeyInfoDao">

    <resultMap type="com.tipray.dlp.bean.PkeyInfo" id="PkeyInfoMap">
        <result property="id" column="id"/>
        <result property="keyId" column="key_id"/>
        <result property="pkey" column="pkey"/>
        <result property="flag" column="flag"/>
        <result property="pcrc" column="pcrc"/>
    </resultMap>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultMap="PkeyInfoMap">
        select id, key_id, pkey, flag, pcrc
        from pkey_info
        where 1=1
        <include refid="commonSQL.andNotDel"></include>
    </select>

</mapper>
