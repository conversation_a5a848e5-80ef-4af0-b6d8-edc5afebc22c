<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.IssueLogDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.IssueLog">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="term_id" property="terminalId"/>
        <result column="loss_type" property="lossType"/>
        <result column="severity" property="severity"/>
        <result column="stg_def_id" property="strategyId"/>
        <result column="content" property="content"/>
        <result column="rules" property="rules"/>
        <result column="responds" property="responds"/>
        <result column="file_name" property="fileName"/>
        <result column="sender" property="sender"/>
        <result column="receiver" property="receiver"/>
        <result column="sender_ip" property="senderIp"/>
        <result column="receiver_ip" property="receiverIp"/>
        <result column="create_time" property="occurTime"/>
        <result column="violator" property="violator"/>
        <result column="stg_name" property="strategy"/>
        <result column="backup_server_id" property="devId"/>
        <result column="upload_file_guid" property="fileGuid"/>
        <result column="encryption_type" property="encryptionType"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="childFilePath" column="child_file_path"/>
        <result property="eventGuid" column="event_guid"/>
        <result property="recvInfoType" column="recv_info_type"/>
    </resultMap>
    <sql id="full_fields_sql">
        l.id,l.user_id,l.term_id,l.loss_type,l.severity,l.stg_def_id,l.content,l.rules,l.responds,l.file_name,l.sender,l.receiver,l.sender_ip,l.receiver_ip,l.create_time,l.stg_name,l.backup_server_id,l.upload_file_guid,l.encryption_type,l.term_group_id,l.user_group_id,l.child_file_path,l.event_guid,l.recv_info_type
    </sql>

    <sql id="vo_where_sql">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="lossType != null">
                and loss_type = #{lossType}
            </if>
            <if test="severity != null">
                and severity = #{severity}
            </if>
            <if test="strategyId != null">
                and stg_def_id = #{strategyId}
            </if>
            <if test="filterNullFile != null and filterNullFile == 1">
                and file_name is not null and file_name != ''
            </if>
            <if test="filterLossType != null and filterLossType.size()>0">
                <foreach item="item" index="index" collection="filterLossType" separator="," open="and loss_type not in (" close=")">
                    #{item}
                </foreach>
            </if>
            <choose>
                <when test="strategyName != null and strategyName != ''">
                    and ( stg_name like CONCAT('%',#{strategyName},'%')
                        <if test="strategyIds != null and strategyIds != ''">
                            or stg_def_id in (${strategyIds}) and stg_name is null
                        </if>
                    )
                </when>
                <otherwise>
                    <if test="strategyIds != null and strategyIds != ''">
                        and stg_def_id in (${strategyIds})
                    </if>
                </otherwise>
            </choose>
        </where>
    </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="resultMap">
		select <include refid="full_fields_sql"/>,
		(select name from stg_def s where s.id = stg_def_id ) strategy,
		(select name from terminal t where t.id = term_id ) violator
		from general_scan_violate_log l where id=#{value}
	</select>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.IssueLogVO" resultType="java.lang.Long">
        select count(0) from general_scan_violate_log l
        <include refid="vo_where_sql"/>
    </select>
    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.IssueLogVO" resultMap="resultMap">
        select <include refid="full_fields_sql"/>
        from general_scan_violate_log l
        <include refid="vo_where_sql"/>
    </select>

</mapper>
