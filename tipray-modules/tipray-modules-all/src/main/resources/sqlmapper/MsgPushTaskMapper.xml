<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.MsgPushTaskDao">

    <resultMap id="resultMap" type="com.tipray.dlp.bean.MsgPushTask">
        <id property="id" column="id"/>
        <result column="name" property="name"/>
        <result column="data_type" property="dataType"/>
        <result column="data_config_id" property="dataConfigId"/>
        <result column="execution_rules" property="executionRules"/>
        <result column="remark" property="remark"/>
        <result column="push_status" property="isPush"/>
        <result column="next_push_time" property="nextPushTime"/>
        <result column="last_exec_time" property="lastExecTime"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
<!--            <if test="time != null">-->
<!--                <![CDATA[ and DATE_FORMAT(next_push_time , '%Y-%m-%d %H-%i') <= DATE_FORMAT(#{time}, '%Y-%m-%d %H-%i')]]>-->
<!--            </if>-->
            <if test="dataType != null">
                and data_type = #{dataType}
            </if>
             <if test="isPush != null">
                 and push_status = #{isPush}
             </if>
        </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.MsgPushTaskVO" resultMap="resultMap">
        select *
        from msg_push_task
        <include refid="vo_where_sql"></include>
    </select>
<!--    <select id="selectListByEntity" resultType="com.tipray.dlp.bean.MsgPushTask">-->
<!--        select task.*, report.id as m_id, report.*  from msg_push_task task left join rel_task_push_report report -->
<!--        <where>-->
<!--        </where>-->
<!--    </select>-->


</mapper>