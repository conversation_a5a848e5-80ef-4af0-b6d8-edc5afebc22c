<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SensitiveFileOutsendConfigDao">

    <resultMap type="com.tipray.dlp.bean.SensitiveFileOutsendConfig" id="SensitiveFileOutsendConfigMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="maxDay" column="max_day" jdbcType="INTEGER"/>
        <result property="outSendTimes" column="out_send_times" jdbcType="INTEGER"/>
        <result property="isAutoDec" column="is_auto_dec" jdbcType="INTEGER"/>
        <result property="isShowTip" column="is_show_tip" jdbcType="INTEGER"/>
        <result property="matchType" column="match_type" jdbcType="INTEGER"/>
        <result property="notLimitFile" column="not_limit_file" jdbcType="INTEGER"/>
        <result property="active" column="active" jdbcType="INTEGER"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectAll" resultMap="SensitiveFileOutsendConfigMap">
        select id, max_day, out_send_times, is_auto_dec,
        is_show_tip, match_type,not_limit_file, active
        from sensitive_file_outsend_config where deleted = 0
    </select>
</mapper>
