<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.cloud.offlinestrategy.dao.OfflineStrategyExtendLogDao">

    <!-- 日志查询结果映射 -->
    <resultMap id="logVOResultMap" type="com.tipray.dlp.cloud.offlinestrategy.controller.vo.OfflineStrategyExtendLogVO">
        <id column="id" property="id"/>
        <result column="offline_strategy_extend_config_id" property="offlineStrategyExtendConfigId"/>
        <result column="version" property="version"/>
        <result column="log_type" property="logType"/>
        <result column="log_type_desc" property="logTypeDesc"/>
        <result column="log_content" property="logContent"/>
        <result column="ip" property="ip"/>
        <result column="ip_location" property="ipLocation"/>
        <result column="create_time" property="createTime"/>
        <result column="user_id" property="userId"/>
        <result column="target_type" property="targetType"/>
        <result column="target_type_desc" property="targetTypeDesc"/>
        <result column="target" property="target"/>
        <result column="operate_time" property="operateTime"/>
    </resultMap>

    <!-- 查询条件 -->
    <sql id="logQueryConditions">
        <where>
            <if test="offlineStrategyExtendConfigId != null">
                AND l.offline_strategy_extend_config_id = #{offlineStrategyExtendConfigId}
            </if>
            <if test="logType != null and logType != ''">
                AND l.log_type = #{logType}
            </if>
            <if test="userId != null and userId != ''">
                AND l.user_id = #{userId}
            </if>
            <if test="startTime != null and startTime != ''">
                AND l.operate_time >= CONCAT(#{startTime}, ' 00:00:00')
            </if>
            <if test="endTime != null and endTime != ''">
                AND l.operate_time &lt;= CONCAT(#{endTime}, ' 23:59:59')
            </if>
            <if test="targetType != null and targetType != ''">
                AND target_type = #{targetType}
            </if>
            <if test="target != null and target != ''">
                AND target = #{target}
            </if>
        </where>
    </sql>

    <!-- 分页查询日志VO（连表查询） -->
    <select id="selectLogPageByVO" resultMap="logVOResultMap">
        SELECT
            l.id,
            l.offline_strategy_extend_config_id,
            l.target_type,
            l.target,
            l.version,
            l.log_type,
            l.log_content,
            l.ip,
            l.ip_location,
            l.create_time,
            l.user_id,
            l.operate_time
        FROM ose_offline_strategy_extend_log l
        <include refid="logQueryConditions"/>
        ORDER BY l.operate_time DESC, l.id DESC
    </select>

    <!-- 批量插入日志（保留原始创建时间） -->
    <insert id="batchInsertWithOriginalTime" parameterType="java.util.List">
        INSERT INTO ose_offline_strategy_extend_log (
            target_type,
            target,
            offline_strategy_extend_config_id,
            version,
            log_type,
            log_content,
            ip,
            ip_location,
            user_id,
            create_time,
            modify_time,
            operate_time
        ) VALUES
        <foreach collection="logs" item="log" separator=",">
            (
                #{log.targetType},
                #{log.target},
                #{log.offlineStrategyExtendConfigId},
                #{log.version},
                #{log.logType},
                #{log.logContent},
                #{log.ip},
                #{log.ipLocation},
                #{log.userId},
                #{log.createTime},
                #{log.modifyTime},
                #{log.operateTime}
            )
        </foreach>
    </insert>

</mapper>
