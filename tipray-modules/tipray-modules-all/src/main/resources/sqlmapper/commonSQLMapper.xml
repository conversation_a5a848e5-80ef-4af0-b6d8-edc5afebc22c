<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="commonSQL">
    <sql id="andNotDel">
        and deleted = 0
    </sql>
    <sql id="isDel">
        deleted = 0
    </sql>
    <sql id="andObjectTypeAndObjectId">
        <if test="objectType == 1">
            <if test="objectId != null">
                and term_id = #{objectId}
            </if>
            <if test="objectIds != null and objectIds != ''">
                and term_id in (${objectIds})
            </if>
        </if>
        <if test="objectType == 2">
            <if test="objectId != null">
                and user_id = #{objectId}
            </if>
            <if test="objectIds != null and objectIds != ''">
                and user_id in (${objectIds})
            </if>
        </if>
        <if test="objectType == 3">
            <if test="objectId != null">
                and term_group_id = #{objectId}
            </if>
            <if test="objectIds != null and objectIds != ''">
                and term_group_id in (${objectIds})
            </if>
        </if>
        <if test="objectType == 4">
            <if test="objectId != null">
                and user_group_id = #{objectId}
            </if>
            <if test="objectIds != null and objectIds != ''">
                and user_group_id in (${objectIds})
            </if>
        </if>
        <!--<if test="isTermObject == true">
            <if test="objectId != null">
                and term_id = #{objectId}
            </if>
            <if test="objectIds != null and objectIds != ''">
                and term_id in (${objectIds})
            </if>
        </if>
        <if test="isTermObject == false">
            <if test="objectId != null">
                and user_id = #{objectId}
            </if>
            <if test="objectIds != null and objectIds != ''">
                and user_id in (${objectIds})
            </if>
        </if>-->
    </sql>
</mapper>