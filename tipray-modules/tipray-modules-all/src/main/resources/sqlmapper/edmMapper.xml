<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.EdmDao">

    <resultMap type="com.tipray.dlp.bean.Edm" id="EdmMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="groupId" column="group_id"/>
        <result property="tableName" column="table_name"/>
        <result property="cols" column="cols"/>
        <result property="querySql" column="query_sql"/>
        <result property="fileMd5" column="file_MD5"/>
        <result property="fileName" column="file_name"/>
        <result property="colSeparator" column="col_separator"/>
        <result property="firstRowAsColName" column="first_row_as_col_name"/>
        <result property="state" column="state"/>
        <result property="cycle" column="cycle"/>
        <result property="trainCostTime" column="train_cost_time"/>
        <result property="trainTime" column="train_time"/>
        <result property="result" column="fail_result"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <select id="countRuleByFileMd5" resultType="java.lang.Long">
        select count(0) from rule_edm where file_MD5 = #{value}
    </select>

    <select id="listFileMd5" resultType="java.lang.String">
        select file_MD5 from rule_edm
    </select>

    <!--查询单个-->
    <select id="getById" resultMap="EdmMap">
        select id, name, group_id, table_name, cols, query_sql, file_MD5, file_name, col_separator, first_row_as_col_name, state, cycle, train_cost_time, train_time, create_time, modify_time, fail_result        from rule_edm
        where id = #{id}
    </select>

    <select id="getByName" resultMap="EdmMap">
        select id, name, group_id, table_name, cols, query_sql, file_MD5, file_name, col_separator, first_row_as_col_name, state, cycle, train_cost_time, train_time, create_time, modify_time, fail_result        from rule_edm
        where name = #{name}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="list" resultMap="EdmMap">
        select id, name, group_id, table_name, cols, query_sql, file_MD5, file_name, col_separator, first_row_as_col_name, state, cycle, train_cost_time, train_time, create_time, modify_time, fail_result        from rule_edm
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into rule_edm(id, name, group_id, table_name, cols, query_sql, file_MD5, file_name, col_separator, first_row_as_col_name, state, cycle, train_cost_time, train_time, create_time, modify_time)
        values (#{id}, #{name}, #{groupId}, #{tableName}, #{cols}, #{querySql}, #{fileMd5}, #{fileName}, #{colSeparator}, #{firstRowAsColName}, #{state}, #{cycle}, #{trainCostTime}, #{trainTime}, #{createTime}, #{modifyTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update rule_edm
        <set>
            table_name = #{tableName},
            cols = #{cols},
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="groupId != null">
                group_id = #{groupId},
            </if>
            <if test="querySql != null and querySql != ''">
                query_sql = #{querySql},
            </if>
            <if test="fileMd5 != null and fileMd5 != ''">
                file_MD5 = #{fileMd5},
            </if>
            <if test="fileName != null and fileName != ''">
                file_name = #{fileName},
            </if>
            <if test="colSeparator != null and colSeparator != ''">
                col_separator = #{colSeparator},
            </if>
            <if test="firstRowAsColName != null">
                first_row_as_col_name = #{firstRowAsColName},
            </if>
            <if test="state != null">
                state = #{state},
            </if>
            <if test="cycle != null">
                cycle = #{cycle},
            </if>
            <if test="trainCostTime != null">
                train_cost_time = #{trainCostTime},
            </if>
            <if test="trainTime != null">
                train_time = #{trainTime},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
            <if test="result != null and result != ''">
                fail_result = #{result}
            </if>
        </set>
        where id = #{id}
    </update>

    <sql id="vo_where_sql">
        <where>
            <if test="groupId != null and groupId != 0">
                and group_id = #{groupId}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and name like CONCAT('%',#{searchInfo},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO"  parameterType="com.tipray.dlp.bean.vo.EdmVO" resultType="java.lang.Long">
        select count(0) from rule_edm
        <include refid="vo_where_sql"/>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.EdmVO" resultMap="EdmMap">
        select * from rule_edm
        <include refid="vo_where_sql"/>
    </select>

    <select id="listById" parameterType="java.lang.String" resultMap="EdmMap">
        select * from rule_edm where id in (${value})
    </select>

    <update id="updateStatus">
        update rule_edm
        <set>
            state = #{state},
            <if test="fileMd5 != null and fileMd5 != ''">
                file_MD5 = #{fileMd5},
            </if>
            <if test="fileName != null and fileName != ''">
                file_name = #{fileName},
            </if>
            <if test="trainCostTime != null">
                train_cost_time = #{trainCostTime},
            </if>
            <if test="result != null and result != ''">
                fail_result = #{result}
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="listTrainingId" resultType="java.lang.Long">
        select id from rule_edm where state = 1
    </select>

    <select id="getQuerySql" parameterType="java.lang.Long" resultType="java.lang.String">
        select query_sql from rule_edm where id = #{value}
    </select>

</mapper>
