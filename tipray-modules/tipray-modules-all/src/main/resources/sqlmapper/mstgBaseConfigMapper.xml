<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.MstgBaseConfigDao">

    <resultMap type="com.tipray.dlp.bean.MstgBaseConfig" id="MstgBaseConfigMap">
        <result property="id" column="id"/>
        <result property="number" column="number"/>
        <result property="strategyKey" column="stg_key"/>
        <result property="name" column="name"/>
        <result property="usedScope" column="used_scope"/>
        <result property="osType" column="os_type"/>
        <result property="osAble" column="os_able"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="list" resultMap="MstgBaseConfigMap">
        select id, number, stg_key, name, used_scope, os_type, os_able
        from mstg_base_config
    </select>

    <select id="getUsedScopeByStgTypeNumber" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT used_scope FROM mstg_base_config
        WHERE number = #{stgTypeNumber}
    </select>

</mapper>
