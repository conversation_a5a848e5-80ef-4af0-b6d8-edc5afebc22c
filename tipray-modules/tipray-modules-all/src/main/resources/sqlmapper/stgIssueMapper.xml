<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.StgIssueDao">

    <resultMap type="com.tipray.dlp.bean.StgIssue" id="StgIssueMap">
        <result property="id" column="id"/>
        <result property="dataType" column="data_type"/>
        <result property="objectType" column="object_type"/>
        <result property="objectIds" column="object_ids"/>
        <result property="objectGroupIds" column="object_group_ids"/>
        <result property="dealStatus" column="deal_status"/>
        <result property="content" column="content"/>
        <result property="stgType" column="stg_type"/>
    </resultMap>

    <select id="listContent" resultType="java.lang.String">
        select content from stg_issue
    </select>

    <select id="listUnDeal" parameterType="java.lang.Integer" resultMap="StgIssueMap">
        select id, data_type, object_type, object_ids, object_group_ids, deal_status, content, stg_type        from stg_issue
        WHERE deal_status = 0
        <if test="type != null and type == 1">
            and stg_type is null or stg_type = #{type}
        </if>
        <if test="type != null and type == 2">
            and stg_type = #{type}
        </if>
        order by object_type
    </select>
</mapper>
