<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.AlarmLogDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.AlarmLog">
        <id column="id" property="id"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="alarm_bus" property="alarmBus"/>
        <result column="computer_name" property="terminalName"/>
        <result column="user_name" property="userName"/>
        <result column="term_id" property="terminalId"/>
        <result column="user_id" property="userId"/>
        <result column="create_time" property="createTime"/>
        <result column="file_path" property="filePath"/>
        <result column="action" property="action"/>
        <result column="alarm_limit" property="alarmLimit"/>
        <result column="event_desc" property="eventDescription"/>
        <result column="event_param" property="eventParam"/>
        <result column="alarm_guid" property="alarmGuid"/>
        <result column="capscreen_guid" property="capscreenGuid"/>
        <result column="recscreen_guid" property="recscreenGuid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="taskGuid" column="task_guid"/>
    </resultMap>
    <sql id="full_fields_sql">
        id, alarm_type, alarm_level, alarm_bus, computer_name, user_name, term_id, user_id, create_time, file_path, action, alarm_limit, event_desc, event_param, alarm_guid, capscreen_guid, recscreen_guid, term_group_id, user_group_id, task_guid
    </sql>

    <sql id="vo_where_sql">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="alarmType != null">
                and alarm_type = #{alarmType}
            </if>
<!--            <if test="alarmTypeAndBus != null and alarmTypeAndBus != ''">-->
<!--                and CONCAT(alarm_type,'-',COALESCE(alarm_bus, '0')) in (${alarmTypeAndBus})-->
<!--            </if>-->
            <if test="alarmTypes != null and alarmTypes != ''">
                and alarm_type in (${alarmTypes})
            </if>
            <if test="alarmLevel != null">
                and alarm_level = #{alarmLevel}
            </if>
            <if test="alarmBus != null">
                and alarm_bus = #{alarmBus}
            </if>
            <if test="action != null">
                and action = #{action}
            </if>
            <if test="alarmLimit != null">
                and alarm_limit = #{alarmLimit}
            </if>
            <if test="guid != null">
                and alarm_guid = #{guid}
            </if>
            <if test="guids != null and guids !=''">
                and alarm_guid in (${guids})
            </if>
            <if test="taskGuid != null and taskGuid !=''">
                and task_guid = #{taskGuid}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.AlarmLogVO" resultType="java.lang.Long">
        select count(0) from alarm_log
        <include refid="vo_where_sql"/>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.AlarmLogVO" resultMap="resultMap">
        select <include refid="full_fields_sql"/>
        from alarm_log
        <include refid="vo_where_sql"/>
    </select>

</mapper>
