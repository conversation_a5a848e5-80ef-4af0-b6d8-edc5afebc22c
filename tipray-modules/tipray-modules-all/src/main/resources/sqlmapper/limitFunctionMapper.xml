<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.LimitFunctionDao">

    <resultMap type="com.tipray.dlp.bean.LimitFunction" id="LimitFunctionMap">
        <result property="id" column="id"/>
        <result property="deviceName" column="device_name"/>
        <result property="parentId" column="parent_id"/>
        <result property="active" column="active"/>
        <result property="osType" column="os_type"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="list" resultMap="LimitFunctionMap">
        select id, device_name, parent_id, active, os_type, create_time, modify_time
        from limit_function
        where active = 1
        order by id
    </select>

</mapper>
