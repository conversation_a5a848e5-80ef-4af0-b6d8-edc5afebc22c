<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.StgEffectiveInfoDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.StgEffectiveInfo">
        <id column="id" property="id" />
        <result column="term_id" property="termId"/>
        <result column="user_id" property="userId"/>
        <result column="stg_type" property="stgType"/>
        <result column="stg_type_number" property="stgTypeNumber"/>
        <result column="stg_ver" property="stgVer"/>
        <result column="stg_modify_time" property="stgModifyTime"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="termId != null">
                and term_id = #{termId}
            </if>
            <if test="stgType != null">
                and stg_type = #{stgType}
            </if>
            <if test="stgTypeNumber != null">
                and stg_type_number = #{stgTypeNumber}
            </if>
            <if test="stgTypeNumbers != null and stgTypeNumbers != ''">
                and stg_type_number in (${stgTypeNumbers})
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
        </where>
    </sql>

    <select id="listVO" parameterType="com.tipray.dlp.bean.vo.StgEffectiveInfoVO" resultMap="resultMap">
		select id, term_id, user_id, stg_type, stg_type_number, stg_ver, stg_modify_time, modify_ver, modify_time from stg_effective_info
        <include refid="sql_where"></include>
	</select>

</mapper>
