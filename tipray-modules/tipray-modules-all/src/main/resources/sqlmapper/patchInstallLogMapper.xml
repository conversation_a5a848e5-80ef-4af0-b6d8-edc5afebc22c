<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.PatchInstallLogDao">

    <resultMap type="com.tipray.dlp.bean.PatchInstallLog" id="patchInstallLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="createTime" column="create_time"/>
        <result property="patchKb" column="patch_kb"/>
        <result property="patchTitle" column="patch_title"/>
        <result property="patchLevel" column="patch_level"/>
        <result property="installStyle" column="install_style"/>
        <result property="installState" column="install_state"/>
        <result property="installResult" column="install_result"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="patchKb != null and patchKb != ''">
                and patch_kb like CONCAT('%',#{patchKb},'%')
            </if>
            <if test="patchTitle != null and patchTitle != ''">
                and patch_title like CONCAT('%',#{patchTitle},'%')
            </if>
            <if test="installState != null">
                and install_state = #{installState}
            </if>
            <if test="patchLevel != null and patchLevel != ''">
                and patch_level = #{patchLevel}
            </if>
            <if test="installStyle != null and installStyle != ''">
                and install_style = #{installStyle}
            </if>
            <if test="installResult != null and installResult != ''">
                and install_result = #{installResult}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.PatchInstallLogVO" resultType="java.lang.Long">
        select count(0) from patch_install_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.PatchInstallLogVO" resultMap="patchInstallLogMap">
        select id, user_id,term_id, create_time, patch_kb, patch_title, patch_level, install_style,install_state, install_result, term_group_id, user_group_id
        from patch_install_log
        <include refid="sql_where"></include>
    </select>

</mapper>
