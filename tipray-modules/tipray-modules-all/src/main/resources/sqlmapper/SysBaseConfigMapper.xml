<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SysBaseConfigDao">
    <resultMap id="baseResultMap" type="com.tipray.dlp.bean.SysBaseConfig">
        <id column="id" property="id" />
        <result column="func_id" property="funcId" />
        <result column="func_name" property="funcName" />
        <result column="parent_id" property="parentId" />
    </resultMap>

    <select id="listFunctions" resultMap="baseResultMap">
        select func_id, func_name,parent_id from base_function
        group by func_id, func_name, parent_id
        order by func_id, func_name, parent_id
    </select>

</mapper>
