<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DevServerLoginLogDao">

    <resultMap type="com.tipray.dlp.bean.DevServerLoginLog" id="resultMap">
        <result property="id" column="id"/>
        <result property="devId" column="dev_id"/>
        <result property="devGuid" column="dev_guid"/>
        <result property="devType" column="dev_type"/>
        <result property="devVersion" column="dev_version"/>
        <result property="showVersion" column="show_version"/>
        <result property="devMac" column="dev_mac"/>
        <result property="intranetPort" column="intranet_port"/>
        <result property="intranetIpv4" column="intranet_ipv4"/>
        <result property="intranetIpv6" column="intranet_ipv6"/>
        <result property="ipv4List" column="ipv4_list"/>
        <result property="ipv6List" column="ipv6_list"/>
        <result property="macList" column="mac_list"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <if test="devId != null">
                and dev_id = #{devId}
            </if>
            <if test="devTypes != null and devTypes != ''">
                and dev_type in (${devTypes})
            </if>
            <if test="ip != null and ip != ''">
                and (intranet_ipv4 like CONCAT('%', #{ip}, '%') or intranet_ipv6 like CONCAT('%', #{ip}, '%'))
            </if>
            <if test="devMac != null and devMac != ''">
                and dev_mac like CONCAT('%', #{devMac}, '%')
            </if>
            <if test="devVersion != null and devVersion != ''">
                and
                (
                ((show_version IS NULL AND dev_version LIKE CONCAT('%', #{devVersion}, '%'))
                OR
                (show_version IS NOT NULL AND (show_version LIKE CONCAT('%', #{devVersion}, '%') OR dev_version LIKE
                CONCAT('%', #{devVersion}, '%'))))
                )
            </if>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="Long">
        select count(*)
        from dev_server_login_log
        <include refid="sql_where"/>
    </select>

    <select id="listByVO" resultMap="resultMap">
        select id, dev_id, dev_guid, dev_type, dev_version, show_version, dev_mac, intranet_port, intranet_ipv4, intranet_ipv6, ipv4_list, ipv6_list, mac_list, create_time
        from dev_server_login_log
        <include refid="sql_where"/>
    </select>

</mapper>
