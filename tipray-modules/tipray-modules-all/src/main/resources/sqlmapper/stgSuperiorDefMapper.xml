<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.StgSuperiorDefDao">

    <select id="listStgIdBySuperiorStgDefId" resultType="java.lang.Long">
        select stg_def_id from stg_superior_def where superior_stg_def_id = #{superiorStgDefId}
        <if test="stgTypeNum != null and stgTypeNum != 0">
            and stg_type_number = #{stgTypeNum}
        </if>
    </select>

    <select id="listStgIdBySuperiorStgDefIds" resultType="java.lang.Long">
        select stg_def_id from stg_superior_def where superior_stg_def_id in (${superiorStgDefIds})
    </select>

    <insert id="insert">
        insert into stg_superior_def(stg_def_id, stg_type_number, superior_stg_def_id)
        value (#{stgDefId}, #{stgTypeNum}, #{superiorStgDefId})
    </insert>

    <delete id="delete">
       delete from stg_superior_def where superior_stg_def_id in (${value})
    </delete>
</mapper>