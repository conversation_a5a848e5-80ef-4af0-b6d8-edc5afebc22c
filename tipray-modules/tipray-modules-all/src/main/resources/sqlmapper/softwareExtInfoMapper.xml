<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SoftwareExtInfoDao">

    <resultMap type="com.tipray.dlp.bean.SoftwareExtInfo" id="resultMap">
        <result property="softwareId" column="software_id"/>
        <result property="softwareName" column="software_name"/>
        <result property="chargeType" column="charge_type"/>
        <result property="softwareType" column="software_type"/>
        <result property="industryType" column="industry_type"/>
        <result property="softwareDesc" column="software_desc"/>
        <result property="publisher" column="publisher"/>
        <result property="appInfo" column="app_info"/>
    </resultMap>

    <select id="selectPageByVO" resultMap="resultMap">
        select software_id,software_name,charge_type,software_type,industry_type,software_desc,publisher from software_ext_info
        <where>
            <if test="softwareName != null and softwareName != ''">
                and software_name like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="chargeType != null and chargeType != ''">
                and charge_type = #{chargeType}
            </if>
            <if test="softwareType != null and softwareType != ''">
                and software_type = #{softwareType}
            </if>
            <if test="industryType != null and industryType != ''">
                and industry_type = #{industryType}
            </if>
        </where>
    </select>

    <select id="listSoftwareNames" resultType="java.lang.String">
        select software_name from software_ext_info
        <where>
            <if test="softwareName != null and softwareName != ''">
                and software_name like CONCAT('%',#{softwareName},'%')
            </if>
            <if test="chargeType != null and chargeType != ''">
                and charge_type = #{chargeType}
            </if>
            <if test="softwareType != null and softwareType != ''">
                and software_type = #{softwareType}
            </if>
            <if test="industryType != null and industryType != ''">
                and industry_type = #{industryType}
            </if>
            <if test="publisher != null and publisher != ''">
                and publisher like CONCAT('%',#{publisher},'%')
            </if>
        </where>
    </select>

    <!--查询单个-->
    <select id="getBySoftwareName" resultMap="resultMap">
        select * from software_ext_info
        where software_name = #{value}
    </select>

    <select id="listBySoftwareName" resultMap="resultMap">
        select * from software_ext_info
        where software_name in (${value})
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into software_ext_info(software_id, software_name, charge_type, software_type, industry_type, software_desc, publisher, app_info)
        values (#{softwareId}, #{softwareName}, #{chargeType}, #{softwareType}, #{industryType}, #{softwareDesc}, #{publisher}, #{appInfo})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update software_ext_info
        <set>
            software_id = #{softwareId},
            charge_type = #{chargeType},
            software_type = #{softwareType},
            industry_type = #{industryType},
            software_desc = #{softwareDesc},
            publisher = #{publisher},
            app_info = #{appInfo}
        </set>
        where software_name = #{softwareName}
    </update>

    <!--通过主键删除-->
    <delete id="deleteBySoftwareNames">
        delete from software_ext_info where software_name in (${value})
    </delete>

    <select id="listSoftwareTypes" resultType="java.lang.String">
        select software_type from software_ext_info group by software_type
    </select>

    <select id="listSoftwareTypeNum" resultType="com.tipray.dlp.bean.SoftwareStatistics">
        select software_type softwareType,COUNT(*) installSize
        from software_ext_info
        group by software_type
        order by installSize desc
    </select>

    <select id="listChargeTypes" resultType="java.lang.String">
        select charge_type from software_ext_info group by charge_type
    </select>

    <select id="listIndustryTypes" resultType="java.lang.String">
        select industry_type from software_ext_info group by industry_type
    </select>

    <select id="listSimpleExtInfo" resultMap="resultMap">
        select software_id, software_name, charge_type, software_type, industry_type, software_desc, publisher, app_info from software_ext_info
        <where>
            software_name in (${value})
        </where>
    </select>

</mapper>
