<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.UrlsLogDao">

    <resultMap type="com.tipray.dlp.bean.UrlsLog" id="UrlsLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="timeStr" column="create_time"/>
        <result property="url" column="url"/>
        <result property="host" column="host"/>
        <result property="webTitle" column="web_title"/>
        <result property="urlEx1" column="url_ext"/>
        <result property="urlGuid" column="url_guid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <if test="hostGroupId != null">
                and host in (@hostList)
            </if>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="webTitle != null and webTitle != ''">
                and web_title like CONCAT('%',#{webTitle},'%')
            </if>
            <if test="host != null and host != ''">
                and host like CONCAT('%',#{host},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from urls_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="UrlsLogMap">
        select id, user_id, term_id, create_time, url, host, web_title, url_ext, url_guid, term_group_id, user_group_id
        from urls_log a
        <include refid="sql_where"></include>
    </select>

</mapper>
