<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.FileOpLogDao">

    <resultMap type="com.tipray.dlp.bean.FileOpLog" id="FileOpLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="opTimeStr" column="create_time"/>
        <result property="processName" column="process_name"/>
        <result property="fileOpType" column="file_op_type"/>
        <result property="fileName" column="file_name"/>
        <result property="diskType1" column="src_disk_type"/>
        <result property="fileOpPath1" column="src_file_path"/>
        <result property="diskType2" column="dest_disk_type"/>
        <result property="fileOpPath2" column="dest_file_path"/>
        <result property="fileMd5" column="file_md5"/>
        <result property="diskName2" column="disk_name"/>
        <result property="localFilePath" column="backup_file_path"/>
        <result property="srcFileSize" column="src_file_size"/>
        <result property="srcFileCreateTime" column="src_file_create_time"/>
        <result property="srcFileModifyTime" column="src_file_modify_time"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>
    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name like CONCAT('%',#{fileName},'%')
            </if>
            <if test="fileOpType != null and fileOpType != 0">
                and file_op_type = #{fileOpType}
            </if>
            <if test="diskType != null">
                and (src_disk_type = #{diskType} or dest_disk_type = #{diskType})
            </if>
            <if test="type == 2">
                and (dest_disk_type = 2 OR src_disk_type=2) AND file_op_type IN(1, 4)
            </if>
            <if test="extList != null and extList.size() > 0 and extList.get(0) != ''">
                <foreach item="ext" open="and (" close=")" separator="or" collection="extList">
                    file_name like CONCAT('%',#{ext})
                </foreach>
            </if>
        </where>
    </sql>
    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from file_op_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="FileOpLogMap">
        select id, user_id, term_id, create_time, process_name, file_op_type, file_name, src_disk_type, src_file_path,
        dest_disk_type, dest_file_path, file_md5, disk_name, backup_file_path,src_file_size,src_file_create_time,src_file_modify_time,
        backup_server_id, upload_file_guid,term_group_id,user_group_id
        from file_op_log a
        <include refid="sql_where"></include>
    </select>

</mapper>
