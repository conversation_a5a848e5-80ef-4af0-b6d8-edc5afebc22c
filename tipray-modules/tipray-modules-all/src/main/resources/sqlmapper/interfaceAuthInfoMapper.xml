<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.InterfaceAuthInfoDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.InterfaceAuthInfo">
        <id column="id" property="id" />
        <result column="name" property="name"/>
        <result column="version" property="version"/>
        <result column="type" property="type"/>
        <result column="auth_type" property="authType"/>
        <result column="decrypt_version" property="decryptVersion"/>
        <result column="group_id" property="groupId"/>
        <result column="encrypt_level" property="encryptLevel"/>
        <result column="permanent" property="permanent"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <if test="searchInfo != null and searchInfo != ''">
                name like CONCAT('%',#{searchInfo},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultType="java.lang.Long">
        select count(id) from interface_auth_info
        <include refid="vo_where_sql"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultMap="resultMap">
        select id, name, version, type, auth_type, decrypt_version, group_id, encrypt_level, permanent, start_date, end_date from interface_auth_info
        <include refid="vo_where_sql"/>
    </select>

    <update id="update" parameterType="com.tipray.dlp.bean.InterfaceAuthInfo">
        update interface_auth_info set
        name=#{name}, version=#{version}, type=#{type}, auth_type=#{authType} decrypt_version=#{decryptVersion}, group_id=#{groupId},
        encrypt_level=#{encryptLevel},start_date=#{startDate}, end_date=#{endDate}, permanent=#{permanent}
        where id=#{id}
    </update>

</mapper>
