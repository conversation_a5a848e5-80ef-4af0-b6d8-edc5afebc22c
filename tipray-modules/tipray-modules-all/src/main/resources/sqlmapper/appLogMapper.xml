<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.AppLogDao">

    <resultMap type="com.tipray.dlp.bean.AppLog" id="AppLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="logTime" column="create_time"/>
        <result property="logType" column="log_type"/>
        <result property="logDesc" column="log_desc"/>
        <result property="objName" column="obj_name"/>
        <result property="processId" column="process_id"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="logDesc != null and logDesc != ''">
                and log_desc like CONCAT('%',#{logDesc},'%')
            </if>
            <if test="keyword2 != null and keyword2 != ''">
                and log_type = #{keyword2}
            </if>
            <if test="keyword3 != null and keyword3 != ''">
                <if test='"1".equals(keyword3)'>
                    and (log_type = 9 or log_type = 10)
                </if>
                <if test='"2".equals(keyword3)'>
                    and log_type = 8
                </if>
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from app_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="AppLogMap">
        select id, user_id, term_id, create_time, log_type, log_desc, obj_name, process_id, term_group_id, user_group_id from app_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listTermAppNum" resultMap="AppLogMap">
        select term_id,term_group_id,count(*) appNum
        from app_log a
        <include refid="sql_where"></include>
        group by term_id,term_group_id
    </select>

</mapper>
