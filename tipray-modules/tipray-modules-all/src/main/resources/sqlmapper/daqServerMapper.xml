<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DAQServerDao">

    <select id="existChild" parameterType="java.lang.String" resultType="java.lang.Boolean">
		select count(0) from dev_server where group_id in (${value}) and dev_type = 6 and deleted = 0
	</select>

</mapper>
