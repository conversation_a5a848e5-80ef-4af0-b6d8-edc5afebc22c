<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.dlp.dao.TermGuidFilterDao">
    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.TermGuidFilterVO"
            resultType="com.tipray.dlp.bean.TermGuidFilter">
        select * from terminal_guid_filter
        <where>
            <if test="termGuid != null and termGuid != ''">
                and term_guid like CONCAT('%',#{termGuid},'%')
            </if>
        </where>
    </select>

</mapper>
