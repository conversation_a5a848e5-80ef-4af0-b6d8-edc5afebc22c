<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.FlowIPPortDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.FlowIPPort">
        <id column="id" property="id" />
        <result column="ip_type" property="ipType"/>
        <result column="begin_ip" property="beginIp"/>
        <result column="end_ip" property="endIp"/>
        <result column="port_type" property="portType"/>
        <result column="begin_port" property="beginPort"/>
        <result column="end_port" property="endPort"/>
        <result column="flow_id" property="flowId"/>
        <result column="protocol" property="protocol"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.tipray.dlp.bean.FlowIPPort">
		insert into flow_ipport(ip_Type,begin_ip,end_ip,port_type,begin_port,end_port,flow_id,protocol,remark)
		values(#{ipType},#{beginIp},#{endIp},#{portType},#{beginPort},#{endPort},#{flowId},#{protocol},#{remark})
	</insert>

    <update id="update" parameterType="com.tipray.dlp.bean.FlowIPPort">
        update flow_ipport set
        ip_Type=#{ipType},begin_ip=#{beginIp},end_ip=#{endIp},port_type=#{portType},begin_port=#{beginPort},end_port=#{endPort},flow_id=#{flowId},protocol=#{protocol},remark=#{remark}
        where id=#{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
		delete from flow_ipport where id=#{id}
	</delete>

    <delete id="deleteByIds" parameterType="java.lang.String">
		delete from flow_ipport where id in (${value})
	</delete>

    <select id="getById" parameterType="java.lang.Long" resultMap="resultMap">
		select * from flow_ipport  where id=#{value}
	</select>

    <select id="listByFlowId" parameterType="java.lang.Long" resultMap="resultMap">
		select * from flow_ipport where flow_id = #{value}
	</select>

    <select id="getByRemark" parameterType="java.lang.String" resultMap="resultMap">
        select * from flow_ipport where remark = #{value}
    </select>

    <update id="updateFlowId">
        update flow_ipport set flow_id = #{flowId} where id in (${ids})
    </update>
</mapper>