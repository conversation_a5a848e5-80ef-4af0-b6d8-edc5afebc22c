<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SoftwareDefDao">

    <resultMap type="com.tipray.dlp.bean.SoftwareDef" id="resultMap">
        <result property="id" column="id"/>
        <result property="softwareName" column="software_name"/>
        <result property="originalName" column="original_name"/>
    </resultMap>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" >
        insert into
        softasset_def(software_name, original_name)
        values
        <foreach collection="addList" item="c" separator=",">
            (
            #{c.softwareName}, #{c.originalName}
            )
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        update softasset_def
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="software_name = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    <if test="item.softwareName!=null">
                        when id = #{item.id} then #{item.softwareName}
                    </if>
                </foreach>
            </trim>

            <trim prefix="original_name = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    <if test="item.originalName != null">
                        when id = #{item.id} then #{item.originalName}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in (@updateList.id)
    </update>

    <select id="listByOriginalName" resultMap="resultMap">
        select id, software_name, original_name from softasset_def
        where original_name in (@originalNames)
    </select>

    <select id="listAll" resultMap="resultMap">
        select id, software_name, original_name from softasset_def
    </select>

    <delete id="deleteAll">
        delete from softasset_def
    </delete>

</mapper>