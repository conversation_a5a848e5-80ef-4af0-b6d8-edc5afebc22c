<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.MailWhiteListReceiverDao">

    <resultMap type="com.tipray.dlp.bean.MailWhiteListReceiver" id="resultMap">
        <result property="id" column="id"/>
        <result property="userName" column="user_name"/>
        <result property="mailAddress" column="mail_address"/>
        <result property="office" column="office"/>
        <result property="unit" column="unit"/>
        <result property="groupId" column="group_id"/>
    </resultMap>

    <sql id="vo_where_sql">
        <if test="groupId != null and groupId != 0">
            and group_id = #{groupId}
        </if>
        <if test="searchInfo != null and searchInfo != ''">
            and user_name like CONCAT('%',#{searchInfo},'%')
        </if>
    </sql>

    <select id="getByMailAddress" parameterType="java.lang.String" resultMap="resultMap">
		select * from mail_white_list_receiver where mail_address=#{value}
	</select>

    <!--查询单个-->
    <select id="getById" resultMap="resultMap">
        select * from mail_white_list_receiver where id = #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="list" resultMap="resultMap">
        select * from mail_white_list_receiver
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into mail_white_list_receiver(user_name, mail_address, office, unit, group_id)
        values(#{userName}, #{mailAddress}, #{office}, #{unit}, #{groupId})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update mail_white_list_receiver
        <set>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="mailAddress != null and mailAddress != ''">
                mail_address = #{mailAddress},
            </if>
            <if test="office != null and office != ''">
                office = #{office},
            </if>
            <if test="unit != null and unit != ''">
                unit = #{unit},
            </if>
            <if test="groupId != null">
                group_id = #{groupId},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from mail_white_list_receiver where id = #{value}
    </delete>

    <delete id="deleteByIds">
        delete from mail_white_list_receiver where id in (${value})
    </delete>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.MailWhiteListReceiverVO" resultType="java.lang.Long">
        select count(0) from mail_white_list_receiver where 1 = 1
        <include refid="vo_where_sql"/>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.MailWhiteListReceiverVO" resultMap="resultMap">
        select * from mail_white_list_receiver where 1 = 1
        <include refid="vo_where_sql"/>
    </select>

</mapper>