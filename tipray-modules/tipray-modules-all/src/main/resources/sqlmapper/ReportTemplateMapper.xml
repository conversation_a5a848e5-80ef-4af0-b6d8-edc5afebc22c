<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ReportTemplateDao">

    <resultMap id="resultMap" type="com.tipray.dlp.bean.ReportTemplate">
        <id column="id" property="id" />
        <result column="title" property="title"/>
        <result column="template_file_name" property="templateFileName"/>
        <result column="temp_json" property="tempJson"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            deleted = 0
            <if test="title != null">
                and title = #{title}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and (title like CONCAT('%',#{searchInfo},'%'))
            </if>
        </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.ReportTemplateVo" resultMap="resultMap">
        select * from report_temp
        <include refid="vo_where_sql"/>
    </select>

    <insert id="insert" parameterType="com.tipray.dlp.bean.ReportTemplate">
        insert into report_temp(title, template_file_name, temp_json, create_time, modify_time)
        SELECT #{title}, #{templateFileName}, #{tempJson}, #{createTime}, #{modifyTime}
        WHERE NOT EXISTS (SELECT 1 FROM report_temp WHERE title = #{title} AND deleted = 0)
    </insert>

</mapper>