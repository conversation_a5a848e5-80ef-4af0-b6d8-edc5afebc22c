<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.AssetPropDao">

    <resultMap type="com.tipray.dlp.bean.AssetProp" id="AssetPropMap">
        <result property="propId" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="flag" column="flag"/>
        <result property="name" column="name"/>
        <result property="nameKey" column="name_key"/>
    </resultMap>

    <!--查询资产属性名称-->
    <select id="getNameById" resultType="java.lang.String">
        select name_key from asset_prop where id = #{id}
    </select>

    <!--查询单个-->
    <select id="getById" resultMap="AssetPropMap">
        select id, parent_id, flag, name, name_key
        from asset_prop
        where id = #{propId}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="list" resultMap="AssetPropMap">
        select id, parent_id, flag, name, name_key
        from asset_prop
        where deleted = 0
        <if test='type == 1'>
           and ((id &gt;= 1001 and id &lt;= 1031) or (parent_id &gt;= 1001 and parent_id &lt;= 1031 ))
        </if>
        <if test='type == 2'>
            and ((id &gt;= 2001 and id &lt;= 2004) or (parent_id &gt;= 2001 and parent_id &lt;= 2004 ))
        </if>
        <if test="userId != null">
            and id not in (select prop_id from asset_prop_user where user_id = #{userId} and prop_id >= 10000)
        </if>
        <if test="parentId != null">
            and parent_id = #{parentId}
        </if>

    </select>

    <select id="countByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultType="java.lang.Long">
      select count(0) from asset_prop
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultMap="AssetPropMap">
      select id, parent_id, flag, name, name_key from asset_prop
    </select>

    <!-- 查看明细详情要显示的配置类型 -->
    <select id="getDetailProp" resultMap="AssetPropMap">
        select id, parent_id, flag, name, name_key from asset_prop
        where deleted = 0
        <if test='type == 1 and parentId != null'>
            <!-- 硬件信息只显示指定父级资产属性类型 -->
            and parent_id = #{parentId}
        </if>
        <if test='type == 2'>
            and parent_id = 102
        </if>
    </select>

    <select id="listForType" resultMap="AssetPropMap">
        select id, parent_id, flag, name, name_key
        from asset_prop
        where deleted = 0
        <if test='type == 1'>
            and parent_id = 101
        </if>
        <if test='type == 2'>
            and parent_id = 102
        </if>
        <if test="type == null and ids != null and ids != ''">
            and id in (${ids})
        </if>
    </select>

    <select id="listLeafProp" resultMap="AssetPropMap">
        select id, parent_id, flag, name, name_key
        from asset_prop
        where deleted = 0
        <if test='type == 1'>
            and (parent_id &gt;= 1001 and parent_id &lt;= 1031)
        </if>
        <if test='type == 2'>
            and (parent_id &gt;= 2001 and parent_id &lt;= 2004)
        </if>
    </select>

    <select id="listMainPropId" resultType="long">
        SELECT id FROM asset_prop WHERE (flag&amp;1) =1 and deleted = 0
    </select>
</mapper>
