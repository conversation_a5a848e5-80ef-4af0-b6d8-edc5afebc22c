<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.LocalShareExtBaseDao">

    <sql id="vo_where_sql">
        <where>
            <if test="searchInfo != null and searchInfo != ''">
                and (t1.remark like CONCAT('%',#{searchInfo},'%') or suffix like CONCAT('%',#{searchInfo},'%'))
            </if>
            <if test="ids != null and ids != ''">
                and t1.id in (${ids})
            </if>
            <include refid="commonSQL.andNotDel"></include>
        </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.LocalShareExtBaseVO" resultType="com.tipray.dlp.bean.LocalShareExtBase">
        select * from local_share_ext_base t1
        <include refid="vo_where_sql"/>
    </select>

    <select id="listLocalShareExtBase" resultType="com.tipray.dlp.bean.LocalShareExtBase">
        select * from local_share_ext_base t1
        <include refid="vo_where_sql"/>
    </select>

</mapper>
