<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SignReportPushLogDao">


    <select id="listByVO" resultType="com.tipray.dlp.bean.SignReportPushLog">
        select * from sign_report_push_log where
        <if test="id != null and id != ''">
            id = #{id}
        </if>
        <if test="id == null or id == ''">
            <if test="startDate != null and endDate != null">
                create_time >= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="kw != null and kw != ''">
                and sign_report_name like concat('%',#{kw},'%')
            </if>
            <if test="sortName != null and sortName != ''">
                order by create_time ${sortOrder}
            </if>
        </if>
    </select>
    <select id="selectPageByVO" resultType="com.tipray.dlp.bean.SignReportPushLog">
        select * from sign_report_push_log where
        <if test="id != null and id != ''">
            id = #{id}
        </if>
        <if test="id == null or id == ''">
            <if test="startDate != null and endDate != null">
                create_time >= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="kw != null and kw != ''">
                and sign_report_name like concat('%',#{kw},'%')
            </if>
            <if test="sortName != null and sortName != ''">
                order by create_time ${sortOrder}
            </if>
        </if>
    </select>
</mapper>
