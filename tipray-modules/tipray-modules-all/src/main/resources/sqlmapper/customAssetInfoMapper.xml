<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.CustomAssetInfoDao">

    <resultMap type="com.tipray.dlp.bean.CustomAssetInfo" id="CustomCustomAssetInfoMap">
        <result property="id" column="id"/>
        <result property="term_id" column="terminalId"/>
        <result property="prop_id" column="propId"/>
        <result property="value" column="value"/>
        <result property="recordNo" column="record_no"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <if test="terminalId != null">
                and term_id = #{terminalId}
            </if>
            <if test="propId != null">
                and prop_id = #{propId}
            </if>
            <if test="propIds != null and propIds != ''">
                and prop_id in (@propIds)
            </if>
            <if test="recordNo != null">
                and record_no = #{recordNo}
            </if>
        </where>
    </sql>

    <!--查询资产信息-->
    <select id="countByPropIds" parameterType="java.lang.String" resultType="java.lang.Long">
        select count(*)
        from custom_asset_info
        where prop_id in (@propIds) and value is not null
    </select>

    <select id="getCustomAssetInfo" resultMap="CustomCustomAssetInfoMap">
        select id, term_id, prop_id, value, record_no from custom_asset_info
        where term_id = #{terminalId} and prop_id = #{propId} and record_no = #{recordNo}
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.CustomAssetInfoVO" resultMap="CustomCustomAssetInfoMap">
        select id, term_id, prop_id, value, record_no from custom_asset_info
        <include refid="vo_where_sql"/>
    </select>

    <delete id="deleteByPropIds">
        delete from custom_asset_info where prop_id in (@propIds)
    </delete>

    <select id="listTermIdByConditions" resultType="java.lang.Long">
        select term_id from custom_asset_info
        <where>
            <foreach collection="list" item="item" open="(" close=")" separator=" or ">
                prop_id = #{item.propId}
                <if test="item.value != null and item.value != ''">
                    and value like CONCAT('%',#{item.value},'%')
                </if>
                <if test="item.value == null or item.value == ''">
                    and value = ''
                </if>
            </foreach>
        </where>
    </select>
</mapper>
