<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DiskScanSensLogDao">

    <resultMap type="com.tipray.dlp.bean.DiskScanSensLog" id="DiskScanLogMap">
        <result property="id" column="id"/>
        <result property="guid" column="guid"/>
        <result property="sensGuid" column="content_aware_guid"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="opType" column="op_type"/>
        <result property="fileName" column="file_name"/>
        <result property="lossType" column="loss_type"/>
        <result property="severity" column="severity"/>
        <result property="createTime" column="create_time"/>
        <result property="strategyId" column="strg_id"/>
        <result property="content" column="content"/>
        <result property="rules" column="rules"/>
        <result property="responds" column="responds"/>
        <result property="runGuid" column="run_guid"/>
        <result property="batchNo" column="batch_no"/>
        <result property="sstTimes" column="sst_times"/>
        <result property="localFilePath" column="backup_file_name"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="devId" column="backup_server_id"/>
        <result property="remark" column="remark"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="result" column="result"/>
    </resultMap>
    <sql id="full_fields_sql">
        id,term_id,user_id,create_time,content_aware_guid,guid,op_type,loss_type,severity,strg_id,content,rules,responds,run_guid,batch_no,sst_times,backup_file_name,upload_file_guid,backup_server_id,file_name,remark,term_group_id,user_group_id,result
    </sql>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="opType != null">
                and op_type = #{opType}
            </if>
            <if test="opTypes != null and opTypes != ''">
                and op_type in (${opTypes})
            </if>
            <if test="result != null and result != ''">
                and result in (${result})
            </if>
            <if test="lossType != null">
                and loss_type = #{lossType}
            </if>
            <if test="severity != null">
                and severity = #{severity}
            </if>
            <if test="guid != null and guid != ''">
                and guid like CONCAT('%',#{guid},'%')
            </if>
            <if test="guids != null and guids != ''">
                and guid in (${guids})
            </if>
            <if test="rangeId != null">
                and id > #{rangeId}
            </if>
            <if test="runGuid != null">
                and run_guid = #{runGuid}
            </if>
            <if test="filePath != null">
                and file_name = #{filePath}
            </if>
            <if test="sentiveType != null">
                and sst_times = #{sentiveType}
            </if>
            <if test="severalTimes != null">
                and sst_times > 2
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.DiskScanLogVO" resultType="java.lang.Long">
      select count(0) from disk_scan_contentaware_log
      <include refid="sql_where"/>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.DiskScanLogVO" resultMap="DiskScanLogMap">
      select <include refid="full_fields_sql"/>
      from disk_scan_contentaware_log
      <include refid="sql_where"/>
    </select>

    <select id="listGuidByVO" parameterType="com.tipray.dlp.bean.vo.DiskScanLogVO" resultType="com.tipray.dlp.bean.DiskScanSensLog">
        select id, guid as file_guid from disk_scan_contentaware_log <include refid="sql_where"/>
    </select>

</mapper>
