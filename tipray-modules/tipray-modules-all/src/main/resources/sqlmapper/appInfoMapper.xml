<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.AppInfoDao">
    <resultMap id="appInfoMap" type="com.tipray.dlp.bean.AppInfo">
        <id column="id" property="id" />
        <result column="os_type" property="osType"/>
        <result column="type_id" property="typeId"/>
        <result column="check_md5" property="checkMd5"/>
        <result column="process_name" property="processName"/>
        <result column="product_name" property="productName"/>
        <result column="product_version" property="productVersion"/>
        <result column="original_filename" property="originalFilename"/>
        <result column="file_desc" property="fileDescription"/>
        <result column="internal_name" property="internalName"/>
        <result column="legal_copyright" property="legalCopyright"/>
        <result column="company_name" property="companyName"/>
        <result column="file_md5" property="fileMd5"/>
        <result column="quickly_md5" property="quicklyMd5"/>
        <result column="soft_sign" property="softSign"/>
        <result column="ori_id" property="oriId"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="select_info_where_vo">
        <where>
            <if test="typeId != null and typeId != 0 ">
                and type_id = #{typeId}
            </if>
            <if test="searchInfo != null and searchInfo != '' ">
                and process_name like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="ids != null and ids !=''">
                and id in (${ids})
            </if>
            <if test="osType != null">
                and os_type = #{osType}
            </if>
        </where>
    </sql>

    <select id="countByAppInfoVO" parameterType="com.tipray.dlp.bean.vo.AppInfoVO" resultType="java.lang.Long">
        select count(0) from app_info
        <include refid="select_info_where_vo"></include>
    </select>
    <select id="listByAppInfoVO" parameterType="com.tipray.dlp.bean.vo.AppInfoVO" resultMap="appInfoMap">
        select * from app_info
        <include refid="select_info_where_vo"></include>
    </select>
</mapper>
