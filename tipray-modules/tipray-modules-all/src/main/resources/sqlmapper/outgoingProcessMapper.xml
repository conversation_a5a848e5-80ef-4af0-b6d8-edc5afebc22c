<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.OutgoingProcessDao">

    <resultMap type="com.tipray.dlp.bean.OutgoingProcess" id="OutgoingProcessMap">
        <result property="id" column="id"/>
        <result column="process_name" property="processName"/>
        <result column="product_name" property="productName"/>
        <result column="product_version" property="productVersion"/>
        <result column="original_filename" property="originalFilename"/>
        <result column="file_desc" property="fileDescription"/>
        <result column="internal_name" property="internalName"/>
        <result column="legal_copyright" property="legalCopyright"/>
        <result column="company_name" property="companyName"/>
        <result column="file_md5" property="fileMd5"/>
        <result column="quickly_md5" property="quicklyMd5"/>
        <result column="soft_sign" property="softSign"/>
        <result property="classId" column="class_id"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="where_sql">
        <if test="classId != null">
            and class_id = #{classId}
        </if>
        <if test="fileMd5 != null and fileMd5 != ''">
            and file_md5 = #{fileMd5}
        </if>
        <if test="softSign != null and softSign != ''">
            and soft_sign = #{softSign}
        </if>
        <if test="processName != null and processName != ''">
            and process_name = #{processName}
        </if>
        <if test="classIds != null and classIds != ''">
            and class_id in (${classIds})
        </if>
        <if test="ids != null and ids != ''">
            and id in (${ids})
        </if>
        <if test="searchInfo != null and searchInfo != ''">
            and process_name like CONCAT('%',#{searchInfo},'%')
        </if>
        <if test="processType != null">
            and process_type = #{processType}
        </if>
    </sql>

    <!--查询单个-->
    <select id="getById" resultMap="OutgoingProcessMap">
        select *
        from outgoing_process
        where id = #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="list" resultMap="OutgoingProcessMap">
        select * from outgoing_process where 1=1
        <include refid="where_sql"/>
    </select>

    <select id="countByVO" resultType="java.lang.Long">
      select count(0) from outgoing_process
      <where>
        <include refid="where_sql"/>
      </where>
    </select>

    <select id="listByVO" resultMap="OutgoingProcessMap">
      select *
      from outgoing_process
      <where>
          <include refid="where_sql"/>
      </where>
    </select>

    <!--通过主键修改数据-->
    <update id="update">
        update outgoing_process
        <set>
            <if test="classId != null">
                class_id = #{classId},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from outgoing_process where id = #{value}
    </delete>

    <delete id="deleteByIds">
        delete from outgoing_process where id in (${value})
    </delete>

</mapper>
