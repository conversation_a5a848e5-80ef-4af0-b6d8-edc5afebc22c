<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ChatFileLogDao">

    <resultMap type="com.tipray.dlp.bean.ChatFileLog" id="ChatFileLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="msgTime" column="create_time"/>
        <result property="chatSessionInfo" column="chat_session_info"/>
        <result property="needBackUp" column="need_backup"/>
        <result property="chatType" column="chat_type"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileName" column="file_name"/>
        <result property="localFilePath" column="local_file_path"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="keyword1 != null and keyword1 != ''">
                and chat_type = #{keyword1}
            </if>
            <if test="keyword2 != null and keyword2 != ''">
                and file_name like CONCAT('%',#{keyword2},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from chat_file_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="ChatFileLogMap">
        select id, user_id, term_id, create_time, chat_session_info, need_backup, chat_type, file_size, file_name, backup_server_id, upload_file_guid,
        local_file_path, term_group_id, user_group_id
        from chat_file_log a
        <include refid="sql_where"></include>
    </select>

    <delete id="deleteByIds">
        delete from chat_file_log where id in (${value})
    </delete>

</mapper>
