<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SmartBackupServerDao">

	<select id="getSmartBackupByBindFileServerDevId">
		select * from dev_server where dev_type = 186 and dev_id in (
			select server_id from rel_data_server_device where dev_type = 11 and device_id = #{fileServerDevId})
	</select>

</mapper>
