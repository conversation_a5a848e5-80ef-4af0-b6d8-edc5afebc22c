<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.FingerprintRelaDao">

    <resultMap type="com.tipray.dlp.bean.FingerprintRela" id="ProcessFileMd5Map">
        <result property="id" column="id"/>
        <result property="relevanceId" column="relevance_id"/>
        <result property="fingerprintId" column="fingerprint_id"/>
        <result property="processName" column="process_name"/>
        <result property="processMd5" column="process_md5"/>
        <result property="checkMd5" column="check_md5"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="productName" column="product_name"/>
        <result property="productVersion" column="product_version"/>
        <result property="originalFilename" column="original_filename"/>
        <result property="companyName" column="company_name"/>
        <result property="fileDescription" column="file_desc"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andNotDel"></include>
            <if test="relevanceId != null">
                and relevance_id = #{relevanceId}
            </if>
            <if test="processName != null and processName != ''">
                and process_name like CONCAT(#{processName},'%')
            </if>
        </where>
    </sql>

    <select id="getIdForDelete" resultType="java.lang.Long">
        select id from ${tableName}
        <where>
            <if test="relevanceId != null">
                and relevance_id = #{relevanceId}
            </if>
            <if test="ids != null and ids != ''">
                and relevance_id in (@ids)
            </if>
            <include refid="commonSQL.andNotDel"></include>
        </where>
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="list" resultMap="ProcessFileMd5Map">
        select a.id, a.relevance_id, a.fingerprint_id, a.process_name, a.process_md5, a.check_md5, a.create_time, a.modify_time
        from ${tableName} a
        <include refid="sql_where"></include>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into ${tableName}(relevance_id, fingerprint_id, process_name, process_md5, check_md5, create_time, modify_time)
        values (#{relevanceId}, #{fingerprintId}, #{processName}, #{processMd5}, #{checkMd5}, #{createTime}, #{modifyTime})
    </insert>

    <update id="deleteByIds">
        update ${tableName} set modify_time=#{modifyTime}, deleted=1
        where id in (@ids)
    </update>
    <update id="deleteByVo">
        update ${tableName} set modify_time=#{modifyTime}, deleted=1
        where
        relevance_id = #{processId}
        <if test="fingerPrintIds != null and fingerPrintIds != ''">
            and fingerprint_id in (@fingerPrintIds)
        </if>
    </update>

    <update id="deleteByRelevanceIdsAndTableName" parameterType="com.tipray.dlp.bean.FingerprintRela">
        update ${tableName} set modify_time=#{modifyTime}, deleted=1
        where relevance_id in (@relevanceIds)
    </update>

</mapper>
