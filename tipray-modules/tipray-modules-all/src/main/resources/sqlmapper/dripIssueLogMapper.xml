<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DripIssueLogDao">
    <resultMap id="issueResultMap" type="com.tipray.dlp.bean.DripIssueLog">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="term_id" property="terminalId"/>
        <result column="severity" property="severity"/>
        <result column="stg_def_id" property="strategyId"/>
        <result column="rules" property="rules"/>
        <result column="responds" property="responds"/>
        <result column="sender" property="sender"/>
        <result column="sender_ip" property="senderIp"/>
        <result column="create_time" property="createTime"/>
        <result column="event_guid" property="guid"/>
        <result column="stg_name" property="strategy"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>
    <resultMap id="matchResultMap" type="com.tipray.dlp.bean.DripMatchLog">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="term_id" property="terminalId"/>
        <result column="event_guid" property="issueGuid"/>
        <result column="op_type" property="lossType"/>
        <result column="content" property="content"/>
        <result column="rule_id" property="ruleId"/>
        <result column="file_name" property="fileName"/>
        <result column="receiver" property="receiver"/>
        <result column="receiver_ip" property="receiverIp"/>
        <result column="create_time" property="createTime"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>
    <sql id="issue_full_fields_sql">
        id,term_id,user_id,create_time,severity,stg_def_id,stg_name,rules,responds,sender,sender_ip,handler_name,event_guid,remark,term_group_id,user_group_id
    </sql>
    <sql id="match_full_fields_sql">
        id,term_id,user_id,create_time,rule_id,event_guid,content,file_name,op_type,receiver,receiver_ip,term_group_id,user_group_id
    </sql>

    <sql id="issue_vo_where_sql">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="severity != null">
                and severity = #{severity}
            </if>
            <if test="strategyId != null">
                and stg_def_id = #{strategyId}
            </if>
            <choose>
                <when test="strategyName != null and strategyName != ''">
                    and ( stg_name like CONCAT('%',#{strategyName},'%')
                    <if test="strategyIds != null and strategyIds != ''">
                        or stg_def_id in (${strategyIds}) and stg_name is null
                    </if>
                    )
                </when>
                <otherwise>
                    <if test="strategyIds != null and strategyIds != ''">
                        and stg_def_id in (${strategyIds})
                    </if>
                </otherwise>
            </choose>
        </where>
    </sql>
    <sql id="match_vo_where_sql">
        <where>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="guid != null">
                and event_guid = #{guid}
            </if>
            <if test="guids != null and guids != ''">
                and event_guid in (${guids})
            </if>
        </where>
    </sql>

    <delete id="deleteIssueLogByIds" parameterType="java.lang.String">
		delete from drip_scan_violate_log where id in (${value})
	</delete>
    <select id="countIssueLogByVO" parameterType="com.tipray.dlp.bean.vo.IssueLogVO" resultType="java.lang.Long">
        select count(0) from drip_scan_violate_log
        <include refid="issue_vo_where_sql"/>
    </select>
    <select id="listIssueLogByVO" parameterType="com.tipray.dlp.bean.vo.IssueLogVO" resultMap="issueResultMap">
        select <include refid="issue_full_fields_sql"/>
        from drip_scan_violate_log
        <include refid="issue_vo_where_sql"/>
    </select>


    <delete id="deleteMatchLogByIds" parameterType="java.lang.String">
		delete from drip_scan_match_log where id in (${value})
	</delete>
    <select id="countMatchLogByVO" parameterType="com.tipray.dlp.bean.vo.IssueLogVO" resultType="java.lang.Long">
        select count(0) from drip_scan_match_log
        <include refid="match_vo_where_sql"/>
    </select>
    <select id="listMatchLogByVO" parameterType="com.tipray.dlp.bean.vo.IssueLogVO" resultMap="matchResultMap">
        select <include refid="match_full_fields_sql"/>
        from drip_scan_match_log
        <include refid="match_vo_where_sql"/>
    </select>
</mapper>
