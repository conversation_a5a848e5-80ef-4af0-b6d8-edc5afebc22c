<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.dlp.dao.CleanUpConfigDao">
    <update id="cleanupTerminal">
        UPDATE terminal SET use_type = #{useType}
        WHERE DATEDIFF(NOW(), last_online_time) &gt;= #{cleanDuration}
    </update>

    <update id="recoverTerms">
        UPDATE terminal SET use_type = 0
        WHERE id IN (${termIds})
    </update>

    <select id="countLongTimeOfflineTerminals" resultType="java.lang.Integer">
        SELECT COUNT(id) FROM terminal
        WHERE last_online_time IS NULL OR DATEDIFF(NOW(), last_online_time) &gt;= #{cleanDuration}
    </select>
</mapper>