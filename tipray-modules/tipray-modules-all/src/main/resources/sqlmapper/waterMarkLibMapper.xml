<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.WaterMarkLibDao">

    <resultMap type="com.tipray.dlp.bean.WaterMarkLib" id="resultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="groupId" column="group_id"/>
        <result property="json" column="info"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="vo_where_sql">
      <where>
        <if test="type != null">
            and type = #{type}
        </if>
        <if test="ids != null and ids != ''" >
            and id in (${ids})
        </if>
        <if test="groupId != null">
            and group_id = #{groupId}
        </if>
        <if test="groupIds != null and groupIds != ''">
            and group_id in (${groupIds})
        </if>
        <if test="searchInfo != null and searchInfo != ''">
            and name like CONCAT('%',#{searchInfo},'%')
        </if>

      </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.WaterMarkVO" resultType="java.lang.Long">
        select count(0) from water_mark_lib
        <include refid="vo_where_sql"/>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.WaterMarkVO" resultMap="resultMap">
        select id, name, type, info, remark, group_id from water_mark_lib
        <include refid="vo_where_sql"/>
    </select>

</mapper>
