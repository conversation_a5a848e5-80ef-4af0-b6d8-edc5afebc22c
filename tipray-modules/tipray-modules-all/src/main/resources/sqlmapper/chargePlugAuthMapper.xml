<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ChargePlugAuthDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.ChargePlugAuth">
        <id column="id" property="id" />
        <result property="guid" column="guid"/>
        <result property="deadline" column="deadline"/>
        <result property="totalTimes" column="total_times"/>
        <result property="leftTimes" column="left_times"/>
        <result property="authContent" column="auth_content"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="select_where_vo">
        <where>
            <if test="displayable == null or displayable == false">
                and deadline &gt;= #{currentDate} and left_times > 0
            </if>
        </where>
    </sql>

    <select id="getAllLeftTimes" resultType="java.lang.Long">
        select sum(left_times) from charge_plug_auth
        where deadline &gt;= #{currentDate} and left_times > 0
    </select>

    <select id="selectPageByVO" resultMap="resultMap">
        select id, guid, deadline, total_times, left_times, create_time, modify_time from charge_plug_auth
        <include refid="select_where_vo"></include>
    </select>

    <select id="getByGuid" parameterType="java.lang.String" resultMap="resultMap">
        select * from charge_plug_auth where guid = #{value}
    </select>

    <update id="updateLeftTimes">
        update charge_plug_auth set left_times = #{leftTimes} where guid = #{guid}
    </update>

    <select id="getLastDeadline" resultType="java.util.Date">
        select max(deadline) from charge_plug_auth
    </select>

    <select id="listUndueChargePlugAuth" parameterType="java.util.Date" resultMap="resultMap">
        select id, guid, deadline, total_times, left_times, create_time, modify_time from charge_plug_auth where deadline &gt;= #{value}
    </select>
</mapper>