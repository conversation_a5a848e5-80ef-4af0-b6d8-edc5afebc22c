<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ChatWhiteListDao">


    <select id="findChatWhiteList" resultType="com.tipray.dlp.bean.ChatWhiteList">
        select id,chat_number chatNumber,remark,type, group_id
        from chat_white_list
        <where>
            <if test="type != null">
                type = #{type}
            </if>
            <include refid="commonSQL.andNotDel"></include>
            <if test="chatNumber != null and chatNumber != ''">
                and (chat_number like CONCAT('%',#{chatNumber},'%') or remark like CONCAT('%',#{chatNumber},'%'))
            </if>
        </where>
    </select>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.ChatWhiteListVO" resultType="com.tipray.dlp.bean.ChatWhiteList">
        select id,chat_number chatNumber,remark,type, group_id
        from chat_white_list
        <include refid="select_vo_sql"></include>
    </select>
    <select id="selectByCondition" parameterType="com.tipray.dlp.bean.vo.ChatWhiteListVO" resultType="com.tipray.dlp.bean.ChatWhiteList">
        select id,chat_number chatNumber,remark,type, group_id
        from chat_white_list
        <include refid="select_vo_sql"></include>
    </select>

    <sql id="select_vo_sql">
        <where>
            <if test="type == null and groupType != null">
                and type = #{groupType}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="groupId != null">
                and group_id = #{groupId}
            </if>
            <if test="groupIds != null and groupIds != ''">
                and group_id  in (${groupIds})
            </if>
            <include refid="commonSQL.andNotDel"></include>
            <if test="searchInfo != null and searchInfo != ''">
                and (chat_number like CONCAT('%',#{searchInfo},'%') )
            </if>
            <if test="ids != null and ids != ''">
                <choose>
                    <when test="groupIds == null or groupIds == ''">
                        and
                    </when>
                    <otherwise>
                        or
                    </otherwise>
                </choose>
                id in (${ids})
            </if>
        </where>
    </sql>
</mapper>
