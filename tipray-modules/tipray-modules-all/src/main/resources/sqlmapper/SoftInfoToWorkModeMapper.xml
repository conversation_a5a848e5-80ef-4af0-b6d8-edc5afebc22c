<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SoftInfoToWorkModeDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.SoftInfoToWorkMode">
        <id column="id" property="id" />
        <result column="type_id" property="typeId"/>
        <result column="check_md5" property="checkMd5"/>
        <result column="process_name" property="processName"/>
        <result column="product_name" property="productName"/>
        <result column="product_version" property="productVersion"/>
        <result column="original_filename" property="originalFilename"/>
        <result column="file_desc" property="fileDescription"/>
        <result column="internal_name" property="internalName"/>
        <result column="legal_copyright" property="legalCopyright"/>
        <result column="company_name" property="companyName"/>
        <result column="file_md5" property="fileMd5"/>
        <result column="quickly_md5" property="quicklyMd5"/>
        <result column="soft_sign" property="softSign"/>
        <result column="ori_id" property="oriId"/>
        <result column="os_type" property="osType"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <!--<insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into soft_info_to_work_mode
        (type_id ,check_md5, process_name,product_name,product_version,original_filename, file_desc, company_name,legal_copyright,
        file_md5, quickly_md5,internal_name,soft_sign,ori_id,create_time, modify_time, os_type)
        values(#{typeId},#{checkMd5},#{processName},#{productName},#{productVersion},#{originalFilename},#{fileDescription},#{companyName},#{legalCopyright},
        #{fileMd5},#{quicklyMd5},#{internalName},#{softSign},#{oriId}, #{createTime}, #{modifyTime}, #{osType})
    </insert>
    <delete id="deleteByIds">
        delete from soft_info_to_work_mode
        where id in (${value})
    </delete>
    <update id="update">
        update soft_info_to_work_mode set check_md5 = #{checkMd5},type_id=#{typeId}
        where id = #{id}
    </update>-->
    <select id="listByVO" resultMap="resultMap">
        select * from soft_info_to_work_mode
        <where>
            <if test="typeId != null and typeId != 0">
                and type_id = #{typeId}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and process_name like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="ids != null and ids != ''">
                and id in (${ids})
            </if>
            <if test="osType != null">
                and os_type = #{osType}
            </if>
        </where>
    </select>
    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from soft_info_to_work_mode
        <where>
            <if test="typeId != null and typeId != 0">
                and type_id = #{typeId}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and process_name like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="osType != null">
                and os_type = #{osType}
            </if>
        </where>
    </select>
<!--
    <select id="getById" resultMap="resultMap">
        select * from soft_info_to_work_mode
        <where>
            id = #{id}
        </where>
    </select>-->
</mapper>
