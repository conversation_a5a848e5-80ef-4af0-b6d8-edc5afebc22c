<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ApprovalUsbauthApplyDao">

    <resultMap type="com.tipray.dlp.bean.ApprovalUsbauthApply" id="ApprovalUsbauthApplyMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="opuser_id"/>
        <result property="applyType" column="apply_type"/>
        <result property="devType" column="dev_type"/>
        <result property="volumeName" column="volume_name"/>
        <result property="model" column="model"/>
        <result property="pnpDeviceId" column="pnp_device_id"/>
        <result property="devSize" column="dev_size"/>
        <result property="reason" column="reason"/>
        <result property="driverType" column="driver_type"/>
        <result property="srcPnpDeviceId" column="src_pnp_device_id"/>
        <result property="pid" column="pid"/>
        <result property="vid" column="vid"/>
<!--        <result property="limitType" column="limit_type"/>-->
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <if test="searchInfo != null and searchInfo != ''">
                and (volume_name like CONCAT('%',#{searchInfo},'%') or pnp_device_id like concat('%', #{searchInfo}, '%'))
            </if>
        </where>
    </sql>

    <!--通过实体作为筛选条件查询-->
    <select id="list" resultMap="ApprovalUsbauthApplyMap">
        select * from approval_usbauth_apply
        <where>
            <if test="ids != null and ids != ''">
                id in (${ids})
            </if>
        </where>
    </select>

    <select id="countByVO" resultType="java.lang.Long">
      select count(0) from approval_usbauth_apply
      <include refid="vo_where_sql"/>
    </select>

    <select id="listByVO" resultMap="ApprovalUsbauthApplyMap">
      select * from approval_usbauth_apply
      <include refid="vo_where_sql"/>
    </select>
</mapper>
