<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DiskScanSelfCheckSensLogDao">

    <resultMap type="com.tipray.dlp.bean.DiskScanSelfCheckSensLog" id="DiskScanLogMap">
        <result property="id" column="id"/>
        <result property="guid" column="guid"/>
        <result property="sensGuid" column="content_aware_guid"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="opType" column="op_type"/>
        <result property="fileName" column="file_name"/>
        <result property="lossType" column="loss_type"/>
        <result property="severity" column="severity"/>
        <result property="createTime" column="create_time"/>
        <result property="strategyId" column="strg_id"/>
        <result property="content" column="content"/>
        <result property="rules" column="rules"/>
        <result property="responds" column="responds"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>
    <sql id="full_fields_sql">
        id,term_id,user_id,create_time,content_aware_guid,guid,op_type,loss_type,severity,strg_id,content,rules,responds,file_name,remark,term_group_id,user_group_id
    </sql>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="opType != null">
                and op_type = #{opType}
            </if>
            <if test="lossType != null">
                and loss_type = #{lossType}
            </if>
            <if test="severity != null">
                and severity = #{severity}
            </if>
            <if test="guid != null and guid != ''">
                and guid like CONCAT('%',#{guid},'%')
            </if>
            <if test="guids != null and guids != ''">
                and guid in (${guids})
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.DiskScanLogVO" resultType="java.lang.Long">
      select count(0) from disk_scan_seft_contentaware_log
      <include refid="sql_where"/>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.DiskScanLogVO" resultMap="DiskScanLogMap">
      select <include refid="full_fields_sql"/>
      from disk_scan_seft_contentaware_log
      <include refid="sql_where"/>
    </select>

    <delete id="deleteByIds">
        delete from disk_scan_seft_contentaware_log where id in (${value})
    </delete>

</mapper>
