<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ProcessCollectRuleDao">

    <resultMap type="com.tipray.dlp.bean.ProcessCollectRule" id="ProcessCollectRuleMap">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="ruleParam" column="rule_param"/>
        <result property="ruleCondition" column="rule_condition"/>
        <result property="setFilter" column="set_filter"/>
        <result property="classId" column="class_id"/>
        <result property="className" column="name"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectAll" resultMap="ProcessCollectRuleMap">
        select a.id, a.type, a.rule_param, a.rule_condition, a.set_filter, a.class_id, b.name
        from process_collect_rule a
        left join software_class b on a.class_id=b.id
        <where>
            <include refid="commonSQL.andNotDel"></include>
        </where>
    </select>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultMap="ProcessCollectRuleMap">
      select id, type, rule_param, rule_condition, set_filter, class_id, create_time, modify_time from process_collect_rule
      <where>
          <include refid="commonSQL.andNotDel"></include>
      </where>
    </select>

</mapper>
