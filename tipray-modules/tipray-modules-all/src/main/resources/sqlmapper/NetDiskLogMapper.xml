<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.NetDiskLogDao">

    <resultMap type="com.tipray.dlp.bean.NetDiskLog" id="NetDiskLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="filePath" column="file_path"/>
        <result property="needBackup" column="need_backup"/>
        <result property="sign" column="sign"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileName" column="file_name"/>
        <result property="appType" column="app_type"/>
        <result property="localFilePath" column="local_file_path"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="actionType" column="action_type"/>
    </resultMap>
    <sql id="full_fields_sql">
        id,term_id,user_id,create_time,backup_server_id,upload_file_guid,file_name,file_path,file_size,sign,app_type,need_backup,local_file_path,term_group_id,user_group_id,action_type
    </sql>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name like CONCAT('%',#{fileName},'%')
            </if>
<!--            <if test="sign != null">-->
<!--                and sign = #{sign}-->
<!--            </if>-->
            <if test="signs != null and !signs.isEmpty()">
                 and sign in (@signs)
            </if>
            <if test="actionType != null">
                and action_type = #{actionType}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from net_disk_log a
        <include refid="sql_where"/>
    </select>

    <select id="listByVO" resultMap="NetDiskLogMap">
        select <include refid="full_fields_sql"/>
        from net_disk_log a
        <include refid="sql_where"/>
    </select>

</mapper>
