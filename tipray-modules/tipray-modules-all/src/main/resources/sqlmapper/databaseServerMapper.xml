<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DatabaseServerDao">
    <resultMap id="DBShardingInfoMap" type="com.tipray.dlp.bean.DatabaseServer">
        <id column="id" property="id" />
        <result column="db_name" property="name"/>
        <result column="db_id" property="devId"/>
        <result column="source_id" property="sourceId"/>
        <result column="db_type" property="dbType"/>
        <result column="db_rule" property="rule"/>
        <result column="access" property="access"/>
        <result column="ip" property="ip"/>
        <result column="port" property="port"/>
        <result column="account" property="account"/>
        <result column="password" property="password"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="dbpass_ver" property="dbpassVer"/>
    </resultMap>

    <select id="getMaxDbId" resultType="java.lang.Long">
        select max(db_id) from db_server
    </select>

    <select id="getMaxDbPassVer" resultType="java.lang.Long">
        select max(dbpass_ver) from db_server
    </select>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.ServerVO" resultMap="DBShardingInfoMap">
        select r.db_id source_id,s.* from db_server s LEFT JOIN ref_db_server r on s.db_id = r.ref_db_id
        <where>
            <if test="searchInfo != null and searchInfo != ''">
                and (ip like CONCAT('%',#{searchInfo},'%') or db_name like CONCAT('%',#{searchInfo},'%'))
            </if>
            <if test="name != null">
                and db_name like CONCAT('%',#{name},'%')
            </if>
            <if test="devId != null">
                and s.db_id like CONCAT('%',#{devId},'%')
            </if>
            <if test="dbType != null">
                and db_type like CONCAT('%',#{dbType},'%')
            </if>
            <if test="access != null">
                and access like CONCAT('%',#{access},'%')
            </if>
            <if test="ip != null and ip != ''">
                and ip like CONCAT('%',#{ip},'%')
            </if>
            <if test="port != null">
                and port like CONCAT('%',#{port},'%')
            </if>
        </where>
    </select>

    <select id="listCanBindBranchDbServer" resultMap="DBShardingInfoMap">
    <if test="dbIds == null or dbIds == ''">
        select ds.* from db_server ds where ds.access = 1 and ds.db_id not in (select db_id from ref_db_server);
    </if>
    <if test="dbIds != null and dbIds != ''">
        select ds.*, t.ref_db_id source_id from db_server ds
        left join (select db_id, ref_db_id from ref_db_server where ref_db_id in (${dbIds})) t on t.db_id = ds.db_id
        where ds.access = 1 and ds.db_id not in (select db_id from ref_db_server where ref_db_id not in (${dbIds}));
    </if>
    </select>
</mapper>
