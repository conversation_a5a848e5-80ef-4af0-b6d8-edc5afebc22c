<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SoftwareOrderDao">

    <resultMap type="com.tipray.dlp.bean.SoftwareOrder" id="resultMap">
        <result property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="name" column="name"/>
        <result property="softwareName" column="software_name"/>
        <result property="softwareVersion" column="software_version"/>
        <result property="publisher" column="publisher"/>
        <result property="linkman" column="linkman"/>
        <result property="contact" column="contact"/>
        <result property="purchaseNum" column="purchase_num"/>
        <result property="price" column="price"/>
        <result property="purchaseDate" column="purchase_date"/>
        <result property="expirationDate" column="expiration_date"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result property="remark" column="remark"/>
        <result property="orderSize" column="orderSize"/>
        <result property="active" column="active"/>
        <result property="effectTime" column="effect_time"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <if test="imminentAlarmDate != null">
                and expiration_date &gt;= #{currentDate} and expiration_date &lt;= #{imminentAlarmDate}
            </if>
            <if test="expireAlarmDate != null">
                and expiration_date &lt; #{currentDate} and expiration_date > #{expireAlarmDate}
            </if>
            <if test="name != null and name != ''">
                and name like CONCAT('%',#{name},'%')
            </if>
            <if test="softwareName != null and softwareName != ''">
                and software_name like CONCAT('%',#{softwareName},'%')
            </if>
            <if test="softwareNames != null and softwareNames != ''">
                and software_name in (${softwareNames})
            </if>
            <if test="orderId != null and orderId != ''">
                and order_id like CONCAT('%',#{orderId},'%')
            </if>
            <if test="orderIds != null and orderIds != ''">
                and order_id in (${orderIds})
            </if>
            <if test="publisher != null and publisher != ''">
                and publisher like CONCAT('%',#{publisher},'%')
            </if>
            <if test="softwareVersion != null and softwareVersion != ''">
                and software_version like CONCAT('%',#{softwareVersion},'%')
            </if>
            <if test="ids != null and ids != ''">
                and id in (${ids})
            </if>
            <if test="active != null">
                and active = #{active}
            </if>
            <include refid="commonSQL.andNotDel"></include>
        </where>
    </sql>

    <select id="getActiveByName" parameterType="java.lang.String" resultType="java.lang.Integer">
        select distinct active from software_order where software_name = #{value}
    </select>

    <select id="listSoftwareName" parameterType="com.tipray.dlp.bean.vo.SoftwareOrderVO" resultType="java.lang.String">
      select software_name from software_order
      <include refid="vo_where_sql"/>
    </select>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.SoftwareOrderVO" resultType="java.lang.Long">
        select count(0) from software_order
        <include refid="vo_where_sql"/>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.SoftwareOrderVO" resultMap="resultMap">
        select * from software_order
        <include refid="vo_where_sql"/>
    </select>

    <select id="listPurchaseByVO" parameterType="com.tipray.dlp.bean.vo.SoftwareOrderVO" resultMap="resultMap">
        select order_id, software_name, count(0) orderSize, sum(purchase_num) purchase_num, expiration_date from software_order
        <include refid="vo_where_sql"/>
        group by order_id, software_name
    </select>

    <select id="listPurchaseGroupByName" parameterType="com.tipray.dlp.bean.vo.SoftwareOrderVO" resultMap="resultMap">
        select min(order_id) order_id, software_name, count(0) orderSize, sum(purchase_num) purchase_num from software_order
        <include refid="vo_where_sql"/>
        group by software_name
    </select>

    <update id="updateActive">
        update software_order set
        active = #{active},
        effect_time = #{effectTime}
        where software_name in (@softwareNameList)
    </update>

    <update id="deleteByOrderIds">
        delete from software_order where order_id in (${value})
    </update>

    <select id="listSoftwareOrder" resultMap="resultMap">
        select * from software_order
        where software_name = #{value}
    </select>

    <select id="getPurchaseNum" resultType="java.lang.Integer">
        select SUM(purchase_num) from software_order where software_name = #{value}
    </select>

    <select id="listPurchaseNumBySoftwareName" resultMap="resultMap">
        select software_name, purchase_num from software_order
        where expiration_date >= #{value}
    </select>

    <select id="listStatusGroupBySoftwareName" parameterType="com.tipray.dlp.bean.vo.SoftwareOrderVO" resultMap="resultMap">
        select id, software_name, effect_time, active from software_order
        <include refid="vo_where_sql"/>
        group by software_name
    </select>
</mapper>
