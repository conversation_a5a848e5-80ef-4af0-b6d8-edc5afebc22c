<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ServerLibraryDao">

    <resultMap type="com.tipray.dlp.bean.ServerLibrary" id="resultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="ipType" column="ip_type"/>
        <result property="beginIp" column="begin_ip"/>
        <result property="endIp" column="end_ip"/>
        <result property="groupId" column="group_id"/>
        <result property="beginPort" column="begin_port"/>
        <result property="endPort" column="end_port"/>
        <result property="processName" column="process_name"/>
        <result property="uploadType" column="upload_type"/>
        <result property="uploadFileExt" column="upload_file_ext"/>
        <result property="downloadType" column="download_type"/>
        <result property="downloadFileExt" column="download_file_ext"/>
        <result property="waitAccessLogin" column="wait_access_login"/>
        <result property="upLoadLimitSpeed" column="up_load_limit_speed"/>
        <result property="bufferTime" column="buffer_time"/>
        <result property="useNewVersion" column="use_new_version"/>
    </resultMap>

    <sql id="vo_where_sql">
        <if test="searchInfo != null and searchInfo != ''">
            and (name like CONCAT('%',#{searchInfo},'%') or begin_ip like CONCAT('%',#{searchInfo},'%') or end_ip like CONCAT('%',#{searchInfo},'%'))
        </if>
        <if test="groupId != null and groupId != '' or groupId == 0">
            and group_id = #{groupId}
        </if>
        <if test="ids != null and ids != ''">
            and id in (${ids})
        </if>

        <if test="name != null and name != ''">
            and (name like CONCAT('%',#{name},'%'))
        </if>
        <if test="useNewVersion != null">
            and (use_new_version like CONCAT('%',#{useNewVersion},'%'))
        </if>
        <if test="ipType != null">
            and (ip_type like CONCAT('%',#{ipType},'%'))
        </if>
        <if test="domain != null and domain != ''">
            and (begin_ip like CONCAT('%',#{domain},'%') or end_ip like CONCAT('%',#{domain},'%'))
        </if>
        <if test="port != null">
            and (begin_port like CONCAT('%',#{port},'%') or end_port like CONCAT('%',#{port},'%'))
        </if>
        <if test="processList != null">
            and
            <foreach collection="processList" item="item" open="(" close=")" separator="or">
                process_name like concat('%', #{item}, '%')
            </foreach>
        </if>
        <include refid="commonSQL.andNotDel"></include>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.GroupVO" resultMap="resultMap">
        select * from http_white_list_server
        <where>
            <include refid="vo_where_sql"/>
        </where>
    </select>

    <select id="selectBrowserByVO" resultMap="resultMap">
        select id, name, use_new_version, process_name from http_white_list_server
        <where><include refid="vo_where_sql"/></where>
    </select>

    <update id="updateProcessNameByIds">
        <foreach collection="params" item="item" separator=";">
            update http_white_list_server set process_name = #{item.processName}, modify_time = now() where id = #{item.id}
        </foreach>
    </update>

</mapper>
