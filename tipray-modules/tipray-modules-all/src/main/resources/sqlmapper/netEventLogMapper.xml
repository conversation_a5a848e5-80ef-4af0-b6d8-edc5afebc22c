<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.NetEventLogDao">

    <resultMap type="com.tipray.dlp.bean.NetEventLog" id="UrlsSearchMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="eventType" column="event_type"/>
        <result property="deviceType" column="device_type"/>
        <result property="deviceDesc" column="device_desc"/>
        <result property="netInterfaceKey" column="net_interface_key"/>
        <result property="devTypeId" column="devType_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="keyword1 != null and keyword1 != ''">
                and device_desc = #{keyword1}
            </if>
            <if test="devType != null and devType != ''">
                and device_type like CONCAT('%',#{devType},'%')
            </if>
            <if test="eventType != null">
                and event_type = #{eventType}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from net_event_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="UrlsSearchMap">
        select id, term_id, user_id, create_time, event_type, device_desc, device_type, net_interface_key, dev_type_id
        from net_event_log a
        <include refid="sql_where"></include>
    </select>

</mapper>
