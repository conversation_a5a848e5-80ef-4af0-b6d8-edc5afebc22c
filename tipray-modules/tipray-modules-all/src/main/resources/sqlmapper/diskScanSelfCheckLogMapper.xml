<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DiskScanSelfCheckLogDao">

    <resultMap type="com.tipray.dlp.bean.DiskScanSelfCheckLog" id="detailResultMap">
        <result property="id" column="id"/>
        <result property="guid" column="guid"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="opType" column="op_type"/>
        <result property="status" column="status_type"/>
        <result property="fileTotal" column="proc_total"/>
        <result property="dealSuc" column="proc_suc"/>
        <result property="dealFail" column="proc_fail"/>
        <result property="sensFile" column="sst_file"/>
        <result property="nonSensFile" column="nonsst_File"/>
        <result property="endTime" column="endTime"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="keyword1 != null and keyword1 != ''">
                and op_type = #{keyword1}
            </if>
            <if test="opType != null and opType != ''">
                and op_type = #{opType}
            </if>
            <if test="keyword2 != null and keyword2 != ''">
                and status_type = #{keyword2}
            </if>
            <if test="guid != null and guid != ''">
                and guid like CONCAT('%',#{guid},'%')
            </if>
            <if test="guids != null and guids != ''">
                and guid in (${guids})
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Byte">
        select 0 from disk_scan_seft_log a
        <include refid="sql_where"/>
        group by guid,term_id
    </select>

    <select id="listByVO" resultMap="detailResultMap">
        select max(id) id,term_id,max(user_id) user_id,guid,max(op_type) op_type,max(term_group_id) term_group_id,max(user_group_id) user_group_id,
            min(create_time) create_time,
            max(create_time) endTime,
            max(status_type) status_type,
            max(proc_total) proc_total,
            max(proc_suc) proc_suc,
            max(proc_fail) proc_fail,
            max(sst_file) sst_file,
            max(nonsst_file) nonsst_file
        from disk_scan_seft_log a
        <include refid="sql_where"/>
        group by guid,term_id
    </select>

    <select id="listScanStatus" resultMap="detailResultMap">
        select guid,term_id, status_type, count(0) proc_total from disk_scan_seft_log
        <where>
            guid = #{guid} and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            <if test="objectIds != null and objectIds != ''">
                and term_id in (${objectIds})
            </if>
        </where>
        GROUP BY guid, status_type
    </select>

    <delete id="deleteByIds">
        delete from disk_scan_seft_log where id in (${value})
    </delete>

</mapper>
