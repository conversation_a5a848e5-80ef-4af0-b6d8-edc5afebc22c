<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.FlowProcessDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.FlowProcess">
        <id column="id" property="id" />
        <result column="name" property="name"/>
        <result column="flow_id" property="flowId"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.tipray.dlp.bean.FlowProcess">
		insert into flow_process(name,flow_id,remark)
		values(#{name},#{flowId},#{remark})
	</insert>

    <update id="update" parameterType="com.tipray.dlp.bean.FlowProcess">
        update flow_process set
        name=#{name},flow_id=#{flowId},,remark=#{remark}
        where id=#{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
		delete from flow_process where id=#{id}
	</delete>

    <delete id="deleteByIds" parameterType="java.lang.String">
		delete from flow_process where id in (${value})
	</delete>

    <select id="getById" parameterType="java.lang.Long" resultMap="resultMap">
		select * from flow_process  where id=#{value}
	</select>

    <select id="listByFlowId" parameterType="java.lang.Long" resultMap="resultMap">
		select * from flow_process where flow_id = #{value}
	</select>

    <select id="getByName" parameterType="java.lang.String" resultMap="resultMap">
        select * from flow_process where name = #{value}
    </select>

    <update id="updateFlowId">
        update flow_process set flow_id = #{flowId} where id in (${ids})
    </update>

</mapper>