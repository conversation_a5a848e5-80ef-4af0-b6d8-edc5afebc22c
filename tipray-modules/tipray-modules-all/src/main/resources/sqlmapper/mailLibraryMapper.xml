<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.MailLibraryDao">

    <resultMap type="com.tipray.dlp.bean.MailLibrary" id="resultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="address" column="address"/>
        <result property="office" column="office"/>
        <result property="unit" column="unit"/>
        <result property="type" column="type"/>
        <result property="remark" column="remark"/>
        <result property="groupId" column="group_id"/>
    </resultMap>

    <sql id="vo_where_sql">
      <where>
        <if test="groupId != null and groupId != 0">
            and group_id = #{groupId}
        </if>
        <if test="type != null and type != ''">
            and type = #{type}
        </if>
        <if test="searchInfo != null and searchInfo != ''">
            and (name like CONCAT('%',#{searchInfo},'%') or address like CONCAT('%',#{searchInfo},'%'))
        </if>
          <if test="ids != null and ids != ''">
              and id in (${ids})
          </if>
        <include refid="commonSQL.andNotDel"></include>
      </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.MailLibraryVO" resultMap="resultMap">
        select id, name, address, office, type, unit, remark, group_id, modify_time from mail_library
        <include refid="vo_where_sql"/>
    </select>

    <select id="selectIdleTimePage" resultMap="resultMap">
        select t1.id, t1.name, t1.address, t1.office, t1.type, t1.unit, t1.remark, t1.group_id, t1.modify_time,
        case when t2.last_used_time is null then t1.create_time else t2.last_used_time end lastusedtime,
        datediff(#{nowTime},case when t2.last_used_time is null then t1.create_time else t2.last_used_time end) idleTime
        from mail_library t1
        left join mail_lib_idle_time t2 on t1.id = t2.mail_id
        <where>
            <if test="idleTime != null">
                and datediff(#{nowTime},case when t2.last_used_time is null then t1.create_time else t2.last_used_time end) &gt;= #{idleTime}
            </if>
            and t1.deleted = 0
            <if test="groupId != null and groupId != 0">
                and t1.group_id = #{groupId}
            </if>
            <if test="type != null and type != ''">
                and t1.type = #{type}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and (t1.name like CONCAT('%',#{searchInfo},'%') or t1.address like CONCAT('%',#{searchInfo},'%'))
            </if>
            <if test="ids != null and ids != ''">
                and t1.id in (${ids})
            </if>
        </where>
    </select>

    <select id="selectIdleTimeCount" resultType="long">
        select count(*) total
        from mail_library t1
        left join mail_lib_idle_time t2 on t1.id = t2.mail_id
        <where>
            <if test="idleTime != null">
                and datediff(#{nowTime},case when t2.last_used_time is null then t1.create_time else t2.last_used_time end) &gt;= #{idleTime}
            </if>
            and t1.deleted = 0
        </where>
    </select>
</mapper>
