<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ChatImagetLogDao">

    <resultMap type="com.tipray.dlp.bean.ChatImagetLog" id="ChatImagetLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="localAccountInfo" column="local_account_info"/>
        <result property="chatSessionInfo" column="chat_session_info"/>
        <result property="senderInfo" column="sender_info"/>
        <result property="localFilePath" column="local_file_path"/>
        <result property="chatType" column="chat_type"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            local_file_path is not null and local_file_path != ''
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt;= #{endDate}
            </if>
            <if test="chatType != null">
                and chat_type = #{chatType}
            </if>
            <if test="keyword != null and keyword != ''">
                and chat_session_info = #{keyword}
            </if>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
        </where>
    </sql>

    <delete id="deleteLogByVo">
        delete from chat_image_log
        where 1=1
        <include refid="commonSQL.andObjectTypeAndObjectId"/>
        <if test="startDate != null and endDate != null">
            and DATE(create_time) &gt;= DATE(#{startDate}) and DATE(create_time) &lt;= DATE(#{endDate})
        </if>
        <if test="chatType != null">
            and chat_type = #{chatType}
        </if>
    </delete>

    <select id="getChatSessionInfo" resultMap="ChatImagetLogMap">
        select chat_session_info,local_account_info,term_id,chat_type from chat_image_log a
        <include refid="sql_where"></include>
        group by chat_session_info,local_account_info,term_id,chat_type
    </select>

    <select id="list" resultMap="ChatImagetLogMap">
        select id, user_id, term_id, create_time, local_account_info, chat_session_info, sender_info, chat_type, local_file_path, backup_server_id, upload_file_guid, term_group_id, user_group_id
        from chat_image_log a
        <include refid="sql_where"></include>
    </select>

</mapper>
