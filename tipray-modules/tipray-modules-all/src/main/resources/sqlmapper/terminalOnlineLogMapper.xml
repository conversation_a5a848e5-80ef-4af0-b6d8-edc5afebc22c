<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.TerminalOnlineLogDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.TermLoginLogoutLog">
        <id property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="termState" column="term_state"/>
        <result property="serverId" column="server_id"/>
        <result property="createTime" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="keyword1 != null and keyword1 != ''">
                and term_state = #{keyword1}
            </if>
            <!-- 多日未关机设备的终端虚拟上线状态设为99 -->
            <if test="keyword1 == null or keyword1 == ''">
                <if test="excludeVirtualLog">
                    and term_state != 99
                </if>
            </if>
            <if test="keyword2 != null and keyword2 != ''">
                and server_id = #{keyword2}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from term_login_logout_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="resultMap">
        select id,term_id,term_state,server_id,create_time,term_group_id
        from term_login_logout_log
        <include refid="sql_where"></include>
    </select>

    <select id="statisticGroupByCreateTime" resultType="java.util.Map">
        select count(0) num,date_format(create_time, '%Y-%m-%d') perDay from term_login_logout_log
        where create_time &gt;= #{startDate} and create_time &lt; #{endDate} and term_state = 1
        group by perDay order by perDay
    </select>
</mapper>
