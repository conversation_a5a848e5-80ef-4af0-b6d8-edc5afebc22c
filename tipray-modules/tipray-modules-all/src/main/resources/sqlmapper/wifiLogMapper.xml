<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.WifiLogDao">

    <resultMap type="com.tipray.dlp.bean.WifiLog" id="wifiLogMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="logTime" column="create_time"/>
        <result property="wifiName" column="wifi_ssid"/>
        <result property="wifiMac" column="wifi_bssid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="wifiName != null and wifiName != ''">
                and wifi_ssid like CONCAT('%',#{wifiName},'%')
            </if>
            <if test="wifiMac != null and wifiMac != ''">
                and wifi_bssid like CONCAT('%',#{wifiMac},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from wifi_alarm_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.WifiLogVO" resultMap="wifiLogMap">
        select id, term_id, user_id, create_time, wifi_ssid, wifi_bssid, term_group_id, user_group_id
        from wifi_alarm_log
        <include refid="sql_where"></include>
    </select>

</mapper>
