<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.TermSecurityDetectionLastRecordDao">

    <resultMap type="com.tipray.dlp.bean.TermSecurityDetectionLastRecord" id="TermSecurityDetectionRecordMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="lastDate" column="last_date"/>
        <result property="risk" column="risk"/>
        <result property="score" column="score"/>
        <result property="lastScanJson" column="last_scan_json"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="risk != null">
                and risk = #{risk}
            </if>
            <if test="score != null">
                and score = #{score}
            </if>
            <if test="termIds != null and termIds != ''">
                and term_id in (${termIds})
            </if>
            <if test="pass != null and pass == 1">
                and risk = 0
            </if>
            <if test="pass != null and pass == 0">
                and risk != 0
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and term_id like CONCAT('%',#{searchInfo},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(term_id) as number from term_last_scan_record
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="TermSecurityDetectionRecordMap">
        select id, user_id, term_id, score, last_date, risk,
               <if test="selectedDetails != null and selectedDetails">
                   last_scan_json,
               </if>
               term_group_id, user_group_id
        from term_last_scan_record
        <include refid="sql_where"></include>
    </select>

    <select id="getById" resultType="com.tipray.dlp.bean.TermSecurityDetectionLastRecord">
        select id, user_id, term_id, risk, last_scan_json, score, last_date, term_group_id, user_group_id
        from term_last_scan_record
        <where>
            id = #{id}
        </where>
    </select>
</mapper>
