<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.FileBackupLogDao">

    <resultMap type="com.tipray.dlp.bean.FileBackupLog" id="FileBackupLogMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="localFileName" column="local_file_name"/>
        <result property="backupFileName" column="backup_file_name"/>
        <result property="fileSize" column="file_size"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="localFileName != null and localFileName != ''">
                and local_file_name like CONCAT('%',#{localFileName},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.BackupFileNameVO" resultType="java.lang.Long">
      select count(0) from file_backup_record
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.BackupFileNameVO" resultMap="FileBackupLogMap">
      select id, term_id, user_id, local_file_name, backup_file_name, file_size, create_time, modify_time,backup_server_id,upload_file_guid,term_group_id,user_group_id
      from file_backup_record
      <include refid="sql_where"></include>
    </select>

</mapper>
