<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.PictureLibDao">

    <resultMap type="com.tipray.dlp.bean.PictureLib" id="PictureLibMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="resolution" column="resolution"/>
        <result property="md5" column="md5"/>
        <result property="ext" column="suffix"/>
        <result property="guid" column="guid"/>
        <result property="ftp_id" column="ftpId"/>
        <result property="groupId" column="group_id"/>
        <result property="resultCode" column="result_code"/>
        <result property="resultContent" column="result_content"/>
    </resultMap>

    <sql id="vo_where_sql">
      <where>
          <include refid="commonSQL.andNotDel"></include>
          <if test="groupId != null and groupId != 0">
              and group_id = #{groupId}
          </if>
          <if test="resultCode != null and resultCode != 0">
              and result_code = #{resultCode}
          </if>
          <if test="searchInfo != null and searchInfo != ''">
              and name like CONCAT('%',#{searchInfo},'%')
          </if>
          <if test="ids != null and ids != ''">
              and id in (${ids})
          </if>
      </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.PictureLibVO" resultMap="PictureLibMap">
        select * from picture_lib
        <include refid="vo_where_sql"/>
    </select>

    <select id="listUrlLib" resultMap="PictureLibMap">
        select * from picture_lib
        where 1=1
        <if test="ids != null and ids != ''">
            and id in (${ids})
        </if>
    </select>

    <select id="listUrlGroupLib" resultType="com.tipray.dlp.bean.PictureLibGroup">
        select id,name,parent_id parentId from picture_lib_group
        where 1=1
        <if test="groupIds != null and groupIds != ''">
            and id in (${groupIds})
        </if>
    </select>

    <update id="moveGroup" >
        update picture_lib set group_id = #{groupId}
        where id in (${ids})
    </update>

</mapper>
