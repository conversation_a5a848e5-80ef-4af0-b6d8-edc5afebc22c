<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ReportTypeTemplateDao">

    <resultMap type="com.tipray.dlp.bean.ReportTypeTemplate" id="resultMap">
        <result property="id" column="id"/>
        <result property="businessName" column="business_name"/>
        <result property="templateType" column="template_type"/>
        <result property="appType" column="app_type"/>
        <result property="businessCode" column="business_code"/>
        <result property="templateChipIds" column="template_chip_ids"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <if test="searchInfo != null and searchInfo != ''">
                and business_name like concat('%',#{searchInfo},'%')
            </if>
            <if test="templateType != null">
                and template_type = #{templateType}
            </if>
        </where>
    </sql>
    <insert id="insertWithId">
        insert into report_type_template(id, business_name, template_type, app_type, business_code, template_chip_ids, remark, create_time, modify_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.businessName}, #{item.templateType}, #{item.appType}, #{item.businessCode}, #{item.templateChipIds}, #{item.remark}, #{item.createTime}, #{item.modifyTime})
        </foreach>
    </insert>

    <select id="selectPageByVO" resultMap="resultMap">
        select * from report_type_template
        <include refid="sql_where"></include>
    </select>
</mapper>