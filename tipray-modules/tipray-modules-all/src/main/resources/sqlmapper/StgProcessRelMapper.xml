<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.StgProcessRelDao">

    <select id="listAllUsedProcess" resultType="java.util.Map">
        select rel.process_id as id, p.process_name as processName, rel.stg_def_id as stgId, s.name as stgName  from rel_stg_def_process rel
        left join print_wp_signature_process p on p.id = rel.process_id
        left join stg_def s on  s.id = rel.stg_def_id
        where process_id in (${processIds})
    </select>

</mapper>
