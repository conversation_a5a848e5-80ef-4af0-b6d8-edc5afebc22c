<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SpaceConfigDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.SpaceConfig">
        <id column="id" property="id" />
        <result column="use_percent" property="usePercent"/>
        <result column="box_warn" property="boxWarn"/>
        <result column="email" property="email"/>
        <result column="email_template_id" property="emailTemplateId"/>
        <result column="repeat_email_day" property="repeatEmailDay"/>
        <result column="del_log_day" property="delLogDay"/>
        <result column="email_date" property="createTime"/>
    </resultMap>


    <select id="countByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultType="java.lang.Long">
        select count(0) from space_config
    </select>
    <select id="listByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultMap="resultMap">
        select * from space_config
    </select>

    <select id="getByUsePercen" parameterType="java.lang.Integer" resultMap="resultMap">
        select * from space_config where use_percent = #{value}
    </select>
</mapper>
