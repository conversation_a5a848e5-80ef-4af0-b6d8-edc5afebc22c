<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.StgRelatedSoftwareDao">

    <resultMap type="com.tipray.dlp.bean.StgRelatedSoftware" id="resultMap">
        <result property="softwareName" column="software_name"/>
        <result property="softwareVersions" column="software_versions"/>
        <result property="matchType" column="match_type"/>
        <result property="appInfo" column="app_info"/>
    </resultMap>

    <select id="listSoftwareInfoByStgId" resultMap="resultMap">
        select * from rel_stg_software where stg_id = #{value}
    </select>

    <!--批量新增-->
    <insert id="batchInsert">
        insert into rel_stg_software(software_name, stg_id, software_versions, match_type, app_info)
        values
        <foreach collection="softwareList" item="item" separator=",">
            (#{item.softwareName}, #{stgId}, #{item.softwareVersions}, #{item.matchType}, #{item.appInfo})
        </foreach>
    </insert>

    <delete id="deleteByStgId">
       delete from rel_stg_software where stg_id = #{value}
    </delete>

    <delete id="deleteByStgIds">
       delete from rel_stg_software where stg_id in (@stgIds)
    </delete>
</mapper>