<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.AppReportExLogDao">

    <resultMap type="com.tipray.dlp.bean.AppReportExLog" id="AppReportExLogMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="createTime" column="create_time"/>
        <result property="sysUserName" column="sys_user_Name"/>
        <result property="processName" column="process_name"/>
        <result property="appPath" column="app_path"/>
        <result property="winTitle" column="win_title"/>
        <result property="productName" column="product_name"/>
        <result property="productVer" column="product_ver"/>
        <result property="originFileName" column="origin_file_name"/>
        <result property="fileDesc" column="file_desc"/>
        <result property="companyName" column="company_name"/>
        <result property="copyright" column="copyright"/>
        <result property="interName" column="inter_name"/>
        <result property="totalRunTime" column="total_run_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <if test="appGroupId != null">
                and process_name in (@processNameList)
            </if>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="processName != null and processName != ''">
                and process_name like CONCAT('%',#{processName},'%')
            </if>
            <if test="termId != null and termId != ''">
                and term_id = #{termId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="appPath != null and appPath != ''">
                and app_path = #{appPath}
            </if>
        </where>
    </sql>

    <!--<select id="count" parameterType="com.tipray.dlp.bean.vo.AppReportExVO" resultMap="AppReportExLogMap">
        SELECT distinct term_id, user_id , app_path, DATE_FORMAT(create_time, '%Y-%m-%d') as create_time_fromat FROM app_reportex
        <include refid="sql_where"></include>
    </select>-->

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.AppReportExVO" resultMap="AppReportExLogMap">
      select max(id) id,
             term_id,
             user_id,
             max(create_time) create_time,
             max(sys_user_name) sys_user_name,
             max(process_name) process_name,
             app_path,
             max(win_title) win_title,
             max(product_name) product_name,
             max(product_ver) product_ver,
             max(origin_file_name) origin_file_name,
             max(file_desc) file_desc,
             max(company_name) company_name,
             max(copyright) copyright,
             max(inter_name) inter_name,
             max(term_group_id) term_group_id,
             max(user_group_id) user_group_id,
             DATE_FORMAT(create_time, '%Y-%m-%d') as date_time_format,
             SUM(timestampdiff(second, start_time, end_time)) total_run_time
      from app_reportex
       <include refid="sql_where"></include>
        GROUP BY term_id, user_id, app_path,date_time_format
    </select>

    <select id="listTermTimeCount" parameterType="com.tipray.dlp.bean.vo.AppReportExVO" resultMap="AppReportExLogMap">
        select
        term_id,term_group_id,
        SUM(timestampdiff(second, start_time, end_time)) total_run_time
        from app_reportex
        <include refid="sql_where"></include>
        group by term_id,term_group_id
    </select>

    <select id="listDetail" parameterType="com.tipray.dlp.bean.vo.AppReportExVO" resultMap="AppReportExLogMap">
        select id,term_id, user_id,start_time, end_time, create_time, sys_user_name, process_name, app_path, win_title, product_name,
               product_ver, origin_file_name, file_desc, company_name, copyright, inter_name, term_group_id, user_group_id
        from app_reportex
        <include refid="sql_where"/>
    </select>
    <select id="countDetail" resultType="java.lang.Long">
        SELECT COUNT(1) FROM app_reportex
        <include refid="sql_where"/>
    </select>
    <select id="countNotGroup" resultType="java.lang.Long">
        select count(*) from app_reportex
        <include refid="sql_where"/>
    </select>
    <select id="listNotGroup" resultMap="AppReportExLogMap">
        select id,term_id, user_id,start_time, end_time, create_time, sys_user_name, process_name, app_path, win_title, product_name,
               product_ver, origin_file_name, file_desc, company_name, copyright, inter_name, term_group_id, user_group_id from app_reportex
        <include refid="sql_where"/>
        order by term_id, user_id, app_path, create_time desc
    </select>

</mapper>
