<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.UsbDeviceDao">

    <select id="selectPageByVO" resultType="com.tipray.dlp.bean.UsbDevice">
        select t1.id,t1.usb_name usbName,t1.usb_code usbCode,t1.manufacturer,t1.usb_size usbSize,t1.memo,t1.usb_type usbType,t1.group_id groupId,t1.create_time createTime,
        t1.modify_time modifyTime,t2.name groupName,t1.driver_type driverType, t1.src_pnp_device_id, t1.pid, t1.vid
        from usb_device t1
        left join usb_group t2 on t1.group_id =t2.id
        <where>
            <if test="groupId != null and groupId != ''">
                and group_id = #{groupId}
            </if>
            <if test="usbName != null and usbName != ''">
                and t1.usb_name like CONCAT('%',#{usbName},'%')
            </if>
            <if test="usbCode != null and usbCode != ''">
                and t1.usb_code like CONCAT('%',#{usbCode},'%')
            </if>
            <if test="manufacturer != null and manufacturer != ''">
                and t1.manufacturer like CONCAT('%',#{manufacturer},'%')
            </if>
            <if test="vid != null and vid != ''">
                and t1.vid like CONCAT('%',#{vid},'%')
            </if>
            <if test="pid != null and pid != ''">
                and t1.pid like CONCAT('%',#{pid},'%')
            </if>
            <if test="srcPnpDeviceId != null and srcPnpDeviceId != ''">
                and t1.src_pnp_device_id like CONCAT('%',#{srcPnpDeviceId},'%')
            </if>
            <if test="usbType != null and usbType != ''">
                and t1.usb_type = #{usbType}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and t1.usb_name like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="ids != null and ids != ''">
                and t1.id in (${ids})
            </if>
            <include refid="commonSQL.andNotDel"></include>
        </where>
    </select>

    <select id="getById" resultType="com.tipray.dlp.bean.UsbDevice">
        select u.*, ug.name groupName from usb_device u
        LEFT OUTER JOIN usb_group ug ON u.group_id = ug.id
        where u.id = #{value}
        <include refid="commonSQL.andNotDel"></include>
    </select>

    <update id="moveGroup" >
        update usb_device set group_id = #{groupId}
        where id in (${ids})
    </update>
</mapper>
