<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SensitiveDetectScreenRcdLogDao">

    <resultMap type="com.tipray.dlp.bean.SensitiveDetectScreenRcdLog" id="SensitiveDetectScreenRcdLogMap">
        <result property="id" column="id"/>
        <result property="recordNum" column="record_num"/>
        <result property="eventGuid" column="event_guid"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="begRcdTime" column="begin_rcd_time"/>
        <result property="endRcdTime" column="end_rcd_time"/>
        <result property="processName" column="proc_name"/>
        <result property="sensitiveFilePath" column="sensitive_file_path"/>
        <result property="createTime" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="rcdFileVer" column="rcd_file_ver"/>
        <result property="screenRcdGuid" column="screen_rcd_guid"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="processName != null and processName != ''">
                and process_name like CONCAT('%',#{processName},'%')
            </if>
            <if test="eventGuid != null and eventGuid != ''">
                and event_guid = #{eventGuid}
            </if>
            <!--<if test="eventGuid != null and eventGuid != ''">
                and event_guid = #{eventGuid}
            </if>-->
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.SensitiveDetectScreenRcdLogVO" resultType="java.lang.Long">
        select count(0) from sensitive_detect_screed_rcd_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.SensitiveDetectScreenRcdLogVO" resultMap="SensitiveDetectScreenRcdLogMap">
        select id, create_time ,user_id, term_id, proc_name,begin_rcd_time,end_rcd_time, sensitive_file_path,record_num, term_group_id, user_group_id, rcd_file_ver, screen_rcd_guid from sensitive_detect_screed_rcd_log
        <include refid="sql_where"></include>
    </select>

    <delete id="deleteByIds">
        delete from sensitive_detect_screed_rcd_log where id in (${value})
    </delete>

</mapper>
