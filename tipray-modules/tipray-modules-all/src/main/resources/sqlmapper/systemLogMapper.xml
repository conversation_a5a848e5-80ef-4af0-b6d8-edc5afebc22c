<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SystemLogDao">

    <resultMap type="com.tipray.dlp.bean.SystemLog" id="SystemLogMap">
        <result property="id" column="id"/>
        <result property="recordNum" column="record_num"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="logSource" column="log_src"/>
        <result property="eventId" column="event_id"/>
        <result property="recordId" column="record_id"/>
        <result property="logType" column="log_type"/>
        <result property="logLevel" column="log_level"/>
        <result property="remark" column="log_desc"/>
        <result property="sysLogDate" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="logType != null">
                and log_type = #{logType}
            </if>
            <if test="logLevel != null">
                and log_level = #{logLevel}
            </if>
            <if test="eventType != null">
                and ( event_id = #{eventType} <if test="refEventTypes != null and refEventTypes != ''"> or event_id in (${refEventTypes})</if>)
            </if>
            <if test="isSysTimes != null ">
                <if test="'false'.toString() == isSysTimes.toString()">
                    <if test="sysLogDate != null">
                        and DATE(create_time) = DATE(#{sysLogDate})
                    </if>
                </if>
                <if test="'true'.toString() == isSysTimes.toString()">
                    <if test="sysLogDateStart != null">
                        and DATE(create_time) &gt;= DATE(#{sysLogDateStart})
                    </if>
                    <if test="sysLogDateEnd != null">
                        and DATE(create_time) &lt;= DATE(#{sysLogDateEnd})
                    </if>
                </if>
            </if>
            and event_id != 100 and event_id != 101
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.SystemLogVo" resultType="java.lang.Long">
        select count(0) from system_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.SystemLogVo" resultMap="SystemLogMap">
        select id, record_num, term_id, user_id, create_time, log_src, event_id, record_id, log_type, log_level, log_desc, term_group_id, user_group_id
         from system_log
        <include refid="sql_where"></include>
    </select>

    <select id="statisticGroupByLogLevel" resultType="java.util.Map">
        select count(0) num,log_level logLevel,date_format(create_time, '%Y-%m-%d') perDay from system_log
        where create_time &gt;= #{startDate} and create_time &lt; #{endDate}
        <if test="logType != null">
            and log_type = #{logType}
        </if>
        group by logLevel, perDay
    </select>
</mapper>
