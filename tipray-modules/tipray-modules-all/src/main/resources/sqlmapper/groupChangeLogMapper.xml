<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.GroupChangeLogDao">

    <resultMap type="com.tipray.dlp.bean.GroupChangeLog" id="GroupChangeLogMap">
        <result property="id" column="id"/>
        <result property="recordNum" column="record_num"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="changeType" column="change_type"/>
        <result property="groupName" column="group_name"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="changeType != null">
                and change_type = #{changeType}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.SystemLogVo" resultType="java.lang.Long">
        select count(0) from group_info_change
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.SystemLogVo" resultMap="GroupChangeLogMap">
        select id, record_num, term_id, user_id, create_time, change_type, group_name, term_group_id, user_group_id from group_info_change
        <include refid="sql_where"></include>
    </select>

</mapper>
