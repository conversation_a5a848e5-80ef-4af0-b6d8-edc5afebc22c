<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.LocalFileShareRecordDao">

    <resultMap type="com.tipray.dlp.bean.LocalFileShareRecord" id="LocalFileShareRecordMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="fileName" column="file_name"/>
        <result property="backupFilePath" column="backup_file_path"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileFullPath" column="file_full_path"/>
        <result property="fileType" column="file_type"/>
        <result property="createTime" column="create_time"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="localFilePath" column="local_file_path"/>
        <result property="fileOpType" column="file_op_type"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name like CONCAT('%',#{fileName},'%')
            </if>
            <if test="fileType != null and fileType != ''">
                and file_type = #{fileType}
            </if>
            <if test="fileOpType != null and fileOpType != ''">
                and file_op_type = #{fileOpType}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.LocalFileShareLogVO" resultType="java.lang.Long">
      select count(0) from local_file_share_record
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.LocalFileShareLogVO" resultMap="LocalFileShareRecordMap">
      select id, term_id, user_id, file_name, file_size, create_time,backup_server_id,upload_file_guid,local_file_path,file_type,file_full_path,file_op_type,term_group_id,user_group_id
      from local_file_share_record
      <include refid="sql_where"></include>
    </select>

</mapper>
