<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.dlp.dao.WebpageMatchRuleDao">
    <sql id="vo_where_sql">
        <where>
            <include refid="commonSQL.andNotDel"></include>
            <if test="groupId != null and groupId != 0">
                and group_id = #{groupId}
            </if>
            <if test="groupIds != null and groupIds != ''">
                and group_id in (${groupIds})
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and name like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="name != null and name != ''">
                and name like CONCAT('%',#{name},'%')
            </if>
            <if test="host != null and host != ''">
                and host like CONCAT('%',#{host},'%')
            </if>
            <if test="beginPort != null">
                and begin_port like CONCAT('%',#{beginPort},'%')
            </if>
            <if test="endPort != null">
                and end_port like CONCAT('%',#{endPort},'%')
            </if>
            <if test="ids != null and ids != ''">
                and id in (${ids})
            </if>
        </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.WebpageMatchRuleVO"
            resultType="com.tipray.dlp.bean.WebpageMatchRule">
        select * from webpage_match_rule
        <include refid="vo_where_sql"/>
    </select>
</mapper>