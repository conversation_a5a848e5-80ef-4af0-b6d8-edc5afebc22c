<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ScheduledTaskDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.ScheduledTask">
        <id column="id" property="id" />
        <result column="name" property="name"/>
        <result column="refclass" property="refclass"/>
        <result column="method" property="method"/>
        <result column="cron" property="cron"/>
        <result column="state" property="state"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.tipray.dlp.bean.Dictionary">
		insert into scheduled_task(name,refclass,method,cron,state,remark)
		values(#{name},#{refclass},#{method},#{cron},#{state},#{remark})
	</insert>

    <update id="update" parameterType="com.tipray.dlp.bean.Permission">
        update scheduled_task set
        name=#{name},refclass=#{refclass},method=#{method},cron=#{cron},remark=#{remark}
        where id=#{id}
    </update>

    <update id="updateState">
        update scheduled_task set state=#{state}
        where id=#{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
		delete from scheduled_task where id=#{value}
	</delete>

    <delete id="deleteByIds" parameterType="java.lang.String">
		delete from scheduled_task where id in (${value})
	</delete>

    <select id="getById" parameterType="java.lang.Long" resultMap="resultMap">
		select * from scheduled_task where id=#{value}
	</select>

    <select id="list" resultMap="resultMap">
		select * from scheduled_task
	</select>

    <select id="listByIds" parameterType="java.lang.String" resultMap="resultMap">
		select * from scheduled_task where id in (${value})
	</select>

    <select id="countByVO" resultType="java.lang.Long">
		select count(0) from scheduled_task
	</select>

    <select id="listByVO" resultMap="resultMap">
		select * from scheduled_task
	</select>

</mapper>