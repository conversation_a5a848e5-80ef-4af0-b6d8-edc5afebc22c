<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ViolationResponseRuleDao">

    <resultMap id="violationResponseRuleMap" type="com.tipray.dlp.bean.ViolationResponseRule">
        <id column="id" property="id" />
        <result column="name" property="name"/>
        <result column="alarm_limit" property="alarmLimit"/>
        <result column="msg_form_position" property="msgFormPosition"/>
        <result column="msg_form_type" property="msgFormType"/>
        <result column="msg_form_close" property="msgFormClose"/>
        <result column="strategy_id" property="strategyId"/>
        <result column="alarm_limit_describe" property="alarmLimitDescribe"/>
        <result column="terminal_alarm_param" property="terminalAlarmParam"/>
        <result column="config_method" property="configMethod"/>
    </resultMap>
    <resultMap id="ruleOverviewMap" type="com.tipray.dlp.bean.RuleOverview">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="stg_type_number" property="strategyName"/>
        <result column="strategy_id" property="strategyId"/>
        <result column="alarm_limit_describe" property="alarmLimitDescribe"/>
        <result column="terminal_alarm_param" property="terminalAlarmParam"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="alarm_limit" property="alarmLimit"/>
        <result column="msg_form_position" property="msgFormPosition"/>
        <result column="msg_form_type" property="msgFormType"/>
        <result column="msg_form_close" property="msgFormClose"/>
    </resultMap>

    <sql id="select_info_where_vo">
        where deleted = 0
            <if test="configMethod != null">
                and config_method = #{configMethod}
            </if>
            <if test="searchInfo != null and searchInfo != '' ">
                and name like CONCAT('%',#{searchInfo},'%')
            </if>
        <if test="name != null and name != ''">
            and name like CONCAT('%',#{name},'%')
        </if>
        <if test="alarmLimitSum != null"><![CDATA[
            and alarm_limit & #{alarmLimitSum} = #{alarmLimitSum}
        ]]></if>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.ViolationResponseRuleVO" resultMap="violationResponseRuleMap">
        select * from violation_response_rule
        <include refid="select_info_where_vo"></include>
    </select>

    <select id="listByRuleOverviewVO" parameterType="com.tipray.dlp.bean.vo.RuleOverviewVO" resultMap="ruleOverviewMap">
        select rrs.biz_type stg_type_number, rrs.biz_id strategy_id, r.name, r.alarm_limit_describe, r.terminal_alarm_param, r.id, rrs.alarm_type, r.alarm_limit,
               r.msg_form_position, r.msg_form_type, r.msg_form_close
        from violation_response_rule r,rel_violation_response_rule_stg rrs
        where r.id = rrs.data_id and r.deleted = 0
        <if test="strategyIds != null and strategyIds != ''">
            and rrs.biz_id in (${strategyIds})
        </if>
        <if test="ids != null and ids != ''">
            and r.id in (${ids})
        </if>
        <if test="searchInfo != null and searchInfo != '' ">
            and r.name like CONCAT('%',#{searchInfo},'%')
        </if>
    </select>

</mapper>
