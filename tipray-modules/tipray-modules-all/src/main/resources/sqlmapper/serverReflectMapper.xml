<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ServerReflectDataDao">

    <select id="listServerIds" resultType="java.lang.Long">
        select server_id from ref_data_server
        where ref_server_id = #{value}
    </select>

    <select id="getRefServerIdByServerId" resultType="java.lang.Long">
        select ref_server_id from ref_data_server
        where server_id = #{value}
    </select>

    <!--新增所有列-->
    <insert id="batchInsert">
        INSERT INTO ref_data_server(server_id, ref_server_id)
        VALUES
        <foreach collection="serverIds" item="serverId" separator=",">
            (#{serverId},#{refServerId})
        </foreach>
    </insert>
    <insert id="batchInsertByMap">
        INSERT INTO ref_data_server(server_id, ref_server_id)
        VALUES
        <foreach collection="data" item="item" separator=",">
            (#{item.devId},#{item.refDevId})
        </foreach>
    </insert>

    <insert id="insert">
        insert into ref_data_server(server_id, ref_server_id)
        values (#{serverId},#{refServerId})
    </insert>

    <delete id="deleteByRefServerId">
        delete from ref_data_server where ref_server_id = #{value}
    </delete>

    <delete id="deleteByRefServerIds">
        delete from ref_data_server where ref_server_id in (@serverIds)
    </delete>

    <delete id="deleteByServerId">
        delete from ref_data_server where server_id = #{serverId}
    </delete>

    <delete id="deleteByServerIds">
        delete from ref_data_server where server_id in (@serverIds)
    </delete>

    <select id="findListByRelServerId" resultType="java.util.Map">
        select server_id serverId,ref_server_id refServerId from ref_data_server where ref_server_id = #{value}
    </select>
    <select id="listMapByGroupId" resultType="java.util.Map">
        select server_id serverId,ref_server_id refServerId from ref_data_server r, dev_server s
        where r.server_id = s.dev_id and s.group_id = #{value}
    </select>
    <select id="listAll" resultType="java.util.Map">
        select server_id serverId,ref_server_id refServerId from ref_data_server
    </select>

    <select id="listAllRefServerId" resultType="java.lang.Long">
        select distinct ref_server_id from ref_data_server
    </select>

    <select id="listAllServerId" resultType="java.lang.Long">
        select distinct server_id from ref_data_server
    </select>

</mapper>
