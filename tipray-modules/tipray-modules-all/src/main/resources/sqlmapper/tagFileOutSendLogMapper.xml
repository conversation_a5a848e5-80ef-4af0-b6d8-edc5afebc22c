<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.TagFileOutSendLogDao">

    <resultMap type="com.tipray.dlp.bean.TagFileOutSendLog" id="TagFileOutSendLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="trank" column="trank"/>
        <result property="trankName" column="trank_name"/>
        <result property="fileName" column="file_name"/>
        <result property="filePath" column="file_path"/>
        <result property="lossType" column="loss_type"/>
        <result property="encryFlag" column="encry_flag"/>
        <result property="tagContent" column="tag_content"/>
        <result property="tagContentId" column="tag_content_id"/>
        <result property="isBlock" column="is_block"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="devId" column="backup_server_id"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="trankName != null and trankName != ''">
                and trank_name like CONCAT('%',#{trankName},'%')
            </if>
            <if test="tagContent != null and tagContent != ''">
                and tag_content like CONCAT('%',#{tagContent},'%')
            </if>
            <if test="lossType != null and lossType != ''">
                and loss_type = #{lossType}
            </if>
            <if test="block != null">
                and is_block = #{block}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from label_file_out_send_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="TagFileOutSendLogMap">
        select id, user_id,term_id, trank, trank_name, file_name, file_path, loss_type, encry_flag, tag_content, tag_content_id, is_block, term_group_id, user_group_id, create_time, upload_file_guid, backup_server_id
        from label_file_out_send_log
        <include refid="sql_where"></include>
    </select>

</mapper>
