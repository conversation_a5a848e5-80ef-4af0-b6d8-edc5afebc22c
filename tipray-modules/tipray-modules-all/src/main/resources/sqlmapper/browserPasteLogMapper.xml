<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.BrowserPasteLogDao">

    <resultMap type="com.tipray.dlp.bean.BrowserPasteLog" id="BrowserPasteLogMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="browserType" column="browser_type"/>
        <result property="pasteContent" column="paste_content"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="browserType != null and browserType != ''">
                and browser_type = #{browserType}
            </if>
            <if test="pasteContent != null and pasteContent != ''">
                and paste_content like CONCAT('%',#{pasteContent},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from browser_paste_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="BrowserPasteLogMap">
        select id,term_id,user_id,create_time, browser_type,paste_content, term_group_id, user_group_id
        from browser_paste_log a
        <include refid="sql_where"></include>
    </select>

</mapper>
