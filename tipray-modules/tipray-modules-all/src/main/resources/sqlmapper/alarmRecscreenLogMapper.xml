<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.AlarmRecscreenLogDao">

    <resultMap type="com.tipray.dlp.bean.AlarmRecscreenLog" id="AlarmRecscreenLogMap">
        <result property="id" column="id"/>
        <result property="guid" column="guid"/>
        <result property="createTime" column="create_time"/>
        <result property="backupServerId" column="backup_server_id"/>
        <result property="uploadFileGuid" column="upload_file_guid"/>
        <result property="localfilepath" column="local_file_path"/>
        <result property="recscreenGuid" column="rec_screen_id"/>
        <result property="rcdFileVer" column="rcd_file_ver"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="screenRcdGuid" column="screen_rcd_guid"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="recscreenGuid != null and recscreenGuid != ''">
                and rec_screen_id = #{recscreenGuid}
            </if>
            <!--<if test="rcdFileVer != null">
                and rcd_file_ver = #{rcdFileVer}
            </if>-->
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.AlarmRecscreenLogVO" resultType="java.lang.Long">
      select count(0) from alarm_recscreen_log
      <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.AlarmRecscreenLogVO" resultMap="AlarmRecscreenLogMap">
      select id, guid, create_time, backup_server_id, upload_file_guid, local_file_path, rec_screen_id, rcd_file_ver, start_time, end_time, screen_rcd_guid from alarm_recscreen_log
      <include refid="sql_where"></include>
    </select>

</mapper>
