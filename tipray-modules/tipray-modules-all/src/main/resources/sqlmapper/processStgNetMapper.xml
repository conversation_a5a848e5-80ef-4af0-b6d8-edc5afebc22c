<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ProcessStgNetDao">

    <resultMap type="com.tipray.dlp.bean.ProcessStgNet" id="ProcessStgNetMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="processStgId" column="process_stg_id"/>
        <result property="mode" column="mode"/>
        <result property="protocol" column="protocol"/>
        <result property="beginPort" column="begin_port"/>
        <result property="endPort" column="end_port"/>
        <result property="beginIp" column="begin_ip"/>
        <result property="endIp" column="end_ip"/>
        <result property="includeLan" column="include_lan"/>
    </resultMap>
    <sql id="vo_where_sql">
        <where>
            <if test="groupId != null">
                and process_stg_id = #{groupId}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and name like CONCAT('%',#{searchInfo},'%')
            </if>
            <include refid="commonSQL.andNotDel"></include>
        </where>
    </sql>

    <select id="getByName" resultMap="ProcessStgNetMap">
        select id, name, process_stg_id, mode, protocol, begin_port, end_port, begin_ip, end_ip, include_lan        from process_stg_net
        <where>
            name = #{value}
            <include refid="commonSQL.andNotDel"></include>
        </where>
        limit 1
    </select>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultMap="ProcessStgNetMap">
      select id, name, process_stg_id, mode, protocol, begin_port, end_port, begin_ip, end_ip, include_lan from process_stg_net
      <include refid="vo_where_sql"></include>
    </select>

    <update id="deleteByProcessIds">
        update process_stg_net set deleted=1, modify_time=#{modifyTime}
        where process_stg_id in (${ids})
    </update>

</mapper>
