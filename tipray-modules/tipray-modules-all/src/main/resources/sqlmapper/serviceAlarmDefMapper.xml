<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ServiceAlarmDefDao">

    <resultMap id="resultMap" type="com.tipray.dlp.bean.ServiceAlarmDef">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="active" column="active"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="vo_sql_where">
        <where>
            <if test="searchInfo != null and searchInfo != ''">
                and name like concat('%', #{searchInfo}, '%')
            </if>
            <if test="devType != null or devId != null">
                and id in (
                    select stg_id from service_alarm_config
                    <where>
                        <if test="devType != null">
                            and dev_type = #{devType}
                        </if>
                        <if test="devId != null">
                            and dev_id = #{devId}
                        </if>
                    </where>
                    group by stg_id
                )
            </if>
        </where>
    </sql>

    <select id="selectPageByVO" resultMap="resultMap">
        select id, name, active, remark from server_alarm_def
        <include refid="vo_sql_where"/>
    </select>
</mapper>