<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SecretLevelLogDao">

    <resultMap type="com.tipray.dlp.bean.SecretLevelLog" id="SecretLevelLogMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="fileTotal" column="file_total"/>
        <result property="successCount" column="success_count"/>
        <result property="guid" column="guid"/>
        <result property="createTime" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.LogVO" resultType="java.lang.Long">
      select count(0) from secret_level_log
      <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.LogVO" resultMap="SecretLevelLogMap">
      select id, term_id, user_id, file_total, success_count, guid, create_time, term_group_id, user_group_id from secret_level_log
      <include refid="sql_where"></include>
    </select>

    <delete id="deleteByIds">
        delete from secret_level_log where id in (${value})
    </delete>

</mapper>
