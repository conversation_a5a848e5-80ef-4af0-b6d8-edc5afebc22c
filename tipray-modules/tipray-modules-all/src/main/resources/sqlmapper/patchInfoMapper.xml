<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.PatchInfoDao">

    <resultMap type="com.tipray.dlp.bean.PatchInfo" id="PatchInfoMap">
        <result property="id" column="id"/>
        <result property="guid" column="guid"/>
        <result property="parentGuid" column="parent_guid"/>
        <result property="updateId" column="update_id"/>
        <result property="parentUpdateId" column="parent_update_id"/>
        <result property="patchKb" column="patch_kb"/>
<!--        <result property="patchTitle" column="patch_title"/>-->
<!--        <result property="patchDescribe" column="patch_describe"/>-->
        <result property="downloadState" column="download_state"/>
        <result property="downloadAddr" column="download_addr"/>
        <result property="patchSize" column="patch_size"/>
        <result property="patchLevel" column="patch_level"/>
        <result property="msId" column="ms_id"/>
        <result property="auxiliaryNum" column="auxiliary_num"/>
<!--        <result property="priority" column="priority"/>-->
        <result property="systemByte" column="system_byte"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="termId" column="term_id"/>
        <result property="state" column="state"/>
        <result property="installResult" column="install_result"/>
    </resultMap>
    <resultMap type="com.tipray.dlp.bean.PatchTermScanInfo" id="PatchTermScanInfoMap">
        <result property="id" column="id"/>
        <result property="termId" column="term_id"/>
        <result property="latelyTime" column="lately_time"/>
        <result property="scanState" column="scan_state"/>
        <result property="errMsg" column="err_msg"/>
        <result property="systemVersion" column="system_version"/>
        <result property="totalPatch" column="total_patch"/>
        <result property="totalInstall" column="total_install"/>
        <result property="critical" column="critical"/>
        <result property="important" column="important"/>
        <result property="media" column="media"/>
        <result property="low" column="low"/>
        <result property="undefine" column="undefine"/>
        <result property="installResult" column="install_result"/>
        <result property="state" column="state"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>
    <update id="updatePatchInfo">
        update patch_info
        set download_state = #{downloadState}
        where id = #{id}
    </update>
    <!--查询单个-->
    <select id="getByGuid" parameterType="com.tipray.dlp.bean.vo.PatchVO" resultMap="PatchInfoMap">
        select a.id, a.guid,a.parent_guid, a.update_id, a.parent_update_id, a.patch_kb,a.download_addr, a.patch_size, a.patch_level, a.ms_id, a.auxiliary_num, a.system_byte, a.create_time,a.modify_time
        from patch_info a
        where a.guid = #{guid}
        and a.patch_size <![CDATA[<]]> '1024000000000'
        LIMIT 1;
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="list" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultMap="PatchInfoMap">
        select a.id,a.guid,a.parent_guid, a.update_id, a.patch_kb, a.download_addr, a.patch_size, a.patch_level, a.ms_id, a.auxiliary_num, a.system_byte, a.create_time, a.modify_time,
        b.download_state download_state
        from patch_info a
        left join dlp_sysmgr.patch_file b on a.guid = b.guid
        where a.patch_size <![CDATA[<]]> '1024000000000'
        <if test='guidList != null and guidList.size()>0'>
            and a.guid in (@guidList)
        </if>

    </select>
    <select id="listToSyncByVO" parameterType="com.tipray.dlp.bean.vo.PatchVO" resultMap="PatchInfoMap">
        select distinct a.guid, a.parent_guid, a.update_id, a.parent_update_id, a.download_addr,a.patch_size
        from patch_info a
        <where>
            ( a.parent_guid is null or a.parent_guid = ''
                <if test='patchLevels != null and patchLevels != ""'>
                    or a.patch_level in (@patchLevels)
                </if>
            )
            <if test='guidList != null and guidList.size()>0'>
                and a.guid not in (@guidList)
            </if>
            and a.patch_size <![CDATA[<]]> '1024000000000'
        </where>
    </select>
    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.PatchVO" resultMap="PatchInfoMap">
        select max(a.id) id, a.guid,a.parent_guid,a.update_id, a.parent_update_id, a.patch_kb,a.download_addr,a.patch_size,a.patch_level,a.ms_id,max(a.auxiliary_num) auxiliary_num,
        max(a.system_byte) system_byte,max(a.create_time) create_time,max(a.modify_time) modify_time
        from patch_info a
        where a.patch_size <![CDATA[<]]> '1024000000000'
        <if test='queryParentPatch != null'>
            <if test='queryParentPatch == true'>
                and (a.parent_guid is null or a.parent_guid = '')
            </if>
            <if test='queryParentPatch == false'>
                and  (a.parent_guid is not null and a.parent_guid != '')
            </if>
        </if>
        <if test="ids != null and ids != ''">
            and a.guid in (@ids)
        </if>
        <if test='parentGuid != null'>
            and a.parent_guid = #{parentGuid}
        </if>
        <if test='patchKb != null and patchKb!=""'>
            and a.patch_kb like concat('%',#{patchKb},'%')
        </if>
        <if test='patchLevel != null'>
            and a.patch_level = #{patchLevel}
        </if>
        <if test='patchLevels != null and patchLevels != ""'>
            and a.patch_level in (@patchLevels)
        </if>
        <if test='parentGuidList != null and parentGuidList.size()>0'>
            and a.parent_guid in (@parentGuidList)
        </if>
        <if test='guidList != null and guidList.size()>0'>
            and a.guid in (@guidList)
        </if>
        group by a.guid,a.parent_guid,a.update_id, a.parent_update_id,a.patch_kb,a.download_addr,a.patch_size,a.patch_level,a.ms_id
    </select>
    <select id="countListByVO" parameterType="com.tipray.dlp.bean.vo.PatchVO" resultType="java.lang.Long">
        select count(*)
        from patch_info a
        where a.patch_size <![CDATA[<]]> '1024000000000'
        <if test='queryParentPatch != null'>
            <if test='queryParentPatch == true'>
                and (a.parent_guid is null or a.parent_guid = '')
            </if>
            <if test='queryParentPatch == false'>
                and  (a.parent_guid is not null and a.parent_guid != '')
            </if>
        </if>
        <if test="ids != null and ids != ''">
            and a.guid in (@ids)
        </if>
        <if test='parentGuid != null'>
            and a.parent_guid = #{parentGuid}
        </if>
        <if test='patchKb != null and patchKb!=""'>
            and a.patch_kb like concat('%',#{patchKb},'%')
        </if>
        <if test='patchLevel != null'>
            and a.patch_level = #{patchLevel}
        </if>
        <if test='patchLevels != null and patchLevels != ""'>
            and a.patch_level in (@patchLevels)
        </if>
        <if test='guidList != null and guidList.size()>0'>
            and a.guid in (@guidList)
        </if>
        group by a.guid,a.parent_guid,a.update_id, a.parent_update_id,a.patch_kb,a.download_addr,a.patch_size,a.patch_level,a.ms_id
    </select>

    <sql id="PatchTermScanInfoWhere">
        WHERE b.term_id is not null
        <if test="objectType == 1">
            <if test="objectId != null">
                and b.term_id = #{objectId}
            </if>
            <if test="objectIds != null and objectIds != ''">
                and b.term_id in (@objectIds)
            </if>
        </if>
        <if test='searchType != null and searchType != 0 and searchContent != null and searchContent != ""'>
            <if test='searchType == 1 or searchType == 3'>
                and b.term_id in (@ids)
            </if>
            <if test='searchType == 2'>
                and b.system_version like CONCAT('%',#{searchContent},'%')
            </if>
        </if>
    </sql>

    <select id="listTermPatchScanInfo" resultMap="PatchTermScanInfoMap">
        <!-- 终端，安全等级，操作系统，已安装，未安装，总补丁数，扫描时间，扫描结果 -->
        select
        b.term_id,lately_time,scan_state,err_msg,system_version,total_patch,total_install,critical,important,media,low,undefine,
        (critical+important+media+low+undefine) noInstall
        from patch_term_scan_info b
        <include refid="PatchTermScanInfoWhere"></include>
    </select>
    <select id="countTermPatchScanInfo" parameterType="com.tipray.dlp.bean.vo.PatchVO" resultType="java.lang.Long">
        <!-- 终端，安全等级，操作系统，已安装，未安装，总补丁数，扫描时间，扫描结果 -->
        select count(*)
        from patch_term_scan_info b
        <include refid="PatchTermScanInfoWhere"></include>
    </select>

    <sql id="PatchInfoWhere">
        <if test='searchType != null and searchType != 0 and searchContent != null and searchContent != ""'>
            <if test='searchType == 3'>
                and a.patch_kb like CONCAT('%',#{searchContent},'%')
            </if>
            <if test='searchType == 5'>
                and a.ms_id like CONCAT('%',#{searchContent},'%')
            </if>
            <if test='searchType == 7'>
                and b.state = #{searchContent}
            </if>
            <if test='searchType == 9'>
                and a.patch_level = #{searchContent}
            </if>
        </if>
        <if test='guidList != null and  guidList.size > 0'>
            and a.guid in (@guidList)
        </if>
    </sql>
    <select id="listPatchInfo" parameterType="com.tipray.dlp.bean.vo.PatchVO" resultMap="PatchInfoMap">
        select  max(a.id) id,a.guid ,a.parent_guid ,a.update_id ,a.patch_kb ,a.download_addr ,a.patch_size ,a.patch_level ,a.ms_id ,max(a.auxiliary_num) auxiliary_num,
        max(a.system_byte) system_byte,max(a.create_time) create_time,max(a.modify_time) modify_time
        from patch_info a
        where a.patch_size <![CDATA[<]]> '1024000000000'
        and (a.parent_guid is null or a.parent_guid = '')
      <include refid="PatchInfoWhere"></include>
        group by a.guid,a.parent_guid,a.update_id,a.patch_kb,a.download_addr,a.patch_size,a.patch_level,a.ms_id
    </select>
    <select id="countPatchInfo" resultType="java.lang.Long">
        select count(*)
        from patch_info a
        where a.patch_size <![CDATA[<]]> '1024000000000'
        and (a.parent_guid is null or a.parent_guid = '')
        <include refid="PatchInfoWhere"></include>
        GROUP BY a.id,a.guid,a.parent_guid,a.update_id,a.patch_kb,a.download_addr,a.patch_size,a.patch_level,a.ms_id
    </select>
    <select id="listTermPatchInfo" parameterType="com.tipray.dlp.bean.vo.PatchVO" resultMap="PatchInfoMap">
        select b.id,b.guid,b.update_id,b.term_id,b.state,b.install_result
        from patch_term_info b
        where b.deleted = 0
        and (b.parent_guid is null or b.parent_guid = '')
        <if test='termId != null'>
            and b.term_id = #{termId}
        </if>
        <if test='guid != null'>
            and b.guid = #{guid}
        </if>
        <if test='searchType != null and searchType != 0 and searchContent != null and searchContent != ""'>
        <if test='searchType == 7'>
            and b.state = #{searchContent}
        </if>
        </if>
        <if test='guidList != null and guidList.size>0'>
            and b.guid in (@guidList)
        </if>
    </select>
    <select id="countTermPatchInfo" resultType="java.lang.Long">
        select count(*)
        from patch_term_info b
        where b.deleted = 0
        and (b.parent_guid is null or b.parent_guid = '')
        <if test='termId != null'>
            and b.term_id = #{termId}
        </if>
        <if test='guid != null'>
            and b.guid = #{guid}
        </if>
        <if test='searchType != null and searchType != 0 and searchContent != null and searchContent != ""'>
            <if test='searchType == 7'>
                and b.state = #{searchContent}
            </if>
        </if>
        <if test='guidList != null and guidList.size>0'>
            and b.guid in (@guidList)
        </if>
    </select>

    <select id="countChildPatchSize" resultMap="PatchInfoMap">
        select parent_guid, count(*) auxiliary_num
        from patch_info
        where parent_guid in (${value})
          and patch_size <![CDATA[<]]> '1024000000000'
        GROUP BY parent_guid
    </select>
</mapper>
