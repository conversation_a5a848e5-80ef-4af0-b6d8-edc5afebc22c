<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.MailWhiteListLogDao">

    <resultMap type="com.tipray.dlp.bean.MailWhiteListLog" id="MailWhiteListLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="mailSender" column="mail_sender"/>
        <result property="mailReceiver" column="mail_receiver"/>
        <result property="mailWhiteType" column="mail_white_type"/>
        <result property="guid" column="guid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="mailSender != null and mailSender != ''">
                and mail_sender like CONCAT('%',#{mailSender},'%')
            </if>
            <if test="mailReceiver != null and mailReceiver != ''">
                and mail_receiver like CONCAT('%',#{mailReceiver},'%')
            </if>
<!--            <if test="mailWhiteType != null and mailWhiteType != ''">-->
<!--                and mail_white_type = #{mailWhiteType}-->
<!--            </if>-->
            /* 不加入mailWhiteType == 0查询条件的话，如果mailWhiteType=0，该模糊查询不起作用，只有mailWhiteType=1或为null才起作用 */
            <if test="(mailWhiteType != null and mailWhiteType != '') or mailWhiteType == 0">
                and mail_white_type = #{mailWhiteType}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from mail_white_list_record
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="MailWhiteListLogMap">
        select *
        from mail_white_list_record
        <include refid="sql_where"></include>
    </select>

    <resultMap type="com.tipray.dlp.bean.MailWhiteListAttachLog" id="MailWhiteListAttachLogMap">
        <result property="id" column="id"/>
        <result property="mailAttach" column="mail_attach"/>
        <result property="attachSize" column="attach_size"/>
        <result property="guid" column="guid"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
    </resultMap>

    <sql id="sql_where1">
        <where>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="guid != null and guid != ''">
                and guid = #{guid}
            </if>
            <if test="guids != null and guids != ''">
                and guid in (${guids})
            </if>
        </where>
    </sql>

    <select id="getAttachNum" resultType="java.lang.Integer">
        select count(*) from mail_white_attach_record
        <include refid="sql_where1"></include>
    </select>



    <select id="attachListByVO" resultMap="MailWhiteListAttachLogMap">
        select * from mail_white_attach_record
        <include refid="sql_where1"></include>
    </select>
</mapper>
