<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.UsbApprovalAccessLogDao">

    <resultMap type="com.tipray.dlp.bean.UsbApprovalAccessLog" id="UsbApprovalAccessLogMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="devType" column="dev_type"/>
        <result property="driverType" column="driver_type"/>
        <result property="volumeName" column="volume_name"/>
        <result property="model" column="model"/>
        <result property="pnpDeviceId" column="pnp_device_id"/>
        <result property="devSize" column="dev_size"/>
        <result property="approvalResult" column="approval_result"/>
        <result property="createTime" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="srcPnpDeviceId" column="src_pnp_device_id"/>
        <result property="pid" column="pid"/>
        <result property="vid" column="vid"/>
    </resultMap>

    <sql id="sql_where_vo">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="approvalResult != null and approvalResult != ''">
                and approval_result = #{approvalResult}
            </if>
            <if test="devType != null and devType != ''">
                and dev_type = #{devType}
            </if>
            <if test="driverType != null and driverType != ''">
                and driver_type = #{driverType}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.UsbApprovalLogVO" resultType="java.lang.Long">
        select count(0) from usb_approval_access_log
        <include refid="sql_where_vo"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.UsbApprovalLogVO" resultMap="UsbApprovalAccessLogMap">
        select id,term_id,user_id,dev_type,driver_type,volume_name,model,pnp_device_id,dev_size,approval_result,create_time, term_group_id, user_group_id,
               src_pnp_device_id, pid, vid
        from usb_approval_access_log
        <include refid="sql_where_vo"></include>
    </select>

</mapper>
