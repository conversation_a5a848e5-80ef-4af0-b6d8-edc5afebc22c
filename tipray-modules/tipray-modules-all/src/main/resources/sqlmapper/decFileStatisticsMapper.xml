<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DecFileStatisticsDao">

    <resultMap type="com.tipray.dlp.bean.DecFileStatistics" id="DecFileStatisticsMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="userGroup" column="user_group"/>
        <result property="fileTotal" column="file_total"/>
        <result property="successCount" column="success_count"/>
        <result property="failCount" column="fail_count"/>
        <result property="dectype" column="decType"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
        </where>
    </sql>


    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.LogVO" resultType="java.lang.Long">
      select count(0) from dec_file_statistics
        <include refid="sql_where"></include>
    </select>
    
    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.LogVO" resultMap="DecFileStatisticsMap">
      select id, term_id, user_id, user_group, file_total, success_count, fail_count, decType, create_time from dec_file_statistics
        <include refid="sql_where"></include>
    </select>

    <delete id="deleteByIds">
        delete from dec_file_statistics where id in (${value})
    </delete>

</mapper>