<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.TermInstallerPackageDao">

    <resultMap type="com.tipray.dlp.bean.TermInstallerPackage" id="TermInstallerPackageMap">
        <result property="id" column="id"/>
        <result property="fileName" column="file_name"/>
        <result property="fileGuid" column="file_guid"/>
        <result property="serverId" column="server_id"/>
        <result property="status" column="status"/>
        <result property="statusInfo" column="status_info"/>
        <result property="localPath" column="local_path"/>
        <result property="version" column="version"/>
        <result property="osType" column="os_type"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="bussId" column="buss_id"/>
    </resultMap>

    <select id="getByMd5" resultMap="TermInstallerPackageMap">
        select id, file_name, file_guid, server_id, status, status_info, local_path, version, os_type, buss_id, create_time, modify_time
        from term_installer_package
        where buss_id = #{bussId} and local_path like CONCAT('%',#{md5},'%')
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="list" resultMap="TermInstallerPackageMap">
        select id, file_name, file_guid, server_id, status, status_info, local_path, version, os_type, buss_id, create_time, modify_time
        from term_installer_package
        where 1=1
        <if test="ids != null and ids != ''">
            and id in (${ids})
        </if>
    </select>

    <sql id="sql_where">
        <where>
            <if test="bussId != null">
                and buss_id = #{bussId}
            </if>
            <if test="fileSuffix != null">
                and
                <foreach collection="fileSuffix" index="index" item="suffix" separator="or" open="(" close=")">
                    file_name like CONCAT('%',#{suffix})
                </foreach>
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and file_name like CONCAT('%',#{searchInfo},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from term_installer_package
        <include refid="sql_where"/>
    </select>

    <select id="listByVO" resultMap="TermInstallerPackageMap">
        select id, file_name, file_guid, server_id, status, status_info, local_path, version, os_type, buss_id, create_time, modify_time from term_installer_package
        <include refid="sql_where"/>
    </select>

    <update id="updateStatus">
        update term_installer_package set status=#{status},status_info=#{statusInfo}
        where file_guid = #{fileGuid} and status != 6
    </update>
</mapper>
