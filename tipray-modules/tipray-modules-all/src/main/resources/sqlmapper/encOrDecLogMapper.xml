<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.EncOrDecLogDao">

    <resultMap type="com.tipray.dlp.bean.EncOrDecLog" id="EncOrDecLogMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="opType" column="op_type"/>
        <result property="fileTotal" column="file_total"/>
        <result property="successCount" column="succ_count"/>
        <result property="errorCount" column="fail_count"/>
        <result property="usedTime" column="used_time"/>
        <result property="guid" column="guid"/>
        <result property="createTime" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="deletedIds != null and deletedIds != ''">
                and user_id not in (${deletedIds})
            </if>
            <if test="opType != null and opType != ''">
                and op_type = #{opType}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.EncOrDecLogVO" resultType="java.lang.Long">
      select count(0) from enc_or_dec_log
      <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.EncOrDecLogVO" resultMap="EncOrDecLogMap">
      select id, term_id, user_id, op_type, file_total, succ_count, fail_count, used_time, guid, create_time, term_group_id, user_group_id from enc_or_dec_log
      <include refid="sql_where"></include>
    </select>

</mapper>
