<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.CustomSoftAssetInfoDao">

    <resultMap type="com.tipray.dlp.bean.CustomSoftAssetInfo" id="resultMap">
        <result property="id" column="id"/>
        <result property="propId" column="prop_id"/>
        <result property="softId" column="soft_id"/>
        <result property="value" column="value"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <delete id="deleteByIds">
        delete from custom_soft_asset_info where id in (${value})
    </delete>

    <delete id="deleteBySoftIds">
        delete from custom_soft_asset_info where soft_id in (${value})
    </delete>

    <insert id="batchInsert"  useGeneratedKeys="true" keyProperty="id" >
        insert into
        custom_soft_asset_info(prop_id, soft_id, value, create_time, modify_time)
        values
        <foreach collection="addList" item="c" separator=",">
            (
            #{c.propId}, #{c.softId}, #{c.value}, #{c.createTime}, #{c.modifyTime}
            )
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        update custom_soft_asset_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="prop_id = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    <if test="item.propId!=null">
                        when id = #{item.id} then #{item.propId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="soft_id = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    <if test="item.softId!=null">
                        when id = #{item.id} then #{item.softId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="value = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    <if test="item.value != null">
                        when id = #{item.id} then #{item.value}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    <if test="item.modifyTime!=null">
                        when id = #{item.id} then #{item.modifyTime}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in (@updateList.id)
    </update>
</mapper>
