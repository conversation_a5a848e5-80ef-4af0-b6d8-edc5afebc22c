<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ReadPermissionDao">

    <resultMap id="resultMap" type="com.tipray.dlp.bean.ReadPermission">
        <id column="stg_def_id" property="id" />
        <result column="stg_def_id" property="stgDefId"/>
        <result column="cascade_dept_id" property="cascadeDeptId"/>
        <result column="assign_dept_id" property="assignDeptId"/>
        <result column="except_dept_id" property="exceptDeptId"/>
        <result column="cascade_except_dept_id" property="cascadeExceptDeptId"/>
        <result column="assign_operator_id" property="assignOperatorId"/>
        <result column="except_operator_id" property="exceptOperatorId"/>
    </resultMap>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into read_permission(stg_def_id,cascade_dept_id,assign_dept_id,except_dept_id,cascade_except_dept_id,assign_operator_id,except_operator_id)
        values(#{id},#{cascadeDeptId},#{assignDeptId},#{exceptDeptId},#{cascadeExceptDeptId},#{assignOperatorId},#{exceptOperatorId})
    </insert>
    <delete id="deleteById">
        delete from read_permission where stg_def_id = #{value}
    </delete>
    <delete id="deleteByIds">
        delete from read_permission where stg_def_id in (${ids})
    </delete>
    <update id="update">
        update read_permission set
            cascade_dept_id = #{cascadeDeptId},
            assign_dept_id = #{assignDeptId},
            except_dept_id = #{exceptDeptId},
            cascade_except_dept_id = #{cascadeExceptDeptId},
            assign_operator_id = #{assignOperatorId},
            except_operator_id = #{exceptOperatorId}
        where stg_def_id = #{id}
    </update>

    <select id="getById" resultMap="resultMap">
        select * from read_permission where  stg_def_id = #{value}
    </select>
    <select id="listByIds" resultMap="resultMap">
        select * from read_permission where  stg_def_id in (${value})
    </select>
    <select id="listAll" resultMap="resultMap">
        select * from read_permission
    </select>
</mapper>
