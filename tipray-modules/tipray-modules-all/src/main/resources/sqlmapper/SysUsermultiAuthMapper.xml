<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SysUserMultiAuthDao">
    
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into sys_user_multi_auth(sys_user_id, auth_type, auth_value, enable, create_time)
        values
        <foreach collection="list"  item="item" separator="," index="index">
            ( #{item.sysUserId}, #{item.authType}, #{item.authValue}, #{item.enable}, #{item.createTime})
        </foreach>
    </insert>

    <update id="disableMultiLoginAuthBySysUserIds">
        update sys_user_multi_auth set enable = 0
        where sys_user_id in (@sysUserIds)
    </update>

</mapper>
