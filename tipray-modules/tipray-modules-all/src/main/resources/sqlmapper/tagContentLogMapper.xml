<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.TagContentLogDao">

    <resultMap type="com.tipray.dlp.bean.TagContentLog" id="TagContentLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="tagId" column="tag_id"/>
        <result property="tagContent" column="tag_content"/>
        <result property="tagType" column="tag_type"/>
        <result property="recordGuid" column="record_guid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="recordGuid != null and recordGuid != null">
                and record_guid = #{recordGuid}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from document_tag_detail_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="TagContentLogMap">
        select id, user_id,term_id, tag_id, tag_content, record_guid, tag_type, create_time, term_group_id, user_group_id
        from document_tag_detail_log
        <include refid="sql_where"></include>
    </select>

</mapper>
