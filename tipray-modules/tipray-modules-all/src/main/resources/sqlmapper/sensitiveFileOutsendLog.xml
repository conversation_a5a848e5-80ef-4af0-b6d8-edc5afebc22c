<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SensitiveFileOutsendLogDao">

    <resultMap type="com.tipray.dlp.bean.SensitiveFileOutsendLog" id="SensitiveFileOutsendLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="uploadFileGuid" column="upload_file_guid"/>
        <result property="processSerialId" column="process_serial_id"/>
        <result property="processDefinitionId" column="process_definition_id"/>
        <result property="lossType" column="loss_type"/>
        <result property="filePath" column="file_path"/>
        <result property="localFilePath" column="local_file_path"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="processGuid" column="process_guid"/>
        <result property="fileName" column="file_name"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>
    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="processDefinitionId != null">
                and process_definition_id = #{processDefinitionId}
            </if>
            <if test="processIds != null and processIds.size() > 0">
                and process_definition_id in (@processIds)
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name like CONCAT('%',#{fileName},'%')
            </if>
            <if test="lossType != null">
                and loss_type = #{lossType}
            </if>
        </where>
    </sql>
    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from sensitive_file_outsend_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="SensitiveFileOutsendLogMap">
        select id, user_id, term_id, create_time,backup_server_id, upload_file_guid,process_serial_id,process_definition_id,loss_type,file_path,local_file_path,process_guid,file_name,term_group_id,user_group_id
        from sensitive_file_outsend_log a
        <include refid="sql_where"></include>
    </select>

</mapper>
