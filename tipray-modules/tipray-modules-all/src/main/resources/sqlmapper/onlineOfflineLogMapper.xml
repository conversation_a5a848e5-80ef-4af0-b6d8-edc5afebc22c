<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.dlp.dao.OnlineOfflineLogDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.OnlineOfflineLog">
        <id column="id" property="id"/>
        <result column="term_id" property="termId"/>
        <result column="term_name" property="termName"/>
        <result column="computer_name" property="computerName"/>
        <result column="group_id" property="groupId"/>
        <result column="ip" property="ip"/>
        <result column="mac" property="mac"/>
        <result column="type" property="type"/>
        <result column="create_time" property="createTime"/>
        <result column="data_server_id" property="dataServerId"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <if test="range and startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="!range and date != null">
                and create_time = #{date}
            </if>
            <if test="termName != null and termName != ''">
                and term_name like CONCAT('%',#{termName},'%')
            </if>
            <if test="groupId != null">
                and group_id = #{groupId}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from online_offline_log
        <include refid="vo_where_sql"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.OnlineOfflineLogVo" resultMap="resultMap">
        select id
             , term_id
             , term_name
             , computer_name
             , group_id
             , ip
             , mac
             , type
             , create_time
             , data_server_id
        from online_offline_log
        <include refid="vo_where_sql"></include>
    </select>
</mapper>