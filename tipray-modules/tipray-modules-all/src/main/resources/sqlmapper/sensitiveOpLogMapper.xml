<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SensitiveOpLogDao">

    <resultMap type="com.tipray.dlp.bean.SensitiveOpLog" id="SensitiveOpLogMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="eventGuid" column="event_guid"/>
        <result property="processName" column="process_name"/>
        <result property="eventType" column="event_type"/>
        <result property="actionType" column="action_type"/>
        <result property="filePath" column="file_path"/>
        <result property="createTime" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="eventType != null and eventType != ''">
                and event_type = #{eventType}
            </if>
            <if test="processName != null and processName != ''">
                and process_name like CONCAT('%',#{processName},'%')
            </if>
            <if test="responseType != null">
                and action_type = #{responseType}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.SensitiveOpLogVO" resultType="java.lang.Long">
        select count(0) from sensitive_op_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.SensitiveOpLogVO" resultMap="SensitiveOpLogMap">
        select id, event_guid, create_time ,event_type, process_name, action_type, file_path, term_id, user_id, term_group_id, user_group_id from sensitive_op_log
        <include refid="sql_where"></include>
    </select>

    <delete id="deleteByIds">
        delete from sensitive_op_log where id in (${value})
    </delete>

</mapper>
