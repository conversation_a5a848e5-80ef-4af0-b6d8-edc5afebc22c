<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.TerminalBossCodeDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.TerminalBossCode">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <if test="searchInfo != null and searchInfo != ''">
                and code like CONCAT('%',#{searchInfo},'%')
            </if>
        </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultMap="resultMap">
        select * from terminal_boss_code
        <include refid="vo_where_sql"/>
    </select>
</mapper>
