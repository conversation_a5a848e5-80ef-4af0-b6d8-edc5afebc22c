<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.EnDeFileLogDao">

    <resultMap type="com.tipray.dlp.bean.EnDeFileLog" id="resultMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="filePath" column="file_path"/>
        <result property="fileExt" column="file_ext"/>
        <result property="fileProc" column="file_proc"/>
        <result property="opType" column="op_type"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>
    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="deletedIds != null and deletedIds != ''">
                and user_id not in (@deletedIds)
            </if>
            <if test="fileKeyWord != null and fileKeyWord != ''">
                and file_path like CONCAT('%',#{fileKeyWord},'%')
            </if>
            <if test="extKeyWord != null and extKeyWord != ''">
                and file_ext like CONCAT('%',#{extKeyWord},'%')
            </if>
            <if test="extList != null and extList.size() > 0 and extList.get(0) != ''">
                and file_ext in (@extList)
            </if>
            <if test="keyword1 != null and keyword1 != ''">
                and file_proc != #{keyword1}
            </if>
            <if test="keyword2 != null and keyword2 != ''">
                and file_proc != #{keyword2}
            </if>
            <if test="procKeyWord != null and procKeyWord != ''">
                and file_proc like CONCAT('%',#{procKeyWord},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from enc_dec_files_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="resultMap">
        select id, user_id, term_id, create_time, file_path, file_ext,file_proc, op_type, term_group_id, user_group_id
        from enc_dec_files_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listEncFileCount" resultType="com.tipray.dlp.bean.EnDeFileLog">
        select user_id userId, user_group_id, count(*) fileNum
        from enc_dec_files_log
        <include refid="sql_where"></include>
        group by user_id, user_group_id
        order by fileNum desc
    </select>
</mapper>
