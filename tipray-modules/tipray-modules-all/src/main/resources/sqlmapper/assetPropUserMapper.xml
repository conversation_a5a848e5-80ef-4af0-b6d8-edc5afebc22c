<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.AssetPropUserDao">

    <resultMap type="com.tipray.dlp.bean.AssetPropUser" id="AssetPropUserMap">
        <result property="propId" column="prop_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="flag" column="flag"/>
        <result property="name" column="name"/>
        <result property="userId" column="user_id"/>
        <result property="titleName" column="title_name"/>
    </resultMap>

    <!--查询单个-->
    <select id="getById" resultMap="AssetPropUserMap">
        select prop_id, parent_id, flag, name, user_id, title_name
        from asset_prop_user
        where  prop_id= #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="list" resultMap="AssetPropUserMap">
        select prop_id, parent_id, flag, name, user_id, title_name
        from asset_prop_user
        where 1=1
        <if test="type == 1">
            and ((prop_id &gt;= 1001 and prop_id &lt;= 1031) or (parent_id &gt;= 1001 and parent_id &lt;= 1031 ))
        </if>
        <if test="type == 2">
            and ((prop_id &gt;= 2001 and prop_id &lt;= 2004) or (parent_id &gt;= 2001 and parent_id &lt;= 2004 ))
        </if>
        <if test="userId != null">
            and user_id = #{userId}
        </if>
        <if test="parentId != null">
            and parent_id = #{parentId}
        </if>
    </select>

    <select id="countByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultType="java.lang.Long">
      select count(0) from asset_prop_user
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultMap="AssetPropUserMap">
      select prop_id, parent_id, flag, name, user_id, title_name from asset_prop_user
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="" useGeneratedKeys="true">
        insert into asset_prop_user(prop_id, parent_id, flag, name, user_id, title_name)
        values (#{propId}, #{parentId}, #{flag}, #{name}, #{userId}, #{titleName})
    </insert>

    <!--通过主键修改数据-->
    <update id="updateAssetPropUser">
        update asset_prop_user
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="titleName != null and titleName != ''">
                title_name = #{titleName},
            </if>
        </set>
        where  id= #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteByUserId">
        delete from asset_prop_user where  user_id = #{userId}
        <if test="type == 1">
            and ((prop_id &gt;= 1001 and prop_id &lt;= 1031) or (parent_id &gt;= 1001 and parent_id &lt;= 1031 ))
        </if>
        <if test="type == 2">
            and ((prop_id &gt;= 2001 and prop_id &lt;= 2004) or (parent_id &gt;= 2001 and parent_id &lt;= 2004 ))
        </if>
        <if test="parentId != null">
            and parent_id = #{parentId}
        </if>
    </delete>

    <delete id="deleteByPropIds">
        delete from asset_prop_user where  prop_id in (@propIds)
    </delete>

    <select id="listByPropId" parameterType="java.lang.Long" resultMap="AssetPropUserMap">
        select id, prop_id, parent_id, flag, name, user_id, title_name from asset_prop_user where prop_id = #{value}
    </select>
</mapper>
