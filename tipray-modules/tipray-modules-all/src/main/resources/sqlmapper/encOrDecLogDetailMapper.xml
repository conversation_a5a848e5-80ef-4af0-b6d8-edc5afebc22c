<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.EncOrDecLogDetailDao">

    <resultMap type="com.tipray.dlp.bean.EncOrDecLogDetail" id="EncOrDecLogDetailMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="mainGuid" column="guid"/>
        <result property="opResult" column="op_result"/>
        <result property="fileName" column="file_path"/>
        <result property="createTime" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>
    <sql id="sql_where">
        <where>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="mainGuid != null">
                and guid = #{mainGuid}
            </if>
            <if test="mainGuids != null and mainGuids != ''">
                and guid in (${mainGuids})
            </if>
            <if test="opResult != null and opResult != ''">
                and op_result = #{opResult}
            </if>
            <if test="opResults != null and opResults != ''">
                and op_result in (${opResults})
            </if>
            <if test="fileName != null and fileName != ''">
                and file_path like CONCAT('%',#{fileName},'%')
            </if>
        </where>
    </sql>
    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.EncOrDecLogVO" resultType="java.lang.Long">
      select count(0) from enc_or_dec_log_detail
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.EncOrDecLogVO" resultMap="EncOrDecLogDetailMap">
      select id, term_id, user_id, guid, op_result, file_path, create_time, term_group_id, user_group_id from enc_or_dec_log_detail
        <include refid="sql_where"></include>
    </select>

    <delete id="deleteByIds">
        delete from enc_or_dec_log_detail where id in (${value})
    </delete>

</mapper>
