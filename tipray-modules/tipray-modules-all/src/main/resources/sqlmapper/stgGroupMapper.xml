<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.StrategyGroupDao">

    <resultMap type="com.tipray.dlp.bean.StgGroup" id="StgGroupMap">
        <result property="id" column="id"/>
        <result property="active" column="active"/>
        <result property="name" column="name"/>
        <result property="remark" column="remark"/>
        <result property="groupType" column="group_type"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="objectType" column="object_type"/>
        <result property="info" column="info"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <if test="searchInfo != null and searchInfo != ''">
                name like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="groupType != null">
                and group_type = #{groupType}
            </if>
            <if test="active != null">
                and active = #{active}
            </if>
        </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultMap="StgGroupMap">
      select id, active, name, remark, info, object_type, group_type, modify_time from stg_group
      <include refid="vo_where_sql"/>
    </select>

</mapper>
