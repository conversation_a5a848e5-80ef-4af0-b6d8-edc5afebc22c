<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SoftLimitLogDao">

    <resultMap type="com.tipray.dlp.bean.SoftLimitLog" id="SoftLimitLogMap">
        <result property="id" column="id"/>
        <result property="recordNum" column="record_num"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="reportType" column="report_type"/>
        <result property="wholeMd5" column="whole_md5"/>
        <result property="quickMd5" column="quick_md5"/>
        <result property="processName" column="process_name"/>
        <result property="productName" column="product_name"/>
        <result property="originalFileName" column="original_file_name"/>
        <result property="companyName" column="company_name"/>
        <result property="internalName" column="internal_name"/>
        <result property="fileDesc" column="file_desc"/>
        <result property="legalCopyRight" column="legal_copy_right"/>
        <result property="productVer" column="product_ver"/>
        <result property="fileVer" column="file_ver"/>
        <result property="fileSign" column="file_sign"/>
        <result property="legalTradeMarks" column="legal_trade_marks"/>
        <result property="comments" column="comments"/>
        <result property="privateBuild" column="private_build"/>
        <result property="specialBuild" column="special_build"/>
        <result property="createTime" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="osType" column="os_type"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="softType != null and softType != ''">
                and report_type = #{softType}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from install_pack_report
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="SoftLimitLogMap">
        select id, user_id, term_id, report_type, whole_md5, quick_md5, process_name, product_name, original_file_name, company_name, internal_name, file_desc, legal_copy_right, product_ver, file_ver,
        file_sign, legal_trade_marks, comments, private_build, special_build, create_time, term_group_id, user_group_id, os_type
        from install_pack_report
        <include refid="sql_where"></include>
    </select>

</mapper>
