<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SysBrowserDao">

    <resultMap type="com.tipray.dlp.bean.SysBrowser" id="resultMap">
        <result property="id" column="id"/>
        <result property="name" column="browser_desc"/>
        <result property="nameKey" column="browser_desc_key"/>
        <result property="processName" column="browser_process"/>
        <result property="controlCode" column="control_code"/>
    </resultMap>

    <select id="countByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultType="java.lang.Long">
        select count(0) from sys_browser where control_code = 1
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultMap="resultMap">
        select * from sys_browser where control_code = 1
    </select>

</mapper>
