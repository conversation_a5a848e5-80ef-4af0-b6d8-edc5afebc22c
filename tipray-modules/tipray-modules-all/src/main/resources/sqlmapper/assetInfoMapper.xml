<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.AssetInfoDao">

    <resultMap type="com.tipray.dlp.bean.AssetInfo" id="AssetInfoMap">
        <result property="id" column="id"/>
        <result property="propId" column="prop_id"/>
        <result property="flag" column="flag"/>
        <result property="recordNo" column="record_no"/>
        <result property="value" column="value"/>
        <result property="terminalId" column="term_id"/>
        <result property="assetId" column="asset_id"/>
    </resultMap>

    <resultMap type="com.tipray.dlp.bean.SoftwareInfo" id="SoftwareInfoMap">
        <result property="id" column="id"/>
        <result property="softwareName" column="software_name"/>
        <result property="softwareNames" column="software_names"/>
        <result property="softwareVersion" column="software_version"/>
        <result property="publisher" column="publisher"/>
        <result property="path" column="path"/>
        <result property="installedTime" column="installed_time"/>
        <result property="lastUsedTime" column="last_used_time"/>
        <result property="terminalId" column="term_id"/>
        <result property="terminalName" column="terminal_name"/>
        <result property="terminalIds" column="term_ids"/>
        <result property="softSize" column="soft_size"/>
    </resultMap>

    <sql id="sql_where_hard_name">
        <include refid="commonSQL.andObjectTypeAndObjectId"/>
        <if test="terminalId != null">
            and term_id = #{terminalId}
        </if>
        <if test="terminalIds != null and terminalIds != ''">
            and term_id in (@terminalIds)
        </if>
        <if test="exceptTermIds != null and exceptTermIds != ''">
            and term_id not in (@exceptTermIds)
        </if>
        <if test="propIds != null and propIds != ''">
            and prop_id in (@propIds)
        </if>
        <if test="propId != null and propId != ''">
            and prop_id = #{propId}
        </if>
        <if test="searchInfo != null and searchInfo != ''">
            and value like CONCAT('%',#{searchInfo},'%')
        </if>
        <if test="conditionList != null and conditionList.size > 0">
            and (
            <foreach collection="conditionList" item="condition" index="i">
                <if test="i>0"> ${mark} </if>
                prop_id = #{condition.propId}
                <if test="condition.value != null and condition.value != ''">
                    and value like CONCAT('%',#{condition.value},'%')
                </if>
                <if test="condition.value == null or condition.value == ''">
                    and value = ''
                </if>
            </foreach>
            )
        </if>
    </sql>

    <select id="listHardAssetInfoGroupByTerm" resultType="java.lang.Long">
        SELECT term_id FROM hardasset_info
        <where>
            <include refid="sql_where_hard_name"/>
        </where>
        GROUP BY term_id
    </select>

    <select id="listHardAssetInfo" resultMap="AssetInfoMap">
        select id, term_id, asset_id, prop_id, flag, record_no, value from hardasset_info
        <where>
            <include refid="sql_where_hard_name"/>
        </where>
    </select>

    <select id="countHardAssetInfo" resultType="java.lang.Long">
        select count(0) from hardasset_info
        <where>
            <include refid="sql_where_hard_name"/>
        </where>
    </select>

    <select id="getAssetInfoDetail" resultMap="AssetInfoMap">
        select a.prop_id,a.value,a.flag,a.record_no,a.term_id,a.asset_id
        from
        <if test='type == 1'>
            hardasset_info
        </if>
        <if test='type == 2'>
            softasset_info
        </if>
        a
        where 1=1
        <if test='recordNo != null'>
            and a.record_no = #{recordNo}
        </if>
        <if test='propIds != null and propIds != ""'>
            and a.prop_id in (@propIds)
        </if>
        <if test='terminalId != null'>
            and a.term_id = #{terminalId}
        </if>
        <if test='assetId != null'>
            and a.asset_id = #{assetId}
        </if>
        order by a.record_no, a.prop_id
    </select>

    <select id="getDetailSoft" resultType="com.tipray.dlp.bean.AssetInfo">
        SELECT a.record_no recordNo, a.asset_id assetId, a.value, a.prop_id propId
        FROM
        <if test='type == 1'>
            hardasset_info
        </if>
        <if test='type == 2'>
            softasset_info
        </if>
        a
        WHERE 1=1
        <if test="ids != null and ids != ''">
            and a.prop_id in (@ids)
        </if>
        and a.term_id = #{terminalId}
    </select>

    <select id="listSoftware" resultType="map">
        SELECT term_id terminalId, asset_id assetId
        <foreach collection="propIdList" item="propId">
            ,MAX(CASE prop_id WHEN ${propId} THEN value END) `AssetInfo${propId}`
        </foreach>
        FROM softasset_info
        WHERE prop_id IN (@propIds)
        <if test="objectId != null">
            and term_id = #{objectId}
        </if>
        <if test="objectIds != null and objectIds != ''">
            and term_id in (@objectIds)
        </if>
        <if test="terminalIds != null and terminalIds != ''">
            and term_id in (@terminalIds)
        </if>
        GROUP BY term_id, asset_id
    </select>

    <select id="listOperatingSystem" parameterType="com.tipray.dlp.bean.vo.SoftWareVO"  resultType="com.tipray.dlp.bean.SoftwareInfo">
        SELECT term_id terminalId, asset_id assetId
        ,MAX(CASE WHEN prop_id = 40101 THEN value END) summary
        ,MAX(CASE WHEN prop_id = 40102 THEN value END) softwareName
        ,MAX(CASE WHEN prop_id = 40103 THEN value END) softwareVersion
        ,MAX(CASE WHEN prop_id = 40104 THEN value END) serialNumber
        ,MAX(CASE WHEN prop_id = 40105 THEN value END) path
        ,MAX(CASE WHEN prop_id = 40106 THEN value END) installedTime
        ,MAX(CASE WHEN prop_id = 40107 THEN value END) lastUsedTime
        ,MAX(CASE WHEN prop_id = 40108 THEN value END) servicePatch
        ,MAX(CASE WHEN prop_id = 40109 THEN value END) registeredUser
        ,MAX(CASE WHEN prop_id = 40110 THEN value END) publisher
        ,MAX(CASE WHEN prop_id = 40111 THEN value END) active
        ,MAX(CASE WHEN prop_id = 40112 THEN value END) computerType
        FROM softasset_info
        WHERE prop_id IN (@propIds)
        <if test="exceptTermIds != null and exceptTermIds != ''">
            and term_id not in (@exceptTermIds)
        </if>
        GROUP BY term_id, asset_id
    </select>

    <select id="listAntivirusSoftware" parameterType="com.tipray.dlp.bean.vo.SoftWareVO"  resultType="com.tipray.dlp.bean.SoftwareInfo">
        SELECT term_id terminalId, asset_id assetId
        ,MAX(CASE WHEN prop_id = 40201 THEN value END) softwareName
        ,MAX(CASE WHEN prop_id = 40202 THEN value END) publisher
        ,MAX(CASE WHEN prop_id = 40203 THEN value END) softwareVersion
        FROM softasset_info
        WHERE prop_id IN (@propIds)
        <if test="exceptTermIds != null and exceptTermIds != ''">
            and term_id not in (@exceptTermIds)
        </if>
        GROUP BY term_id, asset_id
    </select>

    <select id="listSoftwareInfo" parameterType="com.tipray.dlp.bean.vo.SoftWareVO"  resultType="com.tipray.dlp.bean.SoftwareInfo">
        SELECT term_id terminalId, asset_id assetId
        ,MAX(CASE WHEN prop_id = 40401 THEN value END) softwareName
        ,MAX(CASE WHEN prop_id = 40402 THEN value END) softwareVersion
        ,MAX(CASE WHEN prop_id = 40403 THEN value END) path
        ,MAX(CASE WHEN prop_id = 40404 THEN value END) installedTime
        ,MAX(CASE WHEN prop_id = 40405 THEN value END) publisher
        ,MAX(CASE WHEN prop_id = 40406 THEN value END) lastUsedTime
        FROM softasset_info
        WHERE prop_id IN (@propIds)
        <if test="exceptTermIds != null and exceptTermIds != ''">
            and term_id not in (@exceptTermIds)
        </if>
        GROUP BY term_id, asset_id
    </select>

    <sql id="sql_where_soft_name">
        <include refid="commonSQL.andObjectTypeAndObjectId"/>
        <if test="searchInfo != null and searchInfo != ''">
            and LOWER(value) like LOWER(CONCAT('%',#{searchInfo},'%'))
        </if>
        <if test="assetIds != null and assetIds != ''">
            and asset_id in (@assetIds)
        </if>
        <if test="propId != null">
            and prop_id = #{propId}
        </if>
        <if test="propIds != null and propIds != ''">
            and prop_id in (@propIds)
        </if>
        <if test="terminalId != null">
            and term_id = #{terminalId}
        </if>
        <if test="terminalIds != null and terminalIds != ''">
            and term_id in (@terminalIds)
        </if>
        <if test="exceptTermIds != null and exceptTermIds != ''">
            and term_id not in (@exceptTermIds)
        </if>
        <if test="softwareName != null and softwareName != ''">
            and value = #{softwareName}
        </if>
        <if test="softwareNames != null and softwareNames != ''">
            and value in (@softwareNames)
        </if>
        <if test="exceptSoftNames != null and exceptSoftNames != ''">
            and value not in (@exceptSoftNames)
        </if>
    </sql>

    <select id="selectByTerm" resultType="com.tipray.dlp.bean.SoftwareInfo">
        select asset_id id, term_id terminalId, value softwareName from softasset_info
        <where>
            prop_id = 40401
            <include refid="sql_where_soft_name"/>
        </where>
    </select>
    <select id="countByTerm" resultType="java.lang.Long">
        select count(0) from softasset_info
        <where>
            <include refid="sql_where_soft_name"/>
        </where>
    </select>
    <select id="listAssetInfoBySoftInfo" resultType="com.tipray.dlp.bean.SoftAssetInfo">
        SELECT a.term_id terminalId,a.asset_id assetId,CONCAT('{#',a.prop_id, ':', a.value, '#}') value from softasset_info a
        <where>
            <foreach collection="list" item="item" open="(" close=")" separator=" or "> term_id = ${item.terminalId} and asset_id = ${item.id} </foreach>
        </where>
    </select>

    <select id="listAssetInfoByVo" parameterType="com.tipray.dlp.bean.vo.SoftWareVO" resultType="com.tipray.dlp.bean.SoftAssetInfo">
        SELECT a.term_id terminalId,a.asset_id assetId,CONCAT('{#',a.prop_id, ':', a.value, '#}') value from softasset_info a,
        (SELECT term_id, asset_id FROM softasset_info
        <where>
            prop_id = 40401
            <include refid="sql_where_soft_name"/>
        </where>
        ) b
        where a.term_id = b.term_id and a.asset_id = b.asset_id
    </select>

    <select id="listAssetInfoByObject" parameterType="com.tipray.dlp.bean.vo.SoftWareVO" resultType="com.tipray.dlp.bean.SoftAssetInfo">
        SELECT a.term_id terminalId,a.asset_id assetId,CONCAT('{#',a.prop_id, ':', a.value, '#}') value from softasset_info a
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="terminalId != null">
                and term_id = #{terminalId}
            </if>
            <if test="terminalIds != null and terminalIds != ''">
                and term_id in (@terminalIds)
            </if>
            <if test="exceptTermIds != null and exceptTermIds != ''">
                and term_id not in (@exceptTermIds)
            </if>
        </where>
    </select>

    <select id="statisticBySoftName" resultType="com.tipray.dlp.bean.SoftwareStatistics">
        SELECT value softwareName, term_id terminalId FROM softasset_info
        <where>
            prop_id = 40401
            <include refid="sql_where_soft_name"/>
        </where>
    </select>

    <select id="listSoftNameCount" resultType="com.tipray.dlp.bean.SoftwareStatistics">
        SELECT value softwareName, count(1) installSize FROM softasset_info
        <where>
            prop_id = 40401
            <include refid="sql_where_soft_name"/>
        </where>
        group BY softwareName
        order by installSize desc
    </select>

    <select id="listTerminalCount" resultType="com.tipray.dlp.bean.SoftwareStatistics">
        SELECT term_id terminalId, count(1) installSize FROM softasset_info
        <where>
            prop_id = 40401
            <include refid="sql_where_soft_name"/>
        </where>
        group BY terminalId
    </select>

    <select id="pageSoftName" resultType="java.lang.String">
        select distinct value from softasset_info
        <where>
            prop_id = 40401
            <include refid="sql_where_soft_name"/>
        </where>
    </select>

    <select id="countSoftName" resultType="java.lang.String">
        select distinct value from softasset_info
        <where>
            prop_id = 40401
            <include refid="sql_where_soft_name"></include>
        </where>
    </select>
    <select id="selectTermId" resultType="java.lang.Long">
        SELECT distinct term_id FROM softasset_info
        <where>
            <include refid="sql_where_soft_name"></include>
        </where>
    </select>

    <select id="listAssetInfoByAssetIds" resultType="com.tipray.dlp.bean.SoftAssetInfo">
        SELECT term_id terminalId,asset_id assetId,prop_id propId, value FROM softasset_info
        <where>
            term_id in (@terminalIds) and asset_id in (@assetIds)
        </where>
    </select>
    <select id="listAssetVersionInfoByAssetIds" resultType="com.tipray.dlp.bean.SoftAssetInfo">
        SELECT term_id terminalId,asset_id assetId,prop_id propId, value FROM softasset_info
        <where>
            prop_id = 40402 and term_id in (@terminalIds) and asset_id in (@assetIds)
        </where>
    </select>

    <delete id="deleteByTermIds">
        delete from softasset_info where term_id in (${value})
    </delete>

    <select id="listChangeSoftwareInfo" parameterType="java.util.List" resultType="com.tipray.dlp.bean.SoftwareInfo">
        SELECT term_id terminalId, asset_id assetId
             ,MAX(CASE prop_id WHEN 40401 THEN value END) softwareName
             ,MAX(CASE prop_id WHEN 40402 THEN value END) softwareVersion
             ,MAX(CASE prop_id WHEN 40403 THEN value END) path
             ,MAX(CASE prop_id WHEN 40404 THEN value END) installedTime
             ,MAX(CASE prop_id WHEN 40405 THEN value END) publisher
             ,MAX(CASE prop_id WHEN 40406 THEN value END) lastUsedTime
        FROM softasset_info
        where asset_id in (@assetIds)
        group by asset_id, term_id
    </select>
</mapper>
