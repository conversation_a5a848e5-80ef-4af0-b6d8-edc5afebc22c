<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.CommonGroupDao">

    <resultMap type="com.tipray.dlp.bean.BaseGroup" id="resultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="parentId" column="parent_id"/>
        <result property="osType" column="os_type"/>
    </resultMap>

    <sql id="os_type_where">
        <if test="isDifficult and osType != null">
            and os_type = #{osType}
        </if>
    </sql>

    <!--查询单个-->
    <select id="getById" resultMap="resultMap">
        select * from ${tableName} where id = #{id}
    </select>
    <select id="getByParentIdAndName" resultMap="resultMap">
        select * from ${tableName} where parent_id = #{parentId} and name = #{name}
        <include refid="os_type_where"></include>
    </select>

    <select id="listByName" resultMap="resultMap">
        select * from ${tableName} where name = #{name}
        <include refid="os_type_where"></include>
    </select>

    <select id="listIdByName" resultType="java.lang.Long">
        select id from ${tableName} where name = #{name}
        <include refid="os_type_where"></include>
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="list" resultMap="resultMap">
        select * from ${tableName}
        <where>
            <include refid="os_type_where"></include>
        </where>
    </select>

    <select id="listByIds" resultMap="resultMap">
        select * from ${tableName} where id in (${ids})
    </select>

    <select id="countChildByGroupId" resultType="java.lang.Long">
        select count(0) from ${tableName} where parent_id = #{id}
    </select>

    <select id="listByParentIdAndNames" resultType="com.tipray.dlp.bean.BaseGroup">
        select * from ${tableName}
        <where>
            <foreach collection="list" index="index" item="item" separator=" or " open="(" close=")">
                name = #{item.name} and parent_id = #{item.parentId}
            </foreach>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into ${tableName}(name, parent_id, create_time, modify_time
        <if test="isDifficult"> , os_type </if>
        )
        values (#{name}, #{parentId}, #{createTime}, #{modifyTime}
        <if test="isDifficult"> , #{osType} </if>
        )
    </insert>

    <!--通过主键修改数据-->
    <update id="updateById">
        update ${tableName}
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            modify_time = #{modifyTime}
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from ${tableName} where id = #{id}
    </delete>

    <delete id="deleteByIds">
        delete from ${tableName} where id in (${ids})
    </delete>

    <select id="listByNameList" resultMap="resultMap">
        select * from ${tableName}
        where name in (@beanList.name)
    </select>

    <insert id="batchInsert"  useGeneratedKeys="true" keyProperty="addList.id" >
        insert into ${tableName} (name, parent_id, create_time, modify_time
            <if test="addList[0].isDifficult"> , os_type </if>
        )
        values
        <foreach collection="addList" item="item" separator=",">
            (
            #{item.name}, #{item.parentId}, #{item.createTime}, #{item.modifyTime}
            <if test="item.isDifficult"> , #{item.osType} </if>
            )
        </foreach>
    </insert>

    <update id="batchUpdate">
        update ${tableName}
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="name = case" suffix="end,">
                <foreach collection="list" item="item" index="index"> when id = #{item.id} then #{item.name} </foreach>
            </trim>
            <trim prefix="parent_id = case" suffix="end,">
                <foreach collection="list" item="item" index="index"> when id = #{item.id} then #{item.parentId} </foreach>
            </trim>
            <if test="list[0].isDifficult">
                <trim prefix="os_type = case" suffix="end,">
                    <foreach collection="list" item="item" index="index"> when id = #{item.id} then #{item.osType} </foreach>
                </trim>
            </if>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")"> #{item.id} </foreach>
    </update>

</mapper>
