<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.MailServerDao">

    <resultMap type="com.tipray.dlp.bean.MailServer" id="resultMap">
        <result property="id" column="id"/>
        <result property="devId" column="dev_id"/>
        <result property="account" column="account"/>
        <result property="password" column="password"/>
        <result property="senderName" column="sender_name"/>
        <result property="ssl" column="use_ssl"/>
        <result property="cc" column="cc"/>
        <result property="bcc" column="bcc"/>
        <result property="name" column="name"/>
        <result property="internetIp" column="internet_ip"/>
        <result property="internetPort" column="internet_port"/>
    </resultMap>
    <sql id="select_field">
        b.id,b.name, b.internet_ip, b.internet_port, b.dev_id, b.remark, m.account, m.password, m.sender_name, m.use_ssl, m.cc, m.bcc
    </sql>

    <!--查询单个-->
    <select id="getByDevId" resultMap="resultMap">
        select <include refid="select_field"/>
        from dev_server_ext_mail m, dev_server b where m.dev_id = b.dev_id and b.dev_id = #{value}
    </select>

    <select id="getById" resultMap="resultMap">
        select <include refid="select_field"/>
        from dev_server_ext_mail m, dev_server b where m.dev_id = b.dev_id and b.id = #{value}
    </select>

    <sql id="select_where_vo">
        <where>
            m.dev_id = b.dev_id
            <if test="searchInfo != null and searchInfo != ''">
                and (b.name like CONCAT('%',#{searchInfo},'%'))
            </if>
            <if test="name != null and name != ''">
                and b.name like CONCAT('%',#{name},'%')
            </if>
            <if test="internetIp != null and internetIp != ''">
                and b.internet_ip like CONCAT('%',#{internetIp},'%')
            </if>
            <if test="internetPort != null">
                and b.internet_port like CONCAT('%',#{internetPort},'%')
            </if>
            <if test="ssl != null">
                and m.use_ssl like CONCAT('%',#{ssl},'%')
            </if>
            <if test="senderName != null and senderName != ''">
                and m.sender_name like CONCAT('%',#{senderName},'%')
            </if>
            <if test="cc != null and cc != ''">
                and m.cc like CONCAT('%',#{cc},'%')
            </if>
            <include refid="commonSQL.andNotDel"></include>
        </where>
    </sql>
    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.ServerVO" resultType="java.lang.Long">
        select count(0) from dev_server_ext_mail m, dev_server b
        <include refid="select_where_vo"/>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.ServerVO" resultMap="resultMap">
        select <include refid="select_field"/>
        from dev_server_ext_mail m, dev_server b
        <include refid="select_where_vo"/>
    </select>

</mapper>
