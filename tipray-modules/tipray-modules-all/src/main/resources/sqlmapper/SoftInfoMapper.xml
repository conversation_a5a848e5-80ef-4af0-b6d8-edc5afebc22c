<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SoftInfoDao">

    <select id="countClassNum" resultType="map">
        select  class_id classId,count(1) num from software_info_table
        group by class_id
    </select>

    <sql id="vo_where_sql">
        <where>
            <if test="classId != null and (noFilterType == 0 or noFilterType == null)">
                and class_id = #{classId}
            </if>
            <if test="classIds != null and classIds != '' and (noFilterType == 0 or noFilterType == null)">
                and class_id in (${classIds})
            </if>
            <if test="processName != null and processName != ''">
                and LOWER(process_name) like LOWER(CONCAT('%',#{processName},'%'))
            </if>
            <if test="productName != null and productName != ''">
                and LOWER(product_name) like LOWER(CONCAT('%',#{productName},'%'))
            </if>
            <if test="companyName != null and companyName != ''">
                and LOWER(company_name) like LOWER(CONCAT('%',#{companyName},'%'))
            </if>
            <if test="productVersion != null and productVersion != ''">
                and LOWER(product_version) like LOWER(CONCAT('%',#{productVersion},'%'))
            </if>
            <if test="softSign != null and softSign != ''">
                and LOWER(soft_sign) like LOWER(CONCAT('%',#{softSign},'%'))
            </if>
            <if test="fileMd5 != null and fileMd5 != ''">
                and file_md5 = #{fileMd5}
            </if>
            <if test="ids != null and ids != ''">
                and t1.id in (${ids})
            </if>
            <if test="filterName != null and filterName != ''">
                and t1.process_name = #{filterName}
            </if>
        </where>
    </sql>

    <select id="listByVO" resultType="com.tipray.dlp.bean.SoftwareInfotable">
        select
        t1.id,t1.process_name processName,t1.product_version productVersion,t1.original_filename originalFilename,
        t1.file_desc fileDescription,t1.company_name companyName,t1.legal_copyright legalCopyright,
        t1.file_md5 fileMd5,t1.class_id classId,t1.product_name productName,quickly_md5 quicklyMd5,internal_name internalName,soft_sign softSign,t2.name
        from software_info_table t1
        left join software_class t2 on t1.class_id = t2.id
        <include refid="vo_where_sql"/>
    </select>
    <select id="countByVO" resultType="Long">
        select count(id) from software_info_table t1
        <include refid="vo_where_sql"/>
    </select>


</mapper>
