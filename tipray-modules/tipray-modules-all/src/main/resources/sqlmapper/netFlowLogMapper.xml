<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.NetFlowLogDao">

    <resultMap type="com.tipray.dlp.bean.NetFlowLog" id="resultMap">
        <result property="id" column="id"/>
        <result property="userId" column="userId"/>
        <result property="terminalId" column="term_id"/>
        <result property="totalFlowNum" column="total_flow_num"/>
        <result property="receiveFlowNum" column="receive_flow_num"/>
        <result property="sendFlowNum" column="send_flow_num"/>
        <result property="createTime" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="guid" column="guid"/>
    </resultMap>

    <select id="countByVO" resultType="java.lang.Long">
        select count(*) from net_flow_log
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="guid != null and guid != ''">
                and guid = #{guid}
            </if>
        </where>
    </select>

    <select id="listByVO" resultType="com.tipray.dlp.bean.NetFlowLog">
        select id, term_id, user_id, total_flow_num, receive_flow_num, send_flow_num, create_time, term_group_id, user_group_id, guid
        from net_flow_log
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="guid != null and guid != ''">
                and guid = #{guid}
            </if>
        </where>
    </select>

</mapper>
