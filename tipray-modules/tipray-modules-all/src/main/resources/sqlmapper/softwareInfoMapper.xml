<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SoftwareInfoDao">

    <resultMap type="com.tipray.dlp.bean.SoftwareInfo" id="resultMap">
        <result property="id" column="id"/>
        <result property="softwareName" column="software_name"/>
        <result property="originalName" column="original_name"/>
        <result property="softwareVersion" column="software_version"/>
        <result property="publisher" column="publisher"/>
        <result property="path" column="path"/>
        <result property="installedTime" column="installed_time"/>
        <result property="lastUsedTime" column="last_used_time"/>
        <result property="terminalId" column="term_id"/>
        <result property="assetId" column="asset_id"/>
        <result property="active" column="active"/>
        <result property="computerType" column="computer_type"/>
        <result property="registeredUser" column="registered_user"/>
        <result property="servicePatch" column="service_patch"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="summary" column="summary"/>
        <result property="assetType" column="asset_type"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <if test="assetType != null">
                and asset_type = #{assetType}
            </if>
            <if test="exceptAssetTypes != null and exceptAssetTypes != ''">
                and asset_type not in (${exceptAssetTypes})
            </if>
            <if test="terminalId != null">
                and term_id = #{terminalId}
            </if>
            <if test="terminalIds != null and terminalIds != ''">
                and term_id in (${terminalIds})
            </if>
            <if test="objectId != null">
                and term_id = #{objectId}
            </if>
            <if test="objectIds != null and objectIds != ''">
                and term_id in (${objectIds})
            </if>
            <if test="exceptTermIds != null and exceptTermIds != ''">
                and term_id not in (${exceptTermIds})
            </if>
            <if test="assetIds != null and assetIds != ''">
                and asset_id in (${assetIds})
            </if>
            <if test="softwareName != null and softwareName != ''">
                and software_name = #{softwareName}
            </if>
            <if test="originalNames != null and originalNames != ''">
                and original_name in (${originalNames})
            </if>
            <if test="originalName != null and originalName != ''">
                and original_name = #{originalName}
            </if>
            <if test="softNames != null and softNames != ''">
                and software_name in (${softNames})
            </if>
            <if test="softwareNames != null and softwareNames != ''">
                and (original_name in (${softwareNames}) or software_name in (${softwareNames}))
            </if>
            <if test="exceptAuthNames != null and exceptAuthNames != ''">
                and CONCAT(original_name, term_id) not in (${exceptAuthNames})
            </if>
            <if test="softwareNameSearch != null and softwareNameSearch != ''">
                and software_name like CONCAT('%',#{softwareNameSearch},'%')
            </if>
            <if test="originalNameSearch != null and originalNameSearch != ''">
                and original_name like CONCAT('%',#{originalNameSearch},'%')
            </if>
            <if test="softwareVersion != null and softwareVersion != ''">
                and software_version = #{softwareVersion}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and (original_name like CONCAT('%',#{searchInfo},'%') or software_name like CONCAT('%',#{searchInfo},'%'))
            </if>
        </where>
    </sql>

    <select id="getById" resultMap="resultMap">
        select id, software_name, original_name, software_version, publisher, path, installed_time, last_used_time,
               term_id, asset_id, summary, serial_number, service_patch, registered_user, computer_type, active, asset_type
        from software_info
        where id = #{id}
    </select>

    <delete id="deleteByModifyVer">
        delete from software_info where modify_ver &lt; #{value}
    </delete>
    <delete id="deleteSoftwareInfoByIds">
        delete from software_info where id in (${ids})
    </delete>

    <select id="getMaxModifyVer" resultType="java.lang.Long">
        select max(modify_ver) from software_info
    </select>

    <update id="updateSoftwareName">
        update software_info set software_name = #{softwareName}
        where original_name in (${originalNames})
    </update>

    <select id="listSoftwareNameInfo" parameterType="com.tipray.dlp.bean.vo.SoftWareVO" resultMap="resultMap">
        select software_name, original_name from software_info
        <include refid="sql_where"></include>
    </select>

    <select id="listSoftwareInfoVer" parameterType="com.tipray.dlp.bean.vo.SoftWareVO" resultType="com.tipray.dlp.bean.SoftwareInfo">
        select id, software_name softwareName, group_concat(software_version) softwareVers, count(0) softSize from software_info
        <include refid="sql_where"></include>
        group by software_name
    </select>
    <select id="listSoftwareInfo" parameterType="com.tipray.dlp.bean.vo.SoftWareVO" resultMap="resultMap">
        select id, software_name, original_name, term_id, software_version, publisher, path, installed_time, last_used_time,
               summary, serial_number, registered_user, computer_type, active, asset_type from software_info
        <include refid="sql_where"></include>
    </select>

    <select id="countSoftwareInfo" parameterType="com.tipray.dlp.bean.vo.SoftWareVO" resultType="java.lang.Long">
        select count(0) from software_info
        <include refid="sql_where"></include>
    </select>

    <select id="listTerminalIds" parameterType="com.tipray.dlp.bean.vo.SoftWareVO" resultType="java.lang.Long">
        select distinct term_id from software_info
        <include refid="sql_where"></include>
    </select>

    <select id="listSimpleSoftwareInfo" parameterType="com.tipray.dlp.bean.vo.SoftWareVO" resultType="com.tipray.dlp.bean.SoftwareInfo">
        select id, software_name softwareName, publisher, group_concat(distinct original_name) originalNames, group_concat(distinct term_id) terminalIds, count(0) softSize, asset_type assetType from software_info
        <include refid="sql_where"></include>
        group by software_name
    </select>

    <select id="listGroupBySoftwareName" parameterType="com.tipray.dlp.bean.vo.SoftWareVO" resultType="com.tipray.dlp.bean.SoftwareInfo">
        select id, software_name softwareName, group_concat(distinct software_version) softwareVersion, group_concat(distinct original_name) originalNames, group_concat(term_id) terminalIds, count(0) softSize from software_info
        <include refid="sql_where"></include>
        group by software_name
    </select>

    <select id="listGroupByOriginalName" parameterType="com.tipray.dlp.bean.vo.SoftWareVO" resultType="com.tipray.dlp.bean.SoftwareInfo">
        select id, original_name originalName, software_name softwareName, group_concat(distinct software_version) softwareVersion, group_concat(term_id) terminalIds, count(0) softSize from software_info
        <include refid="sql_where"></include>
        group by original_name
    </select>

    <select id="listSoftwareInfoVersion" parameterType="com.tipray.dlp.bean.vo.SoftWareVO" resultMap="resultMap">
        select id, original_name, software_version, term_id from software_info
        <include refid="sql_where"></include>
        group by original_name, term_id, asset_id
    </select>

    <select id="listDistinctSoftwareName" parameterType="com.tipray.dlp.bean.vo.SoftWareVO" resultType="java.lang.String">
        select distinct software_name from software_info
        <include refid="sql_where"></include>
    </select>
    <select id="countDistinctSoftwareName" parameterType="com.tipray.dlp.bean.vo.SoftWareVO" resultType="java.lang.Long">
        select count(distinct software_name) from software_info
        <include refid="sql_where"></include>
    </select>

    <select id="countDistinctOriginalName" parameterType="com.tipray.dlp.bean.vo.SoftWareVO" resultType="java.lang.Long">
        select count(distinct original_name) from software_info
        <include refid="sql_where"></include>
    </select>

    <select id="listOriginalName" parameterType="com.tipray.dlp.bean.vo.SoftWareVO" resultType="java.lang.String">
        select distinct original_name from software_info
        <include refid="sql_where"></include>
    </select>

    <select id="getSoftwareNameByOriginalName" parameterType="java.lang.String" resultType="java.lang.String">
        select distinct software_name from software_info where original_name = #{value}
    </select>

    <select id="listOriginalSoftwareInfo" parameterType="com.tipray.dlp.bean.vo.SoftWareVO" resultMap="resultMap">
        select id, term_id, original_name, asset_id from software_info
        <where>
            <if test="terminalIds != null and terminalIds != ''">
                and term_id in (${terminalIds})
            </if>
            <if test="assetIds != null and assetIds != ''">
                and asset_id in (${assetIds})
            </if>
            <if test="softwareNames != null and softwareNames != ''">
                and original_name in (${softwareNames})
            </if>
            <if test="assetType != null">
                and asset_type = #{assetType}
            </if>
        </where>
    </select>

    <insert id="batchInsert"  useGeneratedKeys="true" keyProperty="id" >
        insert into software_info(software_name, original_name, software_version, publisher, path, installed_time, last_used_time, term_id, asset_id, modify_ver,
                                  summary, serial_number, service_patch, registered_user, active, computer_type, asset_type)
        values
        <foreach collection="list"  item="item" separator="," index="index">
            (#{item.softwareName}, #{item.originalName}, #{item.softwareVersion}, #{item.publisher}, #{item.path},#{item.installedTime},
             #{item.lastUsedTime}, #{item.terminalId}, #{item.assetId}, #{item.modifyVer},#{item.summary},#{item.serialNumber},
             #{item.servicePatch},#{item.registeredUser},#{item.active},#{item.computerType},#{item.assetType})
        </foreach>
    </insert>

    <update id="batchUpdate"  parameterType="java.util.List">
        update software_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="software_name = case" suffix="end,">
                <foreach collection="list" item="item" index="index"> when id = #{item.id} then #{item.softwareName} </foreach>
            </trim>
            <trim prefix="software_version = case" suffix="end,">
                <foreach collection="list" item="item" index="index"> when id = #{item.id} then #{item.softwareVersion} </foreach>
            </trim>
            <trim prefix="publisher = case" suffix="end,">
                <foreach collection="list" item="item" index="index"> when id = #{item.id} then #{item.publisher} </foreach>
            </trim>
            <trim prefix="path = case" suffix="end,">
                <foreach collection="list" item="item" index="index"> when id = #{item.id} then #{item.path} </foreach>
            </trim>
            <trim prefix="installed_time = case" suffix="end,">
                <foreach collection="list" item="item" index="index"> when id = #{item.id} then #{item.installedTime} </foreach>
            </trim>
            <trim prefix="last_used_time = case" suffix="end,">
                <foreach collection="list" item="item" index="index"> when id = #{item.id} then #{item.lastUsedTime} </foreach>
            </trim>
            <trim prefix="modify_ver = case" suffix="end,">
                <foreach collection="list" item="item" index="index"> when id = #{item.id} then #{item.modifyVer} </foreach>
            </trim>
            <trim prefix="summary = case" suffix="end,">
                <foreach collection="list" item="item" index="index"> when id = #{item.id} then #{item.summary} </foreach>
            </trim>
            <trim prefix="serial_number = case" suffix="end,">
                <foreach collection="list" item="item" index="index"> when id = #{item.id} then #{item.serialNumber} </foreach>
            </trim>
            <trim prefix="service_patch = case" suffix="end,">
                <foreach collection="list" item="item" index="index"> when id = #{item.id} then #{item.servicePatch} </foreach>
            </trim>
            <trim prefix="registered_user = case" suffix="end,">
                <foreach collection="list" item="item" index="index"> when id = #{item.id} then #{item.registeredUser} </foreach>
            </trim>
            <trim prefix="active = case" suffix="end,">
                <foreach collection="list" item="item" index="index"> when id = #{item.id} then #{item.active} </foreach>
            </trim>
            <trim prefix="computer_type = case" suffix="end,">
                <foreach collection="list" item="item" index="index"> when id = #{item.id} then #{item.computerType} </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")"> #{item.id} </foreach>
    </update>

    <delete id="deleteSoftwareInfo" parameterType="java.util.List">
        delete from software_info
        <where>
            <if test="conditionList != null and conditionList.size > 0">
                and (
                <foreach collection="conditionList" item="condition" index="i">
                    <if test="i>0"> ${mark} </if>
                    <if test="condition.terminalId != null">
                        and term_id = #{condition.terminalId}
                    </if>
                    <if test="condition.assetId == null">
                        and asset_id = #{condition.assetId}
                    </if>
                </foreach>
                )
            </if>
        </where>
    </delete>

    <delete id="deleteSoftwareInfoByTermIds">
        delete from software_info where term_id in (@termIds)
    </delete>

</mapper>