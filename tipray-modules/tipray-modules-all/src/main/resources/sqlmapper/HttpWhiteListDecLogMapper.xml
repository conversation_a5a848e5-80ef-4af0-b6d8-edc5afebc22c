<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.HttpWhiteListDecLogDao">

    <resultMap type="com.tipray.dlp.bean.HttpWhiteListDecLog" id="HttpWhiteListDecLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="fileName" column="file_name"/>
        <result property="fileSize" column="file_size"/>
        <result property="extendParam" column="extend_param"/>
        <result property="devId" column="backup_server_id"/>
        <result property="localFilePath" column="local_file_path"/>
        <result property="uploadFileGuid" column="upload_file_guid"/>
        <result property="webUrl" column="web_url"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name like CONCAT('%',#{fileName},'%')
            </if>
            <if test="operateType != null">
                and (<![CDATA[ (extend_param & 2) = ${operateType} ]]>)
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from http_white_list_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="HttpWhiteListDecLogMap">
        select id, record_num, user_id,term_id, file_name, file_size, extend_param, backup_server_id, upload_file_guid, web_url, local_file_path,
        create_time, term_group_id, user_group_id
        from http_white_list_log
        <include refid="sql_where"></include>
    </select>

</mapper>
