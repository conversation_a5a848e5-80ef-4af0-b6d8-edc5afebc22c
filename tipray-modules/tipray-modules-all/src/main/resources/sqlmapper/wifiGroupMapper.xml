<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.WifiGroupDao">

    <resultMap type="com.tipray.dlp.bean.WifiGroup" id="resultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="parentId" column="parent_id"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <include refid="commonSQL.andNotDel"></include>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="parentId != null">
                and group_id = #{groupId}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and (name like CONCAT('%', #{searchInfo}, '%') or mac_address like CONCAT('%', #{searchInfo}, '%'))
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids != ''">
                and id in (${ids})
            </if>
        </where>
    </sql>


</mapper>
