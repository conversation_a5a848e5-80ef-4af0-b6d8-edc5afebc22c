<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.TransferFileDao">

    <resultMap type="com.tipray.dlp.bean.TransferFile" id="TransferFileMap">
        <result property="id" column="id"/>
        <result property="fileName" column="file_name"/>
        <result property="filePath" column="file_path"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileMd5" column="file_md5"/>
        <result property="fileMemo" column="file_memo"/>
        <result property="fileType" column="file_type"/>
        <result property="installParam" column="install_param"/>
    </resultMap>

    <select id="countByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultType="java.lang.Long">
      select count(0) from transfer_file
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultMap="TransferFileMap">
      select id, file_name, file_path, file_size, file_md5, file_memo, file_type, install_param from transfer_file
    </select>

</mapper>
