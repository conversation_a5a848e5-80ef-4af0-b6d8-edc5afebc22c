<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ProcessStgConfigDao">

    <resultMap type="com.tipray.dlp.bean.ProcessStgConfig" id="ProcessStgConfigMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="processStgId" column="process_stg_id"/>
        <result property="processName" column="process_name"/>
        <result property="dir" column="dir"/>
        <result property="suffix" column="suffix"/>
        <result property="code" column="code"/>
    </resultMap>
    <sql id="vo_where_sql">
        <where>
            <if test="groupId != null">
                and process_stg_id = #{groupId}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and name like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="processName != null and processName != ''">
                and process_name = #{processName}
            </if>
            <if test="dir != null and dir != ''">
                and dir = #{dir}
            </if>
            <if test="suffix != null and suffix != ''">
                and suffix = #{suffix}
            </if>
            <if test="code != null">
                and code = #{code}
            </if>
            <include refid="commonSQL.andNotDel"></include>
        </where>
    </sql>

    <select id="getByName" resultMap="ProcessStgConfigMap">
        select id, name, process_stg_id, process_name, dir, suffix, code        from process_stg_config
        <where>
            name = #{value}
            <include refid="commonSQL.andNotDel"></include>
        </where>
        limit 1
    </select>

    <select id="getByProcessNames" resultMap="ProcessStgConfigMap">
        select id, name, process_stg_id, process_name, dir, suffix, code from process_stg_config
        <where>
            process_name in (@processNames)
            <include refid="commonSQL.andNotDel"></include>
        </where>
    </select>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.mybatis.bean.PageVO" resultMap="ProcessStgConfigMap">
      select id, name, process_stg_id, process_name, dir, suffix, code from process_stg_config
      <include refid="vo_where_sql"></include>
    </select>

    <update id="deleteByProcessIds">
        update process_stg_config set deleted=1, modify_time=#{modifyTime}
        where process_stg_id in (${ids})
    </update>

</mapper>
