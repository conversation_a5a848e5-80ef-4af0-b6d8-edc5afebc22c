<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ProcessStgLibDao">

    <resultMap type="com.tipray.dlp.bean.ProcessStgLib" id="ProcessStgMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="groupId" column="group_id"/>
        <result property="decReadSfx" column="dec_read_sfx"/>
        <result property="encWriteSfx" column="enc_write_sfx"/>
        <result property="encOpenSfx" column="enc_open_sfx"/>
        <result property="enablePast" column="enable_past"/>
        <result property="checkMd5" column="check_md5"/>
        <result property="disableNet" column="disable_net"/>
        <result column="process_name" property="processName"/>
        <result column="product_name" property="productName"/>
        <result column="product_version" property="productVersion"/>
        <result column="original_filename" property="originalFilename"/>
        <result column="file_desc" property="fileDescription"/>
        <result column="internal_name" property="internalName"/>
        <result column="legal_copyright" property="legalCopyright"/>
        <result column="company_name" property="companyName"/>
        <result column="file_md5" property="fileMd5"/>
        <result column="quickly_md5" property="quicklyMd5"/>
        <result column="property_md5" property="propertyMd5"/>
        <result column="property_mark" property="propertyMark"/>
        <result column="soft_sign" property="softSign"/>
        <result column="ori_id" property="oriId"/>
        <result column="os_type" property="osType"/>
    </resultMap>
    <sql id="vo_where_sql">
        <if test="groupIds != null and groupIds != ''">
            and group_id in (${groupIds})
        </if>
        <if test="searchInfo != null and searchInfo != ''">
            and (name like CONCAT('%',#{searchInfo},'%') or process_name like CONCAT('%',#{searchInfo},'%'))
        </if>
        <if test="ids != null and ids != ''">
            and id in (${ids})
        </if>
        <if test="osType != null">
            and os_type = #{osType}
        </if>
    </sql>
    <sql id="processNameFromGroup_vo_where_sql">
        <if test="osType != null">
            and os_type = #{osType}
        </if>
        <if test="groupIds != null and groupIds != '' or ids != null and ids != ''">
            and (
                <if test="groupIds != null and groupIds != ''">
                    group_id in (${groupIds})
                    <if test="searchInfo != null and searchInfo != ''">
                        and process_name like CONCAT('%',#{searchInfo},'%')
                    </if>
                </if>
                <if test="groupIds != null and groupIds != '' and ids != null and ids != ''">
                    or
                </if>
                <if test="ids != null and ids != ''">
                    id in (${ids})
                    <if test="searchInfo != null and searchInfo != ''">
                        and process_name like CONCAT('%',#{searchInfo},'%')
                    </if>
                </if>
            )
        </if>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.ProcessStgVO" resultType="java.lang.Long">
      select count(0) from process_stg
      <where>
          <if test="processNameFromGroup!= null and processNameFromGroup">
              <include refid="processNameFromGroup_vo_where_sql"></include>
          </if>
          <if test="processNameFromGroup==null or processNameFromGroup == false">
              <include refid="vo_where_sql"></include>
          </if>
          <include refid="commonSQL.andNotDel"></include>
      </where>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.ProcessStgVO" resultMap="ProcessStgMap">
      select * from process_stg
      <where>
          <if test="processNameFromGroup!= null and processNameFromGroup">
              <include refid="processNameFromGroup_vo_where_sql"></include>
          </if>
          <if test="processNameFromGroup==null or processNameFromGroup == false">
              <include refid="vo_where_sql"></include>
          </if>
          <include refid="commonSQL.andNotDel"></include>
      </where>
    </select>

</mapper>
