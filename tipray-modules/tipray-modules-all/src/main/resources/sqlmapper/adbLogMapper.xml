<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ADBLogDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.ADBLog">
        <id column="id" property="id" />
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="filePath" column="file_path"/>
        <result property="fileName" column="file_name"/>
        <result property="fileSize" column="file_size"/>
        <result property="createTime" column="create_time"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="localFilePath" column="local_file_path"/>
        <result property="serialNum" column="serial_num"/>
        <result property="manufacturer" column="manufacturer"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="actionType" column="action_type"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name like CONCAT('%',#{fileName},'%')
            </if>
            <if test="actionType != null">
                and action_type = #{actionType}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from adb_monitor_log a
        <include refid="sql_where"></include>
    </select>
    <select id="listByVO" resultMap="resultMap">
        select id, record_num, term_id, user_id, create_time,
        file_path, file_name, file_size, local_file_path,
        backup_server_id, upload_file_guid,serial_num,manufacturer, term_group_id, user_group_id,action_type
        from adb_monitor_log a
        <include refid="sql_where"></include>
    </select>

</mapper>
