<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.dlp.dao.LanSegmentDao">

    <select id="selectPageByVO" resultType="com.tipray.dlp.bean.LanSegment">
        select * from lan_segment
        <where>
            <include refid="commonSQL.andNotDel"></include>
        </where>
    </select>

</mapper>