<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.VideoRecorderDao">

    <resultMap type="com.tipray.dlp.bean.VideoRecorder" id="resultMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="indexServerId" column="index_server_id"/>
        <result property="dataServerId" column="data_server_id"/>
        <result property="indexFileGuid" column="index_upload_file_guid"/>
        <result property="dataFileGuid" column="data_upload_file_guid"/>
        <result property="fileName" column="recorder_file_name"/>
        <result property="createTime" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="rcdFileVer" column="rcd_file_ver"/>
    </resultMap>
    <resultMap type="com.tipray.dlp.bean.VideoRecorder" id="speciallyVideoRecorderMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="indexServerId" column="index_server_id"/>
        <result property="dataServerId" column="data_server_id"/>
        <result property="indexFileGuid" column="index_upload_file_guid"/>
        <result property="dataFileGuid" column="data_upload_file_guid"/>
        <result property="fileName" column="recorder_name"/>
        <result property="createTime" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="rcdFileVer" column="rcd_file_ver"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="ids != null and ids != ''">
                and id in (@ids)
            </if>
            <if test="rcdFileVer != null">
                and rcd_file_ver = #{rcdFileVer}
            </if>
        </where>
    </sql>

    <!--查询单个-->
    <select id="getById" resultMap="resultMap">
        select * from screen_recorder_log where id = #{id}
    </select>

    <select id="listByIdAndTime" resultMap="resultMap">
        select id,term_id,user_id,create_time,recorder_file_name,index_local_file_path,data_local_file_path,
               index_server_id,index_upload_file_guid,data_server_id,data_upload_file_guid,term_group_id,user_group_id, rcd_file_ver
        from screen_recorder_log where id = #{id} and term_id = #{terminalId} and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
    </select>

    <delete id="deleteByIds">
        delete from screen_recorder_log where id in (@ids)
    </delete>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.VideoRecorderVO" resultMap="resultMap">
        SELECT max(id) id,term_id,max(user_id) user_id,max(create_time) create_time,recorder_file_name, DATE_FORMAT(create_time, '%Y-%m-%d' ) createTimeFormat, max(term_group_id) term_group_id,max(user_group_id) user_group_id, screen_rcd_guid
        FROM screen_recorder_log
        <include refid="vo_where_sql"/>
        GROUP BY createTimeFormat, term_id, recorder_file_name, screen_rcd_guid
    </select>

    <select id="speciallyListByVO" parameterType="com.tipray.dlp.bean.vo.VideoRecorderVO" resultMap="speciallyVideoRecorderMap">
        select max(id) id,term_id,user_id,max(create_time) create_time,recorder_name, max(data_upload_file_guid) as data_upload_file_guid, DATE_FORMAT(create_time, '%Y-%m-%d' ) createTimeFormat, max(term_group_id) term_group_id,max(user_group_id) user_group_id, max(rcd_file_ver) rcd_file_ver, screen_rcd_guid
        from specially_screen_rcd_log a
        <include refid="vo_where_sql"/>
        GROUP BY createTimeFormat, term_id, user_id, recorder_name, screen_rcd_guid
    </select>
    <select id="listSpeciallyByIdAndTime" resultMap="speciallyVideoRecorderMap">
        select id,term_id,user_id,create_time,recorder_name,index_local_file_path,data_local_file_path, index_local_file_uuid, data_local_file_uuid,
               index_server_id,index_upload_file_guid,data_server_id,data_upload_file_guid,term_group_id,user_group_id, rcd_file_ver
        from specially_screen_rcd_log where id = #{id} and term_id = #{terminalId} and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
    </select>


    <select id="listLogVideoByVO" parameterType="com.tipray.dlp.bean.vo.VideoRecorderVO" resultMap="resultMap">
        SELECT term_id,min(create_time) create_time, DATE_FORMAT(create_time, '%Y-%m-%d' ) createTimeFormat
        FROM screen_recorder_log
        <include refid="vo_where_sql"/>
        GROUP BY createTimeFormat, term_id
    </select>

    <select id="listTimeRangeVideo" parameterType="com.tipray.dlp.bean.vo.VideoRecorderVO" resultMap="resultMap">
        select max(id) id,term_id,recorder_file_name,min(create_time) create_time, DATE_FORMAT(create_time, '%Y-%m-%d') createTimeFormat, screen_rcd_guid
        from screen_recorder_log
        <include refid="vo_where_sql"/>
        <if test="blurScreenName != null and blurScreenName != ''">
            and
            <if test="screenName != null and screenName != ''">
                (
            </if>
            recorder_file_name like concat(#{blurScreenName}, '%')
        </if>
        <if test="screenName != null and screenName != ''">
            <choose>
                <when test="blurScreenName != null and blurScreenName != ''">
                    or recorder_file_name = #{screenName})
                </when>
                <otherwise>
                    and recorder_file_name = #{screenName}
                </otherwise>
            </choose>
        </if>
        and index_upload_file_guid is not null and index_upload_file_guid != ''
        and data_upload_file_guid is not null and data_upload_file_guid != ''
        group by createTimeFormat, term_id, recorder_file_name, screen_rcd_guid
    </select>

    <select id="listTimeRangeSpeciallyVideo" resultMap="speciallyVideoRecorderMap">
        select max(id) id,term_id,recorder_name,min(create_time) create_time, DATE_FORMAT(create_time, '%Y-%m-%d') createTimeFormat, screen_rcd_guid
        from specially_screen_rcd_log
        <include refid="vo_where_sql"/>
        group by createTimeFormat, term_id, recorder_name, screen_rcd_guid
    </select>

    <select id="listTimeRangeRecordNameByTermId" resultMap="resultMap">
        select recorder_file_name, min(create_time) create_time, max(rcd_file_ver) rcd_file_ver from screen_recorder_log
        <include refid="vo_where_sql"/>
        group by recorder_file_name
    </select>

    <select id="listVideoByIdTimeRange" resultMap="resultMap">
        select * from screen_recorder_log
        <where>
            <foreach collection="queryVos" item="item" open="(" close=")" separator="or">
                ( id = #{item.id} and DATE_FORMAT(create_time, '%Y-%m-%d') = DATE_FORMAT(#{item.createTime}, '%Y-%m-%d') )
            </foreach>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt;= #{endDate}
            </if>
        </where>
    </select>

    <select id="listSpeciallyVideoByIdTimeRange" resultMap="speciallyVideoRecorderMap">
        select * from specially_screen_rcd_log
        <where>
            <foreach collection="queryVos" item="item" open="(" close=")" separator="or">
                ( id = #{item.id} and DATE_FORMAT(create_time, '%Y-%m-%d') = DATE_FORMAT(#{item.createTime}, '%Y-%m-%d') )
            </foreach>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt;= #{endDate}
            </if>
        </where>
    </select>

</mapper>
