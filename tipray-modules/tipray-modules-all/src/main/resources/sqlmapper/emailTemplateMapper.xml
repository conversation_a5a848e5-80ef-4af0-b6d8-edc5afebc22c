<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.EmailTemplateDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.EmailTemplate">
        <id column="id" property="id" />
        <result column="type" property="type"/>
        <result column="subject" property="subject"/>
        <result column="content" property="content"/>
    </resultMap>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.tipray.dlp.bean.EmailTemplate">
		insert into email_template(type,subject,content)
		values(#{type},#{subject},#{content})
	</insert>

    <update id="update" parameterType="com.tipray.dlp.bean.EmailTemplate">
        update email_template set
        type=#{type},subject=#{subject},content=#{content}
        where id=#{id}
    </update>

    <delete id="deleteByIds" parameterType="java.lang.String">
		delete from email_template where id in (${value})
	</delete>

    <select id="getById" parameterType="java.lang.Long" resultMap="resultMap">
		select * from email_template where id=#{value}
	</select>

    <select id="getBySubject" parameterType="java.lang.String" resultMap="resultMap">
		select * from email_template where subject=#{value}
	</select>

    <sql id="vo_where_sql">
        <where>
            1 = 1
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and subject like CONCAT('%',#{searchInfo},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.EmailTemplateVO" resultType="java.lang.Long">
        select count(0) from email_template
        <include refid="vo_where_sql"></include>
    </select>
    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.EmailTemplateVO" resultMap="resultMap">
        select * from email_template
        <include refid="vo_where_sql"></include>
    </select>

    <select id="getByType" parameterType="java.lang.Integer" resultMap="resultMap">
        select * from email_template where type = #{type}
    </select>
</mapper>
