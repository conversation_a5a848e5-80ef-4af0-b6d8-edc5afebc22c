<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.BrowserUploadLogDao">

    <resultMap type="com.tipray.dlp.bean.BrowserUploadLog" id="BrowserUploadLogMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="browserSessionInfo" column="browser_session_info"/>
        <result property="browserType" column="browser_type"/>
        <result property="needBackup" column="need_backup"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileName" column="file_name"/>
        <result property="localFilePath" column="local_file_path"/>
        <result property="uploadUrl" column="upload_url"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="actionType" column="action_type"/>
        <result property="attachmentGuid" column="attachment_guid"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="browserType != null and browserType != ''">
                and browser_type = #{browserType}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name like CONCAT('%',#{fileName},'%')
            </if>
            <if test="actionType != null">
                and action_type = #{actionType}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from browser_upload_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="BrowserUploadLogMap">
        select id,term_id,user_id,create_time,browser_session_info, browser_type,need_backup, file_size, file_name,local_file_path, upload_url, backup_server_id, upload_file_guid, term_group_id, user_group_id,action_type
        from browser_upload_log a
        <include refid="sql_where"></include>
    </select>
    <select id="listByAttachmentGuid" resultMap="BrowserUploadLogMap">
        select term_id, file_name,local_file_path, backup_server_id, upload_file_guid, attachment_guid
        from browser_upload_log
        <where>
            <foreach collection="relTermIdGuids" index="key" item="value" separator="or" open="(" close=")">
                (term_id = ${key} and attachment_guid in (${value}))
            </foreach>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
        </where>
    </select>

</mapper>
