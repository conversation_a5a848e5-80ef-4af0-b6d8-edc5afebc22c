<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.BtConnChangeDao">

    <resultMap type="com.tipray.dlp.bean.BtConnChange" id="beanMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="addr" column="addr"/>
        <result property="devName" column="dev_name"/>
        <result property="devType" column="dev_type"/>
        <result property="changeType" column="change_type"/>
        <result property="devTypeEx" column="dev_type_ex"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="changeType != null and changeType != ''">
                and change_type = #{changeType}
            </if>
            <if test="devTypeEx != null and devTypeEx != ''">
                and dev_type_ex = #{devTypeEx}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from bt_conn_change a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="beanMap">
        select id, term_id, user_id, create_time, addr, dev_name, dev_type, change_type, dev_type_ex, term_group_id, user_group_id
        from bt_conn_change a
        <include refid="sql_where"></include>
    </select>

</mapper>
