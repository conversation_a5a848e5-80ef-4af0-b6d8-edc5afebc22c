<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DenseTransLogDetailDao">

    <resultMap type="com.tipray.dlp.bean.DenseTransLogDetail" id="DenseTransLogDetailMap">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="mainGuid" column="guid"/>
        <result property="srcLevel" column="src_dense"/>
        <result property="desLevel" column="dst_dense"/>
        <result property="opResult" column="op_result"/>
        <result property="filePath" column="file_path"/>
        <result property="createTime" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="mainGuid != null">
                and guid = #{mainGuid}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.DenseTransLogDetailVO" resultType="java.lang.Long">
      select count(0) from dense_trans_details_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.DenseTransLogDetailVO" resultMap="DenseTransLogDetailMap">
      select id, guid, src_dense, dst_dense, op_result, file_path, create_time, term_group_id, user_group_id from dense_trans_details_log
        <include refid="sql_where"></include>
    </select>

    <delete id="deleteByIds">
        delete from dense_trans_details_log where id in (${value})
    </delete>

</mapper>
