<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SoftLimitSpecialDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.SoftLimitSpecial">
        <id column="id" property="id" />
        <result column="type_id" property="typeId"/>
        <result column="check_md5" property="checkMd5"/>
        <result column="process_name" property="processName"/>
        <result column="product_name" property="productName"/>
        <result column="product_version" property="productVersion"/>
        <result column="original_filename" property="originalFilename"/>
        <result column="file_desc" property="fileDescription"/>
        <result column="internal_name" property="internalName"/>
        <result column="legal_copyright" property="legalCopyright"/>
        <result column="company_name" property="companyName"/>
        <result column="file_md5" property="fileMd5"/>
        <result column="quickly_md5" property="quicklyMd5"/>
        <result column="soft_sign" property="softSign"/>
        <result column="ori_id" property="oriId"/>
        <result column="os_type" property="osType"/>
        <result property="bizType" column="biz_type"/>
    </resultMap>

    <select id="selectProcessByVO" resultMap="resultMap">
        select p.*, s.biz_type from soft_limit_process p, soft_limit_rela_special s
        <where>
            p.id = s.relevance_id
            and s.biz_type = #{typeId}
            <if test="searchInfo != null and searchInfo != ''">
                and process_name like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="osType != null">
                and s.os_type = #{osType}
            </if>
        </where>
    </select>
</mapper>
