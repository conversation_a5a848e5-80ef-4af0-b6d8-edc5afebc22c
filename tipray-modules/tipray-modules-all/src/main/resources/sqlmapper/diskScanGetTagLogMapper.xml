<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DiskScanGetTagLogDao">

    <resultMap type="com.tipray.dlp.bean.DiskScanGetTagLog" id="DiskScanGetTagLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="trank" column="trank"/>
        <result property="trankName" column="trank_name"/>
        <result property="tagDetailGuid" column="tag_detail_guid"/>
        <result property="opType" column="op_type"/>
        <result property="result" column="result"/>
        <result property="guid" column="guid"/>
        <result property="fileName" column="file_name"/>
        <result property="filePath" column="file_path"/>
        <result property="tagContent" column="tag_content"/>
        <result property="encryFlag" column="encry_flag"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="clearFlag" column="clear_flag"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="recordGuid != null and recordGuid != null">
                and tag_detail_guid = #{recordGuid}
            </if>
            <if test="opType != null and opType != null">
                and op_type = #{opType}
            </if>
            <if test="guid != null and guid != null">
                and guid like CONCAT('%',#{guid},'%')
            </if>
            <if test="result != null and result != ''">
                and result in (${result})
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from diskscan_get_tag_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="DiskScanGetTagLogMap">
        select id, user_id,term_id, trank, trank_name, tag_detail_guid, op_type, result, guid, file_name, file_path, tag_content, encry_flag, create_time, term_group_id, user_group_id, clear_flag
        from diskscan_get_tag_log
        <include refid="sql_where"></include>
    </select>

</mapper>
