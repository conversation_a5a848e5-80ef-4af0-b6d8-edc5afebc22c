<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DevServerAccessLogDao">

    <resultMap type="com.tipray.dlp.bean.DevServerAccessLog" id="resultMap">
        <result property="id" column="id"/>
        <result property="devId" column="dev_id"/>
        <result property="devGuid" column="dev_guid"/>
        <result property="devName" column="dev_name"/>
        <result property="devType" column="dev_type"/>
        <result property="devVersion" column="dev_version"/>
        <result property="showVersion" column="show_version"/>
        <result property="groupId" column="group_id"/>
        <result property="intranetPort" column="intranet_port"/>
        <result property="intranetIpv4" column="intranet_ipv4"/>
        <result property="intranetIpv6" column="intranet_ipv6"/>
        <result property="intranetType" column="intranet_type"/>
        <result property="devMac" column="dev_mac"/>
        <result property="sysInfo" column="sys_info"/>
        <result property="optInfo" column="opt_info"/>
        <result property="operationType" column="operation_type"/>
        <result property="accessResult" column="access_result"/>
        <result property="authorizedStatus" column="authorized_status"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="pkgType" column="pkg_type"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <if test="devGuid != null and devGuid != ''">
                and dev_guid = #{devGuid}
            </if>
            <if test="devGuids != null and devGuids != ''">
                and dev_guid in (${devGuids})
            </if>
            <if test="devId != null">
                and dev_id = #{devId}
            </if>
            <if test="devIdLike != null">
                and dev_id like concat('%', #{devIdLike}, '%')
            </if>
            <if test="devTypes != null and devTypes != ''">
                and dev_type in (${devTypes})
            </if>
            <if test="approveResults != null and approveResults != ''">
                and access_result in (${approveResults})
            </if>
            <if test="accessCode != null">
                and operation_type = #{accessCode}
            </if>
            <if test="accessCodes != null and accessCodes != ''">
                and operation_type in (${accessCodes})
            </if>
            <if test="ip != null and ip != ''">
                and ip = #{ip}
            </if>
            <if test="devMac != null and devMac != ''">
                and dev_mac = #{devMac}
            </if>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="Long">
        select count(*)
        from dev_server_access_log
        <include refid="sql_where"/>
    </select>

    <select id="listByVO" resultMap="resultMap">
        select id, dev_id, dev_guid, dev_name, dev_type, dev_version, show_version, group_id, intranet_port, intranet_ipv4, intranet_ipv6, intranet_type, dev_mac, sys_info,
        opt_info, operation_type, access_result, authorized_status, remark, create_time, pkg_type
        from dev_server_access_log
        <include refid="sql_where"/>
    </select>

</mapper>
