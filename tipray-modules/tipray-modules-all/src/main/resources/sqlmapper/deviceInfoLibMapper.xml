<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DeviceInfoLibDao">

    <resultMap type="com.tipray.dlp.bean.DeviceInfoLib" id="DeviceInfoLibMap">
        <result property="id" column="id"/>
        <result property="devName" column="dev_name"/>
        <result property="devInstanceId" column="dev_instance_id"/>
        <result property="classGuid" column="class_guid"/>
        <result property="type" column="type"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>
    <sql id="vo_where_sql">
        <where>
            <if test="searchInfo != null and searchInfo != ''">
                and (dev_name like CONCAT('%',#{searchInfo},'%') or dev_instance_id like CONCAT('%',#{searchInfo},'%') or class_guid like CONCAT('%',#{searchInfo},'%') or remark like CONCAT('%',#{searchInfo},'%'))
            </if>
            <if test="devName != null and devName != ''">
                and dev_name = #{devName}
            </if>
        </where>
    </sql>
    <select id="selectPageByVO" resultMap="DeviceInfoLibMap">
        select id, dev_name, dev_instance_id, class_guid, type,remark,  create_time, modify_time
        from device_info_lib
        <include refid="vo_where_sql"/>
    </select>
</mapper>
