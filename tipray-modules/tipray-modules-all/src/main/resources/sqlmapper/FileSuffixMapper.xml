<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.FileSuffixDao">

    <sql id="vo_where_sql">
        <where>
            <if test="groupId != null">
                and group_id = #{groupId}
            </if>
            <if test="groupIds != null and groupIds != ''">
                and group_id in (${groupIds})
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and (t1.name like CONCAT('%',#{searchInfo},'%') or suffix like CONCAT('%',#{searchInfo},'%'))
            </if>
            <if test="ids != null and ids != ''">
                and t1.id in (${ids})
            </if>
        </where>
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.FileSuffixInfoVO" resultType="com.tipray.dlp.bean.FileSuffixInfotable">
        select * from file_suffix_table t1
        <include refid="vo_where_sql"/>
    </select>

</mapper>
