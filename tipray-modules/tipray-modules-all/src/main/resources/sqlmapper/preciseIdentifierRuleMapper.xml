<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.PreciseIdentifierRuleDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.PreciseIdentifierRule">
        <id column="id" property="id" />
        <result column="name" property="name"/>
        <result column="regular" property="regular"/>
        <result column="match_type" property="matchType"/>
        <result column="remark" property="remark"/>
        <result column="modify_ver" property="modifyVer" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <select id="listAll" resultMap="resultMap">
        select * from rule_identifier_precise
        where 1 = 1
        <include refid="commonSQL.andNotDel"></include>
    </select>

</mapper>
