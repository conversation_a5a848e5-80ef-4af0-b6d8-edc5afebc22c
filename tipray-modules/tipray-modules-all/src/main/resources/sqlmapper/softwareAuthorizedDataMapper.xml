<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SoftwareAuthorizedDataDao">

    <resultMap type="com.tipray.dlp.bean.SoftwareAuthorizedData" id="resultMap">
        <result property="softwareName" column="software_name"/>
        <result property="terminalId" column="term_id"/>
    </resultMap>

    <!--批量新增列-->
    <insert id="batchInsert">
        insert into rel_software_authorized_terminal(software_name, term_id)
        value
        <foreach collection="terminalIdList" item="terminalId" separator=",">
            (#{softwareName}, #{terminalId})
        </foreach>
    </insert>

    <!--查询单个-->
    <select id="listTerminalIds" resultType="java.lang.Long">
        select term_id from rel_software_authorized_terminal
        where term_id in (select t.id from terminal t where t.deleted = 0) and software_name in (@softwareNames)
    </select>

    <!--新增列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into rel_software_authorized_terminal(software_name, term_id)
        values (#{softwareName}, #{terminalId})
    </insert>

    <sql id="vo_where_sql">
        <where>
            <if test="softwareName != null and softwareName != ''">
                and software_name = #{softwareName}
            </if>
            <if test="terminalIds != null and terminalIds != ''">
                and term_id in (${terminalIds})
            </if>
            <if test="terminalId != null and terminalId != ''">
                and term_id = #{terminalId}
            </if>
        </where>
    </sql>

    <delete id="delete">
        delete from rel_software_authorized_terminal
        <include refid="vo_where_sql"/>
    </delete>

    <select id="countByTerminalId" resultType="java.lang.Integer">
        select count(0) from rel_software_authorized_terminal where term_id = #{value}
    </select>

    <select id="listSoftwareName" resultType="java.lang.String">
        select software_name from rel_software_authorized_terminal where term_id = #{value}
    </select>

    <select id="countBySoftwareName" resultType="java.lang.Integer">
        select count(0) from rel_software_authorized_terminal where term_id in (select t.id from terminal t where t.deleted = 0) and software_name = #{value}
    </select>

    <select id="selectGroupByTerm">
        select term_id, count(0) softSize from rel_software_authorized_terminal
        <where>
            <if test="terminalIds != null and terminalIds != ''">
                term_id in (@terminalIds)
            </if>
        </where>
        group by term_id
    </select>

    <select id="getPageByVO" resultMap="resultMap">
        select term_id, software_name from rel_software_authorized_terminal s
        <where>
            <if test="terminalId != null">
                and term_id = #{terminalId}
            </if>
            <if test="terminalIds != null and terminalIds != ''">
                and term_id in (@terminalIds)
            </if>
            <if test="softwareName != null and softwareName != ''">
                and software_name = #{softwareName}
            </if>
            <if test="softwareNames != null and softwareNames != ''">
                and software_name in (@softwareNames)
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and software_name like CONCAT('%',#{searchInfo},'%')
            </if>
        </where>
    </select>

    <delete id="deleteUninstalled">
        delete from rel_software_authorized_terminal where term_id not in (@terminalIds)
    </delete>

    <delete id="deleteByTermIds">
        delete from rel_software_authorized_terminal where term_id in (@terminalIds)
    </delete>

</mapper>
