<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.InstallPacketDao">

    <select id="findApprovalSoftList" resultType="com.tipray.dlp.bean.ProcessInfoApproval">
        select t1.id,t1.type,t1.computer_id computerId,t3.name terminalName,t1.reason,process_name processName,t1.product_name productName,t1.file_md5 fileMd5,t1.signature,
        t1.create_time createTime,t1.modify_time modifyTime,t2.name typeName
        from process_info_approval t1,soft_limit_type t2,terminal t3
        where t1.type = t2.id and t3.id = t1.computer_id
        <if test="type != null">
            and t1.type = #{type}
        </if>
    </select>

</mapper>
