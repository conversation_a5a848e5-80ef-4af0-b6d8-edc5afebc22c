<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ChatTextLogDao">

    <resultMap type="com.tipray.dlp.bean.ChatTextLog" id="ChatTextLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="msgTime" column="create_time"/>
        <result property="chatSessionInfo" column="chat_session_info"/>
        <result property="msgText" column="msg_text"/>
        <result property="chatType" column="chat_type"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt;= #{endDate}
            </if>
            <if test="keyword != null and keyword != ''">
                and msg_text like CONCAT('%',#{keyword},'%')
            </if>
            <if test="chatSessionInfo != null and chatSessionInfo != ''">
                and chat_session_info = #{chatSessionInfo}
            </if>
            and chat_type = #{chatType}
        </where>
    </sql>

    <delete id="deleteLogByVo">
        delete from chat_log
        where chat_type = #{chatType}
        <include refid="commonSQL.andObjectTypeAndObjectId"/>
        <if test="startDate != null and endDate != null">
            and DATE(create_time) &gt;= DATE(#{startDate}) and DATE(create_time) &lt;= DATE(#{endDate})
        </if>
    </delete>

    <select id="getChatSessionInfo" resultMap="ChatTextLogMap">
        select chat_session_info,term_id from chat_log a
        <include refid="sql_where"></include>
        group by chat_session_info,term_id
    </select>

    <select id="list" resultMap="ChatTextLogMap">
        select id, user_id, term_id, create_time, chat_session_info, msg_text, chat_type, term_group_id, user_group_id
        from chat_log a
        <include refid="sql_where"></include>
    </select>

</mapper>
