<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.StgFileDataDao">

    <resultMap type="com.tipray.dlp.bean.StgFileData" id="StgFileDataMap">
        <result property="id" column="id"/>
        <result property="fileName" column="file_name"/>
        <result property="md5" column="md5"/>
        <result property="base64Data" column="base64_data"/>
        <result property="fileSuffix" column="file_suffix"/>
        <result property="businessType" column="business_type"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <if test="businessType != null">
                and business_type = #{businessType}
            </if>
            <if test="md5 != null and md5 != ''">
                and md5 = #{md5}
            </if>
            <include refid="commonSQL.andNotDel"></include>
        </where>
    </sql>

    <select id="selectPageByVO" resultMap="StgFileDataMap">
        select id, file_name, md5, base64_data, file_suffix, business_type,  create_time, modify_time
        from stg_file_data
        <include refid="vo_where_sql"/>
    </select>
</mapper>
