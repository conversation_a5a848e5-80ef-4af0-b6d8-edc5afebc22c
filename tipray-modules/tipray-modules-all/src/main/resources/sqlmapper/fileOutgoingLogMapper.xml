<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.FileOutgoingLogDao">

    <resultMap type="com.tipray.dlp.bean.FileOutgoingLog" id="FileOutgoingLogMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="fileFullPath" column="file_full_path"/>
        <result property="fileSize" column="file_size"/>
        <result property="makeType" column="make_type"/>
        <result property="outSendFileType" column="out_send_file_type"/>
        <result property="controlCode" column="control_code"/>
        <result property="rightRules" column="right_rules"/>
        <result property="readTimes" column="read_times"/>
        <result property="beginReadTime" column="begin_read_time"/>
        <result property="endReadTime" column="end_read_time"/>
        <result property="msgTip" column="msg_tip"/>
        <result property="screenWaterId" column="screen_water_id"/>
        <result property="printWaterId" column="print_water_id"/>
        <result property="backupServerId" column="backup_server_id"/>
        <result property="uploadFileGuid" column="upload_file_guid"/>
        <result property="uploadFilePath" column="upload_file_path"/>
        <result property="backupFilePath" column="local_file_path"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_full_path like CONCAT('%',#{fileName},'%')
            </if>
            <if test="makeType != null">
                and make_type = #{makeType}
            </if>
            <if test="outSendFileType != null">
                and out_send_file_type = #{outSendFileType}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.FileOutgoingLogVO" resultType="java.lang.Long">
        select count(0) from ldx_file_record
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.FileOutgoingLogVO" resultMap="FileOutgoingLogMap">
        select id, term_id, user_id, create_time, file_full_path, file_size, make_type, out_send_file_type, control_code, right_rules, read_times, begin_read_time,
               end_read_time, msg_tip, screen_water_id, print_water_id, upload_file_guid, backup_server_id, local_file_path, term_group_id, user_group_id
        from ldx_file_record
        <include refid="sql_where"></include>
    </select>

</mapper>
