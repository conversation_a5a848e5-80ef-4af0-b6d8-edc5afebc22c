<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.MTPLogDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.MTPLog">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="opType" column="op_type"/>
        <result property="fileName" column="file_name"/>
        <result property="srcFilePath" column="src_file_path"/>
        <result property="destFilePath" column="dest_file_path"/>
        <result property="fileSize" column="file_size"/>
        <result property="localFilePath" column="local_file_path"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="createTime" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="actionType" column="action_type"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="keyword2 != null and keyword2 != ''">
                and file_name like CONCAT('%',#{keyword2},'%')
            </if>
            <if test="destFilePath != null and destFilePath != ''">
                and dest_file_path like CONCAT('%',#{destFilePath},'%')
            </if>
            <if test="actionType != null">
                and action_type = #{actionType}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from mtp_file_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="resultMap">
        select id,term_id,user_id,create_time,file_name,local_file_path,src_file_path,dest_file_path,file_size,op_type,backup_server_id,upload_file_guid,term_group_id,user_group_id,action_type
        from mtp_file_log
        <include refid="sql_where"></include>
    </select>
</mapper>
