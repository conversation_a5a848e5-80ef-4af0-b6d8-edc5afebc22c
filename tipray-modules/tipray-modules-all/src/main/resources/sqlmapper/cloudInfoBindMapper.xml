<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.CloudInfoBindDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.CloudInfoBind">
        <id column="id" property="id" />
        <result column="user_type" property="userType"/>
        <result column="user_id" property="userId"/>
        <result column="account" property="account"/>
        <result column="user_name" property="userName"/>
        <result column="phone" property="tel"/>
        <result column="we_chat_info" property="weChatInfo"/>
        <result column="email" property="mail"/>
        <result column="qr_code" property="qrCode"/>
        <result column="short_code" property="shortCode"/>
        <result column="active" property="active"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <if test="searchInfo != null and searchInfo != ''">
                and (user_name like concat('%', #{searchInfo}, '%') or phone like concat('%', #{searchInfo}, '%'))
            </if>
        </where>
    </sql>
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into cloud_info_bind(user_type, user_id, account, user_name, phone, email, qr_code, short_code, active)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.userType}, #{item.userId}, #{item.account}, #{item.userName}, #{item.tel}, #{item.mail}, #{item.qrCode}, #{item.shortCode}, #{item.active})
        </foreach>
    </insert>

    <select id="selectAccountByUserId" parameterType="java.lang.String" resultMap="resultMap">
        select id, account from cloud_info_bind where id in (@userIds)
    </select>

    <select id="selectPageByVO" resultMap="resultMap">
        select * from cloud_info_bind
        <include refid="vo_where_sql"></include>
    </select>
</mapper>