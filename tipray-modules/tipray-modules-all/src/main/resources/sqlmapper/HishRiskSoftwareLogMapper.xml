<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.HighRiskSoftwareLogDao">

    <resultMap type="com.tipray.dlp.bean.HighRiskSoftwareLog" id="resMap">
        <result property="id" column="id"/>
        <result property="recordNum" column="record_num"/>
        <result property="fileName" column="file_name"/>
        <result property="fastMd5" column="fast_md5"/>
        <result property="limitType" column="limit_type"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="createTime" column="create_time"/>
        <result property="localFilePath" column="local_file_path"/>
        <result property="timestamp" column="timestamp"/>
        <result property="backupServerId" column="backup_server_id"/>
        <result property="uploadFileGuid" column="upload_file_guid"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name like CONCAT('%',#{fileName},'%')
            </if>
            <if test="fastMd5 != null and fastMd5 != ''">
                and fast_md5 like CONCAT('%',#{fastMd5},'%')
            </if>
            <if test="limitType != null">
                and limit_type like #{limitType}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from high_risk_software_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="resMap">
        select id, term_id, user_id, term_group_id, user_group_id, create_time, file_name, fast_md5, limit_type, local_file_path, backup_server_id, upload_file_guid, timestamp
        from high_risk_software_log a
        <include refid="sql_where"></include>
    </select>

</mapper>
