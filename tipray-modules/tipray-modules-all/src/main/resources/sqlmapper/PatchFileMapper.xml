<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.PatchFileDao">
    <resultMap type="com.tipray.dlp.bean.PatchFile" id="PatchFileMap">
        <result property="id" column="id"/>
        <result property="guid" column="guid"/>
        <result property="parentGuid" column="parent_guid"/>
        <result property="updateId" column="update_id"/>
        <result property="parentUpdateId" column="parent_update_id"/>
        <result property="fileName" column="file_name"/>
        <result property="suffix" column="suffix"/>
        <result property="fileMd5" column="file_md5"/>
        <result property="ftpGuid" column="ftp_guid"/>
        <result property="fileSize" column="file_size"/>
        <result property="downloadAddr" column="download_addr"/>
        <result property="downloadState" column="download_state"/>
        <result property="downloadDetails" column="download_details"/>
        <result property="childStateSize" column="childStateSize"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>
    <sql id="default_select_field">
        id, guid,parent_guid,update_id,parent_update_id,file_name, suffix, file_md5, ftp_guid, file_size, download_addr,download_state, download_details, create_time, modify_time
    </sql>
    <select id="selectPageByVO"  parameterType="com.tipray.dlp.bean.vo.PatchVO" resultMap="PatchFileMap">
        select <include refid="default_select_field"/>
        from patch_file p
        <where>
            <if test="ids != null and ids != ''">
                and id in (${ids})
            </if>
            <if test="guid != null and guid != ''">
                and guid = ${guid}
            </if>
            <if test='guidList != null and guidList.size>0'>
                and guid in (@guidList)
            </if>
            <if test="downloadState != null and downloadState != '' or downloadState == 0">
                and download_state = #{downloadState}
            </if>
            <if test='downloadStateList != null'>
                and download_state in (@downloadStateList)
            </if>
            <if test='queryParentPatch != null'>
                <if test='queryParentPatch == true'>
                    and (parent_guid is null or parent_guid = '')
                </if>
                <if test='queryParentPatch == false'>
                    and  (parent_guid is not null and parent_guid != '')
                </if>
            </if>
        </where>
    </select>
    <select id="listPatchFileByVO"  parameterType="com.tipray.dlp.bean.vo.PatchVO" resultMap="PatchFileMap">
        select <include refid="default_select_field"/>
        from patch_file
        <where>
            <if test="downloadState != null and downloadState != '' or downloadState == 0">
                and download_state = #{downloadState}
            </if>
            <if test='queryParentPatch != null'>
                <if test='queryParentPatch == true'>
                    and (parent_guid is null or parent_guid = '')
                </if>
                <if test='queryParentPatch == false'>
                    and  (parent_guid is not null and parent_guid != '')
                </if>
            </if>
        </where>
    </select>
    <select id="getPatchFileByGuid"  resultMap="PatchFileMap">
        select <include refid="default_select_field"/>
        from patch_file
        where guid = #{guid}
    </select>
    <select id="getPatchFileByFtpGuid"  resultMap="PatchFileMap">
        select <include refid="default_select_field"/>
        from patch_file
        where ftp_guid = #{ftpGuid}
    </select>
    <select id="statisticDownloadState"  resultMap="PatchFileMap">
        select parent_guid, download_state, COUNT(0) childStateSize
        FROM patch_file
        <where>
            <![CDATA[ parent_guid in ( select guid from patch_file where parent_guid is null and download_state < #{maxState}) ]]>
            <if test="url != null and url != ''">
                AND parent_guid in ( select parent_guid from patch_file where download_addr = #{url})
            </if>
        </where>
        GROUP BY parent_guid, download_state
    </select>
    <select id="getUnSyncParentGuid"  resultMap="PatchFileMap">
        SELECT a.parent_guid
        FROM
        (SELECT parent_guid, COUNT(parent_guid) subPatchCount FROM patch_file
        GROUP BY parent_guid
        ) a
        LEFT JOIN
        (SELECT parent_guid, COUNT(parent_guid) subPatchCount FROM patch_lib WHERE deleted = 0  GROUP BY parent_guid ) b ON a.parent_guid = b.parent_guid
        WHERE a.parent_guid IS NOT NULL
         <![CDATA[ AND (b.parent_guid IS NULL OR b.subPatchCount < a.subPatchCount ) ]]>
        and  exists (SELECT guid FROM patch_file  WHERE a.parent_guid =guid AND  parent_guid IS NULL AND download_state = 7)
    </select>
    <select id="getUnSyncSubGuid"  resultMap="PatchFileMap">
        SELECT pl.id,pf.guid,pf.file_name,pf.ftp_guid,pf.file_md5,pf.file_size,pf.download_addr FROM patch_file pf LEFT JOIN patch_lib pl ON pf.guid = pl.guid
        WHERE pf.file_md5 != pl.file_md5
    </select>
    <insert id="insertPatchFile" keyProperty="id" useGeneratedKeys="true">
        insert into patch_file(guid,parent_guid,update_id,parent_update_id, file_name,suffix, file_md5, ftp_guid, file_size, download_addr, download_state, download_details, create_time, modify_time)
        values (#{guid},#{parentGuid},#{updateId},#{parentUpdateId},#{fileName}, #{suffix}, #{fileMd5}, #{ftpGuid}, #{fileSize}, #{downloadAddr}, #{downloadState},#{downloadDetails},#{createTime},#{modifyTime})
    </insert>
    <update id="updateByGuid">
        update patch_file
        <set>
            <if test="parentGuid != null and parentGuid != ''">
                parent_guid = #{parentGuid},
            </if>
            <if test="updateId != null and updateId != ''">
                update_id = #{updateId},
            </if>
            <if test="parentUpdateId != null and parentUpdateId != ''">
                parent_update_id = #{parentUpdateId},
            </if>
            <if test="fileName != null and fileName != ''">
                file_name = #{fileName},
            </if>
            <if test="suffix != null and suffix != ''">
                suffix = #{suffix},
            </if>
            <if test="fileMd5 != null and fileMd5 != ''">
                file_md5 = #{fileMd5},
            </if>
            <if test="ftpGuid != null and ftpGuid != ''">
                ftp_guid = #{ftpGuid},
            </if>
            <if test="fileSize != null and fileSize != ''">
                file_size = #{fileSize},
            </if>
            <if test="downloadAddr != null and downloadAddr != ''">
                download_addr = #{downloadAddr},
            </if>
            <if test="downloadState != null and downloadState != ''">
                download_state = #{downloadState},
            </if>
            <if test="downloadDetails != null and downloadDetails != ''">
                download_details = #{downloadDetails},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
        </set>
        where guid=#{guid}
    </update>
    <insert id="batchAddTask">
        insert into patch_file(guid,parent_guid,update_id,parent_update_id, file_name,suffix, file_md5, ftp_guid, file_size, download_addr, download_state, download_details, create_time, modify_time)
        values
        <foreach collection='list' item='item' separator=',' >
            (#{item.guid},#{item.parentGuid},#{item.updateId},#{item.parentUpdateId},#{item.fileName}, #{item.suffix}, #{item.fileMd5}, #{item.ftpGuid}, #{item.fileSize}, #{item.downloadAddr}, #{item.downloadState},#{item.downloadDetails},#{item.createTime},#{item.modifyTime})
        </foreach>
    </insert>
    <update id="batchUpdateTask" parameterType="java.util.List">
        <foreach collection="updateList" item="item" index="index" separator=";">
            UPDATE patch_file
            SET
            download_state = #{item.downloadState}
            WHERE guid = #{item.guid}
            and download_state  in ('0','1','3','6')
        </foreach>
    </update>
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="updateList" item="item" index="index" separator=";">
            UPDATE patch_file
            SET
            parent_guid = #{item.parentGuid},
            update_id = #{item.updateId},
            parent_update_id = #{item.parentUpdateId},
            file_name = #{item.fileName},
            suffix = #{item.suffix},
            file_md5 = #{item.fileMd5},
            ftp_guid =  #{item.ftpGuid},
            file_size = #{item.fileSize},
            download_addr = #{item.downloadAddr},
            download_state = #{item.downloadState},
            download_details = #{item.downloadDetails},
            modify_time = #{item.modifyTime}
            WHERE guid = #{item.guid}
        </foreach>
    </update>


</mapper>
