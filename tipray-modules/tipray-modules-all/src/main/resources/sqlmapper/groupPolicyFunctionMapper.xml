<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.GroupPolicyFunctionDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.GroupPolicyFunction">
        <id column="id" property="id" />
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="groupId" property="group_id"/>
        <result column="regKeyPath" property="reg_key_path"/>
        <result column="regKeyName" property="reg_key_name"/>
        <result column="platform" property="platform"/>
        <result column="help" property="helpInfo"/>
        <result column="reminder" property="reminder"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <select id="listByVO" resultMap="resultMap">
        select * from group_policy_function
        <where>
            1=1
            <if test="groupId != null">
                and group_id = #{groupId}
            </if>
            <if test="funcIds != null and funcIds != ''">
                and id in (${funcIds})
            </if>
            and deleted = 0
        </where>
        order by id
    </select>

    <select id="listByGroupId" resultMap="resultMap">
        select * from group_policy_function where group_id = #{value}
    </select>

    <select id="getNameById" resultType="java.lang.String">
        select name from group_policy_function where id = #{value}
    </select>
</mapper>
