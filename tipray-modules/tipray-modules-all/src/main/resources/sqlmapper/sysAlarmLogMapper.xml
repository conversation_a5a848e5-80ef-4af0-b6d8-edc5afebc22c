<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SysAlarmLogDao">

    <resultMap type="com.tipray.dlp.bean.SysAlarmLog" id="resultMap">
        <result property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="bizType" column="biz_type"/>
        <result property="alarmType" column="alarm_type"/>
        <result property="alarmObject" column="alarm_object"/>
        <result property="alarmDesc" column="alarm_desc"/>
        <result property="alarmParam" column="alarm_param"/>
        <result property="subBizType" column="sub_biz_type"/>
        <result property="devType" column="dev_type"/>
        <result property="devId" column="dev_id"/>
        <result property="ruleId" column="rule_id"/>
        <result property="createTime" column="create_time"/>
        <result property="dealStatus" column="deal_status"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            parent_id = 0 <include refid="if_sql"/>
        </where>
    </sql>

    <sql id="if_sql">
        <if test="id != null and id != ''">
            and id = #{id}
        </if>
        <if test="startDate != null and endDate != null">
            and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
        </if>
        <if test="bizType != null">
            and biz_type = #{bizType}
        </if>
        <if test="alarmType != null">
            and ((alarm_type &amp; #{alarmType}) > 0
            -- 由于系统告警记录的alarmType的值要同步为违规响应规则的值（AlarmLimitEnums），作下兼容
            <if test="(alarmType &amp; 4) > 0">
                or alarm_type = 0
            </if>
            <if test="(alarmType &amp; 8) > 0">
                or alarm_type = 1
            </if>)
        </if>
        <if test="alarmObject != null and alarmObject != ''">
            and alarm_object = #{alarmObject}
        </if>
        <if test="dealStatus != null">
            and deal_status = #{dealStatus}
        </if>
    </sql>

    <sql id="field">
        id, parent_id, biz_type, alarm_type, alarm_object, alarm_desc, alarm_param, create_time
    </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="resultMap">
        select id, parent_id, biz_type, alarm_type, alarm_object, alarm_desc, alarm_param, create_time
        from sys_alarm_log where id = #{value}
    </select>
    <select id="listByParentId" parameterType="java.lang.Long" resultMap="resultMap">
        select id, parent_id, biz_type, alarm_type, alarm_object, alarm_desc, alarm_param, create_time
        from sys_alarm_log where parent_id = #{value}
    </select>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.SysAlarmLogVO" resultType="java.lang.Long">
        select count(0) from sys_alarm_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.SysAlarmLogVO" resultMap="resultMap">
        select id, parent_id, biz_type, alarm_type, alarm_object, alarm_desc, alarm_param, create_time
        from sys_alarm_log
        <include refid="sql_where"></include>
    </select>

    <select id="getPopLatestLog" parameterType="com.tipray.dlp.bean.vo.SysAlarmLogVO" resultMap="resultMap">
        select id, parent_id, biz_type, alarm_type, alarm_object, alarm_desc, alarm_param, create_time from sys_alarm_log
        where biz_type = #{bizType} and alarm_type = 0 and create_time >= #{createDate} and deal_status = 0
        order by create_time desc limit 1
    </select>

    <select id="countByBizTypeCurrentDay" parameterType="com.tipray.dlp.bean.vo.SysAlarmLogVO" resultType="java.lang.Long">
        select count(0) from sys_alarm_log
        where biz_type = #{bizType} and create_time >= #{createDate}
        and create_time >= (select modify_time from sys_alarm_setup where biz_type = #{bizType} limit 1 )
    </select>

    <update id="updateDealStatus">
        update sys_alarm_log set deal_status = #{dealStatus} where biz_type = #{bizType}
    </update>

    <select id="listExportDetail" resultMap="resultMap">
        select <include refid="field"/>
        from sys_alarm_log
        where parent_id in (${value})
    </select>

    <select id="listUnionByVO" resultMap="resultMap">
        <if test="bizType == null or bizType != 3">
            select id, parent_id, biz_type, 0 sub_biz_type, alarm_type, alarm_param, create_time, alarm_object, alarm_desc, 0 dev_id, 0 dev_type, 0 rule_id, '' alarm_objects
            from sys_alarm_log
            <include refid="sql_where"/>
        </if>
        <if test="bizType == 3 or unionSql">
            <if test="unionSql">
                union all
            </if>
            select id, 0 parent_id, 3 biz_type, alarm_type as sub_biz_type, alarm_limit as alarm_type, event_param as alarm_param, FROM_UNIXTIME(timestamp) as create_time, '' alarm_object, '' alarm_desc, dev_id, dev_type, notify_group_id as rule_id, alarm_objects
            from dlp_datamgr.service_alarm_msg
            <where>
                <if test="startTimestamp != null and endTimestamp != null">
                    and timestamp &gt;= #{startTimestamp} and timestamp &lt; #{endTimestamp}
                </if>
                <if test="alarmType != null">
                    and (alarm_limit &amp; #{alarmType}) > 0
                </if>
                <if test="id != null">
                    and id = #{id}
                </if>
            </where>
        </if>
    </select>

</mapper>
