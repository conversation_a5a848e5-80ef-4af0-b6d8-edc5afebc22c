<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.RelTaskPushReportDao">

    <resultMap id="resultMap" type="com.tipray.dlp.bean.MsgPushTaskReportConfig">
        <id column="id" property="id" />
        <result column="report_type" property="reportType"/>
        <result column="template_id" property="templateId"/>
        <result column="dim_base_type" property="dimBaseType"/>
        <result column="count_by_object" property="countByObject"/>
        <result column="group_type" property="groupType"/>
        <result column="object_id" property="objectId"/>
        <result column="group_ids" property="groupIds"/>
        <result column="customize_type_ids" property="customizeTypeIds"/>
        <result column="top" property="top"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="report_name" property="reportName"/>
    </resultMap>

    <select id="selectByTemplateIds" resultMap="resultMap">
        select * from rel_task_push_report
        where template_id in (${id})
    </select>
</mapper>