<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.BurnFileDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.BurnFile">
        <id column="id" property="id" />
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="burnId" column="burn_id"/>
        <result property="burnResult" column="burn_result"/>
        <result property="filePath" column="file_path"/>
        <result property="fileMd5" column="file_md5"/>
        <result property="fileSize" column="file_size"/>
        <result property="backupPath" column="local_file_path"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="opType" column="op_type"/>
    </resultMap>

    <sql id="default_select_field">
        id, term_id, user_id, burn_id, burn_result, file_path, file_md5, file_size, local_file_path, backup_server_id, upload_file_guid, term_group_id, user_group_id, op_type
    </sql>

    <sql id="select_where_vo">
        <where>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="burnId != null and burnId != '' ">
                and burn_id = #{burnId}
            </if>
            <if test="burnIds != null and burnIds != '' ">
                and burn_id in (${burnIds})
            </if>
        </where>
    </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="resultMap">
        select <include refid="default_select_field"></include>
        from burn_file_log where id=#{value}
	</select>

    <select id="listByBurnIds" parameterType="java.lang.String" resultMap="resultMap">
        select <include refid="default_select_field"></include>
        from burn_file_log where burn_id in (${value})
    </select>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.BurnFileVO" resultType="java.lang.Long">
        select count(0) from burn_file_log b
        <include refid="select_where_vo"></include>
    </select>
    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.BurnFileVO" resultMap="resultMap">
        select <include refid="default_select_field"></include>
        from burn_file_log b
        <include refid="select_where_vo"></include>
    </select>
</mapper>
