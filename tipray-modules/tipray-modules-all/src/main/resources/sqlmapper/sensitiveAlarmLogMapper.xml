<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SensitiveAlarmLogDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.SensitiveAlarmLog">
        <id column="id" property="id"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="computer_name" property="terminalName"/>
        <result column="user_name" property="userName"/>
        <result column="term_id" property="terminalId"/>
        <result column="user_id" property="userId"/>
        <result column="create_time" property="createTime"/>
        <result column="file_path" property="filePath"/>
        <result column="action" property="action"/>
        <result column="alarm_limit" property="alarmLimit"/>
        <result column="hit_strg" property="hitStrg"/>
        <result column="content" property="content"/>
        <result column="alarm_guid" property="alarmGuid"/>
        <result column="capscreen_guid" property="capscreenGuid"/>
        <result column="recscreen_guid" property="recscreenGuid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>
    <sql id="full_fields_sql">
        id,alarm_type,alarm_level,computer_name,user_name,term_id,user_id,create_time,file_path,action,alarm_limit,hit_strg,content,alarm_guid,capscreen_guid,recscreen_guid,term_group_id,user_group_id
    </sql>

    <sql id="vo_where_sql">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
<!--            <if test="alarmType != null">-->
<!--                and alarm_type = #{alarmType}-->
<!--            </if>-->
            <if test="alarmType == 0">
                and alarm_type = 0
            </if>
            <if test="alarmType != null and alarmType != 0 and alarmType == 10001">
                and ((alarm_type &amp; 0xFFFF) = ${alarmType} or alarm_type =  #{alarmType})
            </if>
            <if test="alarmType != null and alarmType != 0 and alarmType != 10001">
                and ((alarm_type>>16 = ${alarmType} and alarm_type &amp; 0xFFFF != 10001) or alarm_type =  #{alarmType})
                <!--and ((alarm_type DIV 65536 = #{alarmType} and alarm_type != 0) or alarm_type =  #{alarmType})-->
            </if>
            <if test="alarmTypes != null">
                and alarm_type in (${alarmTypes})
            </if>
            <if test="alarmLevel != null">
                and alarm_level = #{alarmLevel}
            </if>
            <if test="action != null">
                and action = #{action}
            </if>
            <if test="alarmLimit != null">
                and alarm_limit = #{alarmLimit}
            </if>
            <if test="guid != null">
                and alarm_guid = #{guid}
            </if>
            <if test="guids != null and guids !=''">
                and alarm_guid in (${guids})
            </if>
            <if test="taskGuid != null">
                and task_guid = #{taskGuid}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.SensitiveAlarmLogVO" resultType="java.lang.Long">
        select count(0) from dlp_alarm_log
        <include refid="vo_where_sql"/>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.SensitiveAlarmLogVO" resultMap="resultMap">
        select <include refid="full_fields_sql"/>
        from dlp_alarm_log
        <include refid="vo_where_sql"/>
    </select>

    <select id="getById" parameterType="java.lang.Long" resultMap="resultMap">
		select <include refid="full_fields_sql"/>
        from dlp_alarm_log where id = #{value}
	</select>

</mapper>
