<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.AlarmTypeParamDao">

    <resultMap type="com.tipray.dlp.bean.AlarmTypeParam" id="AlarmTypeParamMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="sstType" column="sst_type" jdbcType="INTEGER"/>
        <result property="alarmType" column="alarm_type" jdbcType="INTEGER"/>
        <result property="alarmBus" column="alarm_bus" jdbcType="INTEGER"/>
        <result property="alarmDesc" column="alarm_desc" jdbcType="VARCHAR"/>
        <result property="alarmDescKey" column="alarm_desc_key" jdbcType="VARCHAR"/>
        <result property="activeAlarm" column="active_alarm" jdbcType="INTEGER"/>
        <result property="activeSensitive" column="active_sensitive" jdbcType="INTEGER"/>
        <result property="moduleId" column="module_id" jdbcType="INTEGER"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="AlarmTypeParamMap">
        select id, sst_type, alarm_type, alarm_bus, alarm_desc, alarm_desc_key, active_alarm, active_sensitive, module_id
        from alarm_type_param
        where deleted = 0
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="sstType != null">
                and sst_type = #{sstType}
            </if>
            <if test="alarmType != null">
                and alarm_type = #{alarmType}
            </if>
            <if test="alarmDesc != null and alarmDesc != ''">
                and alarm_desc = #{alarmDesc}
            </if>
            <if test="activeAlarm != null">
                and active_alarm = #{activeAlarm}
            </if>
            <if test="activeSensitive != null">
                and active_sensitive = #{activeSensitive}
            </if>
    </select>
    
</mapper>
