<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.UsbFileLogDao">

    <resultMap type="com.tipray.dlp.bean.UsbFileLog" id="usbFileLogMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="processName" column="process_name"/>
        <result property="srcFilePath" column="src_file_path"/>
        <result property="destFilePath" column="dest_file_path"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileOperaType" column="file_opera_type"/>
        <result property="localFilePath" column="local_file_path"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="usbId" column="usb_id"/>
        <result property="usbName" column="usb_name"/>
        <result property="driveSerial" column="drive_serial"/>
        <result property="isWhiteList" column="is_white_list"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="actionType" column="action_type"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="usbId != null">
                and usb_id = #{usbId}
            </if>
            <if test="opType != null and opType != ''">
                and file_opera_type = #{opType}
            </if>
            <if test="oriSource != null and oriSource != ''">
                and src_file_path like CONCAT('%',#{oriSource},'%')
            </if>
            <if test="targetSource != null and targetSource != ''">
                and dest_file_path like CONCAT('%',#{targetSource},'%')
            </if>
            <if test="isWhiteList != null">
                and is_white_list = #{isWhiteList}
            </if>
            <if test="actionType != null">
                and action_type = #{actionType}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from usb_device_opera_log a
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="usbFileLogMap">
        select id,term_id,user_id,create_time, process_name,src_file_path,dest_file_path,file_size,
        file_opera_type,local_file_path,backup_server_id,upload_file_guid,usb_id,usb_name,drive_serial,is_white_list,term_group_id,user_group_id,action_type
        from usb_device_opera_log a
        <include refid="sql_where"></include>
    </select>

</mapper>
