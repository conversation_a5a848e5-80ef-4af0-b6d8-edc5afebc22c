<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.VmlDao">

    <resultMap type="com.tipray.dlp.bean.Vml" id="VmlMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="matchRate" column="match_rate"/>
        <result property="prosFileNum" column="pros_file_num"/>
        <result property="consFileNum" column="cons_file_num"/>
        <result property="sourceDir" column="source_dir"/>
        <result property="state" column="state"/>
        <result property="trainCostTime" column="train_cost_time"/>
        <result property="trainTime" column="train_time"/>
        <result property="result" column="fail_result"/>
        <result property="deepTraining" column="deep_training"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <!--查询单个-->
    <select id="getById" resultMap="VmlMap">
        select id, name, match_rate, pros_file_num, cons_file_num, source_dir, state, train_cost_time, train_time, deep_training, create_time, modify_time, fail_result        from rule_vml
        where id = #{id}
    </select>

    <select id="getByName" resultMap="VmlMap">
        select id, name, match_rate, pros_file_num, cons_file_num, source_dir, state, train_cost_time, train_time, deep_training, create_time, modify_time, fail_result        from rule_vml
        where name = #{name}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="list" resultMap="VmlMap">
        select id, name, match_rate, pros_file_num, cons_file_num, source_dir, state, train_cost_time, train_time, deep_training, create_time, modify_time, fail_result        from rule_vml
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into rule_vml(id, name, match_rate, pros_file_num, cons_file_num, source_dir, state, train_cost_time, train_time, deep_training, create_time, modify_time)
        values (#{id}, #{name}, #{matchRate}, #{prosFileNum}, #{consFileNum}, #{sourceDir}, #{state}, #{trainCostTime}, #{trainTime}, #{deepTraining}, #{createTime}, #{modifyTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update rule_vml
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="matchRate != null">
                match_rate = #{matchRate},
            </if>
            <if test="prosFileNum != null">
                pros_file_num = #{prosFileNum},
            </if>
            <if test="consFileNum != null">
                cons_file_num = #{consFileNum},
            </if>
            <if test="sourceDir != null and sourceDir != ''">
                source_dir = #{sourceDir},
            </if>
            <if test="state != null">
                state = #{state},
            </if>
            <if test="trainCostTime != null">
                train_cost_time = #{trainCostTime},
            </if>
            <if test="trainTime != null">
                train_time = #{trainTime},
            </if>
            <if test="deepTraining != null">
                deep_training = #{deepTraining},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
            <if test="result != null and result != ''">
                fail_result = #{result},
            </if>
        </set>
        where id = #{id}
    </update>

    <sql id="vo_where_sql">
        <where>
            <if test="searchInfo != null and searchInfo != ''">
                and name like CONCAT('%',#{searchInfo},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO"  parameterType="com.tipray.dlp.bean.vo.RuleVO" resultType="java.lang.Long">
        select count(0) from rule_vml
        <include refid="vo_where_sql"/>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.RuleVO" resultMap="VmlMap">
        select * from rule_vml
        <include refid="vo_where_sql"/>
    </select>

    <select id="listByIds" parameterType="java.lang.String" resultMap="VmlMap">
        select * from rule_vml where id in (${value})
    </select>

    <update id="updateStatus">
        update rule_vml
        <set>
            state = #{state},
            <if test="trainCostTime != null">
                train_cost_time = #{trainCostTime},
            </if>
            <if test="result != null and result != ''">
                fail_result = #{result}
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="listTrainingId" resultType="java.lang.Long">
        select id from rule_vml where state = 1
    </select>

</mapper>
