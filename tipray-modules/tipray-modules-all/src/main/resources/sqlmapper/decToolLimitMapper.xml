<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DecToolLimitDao">
    <resultMap id="DecToolLimitMap" type="com.tipray.dlp.bean.DecToolLimit">
        <id column="id" property="id" />
        <result column="limit_type" property="limitType"/>
        <result column="limit_value" property="limitValue"/>
        <result column="limit_value_ex" property="limitValueEx"/>
        <result column="type" property="type"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="sql_where">
        where deleted = 0
        <if test="ids != null and ids != ''">
            and id in (${ids})
        </if>
        <if test="limitValue != null and limitValue != '' ">
            and limit_value like CONCAT('%',#{limitValue},'%')
        </if>
        <if test="limitType != null and limitType != '' ">
            and limit_type = #{limitType}
        </if>
        and type != 1
    </sql>

    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.DecToolLimitVO" resultMap="DecToolLimitMap">
        select * from dec_tool_limit
        <include refid="sql_where"></include>
    </select>

</mapper>