<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.OutgoingTemplateDao">

    <resultMap type="com.tipray.dlp.bean.OutgoingTemplate" id="OutgoingTemplateMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="ctrlValue" column="ctrl_value"/>
        <result property="validateValue" column="validate_value"/>
        <result property="openTimes" column="open_times"/>
        <result property="useDays" column="use_days"/>
        <result property="makeType" column="make_type"/>
        <result property="canUpdate" column="can_update"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="vo_where_sql">
        <include refid="commonSQL.andNotDel"></include>
        <if test="ids != null and ids != ''">
            and id in (${ids})
        </if>
        <if test="searchInfo != null and searchInfo != ''">
            and name like CONCAT('%',#{searchInfo},'%')
        </if>
    </sql>

    <select id="selectPageByVO" resultMap="OutgoingTemplateMap">
      select id, name, ctrl_value, validate_value, open_times, use_days, make_type, can_update, remark
      from outgoing_template where 1 = 1
      <include refid="vo_where_sql"/>
    </select>
</mapper>
