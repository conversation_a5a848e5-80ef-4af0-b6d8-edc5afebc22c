<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.FileFingerprintDao">

    <resultMap type="com.tipray.dlp.bean.FileFingerprint" id="resultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="fileMd5" column="file_MD5"/>
        <result property="fileName" column="file_name"/>
        <result property="state" column="state"/>
        <result property="trainCostTime" column="train_cost_time"/>
        <result property="trainTime" column="train_time"/>
        <result property="result" column="fail_result"/>
        <result property="mulFile" column="mul_file"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <select id="countRuleByFileMd5" resultType="java.lang.Long">
        select count(0) from rule_file_fingerprint where file_MD5 = #{value}
    </select>

    <!--查询单个-->
    <select id="getById" resultMap="resultMap">
        select id, name, file_MD5, file_name, state, train_cost_time, train_time, create_time, modify_time, fail_result, mul_file       from rule_file_fingerprint
        where id = #{id}
    </select>

    <select id="getByName" resultMap="resultMap">
        select id, name, file_MD5, file_name, state, train_cost_time, train_time, create_time, modify_time, fail_result, mul_file        from rule_file_fingerprint
        where name = #{name}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="list" resultMap="resultMap">
        select id, name, file_MD5, file_name, state, train_cost_time, train_time, create_time, modify_time, fail_result, mul_file        from rule_file_fingerprint
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into rule_file_fingerprint(id, name, file_MD5, state, train_cost_time, train_time, mul_file, create_time, modify_time)
        values (#{id}, #{name}, #{fileMd5}, #{state}, #{trainCostTime}, #{trainTime}, #{mulFile}, #{createTime}, #{modifyTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update rule_file_fingerprint
        <set>
            <if test="mulFile != null">
                mul_file = #{mulFile},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>

            <if test="state != null">
                state = #{state},
            </if>

            <if test="trainCostTime != null">
                train_cost_time = #{trainCostTime},
            </if>
            <if test="trainTime != null">
                train_time = #{trainTime},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
            <if test="result != null and result != ''">
                fail_result = #{result}
            </if>
        </set>
        where id = #{id}
    </update>

    <sql id="vo_where_sql">
        <where>
            <if test="searchInfo != null and searchInfo != ''">
                and name like CONCAT('%',#{searchInfo},'%')
            </if>
        </where>
    </sql>

    <select id="countByVO"  parameterType="com.tipray.dlp.bean.vo.RuleVO" resultType="java.lang.Long">
        select count(0) from rule_file_fingerprint
        <include refid="vo_where_sql"/>
    </select>

    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.RuleVO" resultMap="resultMap">
        select * from rule_file_fingerprint
        <include refid="vo_where_sql"/>
    </select>

    <select id="listById" parameterType="java.lang.String" resultMap="resultMap">
        select * from rule_file_fingerprint where id in (${value})
    </select>

    <select id="listAll" resultMap="resultMap">
        select * from rule_file_fingerprint
    </select>

    <update id="updateStatus">
        update rule_file_fingerprint
        <set>
            state = #{state},
            <if test="trainCostTime != null">
                train_cost_time = #{trainCostTime},
            </if>
            <if test="result != null and result != ''">
                fail_result = #{result}
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="listTrainingId" resultType="java.lang.Long">
        select id from rule_file_fingerprint where state = 1
    </select>

</mapper>
