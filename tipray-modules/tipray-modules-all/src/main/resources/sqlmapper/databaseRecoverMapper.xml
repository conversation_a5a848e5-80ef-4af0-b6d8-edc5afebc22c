<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DatabaseRecoverLogDao">

    <resultMap type="com.tipray.dlp.bean.DatabaseRecoverLog" id="resultMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="ip" column="ip"/>
        <result property="backupFileName" column="backup_file_name"/>
        <result property="backupFileLocation" column="backup_file_location"/>
        <result property="recoverStatus" column="recover_status"/>
        <result property="recoverFailMessage" column="recover_fail_message"/>
        <result property="recoverBeginTime" column="recover_begin_time"/>
        <result property="recoverEndTime" column="recover_end_time"/>
        <result property="logProcessTracked" column="log_process_tracked"/>
    </resultMap>


    <insert id="insert" parameterType="com.tipray.dlp.bean.DatabaseRecoverLog">
        insert into dlp_datamgr.db_recover_log(user_id, ip, backup_file_name, backup_file_location, recover_status, recover_fail_message, recover_begin_time, recover_end_time, log_process_tracked)
        values (#{userId}, #{ip}, #{backupFileName}, #{backupFileLocation}, #{recoverStatus}, #{recoverFailMessage}, #{recoverBeginTime}, #{recoverEndTime}, #{logProcessTracked})
    </insert>

    <select id="list" parameterType="com.tipray.dlp.bean.DatabaseRecoverLog" resultMap="resultMap">
        select id, user_id, ip, backup_file_name, backup_file_location, recover_status, recover_fail_message,
               recover_begin_time, recover_end_time, log_process_tracked
        from dlp_datamgr.db_recover_log
        <where>
            <if test="id != null">
            and id = #{id}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="ip != null">
                and ip = #{ip}
            </if>
            <if test="backupFileName != null">
                and backup_file_name = #{backupFileName}
            </if>
            <if test="backupFileLocation != null">
                and backup_file_location = #{backupFileLocation}
            </if>
            <if test="recoverStatus != null">
                and recover_status = #{recoverStatus}
            </if>
            <if test="recoverFailMessage != null">
                and recover_fail_message = #{recoverFailMessage}
            </if>
            <if test="recoverBeginTime != null">
                and recover_begin_time = #{recoverBeginTime}
            </if>
            <if test="recoverEndTime != null">
                and recover_end_time = #{recoverEndTime}
            </if>
            <if test="logProcessTracked != null">
                and log_process_tracked = #{logProcessTracked}
            </if>
        </where>
    </select>

    <select id="selectById" resultMap="resultMap">
        select id, user_id, ip, backup_file_name, backup_file_location, recover_status, recover_fail_message,
               recover_begin_time, recover_end_time, log_process_tracked
        from dlp_datamgr.db_recover_log
        where id = #{id}
    </select>

    <delete id="deleteById">
        delete from dlp_datamgr.db_recover_log where id = #{id}
    </delete>

    <update id="updateById" parameterType="com.tipray.dlp.bean.DatabaseRecoverLog">
        update dlp_datamgr.db_recover_log
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="ip != null">
                ip = #{ip},
            </if>
            <if test="backupFileName != null">
                backup_file_name = #{backupFileName},
            </if>
            <if test="backupFileLocation != null">
                backup_file_location = #{backupFileLocation},
            </if>
            <if test="recoverStatus != null">
                recover_status = #{recoverStatus},
            </if>
            <if test="recoverFailMessage != null">
                recover_fail_message = #{recoverFailMessage},
            </if>
            <if test="recoverBeginTime != null">
                recover_begin_time = #{recoverBeginTime},
            </if>
            <if test="recoverEndTime != null">
                recover_end_time = #{recoverEndTime},
            </if>
            <if test="logProcessTracked != null">
                log_process_tracked = #{logProcessTracked},
            </if>
        </set>
        where id = #{id}

    </update>

</mapper>
