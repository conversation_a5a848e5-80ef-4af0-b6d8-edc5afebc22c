<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.ADBStgRelatedSuffixDao">

    <insert id="batchInsert">
        insert into rel_adb_stg_suffix(stg_def_id, suffix_id)
        values
        <foreach collection="suffixIds" item="suffixId" separator=",">
            (#{stgId},#{suffixId})
        </foreach>
    </insert>

    <select id="listStgIdBySuffixIds" resultType="java.lang.Long">
        select stg_def_id from rel_adb_stg_suffix where suffix_id in (@suffixIds)
    </select>

    <delete id="deleteByStgIds">
        delete from rel_adb_stg_suffix where stg_def_id in (@stgIds)
    </delete>

    <delete id="deleteBySuffixIds">
        delete from rel_adb_stg_suffix where suffix_id in (@suffixIds)
    </delete>
</mapper>