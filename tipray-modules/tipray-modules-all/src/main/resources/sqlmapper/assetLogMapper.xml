<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.AssetLogDao">

    <resultMap type="com.tipray.dlp.bean.AssetLog" id="AssetLogMap">
        <result property="id" column="id"/>
        <result property="propId" column="prop_id"/>
        <result property="logType" column="log_type"/>
        <result property="desp" column="desp"/>
        <result property="isRead" column="is_read"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="alarmFlag" column="alarm_flag"/>
        <result property="parentId" column="parent_id"/>
        <result property="createTime" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="recordFlag" column="record_flag"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="propId != null and propId != ''">
                and parent_id = #{propId}
            </if>
            <if test="logType != null and logType != ''">
                and log_type = #{logType}
            </if>
            <if test="desp != null and desp != ''">
                and desp like CONCAT('%',#{desp},'%')
            </if>
            <if test='"1".equals(parentId)'> <!-- 硬件 -->
                and parent_id &gt;= 1001 and parent_id &lt;= 1031
            </if>
            <if test='"2".equals(parentId)'> <!-- 软件 -->
                and parent_id &gt;= 2001 and parent_id &lt;= 2004
            </if>
            <if test="isRead != null and isRead != ''">
                and is_read = #{isRead}
            </if>
            <if test='"1".equals(alarmFlag)'>    <!-- 是否只显示告警信息 -->
                and alarm_flag = 1
            </if>
            <if test='"0".equals(alarmFlag)'>  <!-- 是否显示变更记录 -->
                and record_flag = 1
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
      select count(0) from asset_log a
      <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="AssetLogMap">
      select id, prop_id, log_type, desp, is_read, term_id, user_id, alarm_flag,parent_id, create_time, term_group_id, user_group_id, record_flag from asset_log a
      <include refid="sql_where"></include>
    </select>

</mapper>
