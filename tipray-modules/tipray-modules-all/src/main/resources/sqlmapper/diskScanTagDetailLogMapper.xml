<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DiskScanTagDetailLogDao">

    <resultMap type="com.tipray.dlp.bean.DiskScanTagDetailLog" id="DiskScanTagDetailLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="createTime" column="create_time"/>
        <result property="opType" column="op_type"/>
        <result property="guid" column="guid"/>
        <result property="tagId" column="tag_id"/>
        <result property="tagContent" column="tag_content"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="recordGuid != null and recordGuid != null">
                and guid = #{recordGuid}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from diskscan_tag_detail_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="DiskScanTagDetailLogMap">
        select id, user_id,term_id, op_type, guid, tag_id, tag_content, create_time, term_group_id, user_group_id
        from diskscan_tag_detail_log
        <include refid="sql_where"></include>
    </select>

</mapper>
