<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.DevServerAccessMappingDao">

    <resultMap type="com.tipray.dlp.bean.DevServerAccessMapping" id="resultMap">
        <result property="id" column="id"/>
        <result property="devId" column="dev_id"/>
        <result property="devGuid" column="dev_guid"/>
        <result property="devMac" column="dev_mac"/>
        <result property="devVersion" column="dev_version"/>
        <result property="showVersion" column="show_version"/>
        <result property="intranetType" column="intranet_type"/>
        <result property="authorizedStatus" column="authorized_status"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="devType" column="dev_type"/>
        <result property="devName" column="dev_name"/>
        <result property="extraInfo" column="extra_info"/>
        <result property="pkgType" column="pkg_type"/>
        <result property="intranetIpv4" column="intranetIpv4"/>
        <result property="intranetIpv6" column="intranetIpv6"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            deleted = 0
            <if test="devGuid != null and devGuid != ''">
                and d.dev_guid = #{devGuid}
            </if>
            <if test="devGuids != null and devGuids != ''">
                and d.dev_guid in (${devGuids})
            </if>
            <if test="devId != null">
                and d.dev_id = #{devId}
            </if>
            <if test="startDate != null and endDate != null">
                and d.create_time &gt;= #{startDate} and d.create_time &lt; #{endDate}
            </if>
            <if test="devMac != null and devMac != ''">
                and d.dev_mac like concat('%', #{devMac}, '%')
            </if>
            <if test="version != null and version != ''">
                and
                (
                (show_version IS NULL AND dev_version LIKE CONCAT('%', #{version}, '%'))
                OR
                (show_version IS NOT NULL AND (show_version LIKE CONCAT('%', #{version}, '%') OR dev_version LIKE CONCAT('%', #{version}, '%')))
                )
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="Long">
        select count(*)
        from dev_server_access_mapping d
        inner join (select dev_id, dev_type, name dev_name, intranet_ip intranetIpv4, intranet_ipv6 intranetIpv6 from dev_server
        <where>
            <if test="devTypes != null and devTypes != ''">
                and dev_type in (${devTypes})
            </if>
            <if test="backupFileDevIds != null and backupFileDevIds != ''">
                and dev_id not in(${backupFileDevIds})
            </if>
            and dev_server.deleted = 0
        </where>

        union all
        select db_id dev_id, 219 dev_type, db_name dev_name, ip intranetIpv4, null intranetIpv6 from db_server
        <if test="devTypes != null and devTypes != ''">
            where 219 in (${devTypes})
        </if>
        ) t
        on t.dev_id = d.dev_id
        <include refid="sql_where"/>
    </select>

    <select id="listByVO" resultMap="resultMap">
        select d.id, d.dev_id, d.dev_guid, d.dev_version, d.show_version, d.intranet_type, d.dev_mac, d.authorized_status,
               d.create_time create_time, d.modify_time, d.extra_info, d.pkg_type,
               t.dev_type, t.dev_name, t.intranetIpv4, t.intranetIpv6
        from dev_server_access_mapping d
        inner join (select dev_id, dev_type, name dev_name, intranet_ip intranetIpv4, intranet_ipv6 intranetIpv6 from dev_server
        <where>
            <if test="devTypes != null and devTypes != ''">
                and dev_type in (${devTypes})
            </if>
            <if test="backupFileDevIds != null and backupFileDevIds != ''">
                and dev_id not in(${backupFileDevIds})
            </if>
            and dev_server.deleted = 0
        </where>

         union all
        select db_id dev_id, 219 dev_type, db_name dev_name, ip intranetIpv4, null intranetIpv6 from db_server
        <if test="devTypes != null and devTypes != ''">
            where 219 in (${devTypes})
        </if>
        ) t
        on t.dev_id = d.dev_id
        <include refid="sql_where"/>
    </select>

</mapper>
