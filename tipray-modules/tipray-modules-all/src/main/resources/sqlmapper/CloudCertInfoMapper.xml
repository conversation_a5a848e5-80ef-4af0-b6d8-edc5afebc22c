<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.CloudCertInfoDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.CloudServerExt">
        <id column="dev_id" property="devId"/>
        <result column="app_id" property="appId"/>
        <result column="app_key" property="appKey"/>
        <result column="secret_key" property="secretKey"/>
    </resultMap>

    <insert id="insert" useGeneratedKeys="false">
        insert into dev_server_ext_cloud(dev_id, app_id, app_key, secret_key, sign_version, open_time)
        values (#{devId}, #{appId}, #{appKey}, #{secretKey}, #{signVersion}, #{openTime})
    </insert>
</mapper>
