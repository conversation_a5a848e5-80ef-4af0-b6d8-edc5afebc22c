<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.CfgSourceCodeDao">

    <resultMap type="com.tipray.dlp.bean.CfgSourceCode" id="CfgSourceCodeMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="suffix" column="suffix"/>
    </resultMap>

    <select id="listNotDeleted" resultMap="CfgSourceCodeMap">
        select id,name,suffix from cfg_source_code where deleted = 0
    </select>

</mapper>
