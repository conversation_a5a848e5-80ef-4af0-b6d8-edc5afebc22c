<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.PatchDetectionDao">
    <resultMap type="com.tipray.dlp.bean.PatchDetectionFile" id="PatchDetectionFileMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="fileMd5" column="file_md5"/>
        <result property="suffix" column="suffix"/>
        <result property="ftpGuid" column="ftp_guid"/>
        <result property="downloadAddr" column="download_addr"/>
        <result property="downloadStatus" column="download_status"/>
        <result property="downloadDetails" column="download_details"/>
        <result property="systemVersion" column="system_version"/>
        <result property="fileSize" column="file_size"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>
    <sql id="vo_where_sql">
        <where>
            <if test="name != null and name != ''">
                and name like CONCAT('%',#{name},'%')
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and name like CONCAT('%',#{searchInfo},'%')
            </if>
            <if test="ids != null and ids != ''">
                and id in (${ids})
            </if>
            <if test="downloadState != null">
                and download_status = #{downloadState}
            </if>
            <if test="systemVersion != null">
                and system_version = #{systemVersion}
            </if>
        </where>
    </sql>
    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.PatchDetectionFileVO" resultMap="PatchDetectionFileMap">
        select id,name,file_md5,suffix,ftp_guid,download_addr,download_status,download_details,system_version,file_size,update_time,create_time,modify_time
        from patch_detection_file p
        <include refid="vo_where_sql"/>
    </select>


</mapper>
