<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.BtFileLogDao">

    <resultMap type="com.tipray.dlp.bean.BtFileLog" id="BtFileLogMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="opType" column="op_type"/>
        <result property="filePath" column="file_path"/>
        <result property="fileName" column="file_name"/>
        <result property="fileSize" column="file_size"/>
        <result property="backupFilePath" column="backup_file_path"/>
        <result property="devId" column="backup_server_id"/>
        <result property="fileGuid" column="upload_file_guid"/>
        <result property="blueName" column="blue_name"/>
        <result property="blueAddress" column="blue_address"/>
        <result property="devType" column="dev_type"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
        <result property="actionType" column="action_type"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="keyword1 != null and keyword1 != ''">
                and op_type = #{keyword1}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name like CONCAT('%',#{fileName},'%')
            </if>
            <if test="devType != null and devType != ''">
                and dev_type = #{devType}
            </if>
            <if test="actionType != null">
                and action_type = #{actionType}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from bt_file_log a
        <include refid="sql_where"></include>
    </select>
    <select id="listByVO" resultMap="BtFileLogMap">
        select id, record_num, term_id, user_id, create_time, op_type, file_path, file_name, file_size, backup_file_path, backup_server_id, upload_file_guid,blue_name,blue_address,dev_type, term_group_id, user_group_id,action_type
        from bt_file_log a
        <include refid="sql_where"></include>
    </select>

</mapper>
