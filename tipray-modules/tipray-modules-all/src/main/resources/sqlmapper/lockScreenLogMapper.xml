<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.LockScreenLogDao">

    <resultMap type="com.tipray.dlp.bean.LockScreenLog" id="LockScreenLogMap">
        <result property="id" column="id"/>
        <result property="recordNum" column="record_num"/>
        <result property="userId" column="user_id"/>
        <result property="terminalId" column="term_id"/>
        <result property="lockType" column="lock_type"/>
        <result property="lockStyle" column="lock_style"/>
        <result property="createTime" column="create_time"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <include refid="commonSQL.andObjectTypeAndObjectId"/>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="lockType != null and lockType != ''">
                and lock_type = #{lockType}
            </if>
        </where>
    </sql>

    <select id="countByVO" resultType="java.lang.Long">
        select count(0) from lock_record
        <include refid="sql_where"></include>
    </select>

    <select id="listByVO" resultMap="LockScreenLogMap">
        select id, user_id, term_id, lock_type, lock_style, create_time, term_group_id, user_group_id
        from lock_record
        <include refid="sql_where"></include>
    </select>

</mapper>
