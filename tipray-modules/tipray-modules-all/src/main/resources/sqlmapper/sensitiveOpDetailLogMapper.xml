<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.SensitiveOpDetailLogDao">

    <resultMap type="com.tipray.dlp.bean.SensitiveOpDetailLog" id="SensitiveOpDetailLogMap">
        <result property="id" column="id"/>
        <result property="terminalId" column="term_id"/>
        <result property="userId" column="user_id"/>
        <result property="eventGuid" column="event_guid"/>
        <result property="actionType" column="action_type"/>
        <result property="actionGuid" column="action_guid"/>
        <result property="fileIndex" column="file_index"/>
        <result property="localFilePath" column="local_file_path"/>
        <result property="backupServerId" column="backup_server_id"/>
        <result property="uploadFileGuid" column="upload_file_guid"/>
        <result property="termGroupId" column="term_group_id"/>
        <result property="userGroupId" column="user_group_id"/>
    </resultMap>

    <sql id="sql_where">
        <where>
            <if test="startDate != null and endDate != null">
                and create_time &gt;= #{startDate} and create_time &lt; #{endDate}
            </if>
            <if test="eventGuid != null and eventGuid != ''">
                and event_guid = #{eventGuid}
            </if>
        </where>
    </sql>

    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.SensitiveOpDetailLogVO" resultType="java.lang.Long">
      select count(0) from sensitive_op_detail_log
      <include refid="sql_where"></include>
    </select>

    <select id="countByEventGuid" parameterType="com.tipray.dlp.bean.vo.SensitiveOpDetailLogVO" resultType="java.lang.Long">
        select count(0) from sensitive_op_detail_log
        <include refid="sql_where"></include>
    </select>

    <select id="listByEventGuid" parameterType="com.tipray.dlp.bean.vo.SensitiveOpDetailLogVO" resultMap="SensitiveOpDetailLogMap">
      select id, term_id, user_id, event_guid, action_type, action_guid, file_index, local_file_path, backup_server_id, upload_file_guid, term_group_id, user_group_id from sensitive_op_detail_log
      <include refid="sql_where"></include>
    </select>

    <delete id="deleteByIds">
        delete from sensitive_op_detail_log where id in (${value})
    </delete>

</mapper>
