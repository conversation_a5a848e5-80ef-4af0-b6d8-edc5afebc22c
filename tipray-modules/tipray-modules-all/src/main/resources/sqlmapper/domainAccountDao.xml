<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.dlp.dao.DomainAccountDao">
    <select id="selectPageByVO" parameterType="com.tipray.dlp.bean.vo.DomainAccountVO"
            resultType="com.tipray.dlp.bean.DomainAccount">
        select * from domain_account
        <where>
            <include refid="commonSQL.andNotDel"></include>
            <if test="account != null">
                and account like CONCAT('%',#{account},'%')
            </if>
        </where>
    </select>

    <select id="countByVO" resultType="java.lang.Long">
        select count(id) from  domain_account
        <where>
            <include refid="commonSQL.andNotDel"></include>
            <if test="account != null">
                and account like CONCAT('%',#{account},'%')
            </if>
        </where>
    </select>
</mapper>
