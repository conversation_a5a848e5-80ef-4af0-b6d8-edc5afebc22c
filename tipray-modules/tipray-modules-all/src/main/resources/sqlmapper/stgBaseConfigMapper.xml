<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.StgBaseConfigDao">

    <resultMap type="com.tipray.dlp.bean.StgBaseConfig" id="StgBaseConfigMap">
        <result property="id" column="id"/>
        <result property="number" column="number"/>
        <result property="name" column="name"/>
        <result property="strategyKey" column="strategy_key"/>
        <result property="type" column="type"/>
        <result property="groupType" column="group_type"/>
        <result property="active" column="active"/>
        <result property="usedScope" column="used_scope"/>
        <result property="sortNumber" column="sort_number"/>
        <result property="routerPath" column="router_path"/>
        <result property="multiActive" column="multi_active"/>
        <result property="table" column="strategy_table"/>
        <result property="osType" column="os_type"/>
        <result property="osAble" column="os_able"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="list" resultMap="StgBaseConfigMap">
        select id, number, name, strategy_key, type, group_type, active, used_scope, sort_number,router_path,multi_active,strategy_table, os_type, os_able
        from stg_base_config
    </select>

    <select id="listExists" resultMap="StgBaseConfigMap">
        select id, number, name, strategy_key, type, group_type, active, used_scope,
        sort_number,router_path,strategy_table, os_type, os_able
        from stg_base_config
        where 1 != 1
        <if test="name != null and name != ''">
            or name = #{name}
        </if>
        <if test="number != null">
            or number = #{number}
        </if>
        <if test="strategyKey != null">
            or strategy_key = #{strategyKey}
        </if>
        <if test="protocolKey != null">
            or strategy_key = #{protocolKey}
        </if>
    </select>

    <!--通过主键修改数据-->
    <update id="update">
        update stg_base_config
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="groupType != null">
                group_type = #{groupType},
            </if>
            <if test="active != null">
                active = #{active},
            </if>
            <if test="usedScope != null">
                used_scope = #{usedScope},
            </if>
            <if test="sortNumber != null">
                sort_number = #{sortNumber},
            </if>
            <if test="osType != null">
                os_type = #{osType},
            </if>
            <if test="osAble != null">
                os_able = #{osAble},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>
