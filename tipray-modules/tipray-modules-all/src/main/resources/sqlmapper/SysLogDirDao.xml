<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.dlp.dao.SysLogDirDao">
    <insert id="insertUserLogDirs">
        insert into sys_user_log_dir(user_id, log_dir_ids)
        values (#{userId}, #{dirIds})
    </insert>

    <update id="updateUserLogDirs">
        update sys_user_log_dir
        set log_dir_ids = #{dirIds}
        where user_id = #{userId}
    </update>

    <select id="getLogDirIdsByUserId" resultType="java.lang.String">
        select log_dir_ids
        from sys_user_log_dir
        where user_id = #{userId}
    </select>

</mapper>