<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.dlp.dao.RuleDao">
    <resultMap id="resultMap" type="com.tipray.dlp.bean.BaseRule">
        <id column="id" property="id" />
        <result column="type" property="type"/>
        <result column="name" property="name"/>
        <result column="json" property="json"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <sql id="vo_where_sql">
        <where>
            <if test="type != null">
                type=#{type}
            </if>
            <if test="searchInfo != null and searchInfo != ''">
                and name like CONCAT('%',#{searchInfo},'%')
            </if>
            and deleted = 0
        </where>
    </sql>
    <select id="countByVO" parameterType="com.tipray.dlp.bean.vo.RuleVO" resultType="java.lang.Long">
		select count(0) from rule
        <include refid="vo_where_sql"/>
	</select>
    <select id="listByVO" parameterType="com.tipray.dlp.bean.vo.RuleVO" resultMap="resultMap">
		select * from rule
        <include refid="vo_where_sql"/>
	</select>
    <select id="listIdNameMap" resultType="java.util.Map">
        select id, name from rule;
    </select>
</mapper>
