<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="urllib.dll">
    <!--说明：由于解析原因，只支持${}的方式配置参数，并且只支持sqlite的相关函数-->
    <select id="selectGroup" resultType="com.tipray.dlp.bean.UrlGroup">
        SELECT GroupID id, GroupName name, ParentId parentId FROM Url_Group_utf8
    </select>
    <sql id="select_where_sql">
        <where>
            <if test="groupId!=null">
                GroupID = #{groupId}
            </if>
            <if test="groupIds!=null and groupIds!=''">
                and GroupID in (${groupIds})
            </if>
            <if test="ids!=null and ids!=''">
                and UrlId in (${ids})
            </if>
            <if test="searchInfo!=null and searchInfo!=''">
                and (UrlTitle like '%${searchInfo}%' or UrlAddress like '%${searchInfo}%')
            </if>
        </where>
    </sql>
    <select id="selectInfo" resultType="com.tipray.dlp.bean.Url" parameterType="com.tipray.dlp.bean.vo.UrlVO">
        SELECT UrlId id, UrlTitle name, UrlAddress Address, GroupID groupId FROM Url_utf8
        <include refid="select_where_sql"/>
        <if test="sortName!=null and sortOrder!=null">
            order by ${sortName} ${sortOrder}
        </if>
        <if test="size!=null and offset!=null">
            limit #{size} offset #{offset}
        </if>
    </select>
    <select id="countInfo" resultType="java.lang.Long" parameterType="com.tipray.dlp.bean.vo.UrlVO">
        SELECT count(0) FROM Url_utf8
        <include refid="select_where_sql"/>
    </select>

</mapper>