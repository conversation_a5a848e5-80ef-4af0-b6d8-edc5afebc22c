<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="reporttemp.dll">
    <!--说明：由于解析原因，只支持${}的方式配置参数，并且只支持sqlite的相关函数-->
    <sql id="select_where_sql">
        <where>
            <if test="ids!=null and ids!=''">
                and id in (${ids})
            </if>
            <if test="searchInfo!=null and searchInfo!=''">
                and (title like '%${searchInfo}%' or template_file_name like '%${searchInfo}%')
            </if>
        </where>
    </sql>
    <select id="selectInfo" resultType="com.tipray.dlp.bean.ReportTemplate" parameterType="com.tipray.dlp.bean.vo.ReportTemplateVo">
        SELECT id, title, template_file_name FROM Report_Template
        <include refid="select_where_sql"/>
        <if test="sortName!=null and sortOrder!=null">
            order by ${sortName} ${sortOrder}
        </if>
        <if test="size!=null and offset!=null">
            limit #{size} offset #{offset}
        </if>
    </select>

    <select id="countInfo" resultType="java.lang.Long" parameterType="com.tipray.dlp.bean.vo.ReportTemplateVo">
        SELECT count(0) FROM Report_Template
        <include refid="select_where_sql"/>
    </select>

</mapper>