<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="policylib.dll">
    <sql id="table_stgy_type">
        <if test="osType==null or osType==1">
            Stgy_Type_utf8
        </if>
        <if test="osType!=null and osType==2">
            Stgy_Type_L_utf8
        </if>
        <if test="osType!=null and osType==4">
            Stgy_Type_M_utf8
        </if>
    </sql>
    <sql id="table_stgy_process">
        <if test="osType==null or osType==1">
            Stgy_Process_utf8
        </if>
        <if test="osType!=null and osType==2">
            Stgy_Process_L_utf8
        </if>
        <if test="osType!=null and osType==4">
            Stgy_Process_M_utf8
        </if>
    </sql>
    <!--说明：只支持sqlite的相关函数-->
    <select id="selectGroup" resultType="com.tipray.dlp.bean.ProcessStgGroup" parameterType="com.tipray.dlp.bean.vo.GroupVO">
        SELECT TypeId id, TypeName name, TypeParentId parentId
        from <include refid="table_stgy_type"/>
    </select>
    <sql id="select_where_sql">
        <where>
            <if test="groupId!=null">
                TypeId = #{groupId}
            </if>
            <if test="groupIds!=null and groupIds!=''">
                and TypeId in (${groupIds})
            </if>
            <if test="ids!=null and ids!=''">
                and ProcessId in (${ids})
            </if>
            <if test="searchInfo!=null and searchInfo!=''">
                and (processName like '%${searchInfo}%' or ExeName like '%${searchInfo}%')
            </if>
        </where>
    </sql>
    <select id="selectInfo" resultType="com.tipray.dlp.bean.ProcessStgLib" parameterType="com.tipray.dlp.bean.vo.ProcessStgVO">
        SELECT ProcessId id, ExeName processName, ProcessName name,  TypeId groupId, FileExt1 decReadSfx, FileExt2 encWriteSfx, AutoEncryptFileExt encOpenSfx, OriginalName originalFilename, HookFlag enablePast, MD5 fileMd5
        FROM <include refid="table_stgy_process"/>
        <include refid="select_where_sql"/>
        <if test="sortName!=null and sortOrder!=null">
            order by ${sortName} ${sortOrder}
        </if>
        <if test="size!=null and offset!=null">
            limit #{size} offset #{offset}
        </if>
    </select>
    <select id="countInfo" resultType="java.lang.Long" parameterType="com.tipray.dlp.bean.vo.ProcessStgVO">
        SELECT count(0) FROM <include refid="table_stgy_process"/>
        <include refid="select_where_sql"/>
    </select>


    <sql id="select_rela_where_sql">
        <where>
            <if test="osType==null and osType!=1">
                <!-- 目前只提供了window的指纹和禁网， 因此其它系统则直接返回空-->
                and 0>1
            </if>
            <if test="ids!=null and ids!=''">
                and ProcessId in (${ids})
            </if>
            <if test="searchInfo!=null and searchInfo!=''">
                and (ExeName like '%${searchInfo}%' or ExeName like '%${searchInfo}%')
            </if>
        </where>
    </sql>
    <select id="selectMd5" resultType="com.tipray.dlp.bean.ProcessStgRelaMd5">
        SELECT ProcessId relevanceId, ExeName processName, MD5 fileMd5, MD5_FAST quicklyMd5, MD5_Attr propertyMd5, CheckMD5 propertyMark, VersionInfo processVersion, FileVersion componentVersion
        FROM Process_MD5_Default_utf8
        <include refid="select_rela_where_sql"/>
    </select>
    <select id="selectProcessIdFromMd5" resultType="java.lang.Long">
        SELECT ProcessId FROM Process_MD5_Default_utf8
        <include refid="select_rela_where_sql"/>
    </select>

    <select id="selectNet" resultType="com.tipray.dlp.bean.ProcessStgNet">
        SELECT ProcessId processStgId, ExeName name, BlockType mode, Protocol protocol, TimeId, BeginIp beginIp, EndIp endIp, BeginPort beginPort, EndPort endPort, BeginIpv6 beginIpv6, EndIpv6 endIpv6, ifIncludeLan includeLan
        FROM Process_ForbitPort_Default_utf8
        <include refid="select_rela_where_sql"/>
    </select>
    <select id="selectProcessIdFromNet" resultType="java.lang.Long">
        SELECT ProcessId FROM Process_ForbitPort_Default_utf8
        <include refid="select_rela_where_sql"/>
    </select>
</mapper>