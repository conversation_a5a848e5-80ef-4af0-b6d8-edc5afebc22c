<!-- 场景 ，每个场景一个文件配置，并最终在sceneConfig.xml中集合配置-->
<!ELEMENT scene (audit+)>
<!ATTLIST scene name CDATA #REQUIRED>
<!ATTLIST scene description CDATA #REQUIRED>

<!-- 场景下的审计配置，一个场景下可以有多个audit标签，即可以包含多个审计日志，
因为我们查询一个场景就相当于查询这个场景下的所有审计日志 -->
<!ELEMENT audit (table, search-fields)>
<!-- 审计的名称 -->
<!ATTLIST audit name CDATA #REQUIRED>
<!-- 审计对应的菜单码，
一方面作为权限，可以使用根据菜单权限来控制用户是否有权限访问此审计，如果没有权限，则聚合搜索也不会查询，
令一方面，菜单码也可以作为后续扩展获取菜单名称，并作为显示，多语言支持。
-->
<!ATTLIST audit mscode CDATA #REQUIRED>
<!ATTLIST audit description CDATA #REQUIRED>

<!-- 表的配置，查询审计也就是为了查询该审计的表，所以需要配置如何查询表 -->
<!ELEMENT table (condition?, field+)>
<!-- 表名 -->
<!ATTLIST table name CDATA #REQUIRED>

<!-- 查询条件，即查询表格时需要带上此条件，有可能不同业务会用到同一张表，需要用不同的条件区分开，如果不配置，则视为不需要额外增加查询条件-->
<!ELEMENT condition (#PCDATA)>

<!-- table的字段，声明的字段将参与查询并作为查询条件 如 select {field} from table where {field} = 1 -->
<!ELEMENT field (#PCDATA)>
<!-- 字段名称 -->
<!ATTLIST field name CDATA #REQUIRED>
<!-- 字段描述，主要用于生成场景说明 -->
<!ATTLIST field description CDATA #REQUIRED>

<!-- 前端查询字段配置 -->
<!ELEMENT search-fields (search-field+)>

<!ELEMENT search-field (#PCDATA)>
<!-- name属性表示为查询字段用的是数据库哪个字段的值 -->
<!ATTLIST search-field name CDATA #REQUIRED>
<!-- mapping属性表示查询字段在前端查询条件中的输入框（或其他）的name属性，如果不配置，search-field#name 属性作为 mapping的值-->
<!ATTLIST search-field mapping CDATA #IMPLIED>