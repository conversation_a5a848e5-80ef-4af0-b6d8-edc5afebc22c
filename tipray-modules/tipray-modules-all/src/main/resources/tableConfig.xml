<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE root>
<!--
	t-name：代表对应的表名;
	cl-name：代表表所对应的java对象;
	c-name：代表表中对应的列名
	f-name：代表列对应的字段名
	type：代表对应字段的字段类型
 -->
<root>
    <table t-name="Stgy_Process" cl-name="com.tipray.dlp.bean.ProcessStgLib">
        <table c-name="ProcessId" f-name="id" type="Long">
        </table>
        <table c-name="ExeName" f-name="processName" type="String">
        </table>
        <!--<table c-name="ExeFlag" f-name="processFlag" type="String">
        </table>-->
        <table c-name="FileExt1" f-name="decReadSfx" type="String">
        </table>
        <table c-name="FileExt2" f-name="encWriteSfx" type="String">
        </table>
        <table c-name="AutoEncryptFileExt" f-name="encOpenSfx" type="String">
        </table>
        <table c-name="OriginalName" f-name="originalFilename" type="String">
        </table>
        <table c-name="HookFlag" f-name="enablePast" type="Integer">
        </table>
        <table c-name="ProcessName" f-name="name" type="String">
        </table>
        <table c-name="TypeId" f-name="groupId" type="Long">
        </table>
        <table c-name="MD5" f-name="fileMd5" type="String">
        </table>
    </table>
    <table t-name="Stgy_Type" bean="com.tipray.dlp.bean.ProcessStgGroup">
        <table c-name="TypeId" f-name="id" type="Long">
        </table>
        <table c-name="TypeName" f-name="name" type="String">
        </table>
        <table c-name="TypeParentId" f-name="parentId" type="Long">
        </table>
    </table>
</root>