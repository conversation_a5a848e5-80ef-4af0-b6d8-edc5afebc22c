<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-react-helmet="true" th:text="${title}"></title>
    <meta charset="utf-8">
    <script th:inline="javascript">
        /*<![CDATA[*/
        window.onload = function() {
            var urlParams = new URLSearchParams(window.location.search);
            // 修改参数值
            urlParams.set('state', 'finished');
            // 更新URL参数而不刷新页面
            window.history.replaceState(null, null, '?' + urlParams.toString());
        };
        /*]]>*/
    </script>
</head>

<body>
<div class="container">
    <div class="error" th:if="${error} eq true">
        <h1 th:text="${failure}"></h1>
        <br/>
        <p th:text="${msg}"></p>
    </div>
    <div class="success" th:if="${error} eq false">
        <h1 th:text="${success}"></h1>
        <br/>
        <p th:text="${msg}"></p>
    </div>

</div>
</body>
<script>

</script>
</html>
<style>
    body,
    html {
        padding: 0;
        margin: 0;
        font-family: 'Quicksand', sans-serif;
        font-weight: 400;
        overflow: hidden;
    }

    .container {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        height: 60vh;
        width: 100%;
        -webkit-transition: -webkit-transform .5s;
        transition: -webkit-transform .5s;
        transition: transform .5s, -webkit-transform .5s;
    }

    .error {
        width: 450px;
        padding: 40px;
        text-align: center;
    }

    .error h1 {
        font-size: 125px;
        padding: 0;
        margin: 0;
        font-weight: 700;
        color: indianred;
    }

    .error h2 {
        margin: -30px 0 0 0;
        padding: 0;
        font-size: 47px;
        letter-spacing: 12px;
    }

    .success {
        width: 450px;
        padding: 40px;
        text-align: center;
    }

    .success h1 {
        font-size: 125px;
        padding: 0;
        margin: 0;
        font-weight: 700;
        color: lightgreen;
    }

    .success h2 {
        margin: -30px 0 0 0;
        padding: 0;
        font-size: 47px;
        letter-spacing: 12px;
    }


    @media screen and (max-width: 1000px) {
        .container {
            -webkit-transform: scale(1.5);
            transform: scale(1.5);
        }
    }

    @media screen and (max-width: 850px) {
        .container {
            -webkit-transform: scale(1.25);
            transform: scale(1.25);
        }
    }

    @media screen and (max-width: 775px) {
        .container {
            -ms-flex-wrap: wrap-reverse;
            flex-wrap: wrap-reverse;
            -webkit-box-align: inherit;
            -ms-flex-align: inherit;
            align-items: inherit;
        }
    }

    @media screen and (max-width: 370px) {
        .container {
            -webkit-transform: scale(1);
            transform: scale(1);
        }
    }
</style>