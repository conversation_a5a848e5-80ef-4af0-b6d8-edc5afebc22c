<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><title>WangEditor Display HTML</title>    <meta charset="UTF-8" /></head><body><p> </p>
<p> </p>
<h1 style="text-align: center;">运营报告模板二</h1>
<h2>一、数据安全运营数据</h2>
<p>1、数据安全</p>
<div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400;">
    <div style="height: 40px; line-height: 40px; text-align: center; font-size: 14px; font-weight: bold; color: #000000;">数据安全对比趋势表</div>
    <table style="border-collapse: collapse; width: 100%; text-align: center;" border="1">
        <tbody>
        <tr style="color: #ffffff; height: 40px; line-height: 40px; font-weight: 400;">
            <td style="background-color: #4874cb;">{{dataSecurityCompare}}统计项</td>
            <td style="background-color: #4874cb;">当前{{curDate}}</td>
            <td style="background-color: #4874cb;">{{qoqLastDate}}</td>
            <td style="background-color: #4874cb;">环比</td>
            <td style="background-color: #4874cb;">{{yoyLastDate}}</td>
            <td style="background-color: #4874cb;">同比</td>
        </tr>
        <tr style="color: #000000; height: 40px; line-height: 40px; font-weight: 400;">
            <td>[name]</td>
            <td>[data]</td>
            <td>[qoqData]</td>
            <td>[qoqDataPercent]</td>
            <td>[yoyData]</td>
            <td>[yoyDataPercent]</td>
        </tr>
        </tbody>
    </table>
    <div class="compareMark" style="display: block; font-size: 12px; line-height: 25px; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none; color: #ff0000;">注：表格中的[qoqDataPercent]和[yoyDataPercent]分别代表环比和同比变化计算规则，您可以根据自身需求，对规则进行修改。以环比表达式举例解析规则如下：</label></div>
    <div class="compareMark" style="display: block; font-size: 12px; color: #ff0000; line-height: 25px; font-weight: 400;"><label class="compareMarkLabel" style="display: none;">如果上一周期qoqData为0，当前data为0 ，则环比变化为0%；当前data不为0，则环比变化为∞；</label></div>
    <div class="compareMark" style="display: block; font-size: 12px; color: #ff0000; line-height: 25px; font-weight: 400;"><label class="compareMarkLabel" style="display: none; color: #ff0000; text-align: left;">如果上一周期qoqData不为0，就根据表达式(data-qoqData)*100.0/qoqData)计算环比变化；%.2f表示精确到小数点后2位。</label></div>
    <div class="qoq compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none;">qoqData == 0 ? (data == 0 ? '0' : '∞') : T(java.lang.String).format('%.2f%%',(data-qoqData)*100.0/qoqData)</label></div>
    <div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none;">yoyData == 0 ? (data == 0 ? '0' : '∞') : T(java.lang.String).format('%.2f%%',(data-yoyData)*100.0/yoyData)</label></div>
    <div> </div>
    <div> </div>
</div>
<div class="imageSrc" style="text-align: center;">
    <div class="dragImageAllDiv" style="width: 100%;">
        <div style="text-align: center; font-size: 15px; font-weight: bold; height: 40px; line-height: 40px;">数据安全报表</div>
        <div class="imageSrc" style="text-align: center;">
            <div class="imageType" style="display: none;">bar</div>
            <div class="imageCode" style="display: none;">22</div>
            <div class="imageBusinessImg" style="display: block; text-align: center;">{{@img22_1745286862449}}</div>
            <div class="dimBaseTypeDiv" style="display: none; position: relative; text-align: center;"> </div>
            <img class="imgShow" style="display: none; width: 80px; height: 80px; margin: auto;" src="data:image/png;base64,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" />
            <div class="compareMark" style="display: block; font-size: 12px; line-height: 25px; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none; color: #ff0000;">注：您可通过下方配置实现自定义排序项,可选排序参数有：批量加解密数:encOrDecSumAll,全盘扫描文件数:diskScanSumAll,外发文件数:outFileSumAll,文件备份数:fileBackupSumAll,加密文件数:encFileSumAll，只可以选择一项指标作为排序项</label></div>
            <div class="sortNameParameterChart" style="display: block; color: #000000; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none;">sortName:encOrDecSumAll</label></div>
        </div>
    </div>
    <div> </div>
    <div> </div>
</div>
<div>2、审批情况</div>
<div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400;"> </div>
<div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400;">
    <div style="height: 40px; line-height: 40px; text-align: center; font-size: 14px; font-weight: bold; color: #000000;">审批对比趋势表</div>
    <table style="border-collapse: collapse; width: 100%; text-align: center;" border="1">
        <tbody>
        <tr style="color: #ffffff; height: 40px; line-height: 40px; font-weight: 400;">
            <td style="background-color: #4874cb;">{{trwfeLogCompare}}统计项</td>
            <td style="background-color: #4874cb;">当前{{curDate}}</td>
            <td style="background-color: #4874cb;">{{qoqLastDate}}</td>
            <td style="background-color: #4874cb;">环比</td>
            <td style="background-color: #4874cb;">{{yoyLastDate}}</td>
            <td style="background-color: #4874cb;">同比</td>
        </tr>
        <tr style="color: #000000; height: 40px; line-height: 40px; font-weight: 400;">
            <td>[name]</td>
            <td>[data]</td>
            <td>[qoqData]</td>
            <td>[qoqDataPercent]</td>
            <td>[yoyData]</td>
            <td>[yoyDataPercent]</td>
        </tr>
        </tbody>
    </table>
    <div class="compareMark" style="display: block; font-size: 12px; line-height: 25px; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none; color: #ff0000;">注：表格中的[qoqDataPercent]和[yoyDataPercent]分别代表环比和同比变化计算规则，您可以根据自身需求，对规则进行修改。以环比表达式举例解析规则如下：</label></div>
    <div class="compareMark" style="display: block; font-size: 12px; color: #ff0000; line-height: 25px; font-weight: 400;"><label class="compareMarkLabel" style="display: none;">如果上一周期qoqData为0，当前data为0 ，则环比变化为0%；当前data不为0，则环比变化为∞；</label></div>
    <div class="compareMark" style="display: block; font-size: 12px; color: #ff0000; line-height: 25px; font-weight: 400;"><label class="compareMarkLabel" style="display: none; color: #ff0000; text-align: left;">如果上一周期qoqData不为0，就根据表达式(data-qoqData)*100.0/qoqData)计算环比变化；%.2f表示精确到小数点后2位。</label></div>
    <div class="qoq compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none;">qoqData == 0 ? (data == 0 ? '0' : '∞') : T(java.lang.String).format('%.2f%%',(data-qoqData)*100.0/qoqData)</label></div>
    <div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none;">yoyData == 0 ? (data == 0 ? '0' : '∞') : T(java.lang.String).format('%.2f%%',(data-yoyData)*100.0/yoyData)</label></div>
    <div> </div>
</div>
<div> </div>
<div>
    <div style="height: 40px; line-height: 40px; text-align: center; font-size: 14px; font-weight: bold; color: #000000;">审批报表</div>
    <table style="border-collapse: collapse; width: 100%; text-align: center;" border="1">
        <tbody>
        <tr style="color: #ffffff; height: 40px; line-height: 40px; font-weight: 400;">
            <td style="background-color: #4874cb;">{{trwfeLog}} 部门</td>
            <td style="background-color: #4874cb;">解密审批完成数</td>
            <td style="background-color: #4874cb;">解密审批同意数</td>
            <td style="background-color: #4874cb;">解密审批拒绝数</td>
            <td style="background-color: #4874cb;">解密审批系统自动应答数</td>
            <td style="background-color: #4874cb;">直接外发审批完成数</td>
            <td style="background-color: #4874cb;">直接外发审批同意数</td>
            <td style="background-color: #4874cb;">直接外发审批拒绝数</td>
            <td style="background-color: #4874cb;">直接外发审批系统自动应答数</td>
            <td style="background-color: #4874cb;">定密审批完成数</td>
            <td style="background-color: #4874cb;">定密审批同意数</td>
            <td style="background-color: #4874cb;">定密审批拒绝数</td>
            <td style="background-color: #4874cb;">定密审批系统自动应答数</td>
            <td style="background-color: #4874cb;">离线审批完成数</td>
            <td style="background-color: #4874cb;">离线审批同意数</td>
            <td style="background-color: #4874cb;">离线审批拒绝数</td>
            <td style="background-color: #4874cb;">离线审批系统自动应答数</td>
            <td style="background-color: #4874cb;">打印审批完成数</td>
            <td style="background-color: #4874cb;">打印审批同意数</td>
            <td style="background-color: #4874cb;">打印审批拒绝数</td>
            <td style="background-color: #4874cb;">打印审批系统自动应答数</td>
            <td style="background-color: #4874cb;">取消文档水印审批完成数</td>
            <td style="background-color: #4874cb;">取消文档水印审批同意数</td>
            <td style="background-color: #4874cb;">取消文档水印审批拒绝数</td>
            <td style="background-color: #4874cb;">取消文档水印审批系统自动应答数</td>
            <td style="background-color: #4874cb;">文件外发审批完成数</td>
            <td style="background-color: #4874cb;">文件外发审批同意数</td>
            <td style="background-color: #4874cb;">文件外发审批拒绝数</td>
            <td style="background-color: #4874cb;">文件外发审批系统自动应答数</td>
            <td style="background-color: #4874cb;">行为管控审批完成数</td>
            <td style="background-color: #4874cb;">行为管控审批同意数</td>
            <td style="background-color: #4874cb;">行为管控审批拒绝数</td>
            <td style="background-color: #4874cb;">行为管控审批系统自动应答数</td>
            <td style="background-color: #4874cb;">阅读权限转换审批完成数</td>
            <td style="background-color: #4874cb;">阅读权限转换审批同意数</td>
            <td style="background-color: #4874cb;">阅读权限转换审批拒绝数</td>
            <td style="background-color: #4874cb;">阅读权限转换审批系统自动应答数</td>
        </tr>
        <tr style="color: #000000; height: 40px; line-height: 40px; font-weight: 400;">
            <td>[countObject]</td>
            <td>[fileDecryptTotal]</td>
            <td>[fileDecApprove]</td>
            <td>[fileDecReject]</td>
            <td>[fileDecAutoApprove]</td>
            <td>[outSendTotal]</td>
            <td>[outSendApprove]</td>
            <td>[outSendReject]</td>
            <td>[outSendAutoApprove]</td>
            <td>[changeFileLevelTotal]</td>
            <td>[changeFileLevelApprove]</td>
            <td>[changeFileLevelReject]</td>
            <td>[changeFileLevelAutoApprove]</td>
            <td>[offlineTotal]</td>
            <td>[offlineApprove]</td>
            <td>[offlineReject]</td>
            <td>[offlineAutoApprove]</td>
            <td>[filePrintTotal]</td>
            <td>[filePrintApprove]</td>
            <td>[filePrintReject]</td>
            <td>[filePrintAutoApprove]</td>
            <td>[cancelWatermarkTotal]</td>
            <td>[cancelWatermarkApprove]</td>
            <td>[cancelWatermarkReject]</td>
            <td>[cancelWatermarkAutoApprove]</td>
            <td>[sensitiveFileOutSendTotal]</td>
            <td>[sensitiveFileOutSendApprove]</td>
            <td>[sensitiveFileOutSendReject]</td>
            <td>[sensitiveFileOutSendAutoApprove]</td>
            <td>[behaviorControlTotal]</td>
            <td>[behaviorControlApprove]</td>
            <td>[behaviorControlReject]</td>
            <td>[behaviorControlAutoApprove]</td>
            <td>[readPermissionConv]</td>
            <td>[readPermissionConvApprove]</td>
            <td>[readPermissionConvReject]</td>
            <td>[readPermissionConvAutoApprove]</td>
        </tr>
        </tbody>
    </table>
    <div class="compareMark" style="display: block; font-size: 12px; line-height: 25px; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none; color: #ff0000;">注：您可通过下方配置实现自定义排序项,可选排序项参数有：解密审批完成数:fileDecryptTotal,直接外发审批完成数:outSendTotal,定密审批完成数:changeFileLevelTotal,离线审批完成数:offlineTotal,离线审批同意数:offlineApprove,打印审批完成数:filePrintTotal,取消文档水印审批完成数:cancelWatermarkTotal,文件外发审批完成数:sensitiveFileOutSendTotal,行为管控审批完成数:behaviorControlTotal,阅读权限转换审批完成数:readPermissionConv，只可以选择一项指标作为排序项</label></div>
    <div class="sortNameParameter" style="display: block; color: #000000; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none;">sortName:fileDecryptTotal</label></div>
    <div> </div>
</div>
<div>
    <h2>二、敏感内容识别</h2>
</div>
<div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;">
    <div style="height: 40px; line-height: 40px; text-align: center; font-size: 14px; font-weight: bold; color: #000000;">敏感关键字对比趋势表</div>
    <table style="border-collapse: collapse; width: 100%; text-align: center;" border="1">
        <tbody>
        <tr style="color: #ffffff; height: 40px; line-height: 40px; font-weight: 400;">
            <td style="background-color: #4874cb;">{{sensitiveKeywordLogCompare}}统计项</td>
            <td style="background-color: #4874cb;">当前{{curDate}}</td>
            <td style="background-color: #4874cb;">{{qoqLastDate}}</td>
            <td style="background-color: #4874cb;">环比</td>
            <td style="background-color: #4874cb;">{{yoyLastDate}}</td>
            <td style="background-color: #4874cb;">同比</td>
        </tr>
        <tr style="color: #000000; height: 40px; line-height: 40px; font-weight: 400;">
            <td>[name]</td>
            <td>[data]</td>
            <td>[qoqData]</td>
            <td>[qoqDataPercent]</td>
            <td>[yoyData]</td>
            <td>[yoyDataPercent]</td>
        </tr>
        </tbody>
    </table>
    <div class="compareMark" style="display: block; font-size: 12px; line-height: 25px; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none; color: #ff0000;">注：表格中的[qoqDataPercent]和[yoyDataPercent]分别代表环比和同比变化计算规则，您可以根据自身需求，对规则进行修改。以环比表达式举例解析规则如下：</label></div>
    <div class="compareMark" style="display: block; font-size: 12px; color: #ff0000; line-height: 25px; font-weight: 400;"><label class="compareMarkLabel" style="display: none;">如果上一周期qoqData为0，当前data为0 ，则环比变化为0%；当前data不为0，则环比变化为∞；</label></div>
    <div class="compareMark" style="display: block; font-size: 12px; color: #ff0000; line-height: 25px; font-weight: 400;"><label class="compareMarkLabel" style="display: none; color: #ff0000; text-align: left;">如果上一周期qoqData不为0，就根据表达式(data-qoqData)*100.0/qoqData)计算环比变化；%.2f表示精确到小数点后2位。</label></div>
    <div class="qoq compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none;">qoqData == 0 ? (data == 0 ? '0' : '∞') : T(java.lang.String).format('%.2f%%',(data-qoqData)*100.0/qoqData)</label></div>
    <div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none;">yoyData == 0 ? (data == 0 ? '0' : '∞') : T(java.lang.String).format('%.2f%%',(data-yoyData)*100.0/yoyData)</label></div>
    <div> </div>
    <div> </div>
</div></body></html>