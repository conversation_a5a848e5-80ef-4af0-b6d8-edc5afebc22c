<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head>    <title>WangEditor Display HTML</title>    <meta charset="UTF-8" /></head><body><p> </p>
<p> </p>
<p> </p>
<p class="MsoNormal" style="margin-top: 8.0500pt; margin-bottom: 8.0500pt; margin-left: 6.0000pt; text-align: center;" align="center"> </p>
<p class="MsoNormal" style="margin-top: 8.0500pt; margin-bottom: 8.0500pt; margin-left: 6.0000pt; text-align: center;" align="center"><strong style="mso-bidi-font-weight: normal;"><span style="mso-spacerun: 'yes'; font-family: 宋体; mso-fareast-font-family: Calibri; mso-bidi-font-family: 'Times New Roman'; color: #000000; mso-ansi-font-weight: bold; mso-ansi-font-style: normal; font-size: 36.0000pt;"><span style="font-family: Calibri;">运</span></span></strong></p>
<p class="MsoNormal" style="margin-top: 8.0500pt; margin-bottom: 8.0500pt; margin-left: 6.0000pt; text-align: center;" align="center"><strong style="mso-bidi-font-weight: normal;"><span style="mso-spacerun: 'yes'; font-family: 宋体; mso-fareast-font-family: Calibri; mso-bidi-font-family: 'Times New Roman'; color: #000000; mso-ansi-font-weight: bold; mso-ansi-font-style: normal; font-size: 36.0000pt;"><span style="font-family: Calibri;">营</span></span></strong></p>
<p class="MsoNormal" style="margin-top: 8.0500pt; margin-bottom: 8.0500pt; margin-left: 6.0000pt; text-align: center;" align="center"><strong style="mso-bidi-font-weight: normal;"><span style="mso-spacerun: 'yes'; font-family: 宋体; mso-fareast-font-family: Calibri; mso-bidi-font-family: 'Times New Roman'; color: #000000; mso-ansi-font-weight: bold; mso-ansi-font-style: normal; font-size: 36.0000pt;"><span style="font-family: Calibri;">分</span></span></strong></p>
<p class="MsoNormal" style="margin-top: 8.0500pt; margin-bottom: 8.0500pt; margin-left: 6.0000pt; text-align: center;" align="center"><strong style="mso-bidi-font-weight: normal;"><span style="mso-spacerun: 'yes'; font-family: 宋体; mso-fareast-font-family: Calibri; mso-bidi-font-family: 'Times New Roman'; color: #000000; mso-ansi-font-weight: bold; mso-ansi-font-style: normal; font-size: 36.0000pt;"><span style="font-family: Calibri;">析</span></span></strong></p>
<p class="MsoNormal" style="margin-top: 8.0500pt; margin-bottom: 8.0500pt; margin-left: 6.0000pt; text-align: center;" align="center"><strong style="mso-bidi-font-weight: normal;"><span style="mso-spacerun: 'yes'; font-family: 宋体; mso-fareast-font-family: Calibri; mso-bidi-font-family: 'Times New Roman'; color: #000000; mso-ansi-font-weight: bold; mso-ansi-font-style: normal; font-size: 36.0000pt;"><span style="font-family: Calibri;">报</span></span></strong></p>
<p class="MsoNormal" style="margin-top: 8.0500pt; margin-bottom: 8.0500pt; margin-left: 6.0000pt; text-align: center;" align="center"><strong style="mso-bidi-font-weight: normal;"><span style="mso-spacerun: 'yes'; font-family: 宋体; mso-fareast-font-family: Calibri; mso-bidi-font-family: 'Times New Roman'; color: #000000; mso-ansi-font-weight: bold; mso-ansi-font-style: normal; font-size: 36.0000pt;"><span style="font-family: Calibri;">告</span></span></strong></p>
<p class="MsoNormal" style="margin-top: 8.0500pt; margin-bottom: 8.0500pt; margin-left: 6.0000pt; text-align: center;" align="center"> </p>
<table class="MsoNormalTable" style="border-collapse: collapse; margin-left: auto; border: none; margin-right: auto;" border="0" cellspacing="0" cellpadding="0.75pt 4.05pt">
    <tbody>
    <tr style="height: 45.0000pt;">
        <td style="width: 104pt; padding: 0.75pt 4.05pt; border-left: none; border-right: none; border-top: none; border-bottom: 1pt outset #000000; text-align: center;" valign="top" width="138">
            <p class="MsoNormal" style="margin-top: 13.4500pt; margin-bottom: 13.4500pt; margin-left: 11.4000pt; text-align: center; line-height: 150%;" align="center"><span style="font-family: simsun, serif; line-height: 150%; color: #000000; font-size: 16px;">报告周期：</span></p>
        </td>
        <td style="width: 452.85pt; padding: 0.75pt 4.05pt; border-left: none; border-right: none; border-top: none; border-bottom: 1pt outset #000000; text-align: center;" valign="top" width="603">
            <p class="MsoNormal" style="margin-top: 13.4500pt; margin-bottom: 13.4500pt; margin-left: 11.4000pt; text-align: justify; text-justify: inter-ideograph; line-height: 150%;" align="justify"><span style="font-family: simsun, serif; line-height: 150%; color: #05073b; font-size: 16px;"><span style="font-size: 16px;">{{</span>dateStr<span style="font-size: 16px;">}}</span></span></p>
        </td>
    </tr>
    <tr style="height: 45.0000pt;">
        <td style="width: 104pt; padding: 0.75pt 4.05pt; border-left: none; border-right: none; border-top: none; border-bottom: 1pt outset #000000; text-align: center;" valign="top" width="138">
            <p class="MsoNormal" style="margin-top: 13.4500pt; margin-bottom: 13.4500pt; margin-left: 11.4000pt; text-align: center; line-height: 150%;" align="center"><span style="font-family: simsun, serif; line-height: 150%; color: #000000; font-size: 16px;">统计范围：</span></p>
        </td>
        <td style="width: 452.85pt; padding: 0.75pt 4.05pt; border-left: none; border-right: none; border-top: none; border-bottom: 1pt outset #000000; text-align: center;" valign="top" width="603">
            <p class="MsoNormal" style="margin-top: 13.4500pt; margin-bottom: 13.4500pt; margin-left: 11.4000pt; text-align: justify; text-justify: inter-ideograph; line-height: 150%;" align="justify"><span style="font-family: simsun, serif;"><span style="font-size: 16px;"><span style="font-size: 16px;"><span style="text-align: center;">{{statisticalRange}}</span></span></span></span></p>
        </td>
    </tr>
    <tr style="height: 45.0000pt;">
        <td style="width: 104pt; padding: 0.75pt 4.05pt; border-left: none; border-right: none; border-top: none; border-bottom: 1pt outset #000000; text-align: center;" valign="top" width="138">
            <p class="MsoNormal" style="margin-top: 13.4500pt; margin-bottom: 13.4500pt; margin-left: 11.4000pt; text-align: center; line-height: 150%;" align="center"><span style="font-family: simsun, serif; line-height: 150%; color: #000000; font-size: 16px;">报告目的：</span></p>
        </td>
        <td style="width: 452.85pt; padding: 0.75pt 4.05pt; border-left: none; border-right: none; border-top: none; border-bottom: 1pt outset #000000; text-align: center;" valign="top" width="603">
            <p class="MsoNormal" style="margin-top: 13.4500pt; margin-bottom: 13.4500pt; margin-left: 11.4000pt; text-align: justify; text-justify: inter-ideograph; line-height: 150%;" align="justify"><span style="font-family: simsun, serif; line-height: 150%; color: #000000; font-size: 16px;">分析数据安全运营状况，识别潜在风险，提出改进措施</span></p>
        </td>
    </tr>
    </tbody>
</table>
<p class="MsoNormal" style="margin-top: 0.0000pt; margin-bottom: 0.0000pt; text-align: left; line-height: 114%;"><span style="mso-spacerun: 'yes'; font-family: Calibri; mso-bidi-font-family: 'Times New Roman'; font-size: 11.0000pt;"> </span></p>
<p class="MsoNormal" style="margin-top: 0.0000pt; margin-bottom: 0.0000pt; text-align: left; line-height: 114%;"><span style="mso-spacerun: 'yes'; font-family: Calibri; mso-bidi-font-family: 'Times New Roman'; font-size: 11.0000pt;"> </span></p>
<p class="MsoNormal" style="margin-top: 0.0000pt; margin-bottom: 0.0000pt; text-align: left; line-height: 114%;"><span style="mso-spacerun: 'yes'; font-family: Calibri; mso-bidi-font-family: 'Times New Roman'; font-size: 11.0000pt;"> </span></p>
<div> </div>
<div>
    <p class="MsoNormal" style="line-height: 150%;"><span style="font-size: 24px;"><strong><span style="font-family: 微软雅黑; line-height: 150%; font-weight: bold;"><span style="font-family: 微软雅黑;">一、数据安全运营数据详情</span></span></strong></span></p>
    <pre class="MsoNormal" style="line-height: 150%;"><span style="font-size: 16px; font-family: SimHei, sans-serif;">1、数据安全方面，本周批量加密文件总数为{{encOrDecTotal}}个，加密文件总数为{{encFileTotal}}个。</span></pre>
    <div style="height: 40px; line-height: 40px; text-align: center; font-size: 14px; font-weight: bold; color: #000000;">数据安全对比趋势表</div>
    <table style="border-collapse: collapse; width: 100%; text-align: center;" border="1">
        <tbody>
        <tr style="color: #ffffff; height: 40px; line-height: 40px; font-weight: 400;">
            <td style="background-color: #4874cb;">{{dataSecurityCompare}}统计项</td>
            <td style="background-color: #4874cb;">当前{{curDate}}</td>
            <td style="background-color: #4874cb;">{{qoqLastDate}}</td>
            <td style="background-color: #4874cb;">环比</td>
            <td style="background-color: #4874cb;">{{yoyLastDate}}</td>
            <td style="background-color: #4874cb;">同比</td>
        </tr>
        <tr style="color: #000000; height: 40px; line-height: 40px; font-weight: 400;">
            <td>[name]</td>
            <td>[data]</td>
            <td>[qoqData]</td>
            <td>[qoqDataPercent]</td>
            <td>[yoyData]</td>
            <td>[yoyDataPercent]</td>
        </tr>
        </tbody>
    </table>
    <div class="compareMark" style="display: block; font-size: 12px; line-height: 25px; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none; color: #ff0000;">注：表格中的[qoqDataPercent]和[yoyDataPercent]分别代表环比和同比变化计算规则，您可以根据自身需求，对规则进行修改。以环比表达式举例解析规则如下：</label></div>
    <div class="compareMark" style="display: block; font-size: 12px; color: #ff0000; line-height: 25px; font-weight: 400;"><label class="compareMarkLabel" style="display: none;">如果上一周期qoqData为0，当前data为0 ，则环比变化为0%；当前data不为0，则环比变化为∞；</label></div>
    <div class="compareMark" style="display: block; font-size: 12px; color: #ff0000; line-height: 25px; font-weight: 400;"><label class="compareMarkLabel" style="display: none; color: #ff0000; text-align: left;">如果上一周期qoqData不为0，就根据表达式(data-qoqData)*100.0/qoqData)计算环比变化；%.2f表示精确到小数点后2位。</label></div>
    <div class="qoq compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none;">qoqData == 0 ? (data == 0 ? '0' : '∞') : T(java.lang.String).format('%.2f%%',(data-qoqData)*100.0/qoqData)</label></div>
    <div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none;">yoyData == 0 ? (data == 0 ? '0' : '∞') : T(java.lang.String).format('%.2f%%',(data-yoyData)*100.0/yoyData)</label></div>
</div>
<div> </div>
<div>
    <div style="height: 40px; line-height: 40px; text-align: center; font-size: 14px; font-weight: bold; color: #000000;">数据安全报表</div>
    <table style="border-collapse: collapse; width: 100%; text-align: center;" border="1">
        <tbody>
        <tr style="color: #ffffff; height: 40px; line-height: 40px; font-weight: 400;">
            <td style="background-color: #4874cb;">{{dataSecurity}} 统计对象</td>
            <td style="background-color: #4874cb;">批量加解密数</td>
            <td style="background-color: #4874cb;">批量加密数</td>
            <td style="background-color: #4874cb;">批量解密数</td>
            <td style="background-color: #4874cb;">全盘扫描文件数</td>
            <td style="background-color: #4874cb;">全盘扫描敏感文件数</td>
            <td style="background-color: #4874cb;">全盘扫描非敏感文件数</td>
            <td style="background-color: #4874cb;">全盘扫描加密文件数</td>
            <td style="background-color: #4874cb;">全盘扫描解密文件数</td>
            <td style="background-color: #4874cb;">外传文件数</td>
            <td style="background-color: #4874cb;">直接外发文件数</td>
            <td style="background-color: #4874cb;">直接外发文件大小</td>
            <td style="background-color: #4874cb;">申请外发文件数</td>
            <td style="background-color: #4874cb;">申请外传文件大小</td>
            <td style="background-color: #4874cb;">文件备份数</td>
            <td style="background-color: #4874cb;">文件备份大小</td>
            <td style="background-color: #4874cb;">加密文件数</td>
        </tr>
        <tr style="color: #000000; height: 40px; line-height: 40px; font-weight: 400;">
            <td>[countObject]</td>
            <td>[batchEncAndDec]</td>
            <td>[batchEnc]</td>
            <td>[batchDec]</td>
            <td>[diskScanFile]</td>
            <td>[diskScanSens]</td>
            <td>[diskScanNonSens]</td>
            <td>[diskScanEnc]</td>
            <td>[diskScanDec]</td>
            <td>[outboundFile]</td>
            <td>[directOutboundFile]</td>
            <td>[directOutboundFileSize]</td>
            <td>[applyOutboundFile]</td>
            <td>[applyOutboundFileSize]</td>
            <td>[fileBackup]</td>
            <td>[fileBackupSize]</td>
            <td>[encFile]</td>
        </tr>
        </tbody>
    </table>
    <div class="compareMark" style="display: block; font-size: 12px; line-height: 25px; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none; color: #ff0000;">注：您可通过下方配置实现自定义排序项,可选排序项参数有：批量加解密数:batchEncAndDec,全盘扫描文件数:diskScanFile,外传文件数:outboundFile,文件备份数:fileBackup,加密文件数:encFile，只可以选择一项指标作为排序项</label></div>
    <div class="sortNameParameter" style="display: block; color: #000000; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none;">sortName:batchEncAndDec</label></div>
    <div> </div>
</div>
<div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"><span style="font-family: SimHei, sans-serif; font-size: 16px;">2、审批管理方面，<span style="line-height: 150%;">审批总数为{{trwfeTotal}}个</span><span style="font-family: SimHei, sans-serif; color: #ff0000; font-size: 16px;">{{<span style="line-height: 150%;">trwfeTotal &lt; 1</span>00 ? '':'（建议关注）' }}<span style="color: #999999;">（模板支持Spring表达式，该表达式表示审批总数达到100时显示“建议关注”，模板使用时建议删除灰色字）</span></span><span style="line-height: 150%;">，</span>审批运营情况如下：</span></div>
<div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"> </div>
<div>
    <div style="height: 40px; line-height: 40px; text-align: center; font-size: 14px; font-weight: bold; color: #000000;">审批报表</div>
    <table style="border-collapse: collapse; width: 100%; text-align: center;" border="1">
        <tbody>
        <tr style="color: #ffffff; height: 40px; line-height: 40px; font-weight: 400;">
            <td style="background-color: #4874cb;">{{trwfeLog}} 部门</td>
            <td style="background-color: #4874cb;">解密审批完成数</td>
            <td style="background-color: #4874cb;">解密审批同意数</td>
            <td style="background-color: #4874cb;">解密审批拒绝数</td>
            <td style="background-color: #4874cb;">解密审批系统自动应答数</td>
            <td style="background-color: #4874cb;">直接外发审批完成数</td>
            <td style="background-color: #4874cb;">直接外发审批同意数</td>
            <td style="background-color: #4874cb;">直接外发审批拒绝数</td>
            <td style="background-color: #4874cb;">直接外发审批系统自动应答数</td>
            <td style="background-color: #4874cb;">定密审批完成数</td>
            <td style="background-color: #4874cb;">定密审批同意数</td>
            <td style="background-color: #4874cb;">定密审批拒绝数</td>
            <td style="background-color: #4874cb;">定密审批系统自动应答数</td>
            <td style="background-color: #4874cb;">离线审批完成数</td>
            <td style="background-color: #4874cb;">离线审批同意数</td>
            <td style="background-color: #4874cb;">离线审批拒绝数</td>
            <td style="background-color: #4874cb;">离线审批系统自动应答数</td>
            <td style="background-color: #4874cb;">打印审批完成数</td>
            <td style="background-color: #4874cb;">打印审批同意数</td>
            <td style="background-color: #4874cb;">打印审批拒绝数</td>
            <td style="background-color: #4874cb;">打印审批系统自动应答数</td>
            <td style="background-color: #4874cb;">取消文档水印审批完成数</td>
            <td style="background-color: #4874cb;">取消文档水印审批同意数</td>
            <td style="background-color: #4874cb;">取消文档水印审批拒绝数</td>
            <td style="background-color: #4874cb;">取消文档水印审批系统自动应答数</td>
            <td style="background-color: #4874cb;">文件外传审批完成数</td>
            <td style="background-color: #4874cb;">文件外传审批同意数</td>
            <td style="background-color: #4874cb;">文件外传审批拒绝数</td>
            <td style="background-color: #4874cb;">文件外传审批系统自动应答数</td>
            <td style="background-color: #4874cb;">行为管控审批完成数</td>
            <td style="background-color: #4874cb;">行为管控审批同意数</td>
            <td style="background-color: #4874cb;">行为管控审批拒绝数</td>
            <td style="background-color: #4874cb;">行为管控审批系统自动应答数</td>
            <td style="background-color: #4874cb;">阅读权限转换审批完成数</td>
            <td style="background-color: #4874cb;">阅读权限转换审批同意数</td>
            <td style="background-color: #4874cb;">阅读权限转换审批拒绝数</td>
            <td style="background-color: #4874cb;">阅读权限转换审批系统自动应答数</td>
        </tr>
        <tr style="color: #000000; height: 40px; line-height: 40px; font-weight: 400;">
            <td>[countObject]</td>
            <td>[fileDecryptTotal]</td>
            <td>[fileDecApprove]</td>
            <td>[fileDecReject]</td>
            <td>[fileDecAutoApprove]</td>
            <td>[outSendTotal]</td>
            <td>[outSendApprove]</td>
            <td>[outSendReject]</td>
            <td>[outSendAutoApprove]</td>
            <td>[changeFileLevelTotal]</td>
            <td>[changeFileLevelApprove]</td>
            <td>[changeFileLevelReject]</td>
            <td>[changeFileLevelAutoApprove]</td>
            <td>[offlineTotal]</td>
            <td>[offlineApprove]</td>
            <td>[offlineReject]</td>
            <td>[offlineAutoApprove]</td>
            <td>[filePrintTotal]</td>
            <td>[filePrintApprove]</td>
            <td>[filePrintReject]</td>
            <td>[filePrintAutoApprove]</td>
            <td>[cancelWatermarkTotal]</td>
            <td>[cancelWatermarkApprove]</td>
            <td>[cancelWatermarkReject]</td>
            <td>[cancelWatermarkAutoApprove]</td>
            <td>[sensitiveFileOutSendTotal]</td>
            <td>[sensitiveFileOutSendApprove]</td>
            <td>[sensitiveFileOutSendReject]</td>
            <td>[sensitiveFileOutSendAutoApprove]</td>
            <td>[behaviorControlTotal]</td>
            <td>[behaviorControlApprove]</td>
            <td>[behaviorControlReject]</td>
            <td>[behaviorControlAutoApprove]</td>
            <td>[readPermissionConv]</td>
            <td>[readPermissionConvApprove]</td>
            <td>[readPermissionConvReject]</td>
            <td>[readPermissionConvAutoApprove]</td>
        </tr>
        </tbody>
    </table>
    <div class="compareMark" style="display: block; font-size: 12px; line-height: 25px; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none; color: #ff0000;">注：您可通过下方配置实现自定义排序项,可选排序项参数有：解密审批完成数:fileDecryptTotal,直接外发审批完成数:outSendTotal,定密审批完成数:changeFileLevelTotal,离线审批完成数:offlineTotal,离线审批同意数:offlineApprove,打印审批完成数:filePrintTotal,取消文档水印审批完成数:cancelWatermarkTotal,文件外传审批完成数:sensitiveFileOutSendTotal,行为管控审批完成数:behaviorControlTotal,阅读权限转换审批完成数:readPermissionConv，只可以选择一项指标作为排序项</label></div>
    <div class="sortNameParameter" style="display: block; color: #000000; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none;">sortName:fileDecryptTotal</label></div>
    <div> </div>
</div>
<div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;">
    <div style="height: 40px; line-height: 40px; text-align: center; font-size: 14px; font-weight: bold; color: #000000;">审批对比趋势表</div>
    <table style="border-collapse: collapse; width: 100%; text-align: center;" border="1">
        <tbody>
        <tr style="color: #ffffff; height: 40px; line-height: 40px; font-weight: 400;">
            <td style="background-color: #4874cb;">{{trwfeLogCompare}}统计项</td>
            <td style="background-color: #4874cb;">当前{{curDate}}</td>
            <td style="background-color: #4874cb;">{{qoqLastDate}}</td>
            <td style="background-color: #4874cb;">环比</td>
            <td style="background-color: #4874cb;">{{yoyLastDate}}</td>
            <td style="background-color: #4874cb;">同比</td>
        </tr>
        <tr style="color: #000000; height: 40px; line-height: 40px; font-weight: 400;">
            <td>[name]</td>
            <td>[data]</td>
            <td>[qoqData]</td>
            <td>[qoqDataPercent]</td>
            <td>[yoyData]</td>
            <td>[yoyDataPercent]</td>
        </tr>
        </tbody>
    </table>
    <div class="compareMark" style="display: block; font-size: 12px; line-height: 25px; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none; color: #ff0000;">注：表格中的[qoqDataPercent]和[yoyDataPercent]分别代表环比和同比变化计算规则，您可以根据自身需求，对规则进行修改。以环比表达式举例解析规则如下：</label></div>
    <div class="compareMark" style="display: block; font-size: 12px; color: #ff0000; line-height: 25px; font-weight: 400;"><label class="compareMarkLabel" style="display: none;">如果上一周期qoqData为0，当前data为0 ，则环比变化为0%；当前data不为0，则环比变化为∞；</label></div>
    <div class="compareMark" style="display: block; font-size: 12px; color: #ff0000; line-height: 25px; font-weight: 400;"><label class="compareMarkLabel" style="display: none; color: #ff0000; text-align: left;">如果上一周期qoqData不为0，就根据表达式(data-qoqData)*100.0/qoqData)计算环比变化；%.2f表示精确到小数点后2位。</label></div>
    <div class="qoq compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none;">qoqData == 0 ? (data == 0 ? '0' : '∞') : T(java.lang.String).format('%.2f%%',(data-qoqData)*100.0/qoqData)</label></div>
    <div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none;">yoyData == 0 ? (data == 0 ? '0' : '∞') : T(java.lang.String).format('%.2f%%',(data-yoyData)*100.0/yoyData)</label></div>
</div>
<div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"> </div>
<div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"><span style="font-size: 24px;"><strong style="text-align: justify;"><span style="font-family: 微软雅黑; line-height: 150%;">二、敏感内容数据详情</span></strong></span></div>
<div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"><span style="font-size: 24px;"><span style="font-family: 微软雅黑; line-height: 150%;"><span style="font-size: 16px; font-family: SimHei, sans-serif;">1、敏感关键字触发次数为{{sensitiveKeywordTriggerTotal}}次，常规检测-数据泄露违规记录总数为{{generalScanViolateTotal}}条，零星检测-数据泄露违规记录总数为{{dripScanViolateLogTotal}}条。</span></span></span></div>
<div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;">
    <div>
        <div class="dragImageAllDiv" style="width: 100%;"> </div>
    </div>
</div>
<div> </div>
<div>
    <div class="dragImageAllDiv" style="width: 100%;">
        <div style="text-align: center; font-size: 15px; font-weight: bold; height: 40px; line-height: 40px;">敏感关键字报表</div>
        <div class="imageSrc" style="text-align: center;">
            <div class="imageType" style="display: none;">pie</div>
            <div class="imageCode" style="display: none;">47</div>
            <div class="imageBusinessImg" style="display: block; text-align: center;">{{@img47_1745288684963}}</div>
            <div class="dimBaseTypeDiv" style="display: none; position: relative; text-align: center;"> </div>
            <img class="imgShow" style="display: none; width: 80px; height: 80px; margin: auto;" src="data:image/png;base64,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" />
            <div class="compareMark" style="display: block; font-size: 12px; line-height: 25px; font-weight: 400; text-align: left; padding-left: 40px;"><label class="compareMarkLabel" style="display: none; color: #ff0000;">注：您可通过下方配置实现自定义排序项,可选排序参数有：敏感关键字触发次数:triggerCountSum，只可以选择一项指标作为排序项</label></div>
            <div class="sortNameParameterChart" style="display: block; color: #000000; font-weight: 400; text-align: left; padding-left: 40px;"><label class="compareMarkLabel" style="display: none;">sortName:triggerCountSum</label></div>
        </div>
    </div>
    <div> </div>
    <div> </div>
</div>
<div>
    <div class="dragImageAllDiv" style="width: 100%;">
        <div style="text-align: center; font-size: 15px; font-weight: bold; height: 40px; line-height: 40px;">敏感内容征兆报表</div>
        <div class="imageSrc" style="text-align: center;">
            <div class="imageType" style="display: none;">bar</div>
            <div class="imageCode" style="display: none;">56</div>
            <div class="imageBusinessImg" style="display: block; text-align: center;">{{@img56_1745288694111}}</div>
            <div class="dimBaseTypeDiv" style="display: none; position: relative; text-align: center;"> </div>
            <img class="imgShow" style="display: none; width: 80px; height: 80px; margin: auto;" src="data:image/png;base64,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" /></div>
    </div>
    <div> </div>
    <div> </div>
</div>
<div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;">
    <div><span style="font-size: 24px;"><strong style="text-align: justify;"><span style="font-family: 微软雅黑; line-height: 150%;">三、桌面管理运营数据详情</span></strong></span></div>
</div>
<div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;">
    <p class="MsoNormal" style="mso-para-margin-left: 0.0000gd; text-align: justify; text-justify: inter-ideograph; line-height: 150%; mso-list: l0 level1 lfo1;" align="justify"><!-- [if !supportLists]--><span style="font-size: 16px; font-family: SimHei, sans-serif;"><span style="mso-list: Ignore;">1. <span style="line-height: 150%;">硬件资产变更次数为{{hardwareAssetChangeTimes}}次，软件资产变更次数为{{softwareAssetChangeTimes}}次。</span></span><!--[endif]-->软硬件资产变更情况：</span></p>
</div>
<div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;">
    <div style="height: 40px; line-height: 40px; text-align: center; font-size: 14px; font-weight: bold; color: #000000;">硬件资产对比趋势表</div>
    <table style="border-collapse: collapse; width: 100%; text-align: center;" border="1">
        <tbody>
        <tr style="color: #ffffff; height: 40px; line-height: 40px; font-weight: 400;">
            <td style="background-color: #4874cb;">{{HardwareAssetCompare}}统计项</td>
            <td style="background-color: #4874cb;">当前{{curDate}}</td>
            <td style="background-color: #4874cb;">{{qoqLastDate}}</td>
            <td style="background-color: #4874cb;">环比</td>
            <td style="background-color: #4874cb;">{{yoyLastDate}}</td>
            <td style="background-color: #4874cb;">同比</td>
        </tr>
        <tr style="color: #000000; height: 40px; line-height: 40px; font-weight: 400;">
            <td>[name]</td>
            <td>[data]</td>
            <td>[qoqData]</td>
            <td>[qoqDataPercent]</td>
            <td>[yoyData]</td>
            <td>[yoyDataPercent]</td>
        </tr>
        </tbody>
    </table>
    <div class="compareMark" style="display: block; font-size: 12px; line-height: 25px; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none; color: #ff0000;">注：表格中的[qoqDataPercent]和[yoyDataPercent]分别代表环比和同比变化计算规则，您可以根据自身需求，对规则进行修改。以环比表达式举例解析规则如下：</label></div>
    <div class="compareMark" style="display: block; font-size: 12px; color: #ff0000; line-height: 25px; font-weight: 400;"><label class="compareMarkLabel" style="display: none;">如果上一周期qoqData为0，当前data为0 ，则环比变化为0%；当前data不为0，则环比变化为∞；</label></div>
    <div class="compareMark" style="display: block; font-size: 12px; color: #ff0000; line-height: 25px; font-weight: 400;"><label class="compareMarkLabel" style="display: none; color: #ff0000; text-align: left;">如果上一周期qoqData不为0，就根据表达式(data-qoqData)*100.0/qoqData)计算环比变化；%.2f表示精确到小数点后2位。</label></div>
    <div class="qoq compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none;">qoqData == 0 ? (data == 0 ? '0' : '∞') : T(java.lang.String).format('%.2f%%',(data-qoqData)*100.0/qoqData)</label></div>
    <div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none;">yoyData == 0 ? (data == 0 ? '0' : '∞') : T(java.lang.String).format('%.2f%%',(data-yoyData)*100.0/yoyData)</label></div>
    <div> </div>
</div>
<div>
    <div style="height: 40px; line-height: 40px; text-align: center; font-size: 14px; font-weight: bold; color: #000000;">软件卸载数量对比趋势表</div>
    <table style="border-collapse: collapse; width: 100%; text-align: center;" border="1">
        <tbody>
        <tr style="color: #ffffff; height: 40px; line-height: 40px; font-weight: 400;">
            <td style="background-color: #4874cb;">{{appUninstallCompare}}统计项</td>
            <td style="background-color: #4874cb;">当前{{curDate}}</td>
            <td style="background-color: #4874cb;">{{qoqLastDate}}</td>
            <td style="background-color: #4874cb;">环比</td>
            <td style="background-color: #4874cb;">{{yoyLastDate}}</td>
            <td style="background-color: #4874cb;">同比</td>
        </tr>
        <tr style="color: #000000; height: 40px; line-height: 40px; font-weight: 400;">
            <td>[name]</td>
            <td>[data]</td>
            <td>[qoqData]</td>
            <td>[qoqDataPercent]</td>
            <td>[yoyData]</td>
            <td>[yoyDataPercent]</td>
        </tr>
        </tbody>
    </table>
    <div class="compareMark" style="display: block; font-size: 12px; line-height: 25px; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none; color: #ff0000;">注：表格中的[qoqDataPercent]和[yoyDataPercent]分别代表环比和同比变化计算规则，您可以根据自身需求，对规则进行修改。以环比表达式举例解析规则如下：</label></div>
    <div class="compareMark" style="display: block; font-size: 12px; color: #ff0000; line-height: 25px; font-weight: 400;"><label class="compareMarkLabel" style="display: none;">如果上一周期qoqData为0，当前data为0 ，则环比变化为0%；当前data不为0，则环比变化为∞；</label></div>
    <div class="compareMark" style="display: block; font-size: 12px; color: #ff0000; line-height: 25px; font-weight: 400;"><label class="compareMarkLabel" style="display: none; color: #ff0000; text-align: left;">如果上一周期qoqData不为0，就根据表达式(data-qoqData)*100.0/qoqData)计算环比变化；%.2f表示精确到小数点后2位。</label></div>
    <div class="qoq compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none;">qoqData == 0 ? (data == 0 ? '0' : '∞') : T(java.lang.String).format('%.2f%%',(data-qoqData)*100.0/qoqData)</label></div>
    <div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"><label class="compareMarkLabel" style="display: none;">yoyData == 0 ? (data == 0 ? '0' : '∞') : T(java.lang.String).format('%.2f%%',(data-yoyData)*100.0/yoyData)</label></div>
</div>
<div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;"> </div>
<div class="yoy compareMark" style="display: block; color: #000000; font-weight: 400; text-align: left;">
    <p class="MsoNormal" style="text-indent: 21.0000pt; mso-char-indent-count: 0.0000; text-align: right;" align="right"><span style="font-size: 16px; font-family: SimHei, sans-serif;">{{new java.text.SimpleDateFormat('yyyy年MM月dd日').format(new <span style="text-align: left;">java.util.Date())}}</span></span></p>
    <p class="MsoNormal" style="text-indent: 21.0000pt; mso-char-indent-count: 0.0000; text-align: right;" align="right"><span style="font-family: SimHei, sans-serif; font-size: 16px;">xxxx公司</span></p>
</div>
<div>
    <div> </div>
    <div> </div>
</div></body></html>