<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE root>
<!--
    name: 解说模板名称
    code：BeanShell代码
    report-code: 适用图表编码
-->
<root>
    <interpretation id="0" name="折线图基础解说模板">
        <code>
            <![CDATA[
                import com.tipray.dlp.bean.dto.EChartsOptionDTO;
                List seriesList = echartOptionDTO.getOption().getSeries();
                if (seriesList != null && !seriesList.isEmpty()) {
                    StringBuilder explain = new StringBuilder();
                    Long curNum = null, lastNum = null;
                    com.github.abel533.echarts.series.Series series0 = seriesList.get(0);
                    EChartsOptionDTO lastEchartsOption = null;
                    Map params = new HashMap();
                    if (series0.getData().size() == 1) {
                        com.tipray.dlp.bean.vo.OperationalReportVo lastTrendVo = cn.hutool.core.bean.BeanUtil.toBean(trendVo, com.tipray.dlp.bean.vo.OperationalReportVo.class);
                        lastTrendVo.setDimValue(Long.valueOf(com.tipray.dlp.util.ReportUtil.calcTrendGraphDate(dimBaseType, trendVo.getDimBaseType(), -2)));
                        lastEchartsOption = reportPanelController.getEchartOptionDTO(imgCode, lastTrendVo);
                    }
                    for (int i = 0; i < seriesList.size(); i++) {
                        com.github.abel533.echarts.series.Series series = seriesList.get(i);
                        String name = series.getName();
                        if (lastEchartsOption != null) {
                            curNum = Long.valueOf(series.getData().get(0).toString());
                            List lastData = lastEchartsOption.getOption().getSeries().get(i).getData();
                            lastNum = Long.valueOf(lastData.get(lastData.size() - 1).toString());
                        } else if (series.getData().size() > 1) {
                            List curData = series.getData();
                            curNum = Long.valueOf(curData.get(curData.size() - 1).toString());
                            lastNum = Long.valueOf(curData.get(curData.size() - 2).toString());
                        }
                        if (curNum == null || curNum.equals(0)) {
                            explain.append("本周期" + name + "无数据。\n");
                        } else {
                            /* 比较本周期与上一周期数据变化 */
                            params.put("name", name);
                            params.put("total", curNum);
                            params.put("lastTotal", lastNum);
                            StringBuilder temp = com.tipray.dlp.report.util.ReportUtils.calcDataChange("本周期{name}总数为{total}{lastDataStr}。\n", params, yaxisTypeCode);
                            explain.append(temp);
                        }
                    }
                    resultData.put("img" + imgCode + "_explain", explain);
                } else {
                    resultData.put("img" + imgCode + "_explain", "本周期无数据。");
                }
            ]]>
        </code>
    </interpretation>

    <interpretation id="1" name="饼图数据基础解说模板">
        <code>
            <![CDATA[
                import com.alibaba.fastjson.JSONObject;
                import com.alibaba.fastjson.JSONArray;
                import com.tipray.dlp.util.StringUtil;
                import cn.hutool.core.bean.BeanUtil;
                import com.tipray.dlp.bean.vo.OperationalReportVo;
                import com.tipray.dlp.bean.dto.EChartsOptionDTO;
                import com.tipray.dlp.report.util.ReportUtils;
                import com.tipray.dlp.util.ReportUtil;
                StringBuilder explain = new StringBuilder();

                /* 计算查询上一周期分布图数据的参数 */
                OperationalReportVo lastVo = BeanUtil.toBean(vo, OperationalReportVo.class);
                Long dimValue = ReportUtils.calcLastDate(lastVo.getDimBaseType(), lastVo.getDimValue());
                lastVo.setDimValue(dimValue);

                /* 获取上一周期数据，计算总和 */
                Double lastTotal = 0.0;
                Map lastMap = new HashMap();
                EChartsOptionDTO lastEchartsOption = reportPanelController.getEchartOptionDTO(imgCode, lastVo);
                JSONArray lastOriData = (JSONArray) lastEchartsOption.getOriData();
                List deptRates = new LinkedList();
                if (lastOriData != null && !lastOriData.isEmpty()) {
                    for (int i = 0; i < lastOriData.size(); i++) {
                        JSONObject oriDatum = lastOriData.getJSONObject(i);
                        Double lastV = oriDatum.getDouble(vo.getSortName());
                        lastTotal += lastV;
                        lastMap.put(oriDatum.getString("labelValue"), lastV);
                    }
                }

                List names = new LinkedList();
                Double maxValue = 0.0;
                JSONArray oriData = (JSONArray) echartOptionDTO.getOriData();
                Map map = new HashMap();
                for (int i = 0; i < oriData.size(); i++) {
                    JSONObject oriDatum = oriData.getJSONObject(i);
                    Double v = oriDatum.getDouble(vo.getSortName());
                    String labelValue = oriDatum.getString("labelValue");
                    map.put(labelValue, v);
                    if ("0".equals(labelValue)) {
                        continue;
                    } else if (maxValue.compareTo(v) == 0) {
                        names.add(oriDatum.getString("label"));
                    } else if (maxValue.compareTo(v) < 0) {
                        maxValue = v;
                        names.clear();
                        names.add(oriDatum.getString("label"));
                    }
                    Double lastV = lastMap.get(labelValue);
                    if (lastV != null) {
                        Double rate = (v - lastV) / lastV;
                        if (rate > 0.5) {
                            Object[] args1 = new Object[]{oriDatum.getString("label"), rate * 100};
                            deptRates.add(String.format("<span style=\"color: DarkOrange;\">%s</span>（%.2f%%）", args1));
                        }
                    } else if (v.compareTo(total * 0.5) > 0) {
                        Object[] args1 = new Object[]{oriDatum.getString("label")};
                        deptRates.add(String.format("<span style=\"color: DarkOrange;\">%s</span>", args1));
                    }
                }


                String reportName = ReportUtil.formatI18n(vo.getSortName(), echartOptionDTO.getOption().getTitle().getText());
                /* 比较本周期与上一周期数据变化 */
                StringBuilder temp = ReportUtils.calcDataChange("本周期" + reportName + "总数为<span style=\"color: #ff5959;\">%.0f次</span>%s。", total, Double.valueOf(lastTotal), 0);
                explain.append(temp);

                Object[] args2 = new Object[]{StringUtil.join(names, "、"), maxValue, deptRates.isEmpty() ? "。" : "，建议关注增长率较高的：" + StringUtil.join(deptRates, "，") + "。"};
                explain.append(String.format("<span style=\"color: #ff5959;\">%s</span>在本周期占比最多，达%.0f次%s\n", args2));

                resultData.put("img" + imgCode + "_explain", explain);
            ]]>
        </code>
    </interpretation>

    <interpretation id="2" name="柱状图数据基础解说模板">
        <code>
            <![CDATA[
                import com.github.abel533.echarts.axis.Axis;
                import com.github.abel533.echarts.series.Series;
                import com.tipray.dlp.util.StringUtil;
                List series = option.getSeries();
                List axis = option.getxAxis().get(0).getData();
                StringBuilder explain = new StringBuilder();
                for (Series s : series) {
                    String type = s.getName();
                    List data = s.getData();
                    Long maxValue = 0L, sumValue = 0L;
                    List names = new LinkedList();
                    for (int i = 0; i < data.size(); i++) {
                        Long v = Long.parseLong(data.get(i).toString());
                        if (Double.compare(v, maxValue) > 0) {
                            maxValue = v;
                            sumValue += v;
                            names.add(axis.get(i));
                        }
                    }
                    if (!names.isEmpty()) {
                        if (names.size() > 1) {
                            Object[] args = {type, maxValue, StringUtil.join(names, "、")};
                            explain.append(String.format("<div style=\"text-indent: 2em;\">当前周期%s最多为%s，分别是<span style=\"color: #ff5959;\">%s</span>", args));
                        } else {
                            Object[] args = {StringUtil.join(names, "、"), type, maxValue};
                            explain.append(String.format("<div style=\"text-indent: 2em;\">当前周期<span style=\"color: #ff5959;\">%s</span>%s最多，为%s", args));
                        }
                        if (!names.isEmpty() && Double.compare(maxValue * data.size() * 1.0 / sumValue, 1.5) > 0) {
                            explain.append("，远高于平均水平");
                        }
                        explain.append("。</div>");
                    }
                }
                resultData.put("img" + imgCode + "_explain", explain);
            ]]>
        </code>
    </interpretation>

    <interpretation id="3" name="柱状图数据解说模板一">
        <code>
            <![CDATA[
                import com.tipray.dlp.util.StringUtil;
                StringBuilder explain = new StringBuilder();
                int columnCount = dataset.getColumnCount();
                Double maxValue = (Double) dataset.getValue(0, 0);
                List typeName = new LinkedList();
                for (int j = 1; j < columnCount; j++) {
                    Double v = (Double) dataset.getValue(0, j);
                    if (v.compareTo(maxValue * 0.9) > 0) {
                        typeName.add(dataset.getColumnKey(j).toString());
                    } else {
                        break;
                    }
                }
                if (!typeName.isEmpty()) {
                    Object[] args = {dataset.getColumnKey(0), StringUtil.join(typeName, "、")};
                    explain.append(String.format("从数量上看，%s方式占比最高，%s紧随其后。\n", args));
                } else {
                    Object[] args = {dataset.getColumnKey(0)};
                    explain.append(String.format("从数量上看，%s方式占比最高。\n", args));
                }
                resultData.put("img" + imgCode + "_explain", explain);
            ]]>
        </code>
    </interpretation>

    <interpretation id="4" name="敏感关键字分布图解说" report-code="47">
        <code>
            <![CDATA[
                import com.tipray.dlp.util.StringUtil;
                import com.tipray.dlp.bean.ChartData;
                import com.tipray.dlp.bean.dto.EChartsOptionDTO;
                import com.tipray.dlp.bean.vo.OperationalReportVo;
                import com.tipray.dlp.report.util.ReportUtils;
                StringBuilder explain = new StringBuilder();
                Map params = new HashMap();
                if (!total.equals(0L)) {
                    EChartsOptionDTO departmentOption, keyWordOption;
                    /* 解说模板中需要分别查询按照操作员或终端部门查询数据和按照关键字查询数据，判断已经获取到的数据并赋值，减少一次查询*/
                    if (vo.getCountByObject().equals(3)) {
                        OperationalReportVo departmentVo = cn.hutool.core.bean.BeanUtil.toBean(vo, OperationalReportVo.class);
                        departmentVo.setCountByObject(1);
                        departmentOption = reportPanelController.getEchartOptionDTO(imgCode, departmentVo);
                        keyWordOption = echartOptionDTO;
                    } else {
                        departmentOption = echartOptionDTO;
                        OperationalReportVo keyWordVo = cn.hutool.core.bean.BeanUtil.toBean(vo, OperationalReportVo.class);
                        keyWordVo.setCountByObject(3);
                        keyWordOption = reportPanelController.getEchartOptionDTO(imgCode, keyWordVo);
                    }
                    /* 周期内触发敏感关键字触发次数最多的部门 */
                    List data = departmentOption.getOption().getSeries().get(0).getData();
                    List names = new LinkedList();
                    Long maxValue = 0L;
                    for (ChartData chartData : data) {
                        Long v = Long.parseLong(chartData.getValue().toString());
                        if (chartData.getId() > vo.getRecordSize()) {
                            continue;
                        } else if (maxValue.compareTo(v) == 0) {
                            names.add(chartData.getName());
                        } else {
                            maxValue = v;
                            names.clear();
                            names.add(chartData.getName());
                        }
                    }

                    /* 计算查询上一周期分布图数据的参数 */
                    OperationalReportVo lastVo = cn.hutool.core.bean.BeanUtil.toBean(vo, OperationalReportVo.class);
                    Long dimValue = ReportUtils.calcLastDate(lastVo.getDimBaseType(), lastVo.getDimValue());
                    lastVo.setDimValue(dimValue);

                    /* 获取上一周期数据，计算总和 */
                    Long lastTotal = 0L;
                    EChartsOptionDTO lastEchartsOption = reportPanelController.getEchartOptionDTO(imgCode, lastVo);
                    List lastData = lastEchartsOption.getOption().getSeries().get(0).getData();
                    if (lastData != null && !lastData.isEmpty()) {
                        for (Object item : lastData) {
                            lastTotal += Long.valueOf(((ChartData) item).getValue().toString());
                        }
                    }
                    /* 比较本周期与上一周期数据变化 */
                    params.put("total", total);
                    params.put("lastTotal", lastTotal);
                    StringBuilder temp = ReportUtils.calcDataChange("1、本周期敏感关键字触发总次数为<span style=\"color: #ff5959;\">{total}次</span>{lastDataStr}。", params, 0);
                    explain.append(temp);

                    Object[] args = new Object[]{StringUtil.join(names, "、"), maxValue * 100.0 / total};
                    explain.append(String.format("<span style=\"color: #ff5959;\">%s部门</span>在本周期触发敏感关键字触发次数最多，占比<span style=\"color: #ff5959;\">%.2f%%</span>。", args));

                    /* 关键字占比 */
                    List keyWordsList = (List) keyWordOption.getOriData();
                    if (keyWordsList != null && !keyWordsList.isEmpty()) {
                        List keyWords = new LinkedList();
                        Long maxValueLong = 0L;
                        int size = keyWordsList.size() >= vo.getRecordSize() ? vo.getRecordSize() : keyWordsList.size();
                        for (int i = 0; i < size; i++) {
                            Map item = (Map)keyWordsList.get(i);
                            String word = item.get("label").toString();
                            Long value = Long.valueOf(item.get("triggerCountSum").toString());
                            maxValueLong = maxValueLong > value ? maxValueLong : value;
                            keyWords.add("<span style=\"color: DarkOrange;\">" + word + "</span>(" + value + "次)");
                        }
                        explain.append("<div style=\"text-indent: 2em\">2、常触发的关键字是：" + StringUtil.join(keyWords, "、"));
                        if (maxValueLong.compareTo(1000L) > 0) {
                            explain.append("<span style=\"color: #ff5959;\">（关键字触发频率较高，建议关注或检查策略配置合理性）</span>");
                        }
                        explain.append("。</div>");
                    }

                } else {
                    explain.append("本周期无数据。");
                }
                resultData.put("img" + imgCode + "_explain", explain.toString());
            ]]>
        </code>
    </interpretation>

    <interpretation id="5" name="风险报表解说" report-code="60">
        <code>
            <![CDATA[
                import com.tipray.dlp.util.StringUtil;
                import com.alibaba.fastjson.JSONObject;
                import com.tipray.dlp.bean.dto.EChartsOptionDTO;
                import com.tipray.dlp.report.util.ReportUtils;
                import com.tipray.dlp.util.I18nUtils;
                import com.tipray.dlp.bean.vo.OperationalReportVo;
                import cn.hutool.core.bean.BeanUtil;
                import com.alibaba.fastjson.JSONArray;

                Long riskCount = riskData.getLong("riskCount");
                Map params = new HashMap();
                if (riskCount != null && riskCount != 0) {
                    StringBuilder explain = new StringBuilder();
                    explain.append("<div>");

                    /* 计算查询上一周期分布图数据的参数 */
                    OperationalReportVo lastVo = BeanUtil.toBean(vo, OperationalReportVo.class);
                    lastVo.setGroupType(0);
                    Long dimValue = ReportUtils.calcLastDate(lastVo.getDimBaseType(), lastVo.getDimValue());
                    lastVo.setDimValue(dimValue);
                    /* 获取上一周期数据，获取数据变化情况 */
                    EChartsOptionDTO lastEchartsOption = reportPanelController.getEchartOptionDTO(imgCode, lastVo);
                    JSONObject lastRiskData = (JSONObject) lastEchartsOption.getData();
                    Long lastRiskCount = lastRiskData.getLong("riskCount");
                    if (lastRiskCount != null) {
                        params.put("lastTotal", lastRiskCount);
                    }
                    params.put("total", riskCount);
                    StringBuilder temp = ReportUtils.calcDataChange("interpretation.riskReport1", params, 0, I18nUtils.get("pages.openTimes2"));
                    explain.append(temp);

                    Long maxCount = 0L;
                    JSONArray riskScatterList = riskData.getObject("riskScatterList", JSONArray.class);
                    JSONArray sortedRiskScatter = ReportUtils.sortByValue(riskScatterList, "eventCount");
                    maxCount = sortedRiskScatter.getJSONObject(0).getLong("eventCount");
                    double midHight = maxCount * 1.0 / 2;

                    /* 高频高风险 */
                    Long HFHR = 0L, HFLR = 0L, LFHR = 0L, LFLR = 0L;
                    List userStr = new LinkedList();
                    Boolean isUser = (riskScatterList.getJSONObject(0)).containsKey("userName");
                    for (int i = 0; i < sortedRiskScatter.size(); i++) {
                        JSONObject risk = sortedRiskScatter.getJSONObject(i);
                        Long riskLevel = Long.parseLong(risk.get("riskLevel").toString());
                        Long eventCount = Long.parseLong(risk.get("eventCount").toString());
                        if (Double.compare(eventCount.doubleValue(), midHight) > 0) {
                            if (riskLevel > 2) {
                                HFHR += eventCount;
                                userStr.add(isUser ? risk.get("userName") : risk.get("terminalName") + "（" + eventCount + I18nUtils.get("pages.openTimes2") + "）");
                            } else {
                                HFLR += eventCount;
                            }
                        } else {
                            if (riskLevel > 2) {
                                LFHR += eventCount;
                            } else {
                                LFLR += eventCount;
                            }
                        }
                    }
                    if (HFHR != 0 || LFHR != 0) {
                        if (HFHR == 0) {
                            params.clear();
                            params.put("LFHR", LFHR);
                            explain.append(I18nUtils.get("interpretation.riskReport2", params));
                        } else {
                            params.clear();
                            params.put("HR", HFHR + LFHR);
                            params.put("userOrTerm", isUser ? I18nUtils.get("pages.user") : I18nUtils.get("pages.terminal"));
                            params.put("user", StringUtil.join((userStr.size() > 3 ? userStr.subList(0, 3) : userStr), "、"));
                            explain.append(I18nUtils.get("interpretation.riskReport3", params));
                        }
                    } else {
                        if (HFLR != 0) {
                            params.clear();
                            params.put("HFLR", HFLR);
                            explain.append(I18nUtils.get("interpretation.riskReport4", params));
                        } else {
                            explain.append(I18nUtils.get("interpretation.riskReport5"));
                        }
                    }
                    explain.append("</div>");
                    resultData.put("riskScatter", explain.toString());

                    /* 风险类型 */
                    Map riskDistribute = riskData.getObject("riskDistribute", Map.class);
                    if (riskDistribute != null && !riskDistribute.isEmpty()) {
                        explain = new StringBuilder();
                        List maxRiskTypeNames = new LinkedList();
                        List otherRiskTypeNames = new LinkedList();
                        List typeDataList = ReportUtils.sortMapByValue(riskDistribute, maxRiskTypeNames, otherRiskTypeNames);
                        Map.Entry typeEntry = (Map.Entry) typeDataList.get(0);
                        Long maxRiskCount = Long.parseLong(typeEntry.getValue().toString());
                        Double proportion = maxRiskCount * maxRiskTypeNames.size() / (riskCount * 0.01);
                        Object[] args;
                        args = new Object[]{StringUtil.join(maxRiskTypeNames, "、"), maxRiskCount, proportion, StringUtil.join(otherRiskTypeNames, "、")};
                        if (maxRiskTypeNames.size() > 1) {
                            params.put("respectively", I18nUtils.get("interpretation.respectively"));
                            params.put("total", I18nUtils.get("interpretation.total"));
                            params.put("others", "");
                        } else {
                            params.put("respectively", "");
                            params.put("total", "");
                            params.put("others", otherRiskTypeNames.isEmpty() ? "" : I18nUtils.get("interpretation.others"));
                        }
                        explain.append(String.format(I18nUtils.get("interpretation.riskReport6", params), args));
                        resultData.put("riskDistribute", explain.toString());
                    }

                    /* 部门风险分析 */
                    Map deptData = riskData.getObject("deptDistribute", Map.class);
                    if (deptData != null && !deptData.isEmpty()) {
                        explain = new StringBuilder();
                        /* 部门风险情况排序, 获取触发风险最多的部门信息，和其余部门信息 */
                        List maxDeptNames = new LinkedList();
                        List otherDeptNames = new LinkedList();
                        List deptDataList = ReportUtils.sortMapByValue(deptData, maxDeptNames, otherDeptNames);
                        Map.Entry deptEntry = (Map.Entry) deptDataList.get(0);
                        Long maxDeptCount = Long.parseLong(deptEntry.getValue().toString());
                        Double deptProportion = maxDeptCount / (riskCount * 0.01);

                        Object[] args = new Object[]{StringUtil.join(maxDeptNames, "、"), maxDeptCount, deptProportion, StringUtil.join(otherDeptNames, "、")};
                        params.clear();
                        params.put("attention", (deptProportion.compareTo(30.0) < 0) ? "" : I18nUtils.get("interpretation.attention"));
                        if (maxDeptNames.size() > 1) {
                            params.put("respectively", I18nUtils.get("interpretation.respectively"));
                            params.put("total", I18nUtils.get("interpretation.total"));
                            params.put("others", "");
                        } else {
                            params.put("respectively", "");
                            params.put("total", "");
                            params.put("others", otherDeptNames.isEmpty() ? "" : I18nUtils.get("interpretation.others"));
                        }
                        String template1 = I18nUtils.get("interpretation.riskReport9", params);
                        explain.append(String.format(template1, args));
                        resultData.put("deptDistribute", explain.toString());
                    }

                    /* 终端风险分析 */
                    Map termOrUserDistribute = riskData.getObject("termOrUserDistribute", Map.class);
                    if (termOrUserDistribute != null && !termOrUserDistribute.isEmpty()) {
                        explain = new StringBuilder();
                        List maxTermNames = new LinkedList();
                        List otherTermNames = new LinkedList();
                        List termDataList = ReportUtils.sortMapByValue(termOrUserDistribute, maxTermNames, otherTermNames);
                        Map.Entry entry = (Map.Entry) termDataList.get(0);
                        Long maxTermCount = Long.parseLong(entry.getValue().toString());
                        Double proportion = maxTermCount / (riskCount * 0.01);
                        Object[] args;
                        args = new Object[]{StringUtil.join(maxTermNames, "、"), maxTermCount, proportion, StringUtil.join(otherTermNames, "、")};
                        params.clear();
                        params.put("userOrTerm", isUser ? I18nUtils.get("pages.user") : I18nUtils.get("pages.terminal"));
                        if (maxTermNames.size() > 1) {
                            params.put("respectively", I18nUtils.get("interpretation.respectively"));
                            params.put("total", I18nUtils.get("interpretation.total"));
                            params.put("others", "");
                        } else {
                            params.put("respectively", "");
                            params.put("total", "");
                            params.put("others", otherTermNames.isEmpty() ? "" : I18nUtils.get("interpretation.others"));
                        }
                        explain.append(String.format(I18nUtils.get("interpretation.riskReport11", params), args));
                        resultData.put("termOrUserDistribute", explain.toString());
                    }

                    /* 泄露方式风险分析 */
                    List maxLossTypeStr = new LinkedList();
                    List otherLossTypeStr = new LinkedList();
                    Map lossTypeData = riskData.getObject("lossTypeDistribute", Map.class);
                    if (!lossTypeData.isEmpty()) {
                        params.clear();
                        explain = new StringBuilder();
                        List lossTypeList = ReportUtils.sortMapByValue(lossTypeData, maxLossTypeStr, otherLossTypeStr);
                        Long maxLossTypeValue = lossTypeList.get(0).getValue();
                        if (maxLossTypeStr.size() > 1) {
                            params.put("respectively", I18nUtils.get("interpretation.respectively"));
                            params.put("total", I18nUtils.get("interpretation.total"));
                        } else {
                            params.put("respectively", "");
                            params.put("total", "");
                        }
                        params.put("others", otherLossTypeStr.isEmpty() ? "" : I18nUtils.get("interpretation.others"));
                        Object[] lossTypeArgs = new Object[]{StringUtil.join(maxLossTypeStr, "、"), maxLossTypeValue, 100.0 * maxLossTypeStr.size() * maxLossTypeValue / riskCount, StringUtil.join(otherLossTypeStr, "、")};
                        explain.append(String.format(I18nUtils.get("interpretation.riskReport12", params), lossTypeArgs));
                        resultData.put("lossTypeDistribute", explain.toString());
                    }
                }
            ]]>
        </code>
    </interpretation>

    <interpretation id="6" name="违规泄露方式统计报表解说" report-code="51,54">
        <code>
            <![CDATA[
                import com.tipray.dlp.util.StringUtil;
                import com.alibaba.fastjson.JSONObject;
                import com.tipray.dlp.bean.vo.OperationalReportVo;
                import com.tipray.dlp.bean.dto.EChartsOptionDTO;
                import cn.hutool.core.bean.BeanUtil;
                import com.tipray.dlp.report.util.ReportUtils;
                import com.alibaba.fastjson.JSONArray;
                import org.apache.commons.math3.stat.regression.SimpleRegression;
                import com.tipray.dlp.util.I18nUtils;

                StringBuilder explain = new StringBuilder();
                JSONObject itemValueMap = (JSONObject) data.get("itemValueMap");
                Long total = itemValueMap.getLong("violateTotal");

                /* 计算查询上一周期分布图数据的参数 */
                OperationalReportVo lastVo = cn.hutool.core.bean.BeanUtil.toBean(vo, OperationalReportVo.class);
                Long dimValue = ReportUtils.calcLastDate(lastVo.getDimBaseType(), lastVo.getDimValue());
                lastVo.setDimValue(dimValue);
                /* 获取上一周期数据，计算总和 */
                EChartsOptionDTO lastEchartsOption = reportPanelController.getEchartOptionDTO(imgCode, lastVo);
                JSONObject lastData = (JSONObject) lastEchartsOption.getData();
                StringBuilder temp;
                Map params = new HashMap();
                params.put("total", total);
                params.put("checkType", imgCode == 51 ? I18nUtils.get("pages.nomalCheck") : I18nUtils.get("pages.doDrip"));
                if (lastData != null && lastData.containsKey("violateTotal")) {
                    Long lastViolateTotal = lastData.getLong("violateTotal");
                    params.put("lastTotal", lastViolateTotal);
                }
                temp = com.tipray.dlp.report.util.ReportUtils.calcDataChange("interpretation.divulge1", params, 0, I18nUtils.get("pages.openTimes2"));
                explain.append(temp);

                JSONObject chartDataObjMap = data.getJSONObject("chartDataObjMap");

                /* 违规泄露方式分析 */
                JSONObject lossTypeBarChartData = chartDataObjMap.getJSONObject("lossTypeBarChartData");
                if (lossTypeBarChartData != null) {
                    JSONArray seriesData = (JSONArray) lossTypeBarChartData.get("seriesData");
                    JSONArray xaxisData = (JSONArray) lossTypeBarChartData.get("xaxisData");
                    Long maxValue = seriesData.getLong(0);
                    List typeName = new LinkedList();
                    for (int j = 0; j < seriesData.size(); j++) {
                        Long v = seriesData.getLong(j);
                        if (Double.compare(v.doubleValue(), maxValue * 0.9) > 0) {
                            typeName.add(xaxisData.getString(j));
                        } else {
                            break;
                        }
                    }
                    explain.append(I18nUtils.get("interpretation.divulge2", new Object[]{StringUtil.join(typeName, "、")}));
                }
                resultData.put("divulgeMode", explain.toString());

                /* 部门违规泄露情况分析 */
                JSONObject deptData = chartDataObjMap.getJSONObject("deptBarChartData");
                if (deptData != null && !deptData.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray seriesData = (JSONArray) deptData.get("seriesData");
                    JSONArray xaxisData = (JSONArray) deptData.get("xaxisData");
                    /* 部门违规泄露情况排序, 获取触发风险最多的部门信息，和其余部门信息 */
                    List maxDeptNames = new LinkedList();
                    List otherDeptNames = new LinkedList();

                    /* 获取最大值的key(value)，再列出其余key(value) */
                    Long maxDeptCount = seriesData.getLong(0);
                    for (int j = 0; j < seriesData.size(); j++) {
                        Long count = seriesData.getLong(j);
                        if (maxDeptCount.equals(count)) {
                            maxDeptNames.add(xaxisData.getString(j));
                        } else {
                            otherDeptNames.add(xaxisData.getString(j) + "(" + count + ")");
                        }
                    }
                    Double deptProportion = maxDeptCount / (total * 0.01);
                    Object[] args = new Object[]{StringUtil.join(maxDeptNames, "、"), maxDeptCount, deptProportion, StringUtil.join(otherDeptNames, "、")};

                    params.clear();
                    params.put("attention", (deptProportion.compareTo(30.0) < 0) ? "" : I18nUtils.get("interpretation.attention"));
                    String template = I18nUtils.get(otherDeptNames.isEmpty() ? "interpretation.divulge3" : "interpretation.divulge4", params);
                    explain.append(String.format(template, args));
                    resultData.put("deptDistribute", explain.toString());
                }

                /* 终端泄露方式分析 */
                Map termOrUserDistribute = chartDataObjMap.getJSONObject("terminalUserPieChartData");
                if (termOrUserDistribute != null && !termOrUserDistribute.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray chartData = (JSONArray) termOrUserDistribute.get("chartData");
                    List maxTermNames = new LinkedList();
                    List otherTermNames = new LinkedList();
                    ReportUtils.sortByValue(chartData, maxTermNames, otherTermNames, I18nUtils.get("pages.openTimes2"));

                    params.clear();
                    params.put("userOrTerm", vo.getCountByObject() == 1 ? I18nUtils.get("pages.user") : I18nUtils.get("pages.terminal"));
                    params.put("name", StringUtil.join(maxTermNames, "、"));
                    params.put("otherName", StringUtil.join(otherTermNames, "、"));
                    explain.append(I18nUtils.get(otherTermNames.isEmpty() ? "interpretation.divulge5" : "interpretation.divulge6", params));
                    resultData.put("termOrUserDistribute", explain.toString());
                }
                /* 违规数量趋势分析  */
                Map trendData = chartDataObjMap.getJSONObject("violateCountTrendLineCharData");
                if (trendData != null && !trendData.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray seriesData = (JSONArray) trendData.get("seriesData");
                    JSONArray xaxisData = (JSONArray) trendData.get("xaxisData");
                    SimpleRegression regression = new SimpleRegression();
                    /* 获取最大值和最小值 */
                    Long maxValue = seriesData.getLong(0);
                    List maxList  = new LinkedList();
                    for (int i = 0; i < seriesData.size(); i++) {
                        Long value = seriesData.getLong(i);
                        regression.addData(i, value);
                        if (value > maxValue) {
                            maxValue = value;
                            maxList.clear();
                            maxList.add(xaxisData.getString(i));
                        } else if (value.equals(maxValue)) {
                            maxList.add(xaxisData.getString(i));
                        }
                    }
                    Double slope = regression.getSlope();
                    Double rSquare = regression.getRSquare();
                    String template = "interpretation.divulge9";
                    params.clear();
                    params.put("times", maxValue);
                    params.put("date", StringUtil.join(maxList, "、"));
                    if (rSquare != null && rSquare > 0.8) {
                        if (slope > 0) {
                            template = "interpretation.divulge7";
                        } else if (slope < 0) {
                            template = I18nUtils.get("interpretation.divulge8");
                        }
                    }
                    explain.append(I18nUtils.get(template, params));

                    resultData.put("violationNum", explain.toString());
                }
                if (vo.getIsOperationalReport() == null || vo.getIsOperationalReport() == 0) {
                    itemValueMap.put("explanation", resultData);
                } else {
                    StringBuilder explanation = new StringBuilder();
                    explanation.append("<div style=\"text-indent: 2em\">" + resultData.get("divulgeMode") + resultData.get("violationNum") + "</div>");
                    explanation.append("<div style=\"text-indent: 2em\">" + resultData.get("deptDistribute") + resultData.get("termOrUserDistribute") + "</div>");
                    itemValueMap.put("explanation", explanation.toString());
                }

            ]]>
        </code>
    </interpretation>

    <interpretation id="7" name="程序运行时长柱状图解说模板" report-code="16">
        <code>
            <![CDATA[
                import com.alibaba.fastjson.JSONObject;
                import com.alibaba.fastjson.JSONArray;
                import com.tipray.dlp.util.StringUtil;
                import com.tipray.dlp.bean.vo.OperationalReportVo;
                import com.tipray.dlp.bean.dto.EChartsOptionDTO;
                import com.tipray.dlp.report.util.ReportUtils;
                import cn.hutool.core.bean.BeanUtil;
                import com.tipray.dlp.bean.SoftGroup;
                import com.tipray.dlp.util.I18nUtils;

                StringBuilder explain = new StringBuilder();
                Double maxProp = 0.0, minProp = null;
                Long maxRunTime = 0L, minRunTime = null;
                List maxPropDept = new LinkedList();
                List minPropDept = new LinkedList();
                Map maxRunTimeDept = new HashMap();
                List minRunTimeDept = new LinkedList();
                for (int i = 0; i < oriData.size(); i++) {
                    JSONObject oriDatum = oriData.getJSONObject(i);
                    Long totalUptime = oriDatum.getLong("totalUptime");
                    Long activeUptime = oriDatum.getLong("activeUptime");
                    Long runTime = oriDatum.getLong("runTimeSumAll");
                    /* 1、运行时间最长的部门 */
                    if (maxRunTime.compareTo(runTime) <= 0) {
                        if (maxRunTime.compareTo(runTime) < 0) {
                            maxRunTime = runTime;
                            maxRunTimeDept.clear();
                        }
                        /* 获取各软件分组运行时长记录 */
                        List filteredEntries = new ArrayList();
                        for (String key : oriDatum.keySet()) {
                            if (key.startsWith("category_")) {
                                filteredEntries.add(new AbstractMap.SimpleEntry(key.substring(9), oriDatum.get(key)));
                            }
                        }
                        /* 获取排名前2的软件分组名称 */
                        LinkedList top2SoftName = new LinkedList();
                        LinkedList top2SoftIds = new LinkedList();
                        Collections.sort(filteredEntries, Map.Entry.comparingByValue(Comparator.reverseOrder()));
                        if (filteredEntries.size() == 1) {
                            top2SoftIds.add(((Map.Entry)filteredEntries.get(0)).getKey());
                        } else if (filteredEntries.size() > 1) {
                            top2SoftIds.add(((Map.Entry)filteredEntries.get(0)).getKey());
                            top2SoftIds.add(((Map.Entry)filteredEntries.get(1)).getKey());
                        }
                        List softGroups = softInfoService.listGroup(null);
                        for (Object softGroup : softGroups) {
                            SoftGroup group = (SoftGroup) softGroup;
                            if (top2SoftIds.contains(group.getId().toString())) {
                                top2SoftName.add(group.getName());
                            }
                        }
                        if (top2SoftIds.contains("-1")) {
                            top2SoftName.add(I18nUtils.get("interpretation.ungrouped_app"));
                        }
                        maxRunTimeDept.put(oriDatum.getString("label"), StringUtil.join(top2SoftName, "、"));
                    }

                    /* 2、运行时间最短的部门 */
                    if (minRunTime == null || minRunTime.compareTo(runTime) > 0) {
                        minRunTime = runTime;
                        minRunTimeDept.clear();
                        minRunTimeDept.add(oriDatum.getString("label"));
                    } else if (minRunTime.compareTo(runTime) == 0) {
                        minRunTimeDept.add(oriDatum.getString("label"));
                    }

                    if (totalUptime != 0L) {
                        double proportion = activeUptime / (totalUptime * 0.01);
                        /* 3、活动时长占开机时长占比最大 */
                        if (maxProp.compareTo(proportion) < 0) {
                            maxProp = proportion;
                            maxPropDept.clear();
                            maxPropDept.add(oriDatum.getString("label"));
                        } else if (maxProp.compareTo(proportion) == 0) {
                            maxPropDept.add(oriDatum.getString("label"));
                        }

                        /* 4、活动时长占开机时长占比最小 */
                        if (minProp == null || minProp.compareTo(proportion) > 0) {
                            minProp = proportion;
                            minPropDept.clear();
                            minPropDept.add(oriDatum.getString("label"));
                        } else if (minProp.compareTo(proportion) == 0) {
                            minPropDept.add(oriDatum.getString("label"));
                        }
                    }
                }
                if (maxRunTimeDept.size() > 1) {
                    StringBuilder deptStr = new StringBuilder();
                    for (Object dept : maxRunTimeDept.keySet()) {
                        deptStr.append(dept.toString() + "（" + maxRunTimeDept.get(dept) + "）、");
                    }
                    List args1 = new LinkedList();
                    args1.addAll(Arrays.asList(ReportUtils.formatDataType(maxRunTime, 1), deptStr.deleteCharAt(deptStr.length() - 1)));
                    if (!maxRunTime.equals(minRunTime)) {
                        args1.add(StringUtil.join(minRunTimeDept, "、"));
                        args1.add(ReportUtils.formatDataType(minRunTime, 1));
                    }
                    explain.append(String.format("<div style=\"text-indent: 2em;\">" + (maxRunTime.equals(minRunTime) ? I18nUtils.get("interpretation.app_report1") : I18nUtils.get("interpretation.app_report2")) + "</div>", args1.toArray()));
                } else {
                    Object[] depts = maxRunTimeDept.entrySet().toArray();
                    Map.Entry dept = (Map.Entry) depts[0];
                    List args1 = new LinkedList();
                    args1.addAll(Arrays.asList(dept.getKey(), ReportUtils.formatDataType(maxRunTime, 1), dept.getValue()));
                    if (!maxRunTime.equals(minRunTime)) {
                        args1.add(StringUtil.join(minRunTimeDept, "、"));
                        args1.add(ReportUtils.formatDataType(minRunTime, 1));
                    }
                    explain.append(String.format("<div style=\"text-indent: 2em;\">" + (maxRunTime.equals(minRunTime) ? I18nUtils.get("interpretation.app_report3") : I18nUtils.get("interpretation.app_report4")) + "</div>", args1.toArray()));
                }

                if (!maxProp.equals(minProp) && minProp != null) {
                    explain.append("<div style=\"text-indent: 2em;\">");
                    if (maxPropDept.size() > 1) {
                        Object[] args2 = new Object[]{ maxProp, StringUtil.join(maxPropDept, "、") };
                        explain.append(String.format(I18nUtils.get("interpretation.app_report5"), args2));
                    } else {
                        Object[] args2 = new Object[]{ StringUtil.join(maxPropDept, "、"), maxProp };
                        explain.append(String.format(I18nUtils.get("interpretation.app_report6"), args2));
                    }
                    if (minPropDept.size() > 1) {
                        Object[] args2 = new Object[]{ minProp, StringUtil.join(minPropDept, "、") };
                        explain.append(String.format(I18nUtils.get("interpretation.app_report7"), args2));
                    } else {
                        Object[] args2 = new Object[]{ StringUtil.join(minPropDept, "、"), minProp };
                        explain.append(String.format(I18nUtils.get("interpretation.app_report8"), args2));
                    }
                    explain.append("</div>");
                }
                resultData.put("img" + imgCode + "_explain", explain);
            ]]>
        </code>
    </interpretation>

    <interpretation id="8" name="程序运行时长趋势图解说模板（年）" report-code="17">
        <code>
            <![CDATA[
                import com.alibaba.fastjson.JSONObject;
                import com.alibaba.fastjson.JSONArray;
                import com.tipray.dlp.util.StringUtil;
                import com.tipray.dlp.bean.vo.OperationalReportVo;
                import com.tipray.dlp.bean.dto.EChartsOptionDTO;
                import com.tipray.dlp.report.util.ReportUtils;
                import cn.hutool.core.bean.BeanUtil;
                List seriesList = echartOptionDTO.getOption().getSeries();
                if (seriesList != null && !seriesList.isEmpty()) {
                    StringBuilder explain = new StringBuilder();
                    Map params = new HashMap();
                    JSONArray oriData = (JSONArray) echartOptionDTO.getOriData();
                    Double maxProp = 0.0, minProp = null;
                    Long runTimeSum = 0L, maxRunTime = 0L, minRunTime = null;
                    Long totalUpTimeSum = 0L, activeUpTimeSum = 0L;
                    List maxPropMonth = new LinkedList();
                    List minPropMonth = new LinkedList();
                    List maxRunTimeMonth = new LinkedList();
                    List minRunTimeMonth = new LinkedList();
                    for (int i = 0; i < oriData.size(); i++) {
                        JSONObject oriDatum = oriData.getJSONObject(i);
                        Long totalUptime = oriDatum.getLong("totalUptime");
                        Long activeUptime = oriDatum.getLong("activeUptime");
                        Long runTime = oriDatum.getLong("runTimeSumAll");

                        if (totalUptime != 0L) {
                            totalUpTimeSum = totalUpTimeSum + totalUptime;
                            activeUpTimeSum = activeUpTimeSum + activeUptime;
                            double proportion = activeUptime / (totalUptime * 0.01);
                            if (maxProp.compareTo(proportion) < 0) {
                                maxProp = proportion;
                                maxPropMonth.clear();
                                maxPropMonth.add(oriDatum.getString("label") + ReportUtils.formatDimType(trendVo.getDimBaseType()));
                            } else if (maxProp.compareTo(proportion) == 0) {
                                maxPropMonth.add(oriDatum.getString("label") + ReportUtils.formatDimType(trendVo.getDimBaseType()));
                            }

                            if (minProp == null || minProp.compareTo(proportion) > 0) {
                                minProp = proportion;
                                minPropMonth.clear();
                                minPropMonth.add(oriDatum.getString("label") + ReportUtils.formatDimType(trendVo.getDimBaseType()));
                            } else if (minProp.compareTo(proportion) == 0) {
                                minPropMonth.add(oriDatum.getString("label") + ReportUtils.formatDimType(trendVo.getDimBaseType()));
                            }
                        }

                        runTimeSum = runTimeSum + runTime;
                        if (maxRunTime.compareTo(runTime) < 0) {
                            maxRunTime = runTime;
                            maxRunTimeMonth.clear();
                            maxRunTimeMonth.add(oriDatum.getString("label") + ReportUtils.formatDimType(trendVo.getDimBaseType()));
                        } else if (maxRunTime.compareTo(runTime) == 0) {
                            maxRunTimeMonth.add(oriDatum.getString("label") + ReportUtils.formatDimType(trendVo.getDimBaseType()));
                        }
                        if (minRunTime == null || minRunTime.compareTo(runTime) > 0) {
                            minRunTime = runTime;
                            minRunTimeMonth.clear();
                            minRunTimeMonth.add(oriDatum.getString("label") + ReportUtils.formatDimType(trendVo.getDimBaseType()));
                        } else if (minRunTime.compareTo(runTime) == 0) {
                            minRunTimeMonth.add(oriDatum.getString("label") + ReportUtils.formatDimType(trendVo.getDimBaseType()));
                        }
                    }

                    OperationalReportVo lastTrendVo = BeanUtil.toBean(trendVo, OperationalReportVo.class);
                    lastTrendVo.setDimValue(Long.valueOf(com.tipray.dlp.util.ReportUtil.calcTrendGraphDate(dimBaseType, trendVo.getDimBaseType(), -2)));
                    EChartsOptionDTO lastEchartsOption = reportPanelController.getEchartOptionDTO(imgCode, lastTrendVo);
                    List lastSeriesList = lastEchartsOption.getOption().getSeries();
                    Long lastRunTimeSum = 0L;
                    if (lastSeriesList != null && !lastSeriesList.isEmpty()) {
                        List lastData = lastSeriesList.get(2).getData();
                        for (Object lastDatum : lastData) {
                            lastRunTimeSum = lastRunTimeSum + (Long) lastDatum;
                        }
                    }
                    params.put("total", runTimeSum);
                    params.put("lastData", lastRunTimeSum);
                    StringBuilder temp = ReportUtils.calcDataChange("1、本年度应用程序运行时长总数为{total}{lastDataStr}。", params, 1, null);
                    explain.append(temp);
                    Object[] args1 = new Object[]{StringUtil.join(maxRunTimeMonth, "、"), ReportUtils.formatDataType(maxRunTime, 1), StringUtil.join(minRunTimeMonth, "、"), ReportUtils.formatDataType(minRunTime, 1)};
                    explain.append(String.format("%s应用程序运行时长最长（%s），%s应用程序运行时长最少（%s）。\n", args1));
                    if (!totalUpTimeSum.equals(0L)){
                        Object[] args2 = new Object[]{ReportUtils.formatDataType(totalUpTimeSum, 1), activeUpTimeSum / (totalUpTimeSum * 0.01), StringUtil.join(maxPropMonth, "、"), maxProp, StringUtil.join(minPropMonth, "、"), minProp};
                        explain.append(String.format("2、本年度开机总时长为%s，运行时长占开机时长%.2f%%。%s活动时长占比最高为%.2f%%，%s活动时长占比最低为%.2f%%。", args2));
                    }
                    resultData.put("img" + imgCode + "_explain", explain);
                } else {
                    resultData.put("img" + imgCode + "_explain", "本周期无数据。");
                }
            ]]>
        </code>
    </interpretation>

    <interpretation id="9" name="文件操作趋势图解说模板（年）" report-code="9">
        <code>
            <![CDATA[
                import com.alibaba.fastjson.JSONObject;
                import com.alibaba.fastjson.JSONArray;
                import com.tipray.dlp.bean.vo.OperationalReportVo;
                import com.tipray.dlp.bean.dto.EChartsOptionDTO;
                import com.tipray.dlp.report.util.ReportUtils;
                import cn.hutool.core.bean.BeanUtil;

                StringBuilder explain = new StringBuilder();
                List seriesList = echartOptionDTO.getOption().getSeries();
                if (seriesList != null && !seriesList.isEmpty()) {
                    JSONArray oriData = (JSONArray) echartOptionDTO.getOriData();
                    Map params = new HashMap();
                    Long srcFileNumSum = 0L, deleteNumSum = 0L, maxFileNum = 0L, maxDelete = 0L;
                    int maxFileMonth = 0, maxDeleteMonth = 0;
                    for (int i = 0; i < oriData.size(); i++) {
                        JSONObject oriDatum = oriData.getJSONObject(i);
                        Long srcFileNum = oriDatum.getLong("srcFileNumSumAll");
                        srcFileNumSum = srcFileNumSum + srcFileNum;
                        Long deleteNum = oriDatum.getLong("category_3");
                        deleteNumSum = deleteNumSum + deleteNum;
                        if (srcFileNum.compareTo(maxFileNum) > 0) {
                            maxFileNum = srcFileNum;
                            maxFileMonth = i;
                        }
                        if (deleteNum.compareTo(maxDelete) > 0) {
                            maxDelete = deleteNum;
                            maxDeleteMonth = i;
                        }
                    }

                    OperationalReportVo lastTrendVo = BeanUtil.toBean(trendVo, OperationalReportVo.class);
                    lastTrendVo.setDimValue(Long.valueOf(com.tipray.dlp.util.ReportUtil.calcTrendGraphDate(dimBaseType, trendVo.getDimBaseType(), -2)));
                    EChartsOptionDTO lastEchartsOption = reportPanelController.getEchartOptionDTO(imgCode, lastTrendVo);
                    JSONArray lastOriData = (JSONArray) lastEchartsOption.getOriData();
                    Long lastSrcFileNumSum = 0L, lastDeleteNumSum = 0L;
                    for (int i = 0; i < lastOriData.size(); i++) {
                        JSONObject oriDatum = oriData.getJSONObject(i);
                        Long srcFileNum = oriDatum.getLong("srcFileNumSumAll");
                        lastSrcFileNumSum = lastSrcFileNumSum + srcFileNum;
                        Long deleteNum = oriDatum.getLong("category_3");
                        lastDeleteNumSum = lastDeleteNumSum + deleteNum;
                    }
                    params.put("total", srcFileNumSum);
                    params.put("lastData", lastSrcFileNumSum);
                    StringBuilder temp1 = ReportUtils.calcDataChange("1、本周期文件操作总次数为{total}{lastDataStr}。", params, yaxisTypeCode, I18nUtils.get("pages.openTimes2"));
                    explain.append(temp1);
                    Object[] args1 = new Object[]{maxFileMonth + 1, maxFileNum};
                    explain.append(String.format("%s月文件操作总次数达到峰值%s次。\n", args1));


                    Double avgDelete = deleteNumSum / (12 * 1.0);
                    if (Double.compare(avgDelete * 1.5, maxDelete) >= 0) {
                        Object[] args2 = new Object[]{deleteNumSum, maxDeleteMonth + 1, maxDelete, maxDelete / (deleteNumSum * 0.01), avgDelete};
                        explain.append(String.format("2、本年度删除文件总数为%s个，%s月删除文件总数最多（%s），占当月文件操作总次数的%.2f%%，远高于月平均删除文件次数（%.2f），建议关注。\n", args2));
                    } else {
                        Object[] args2 = new Object[]{deleteNumSum, maxDeleteMonth + 1, maxDelete, maxDelete / (deleteNumSum * 0.01)};
                        explain.append(String.format("2、本年度删除文件总数为%s个，%s月删除文件总数最多（%s），占当月文件操作总次数的%.2f%%。\n", args2));
                    }
                    resultData.put("img" + imgCode + "_explain", explain);
                } else {
                    resultData.put("img" + imgCode + "_explain", "本周期无数据。");
                }
            ]]>
        </code>
    </interpretation>

    <interpretation id="10" name="数据安全柱状图解说模板" report-code="17">
        <code>
            <![CDATA[
                import com.alibaba.fastjson.JSONObject;
                import com.alibaba.fastjson.JSONArray;
                import com.tipray.dlp.util.StringUtil;
                import com.tipray.dlp.bean.vo.OperationalReportVo;
                import com.tipray.dlp.bean.dto.EChartsOptionDTO;
                import com.tipray.dlp.report.util.ReportUtils;
                import cn.hutool.core.bean.BeanUtil;

            ]]>
        </code>
    </interpretation>


    <interpretation id="11" name="违规严重程度统计报表解说" report-code="52,55">
        <code>
            <![CDATA[
                import com.tipray.dlp.util.StringUtil;
                import com.alibaba.fastjson.JSONObject;
                import com.tipray.dlp.bean.vo.OperationalReportVo;
                import com.tipray.dlp.bean.dto.EChartsOptionDTO;
                import cn.hutool.core.bean.BeanUtil;
                import com.tipray.dlp.report.util.ReportUtils;
                import com.alibaba.fastjson.JSONArray;
                import org.apache.commons.math3.stat.regression.SimpleRegression;
                import com.tipray.dlp.util.I18nUtils;

                StringBuilder explain = new StringBuilder();
                JSONObject itemValueMap = (JSONObject) data.get("itemValueMap");
                Long total = itemValueMap.getLong("violateTotal");

                /* 计算查询上一周期分布图数据的参数 */
                OperationalReportVo lastVo = cn.hutool.core.bean.BeanUtil.toBean(vo, OperationalReportVo.class);
                Long dimValue = ReportUtils.calcLastDate(lastVo.getDimBaseType(), lastVo.getDimValue());
                lastVo.setDimValue(dimValue);
                /* 获取上一周期数据，计算总和 */
                EChartsOptionDTO lastEchartsOption = reportPanelController.getEchartOptionDTO(imgCode, lastVo);
                JSONObject lastData = (JSONObject) lastEchartsOption.getData();
                StringBuilder temp;
                Map params = new HashMap();
                params.put("doDrip", imgCode == 52 ? I18nUtils.get("pages.nomalCheck") : I18nUtils.get("pages.doDrip"));
                params.put("total", total);
                if (lastData != null && lastData.containsKey("violateTotal")) {
                    Long lastViolateTotal = lastData.getLong("violateTotal");
                    params.put("lastTotal", lastViolateTotal);
                }
                temp = com.tipray.dlp.report.util.ReportUtils.calcDataChange("interpretation.severity1", params, 0, I18nUtils.get("interpretation.recordUnit"));
                explain.append(temp);

                JSONObject chartDataObjMap = data.getJSONObject("chartDataObjMap");
                Long seriousAndVerySerious = 0L;

                /* 违规泄露方式分析 */
                JSONObject severityBarChartData = chartDataObjMap.getJSONObject("severityBarChartData");
                if (severityBarChartData != null) {
                    JSONArray seriesData = (JSONArray) severityBarChartData.get("seriesData");
                    Long serious = seriesData.getLong(2), verySerious = seriesData.getLong(3);
                    Long slight = seriesData.getLong(0), normal = seriesData.getLong(1);
                    seriousAndVerySerious = serious + verySerious;
                    if (serious.equals(0L) && verySerious.equals(0L)) {
                        if (slight.equals(0L) || normal.equals(0L)) {
                            Object[] severityArgs = new Object[]{slight + normal, (slight + normal) * 100.0 / total};
                            params.clear();
                            params.put("lever", slight.equals(0L) ? I18nUtils.get("pages.severityOptions2") : I18nUtils.get("pages.severityOptions1"));
                            tempI18n = I18nUtils.get("interpretation.severity2", params);
                            explain.append(String.format(tempI18n, severityArgs));
                        } else {
                            Object[] severityArgs = new Object[]{slight, slight * 100.0 / total, normal, normal * 100.0 / total};
                            explain.append(String.format(I18nUtils.get("interpretation.severity3"), severityArgs));
                        }
                    } else if (!serious.equals(0L) && !verySerious.equals(0L)) {
                        Object[] severityArgs = new Object[]{serious, verySerious, seriousAndVerySerious * 100.0 / total};
                        explain.append(String.format(I18nUtils.get("interpretation.severity4"), severityArgs));
                    } else {
                        Object[] severityArgs = new Object[]{serious + verySerious, seriousAndVerySerious * 100.0 / total};
                        params.clear();
                        params.put("lever", serious.equals(0L) ? I18nUtils.get("pages.severityOptions4") : I18nUtils.get("pages.severityOptions3"));
                        explain.append(String.format(I18nUtils.get("interpretation.severity5", params), severityArgs));
                    }
                }
                resultData.put("severityPie", explain.toString());

                /* 部门违规严重程度分析 */
                JSONObject deptData = chartDataObjMap.getJSONObject("deptBarChartData");
                if (deptData != null && !deptData.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray seriesData = (JSONArray) deptData.get("seriesData");
                    JSONArray xaxisData = (JSONArray) deptData.get("xaxisData");
                    /* 部门违规泄露情况排序, 获取触发风险最多的部门信息，和其余部门信息 */
                    Long maxSerious = 0L, maxDeptCount = 0L;
                    List maxSeriousDeptIndex = new LinkedList();

                    JSONArray slight = seriesData.getJSONObject(0).getJSONArray("data");
                    JSONArray normal = seriesData.getJSONObject(1).getJSONArray("data");
                    JSONArray serious = seriesData.getJSONObject(2).getJSONArray("data");
                    JSONArray verySerious = seriesData.getJSONObject(3).getJSONArray("data");
                    for (int j = 0; j < xaxisData.size(); j++) {
                        Long slightValue = slight.getLong(j) + normal.getLong(j);
                        Long seriousValue = serious.getLong(j) + verySerious.getLong(j);
                        if (maxDeptCount.compareTo(slightValue + seriousValue) < 0) {
                            maxDeptCount = slightValue + seriousValue;
                        }
                        if (maxSerious.compareTo(seriousValue) < 0) {
                            maxSerious = seriousValue;
                            maxSeriousDeptIndex.clear();
                            maxSeriousDeptIndex.add(j);
                        } else if (seriousValue.equals(maxSerious) && maxSerious != 0L) {
                            maxSeriousDeptIndex.add(j);
                        }
                    }
                    if (maxSeriousDeptIndex.size() > 1) {
                        StringBuilder deptNames = new StringBuilder();
                        for (int i = 0; i < maxSeriousDeptIndex.size(); i++) {
                            deptNames.append((i == 0 ? "" : "、") + "<span style=\"color: #ff5959;\">%s" + xaxisData.getString((int) maxSeriousDeptIndex.get(i)) + "</span>");
                        }
                        Object[] deptArgs = new Object[]{deptNames, maxSerious, maxSerious * (maxSeriousDeptIndex.size() + 1) * 100.0 / total};
                        explain.append(String.format(I18nUtils.get("interpretation.severity6"), deptArgs));
                    } else if (maxSeriousDeptIndex.size() == 1) {
                        int i = (int) maxSeriousDeptIndex.get(0);
                        String maxDeptNames = xaxisData.getString(i);
                        Object[] deptArgs = new Object[]{maxDeptNames + "（" + maxSerious + "次）", maxSerious * 100.0 / seriousAndVerySerious};
                        explain.append(String.format(I18nUtils.get("interpretation.severity7"), deptArgs));
                    } else {
                        Long maxDeptValue = slight.getLong(0) + normal.getLong(0);
                        Object[] deptArgs = new Object[]{xaxisData.getString(0), maxDeptValue};
                        explain.append(String.format(I18nUtils.get("interpretation.severity8"), deptArgs));
                    }
                    resultData.put("deptDistribute", explain.toString());
                }

                /* 终端泄露方式分析 */
                Map termOrUserDistribute = chartDataObjMap.getJSONObject("terminalUserBarChartData");
                if (termOrUserDistribute != null && !termOrUserDistribute.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray seriesData = (JSONArray) termOrUserDistribute.get("seriesData");
                    JSONArray xaxisData = (JSONArray) termOrUserDistribute.get("xaxisData");
                    /* 部门违规泄露情况排序, 获取触发风险最多的部门信息，和其余部门信息 */
                    Long maxSerious = 0L, maxDeptCount = 0L;
                    List maxSeriousDeptIndex = new LinkedList();

                    JSONArray slight = seriesData.getJSONObject(0).getJSONArray("data");
                    JSONArray normal = seriesData.getJSONObject(1).getJSONArray("data");
                    JSONArray serious = seriesData.getJSONObject(2).getJSONArray("data");
                    JSONArray verySerious = seriesData.getJSONObject(3).getJSONArray("data");
                    for (int j = 0; j < xaxisData.size(); j++) {
                        Long slightValue = slight.getLong(j) + normal.getLong(j);
                        Long seriousValue = serious.getLong(j) + verySerious.getLong(j);
                        if (maxDeptCount.compareTo(slightValue + seriousValue) < 0) {
                            maxDeptCount = slightValue + seriousValue;
                        }
                        if (maxSerious.compareTo(seriousValue) < 0) {
                            maxSerious = seriousValue;
                            maxSeriousDeptIndex.clear();
                            maxSeriousDeptIndex.add(j);
                        } else if (seriousValue.equals(maxSerious) && maxSerious != 0L) {
                            maxSeriousDeptIndex.add(j);
                        }
                    }
                    if (maxSeriousDeptIndex.size() > 1) {
                        StringBuilder deptNames = new StringBuilder();
                        for (int i = 0; i < maxSeriousDeptIndex.size(); i++) {
                            deptNames.append((i == 0 ? "" : "、") + "<span style=\"color: #ff5959;\">%s" + xaxisData.getString((int) maxSeriousDeptIndex.get(i)) + "</span>");
                        }
                        params.clear();
                        params.put("CountByObject", vo.getCountByObject() == 1 ? I18nUtils.get("pages.user") : I18nUtils.get("pages.terminal"));
                        Object[] deptArgs = new Object[]{deptNames, maxSerious, maxSerious * (maxSeriousDeptIndex.size() + 1) * 100.0 / total};
                        explain.append(String.format(I18nUtils.get("interpretation.severity9", params), deptArgs));
                    } else if (maxSeriousDeptIndex.size() == 1) {
                        int i = (int) maxSeriousDeptIndex.get(0);
                        String maxDeptNames = xaxisData.getString(i);
                        params.clear();
                        params.put("CountByObject", vo.getCountByObject() == 1 ? I18nUtils.get("pages.user") : I18nUtils.get("pages.terminal"));
                        Object[] deptArgs = new Object[]{maxDeptNames + "（" + maxSerious + "次）", maxSerious * 100.0 / seriousAndVerySerious};
                        explain.append(String.format(I18nUtils.get("interpretation.severity10", params), deptArgs));
                    } else {
                        Long maxDeptValue = slight.getLong(0) + normal.getLong(0);
                        Object[] deptArgs = new Object[]{xaxisData.getString(0), maxDeptValue};
                        params.clear();
                        params.put("CountByObject", vo.getCountByObject() == 1 ? I18nUtils.get("pages.user") : I18nUtils.get("pages.terminal"));
                        explain.append(String.format(I18nUtils.get("interpretation.severity11", params), deptArgs));
                    }
                    resultData.put("termOrUserDistribute", explain.toString());
                }
                /* 违规数量趋势分析  */
                Map trendData = chartDataObjMap.getJSONObject("violateCountTrendLineCharData");
                if (trendData != null && !trendData.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray seriesData = (JSONArray) trendData.get("seriesData");
                    JSONArray xaxisData = (JSONArray) trendData.get("xaxisData");
                    SimpleRegression regression = new SimpleRegression();
                    /* 获取最大值和最小值 */
                    Long maxValue = seriesData.getLong(0);
                    List maxList = new LinkedList();

                    for (int i = 0; i < seriesData.size(); i++) {
                        Long value = seriesData.getLong(i);
                        regression.addData(i, value);
                        if (value > maxValue) {
                            maxValue = value;
                            maxList.clear();
                            maxList.add(xaxisData.getString(i));
                        } else if (value.equals(maxValue)) {
                            maxList.add(xaxisData.getString(i));
                        }
                    }
                    Double slope = regression.getSlope();
                    Double rSquare = regression.getRSquare();
                    if (rSquare != null && rSquare > 0.8) {
                        if (slope > 0) {
                            Object[] trendArgs = new Object[]{StringUtil.join(maxList, "、"), maxValue};
                            explain.append(String.format(I18nUtils.get("interpretation.severity12"), trendArgs));
                        } else if (slope < 0) {
                            explain.append(I18nUtils.get("interpretation.severity13"));
                        } else {
                            Object[] trendArgs = new Object[]{StringUtil.join(maxList, "、"), maxValue};
                            explain.append(String.format(I18nUtils.get("interpretation.severity14"), trendArgs));
                        }
                    } else {
                        Object[] trendArgs = new Object[]{StringUtil.join(maxList, "、"), maxValue};
                        explain.append(String.format(I18nUtils.get("interpretation.severity14"), trendArgs));
                    }
                    resultData.put("violationNum", explain.toString());
                }
                if (vo.getIsOperationalReport() == null || vo.getIsOperationalReport() == 0) {
                    itemValueMap.put("explanation", resultData);
                } else {
                    StringBuilder explanation = new StringBuilder();
                    explanation.append("<div style=\"text-indent: 2em\">" + resultData.get("severityPie") + resultData.get("violationNum") + "</div>");
                    explanation.append("<div style=\"text-indent: 2em\">" + resultData.get("deptDistribute") + resultData.get("termOrUserDistribute") + "</div>");
                    itemValueMap.put("explanation", explanation.toString());
                }
            ]]>
        </code>
    </interpretation>
    <interpretation id="12" name="违规规则类型统计报表解说" report-code="53">
        <code>
            <![CDATA[
                import com.tipray.dlp.util.StringUtil;
                import com.alibaba.fastjson.JSONObject;
                import com.tipray.dlp.bean.vo.OperationalReportVo;
                import com.tipray.dlp.bean.dto.EChartsOptionDTO;
                import cn.hutool.core.bean.BeanUtil;
                import com.tipray.dlp.report.util.ReportUtils;
                import com.alibaba.fastjson.JSONArray;
                import org.apache.commons.math3.stat.regression.SimpleRegression;
                import com.tipray.dlp.util.I18nUtils;

                StringBuilder explain = new StringBuilder();
                JSONObject itemValueMap = (JSONObject) data.get("itemValueMap");
                Long total = itemValueMap.getLong("violateTotal");

                /* 计算查询上一周期分布图数据的参数 */
                OperationalReportVo lastVo = cn.hutool.core.bean.BeanUtil.toBean(vo, OperationalReportVo.class);
                Long dimValue = ReportUtils.calcLastDate(lastVo.getDimBaseType(), lastVo.getDimValue());
                lastVo.setDimValue(dimValue);
                /* 获取上一周期数据，计算总和 */
                EChartsOptionDTO lastEchartsOption = reportPanelController.getEchartOptionDTO(imgCode, lastVo);
                JSONObject lastData = (JSONObject) lastEchartsOption.getData();
                StringBuilder temp;
                Map params = new HashMap();
                params.put("total", total);
                if (lastData != null) {
                    Long lastViolateTotal = lastData.getLong("violateTotal");
                    params.put("lastTotal", lastViolateTotal);
                }
                temp = com.tipray.dlp.report.util.ReportUtils.calcDataChange("interpretation.ruleType1", params, 0, I18nUtils.get("interpretation.recordUnit"));
                explain.append(temp);

                JSONObject chartDataObjMap = data.getJSONObject("chartDataObjMap");

                /* 违规方式分析 */
                JSONObject ruleTypeBarChartData = chartDataObjMap.getJSONObject("ruleTypeBarChartData");
                if (ruleTypeBarChartData != null) {
                    JSONArray seriesData = (JSONArray) ruleTypeBarChartData.get("seriesData");
                    JSONArray xaxisData = (JSONArray) ruleTypeBarChartData.get("xaxisData");
                    Long maxValue = 0L;
                    List typeName = new LinkedList();
                    for (int j = 0; j < seriesData.size(); j++) {
                        Long v = seriesData.getLong(j);
                        if (maxValue < v) {
                            maxValue = v;
                            typeName.clear();
                            typeName.add(xaxisData.getString(j) + "（" + v + I18nUtils.get("pages.openTimes2") + "）" );
                        } else if (maxValue.equals(v)) {
                            typeName.add(xaxisData.getString(j));
                        }
                    }
                    Object[] args = new Object[]{StringUtil.join(typeName, "、"), maxValue * typeName.size() * 100.0 / total};
                    params.clear();
                    params.put("nameSize", typeName.size() > 1 ? I18nUtils.get("interpretation.total") : "");
                    explain.append(String.format(I18nUtils.get("interpretation.ruleType2", params), args));
                }
                resultData.put("ruleType", explain.toString());

                /* 部门违规行为触发情况分析 */
                JSONObject deptData = chartDataObjMap.getJSONObject("deptBarChartData");
                if (deptData != null && !deptData.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray seriesData = (JSONArray) deptData.get("seriesData");
                    JSONArray xaxisData = (JSONArray) deptData.get("xaxisData");
                    /* 部门违规行为触发情况排序, 获取触发风险最多的部门信息，和其余部门信息 */
                    List maxDeptNames = new LinkedList();
                    List otherDeptNames = new LinkedList();

                    /* 获取最大值的key(value)，再列出其余key(value) */
                    Long maxDeptCount = seriesData.getLong(0);
                    for (int j = 0; j < seriesData.size(); j++) {
                        Long count = seriesData.getLong(j);
                        if (maxDeptCount.equals(count)) {
                            maxDeptNames.add(xaxisData.getString(j));
                        } else {
                            otherDeptNames.add(xaxisData.getString(j) + "(" + count + ")");
                        }
                    }
                    Double deptProportion = maxDeptCount / (total * 0.01);
                    Object[] args = new Object[]{StringUtil.join(maxDeptNames, "、"), maxDeptCount, deptProportion, StringUtil.join(otherDeptNames, "、")};
                    params.clear();
                    params.put("attention", (deptProportion.compareTo(30.0) < 0) ? "" : I18nUtils.get("interpretation.attention"));
                    String template1 = I18nUtils.get(otherDeptNames.isEmpty() ? "interpretation.ruleType3" : "interpretation.ruleType4", params);
                    explain.append(String.format(template1, args));
                    resultData.put("deptDistribute", explain.toString());
                }

                /* 终端违规行为触发情况分析 */
                Map termOrUserDistribute = chartDataObjMap.getJSONObject("terminalUserPieChartData");
                if (termOrUserDistribute != null && !termOrUserDistribute.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray chartData = (JSONArray) termOrUserDistribute.get("chartData");
                    List maxTermNames = new LinkedList();
                    List otherTermNames = new LinkedList();
                    ReportUtils.sortByValue(chartData, maxTermNames, otherTermNames, I18nUtils.get("pages.openTimes2"));
                    params.clear();
                    params.put("userOrTerm", vo.getCountByObject() == 1 ? I18nUtils.get("pages.user") : I18nUtils.get("pages.terminal"));
                    params.put("name", StringUtil.join(maxTermNames, "、"));
                    params.put("otherName", otherTermNames.isEmpty() ? null : StringUtil.join(otherTermNames, "、"));
                    explain.append(otherTermNames.isEmpty() ? I18nUtils.get("interpretation.ruleType5", params) : I18nUtils.get("interpretation.ruleType6", params));
                    resultData.put("termOrUserDistribute", explain.toString());
                }
                /* 违规数量趋势分析  */
                Map trendData = chartDataObjMap.getJSONObject("violateCountTrendLineCharData");
                if (trendData != null && !trendData.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray seriesData = (JSONArray) trendData.get("seriesData");
                    JSONArray xaxisData = (JSONArray) trendData.get("xaxisData");
                    SimpleRegression regression = new SimpleRegression();
                    /* 获取最大值和最小值 */
                    Long maxValue = seriesData.getLong(0);
                    List maxList  = new LinkedList();

                    for (int i = 0; i < seriesData.size(); i++) {
                        Long value = seriesData.getLong(i);
                        regression.addData(i, value);
                        if (value > maxValue) {
                            maxValue = value;
                            maxList.clear();
                            maxList.add(xaxisData.getString(i));
                        } else if (value.equals(maxValue)) {
                            maxList.add(xaxisData.getString(i));
                        }
                    }
                    Double slope = regression.getSlope();
                    Double rSquare = regression.getRSquare();
                    if (rSquare != null && rSquare > 0.8) {
                        if (slope > 0) {
                            Object[] trendArgs = new Object[]{StringUtil.join(maxList, "、"), maxValue};
                            explain.append(String.format(I18nUtils.get("interpretation.severity12"), trendArgs));
                        } else if (slope < 0) {
                            explain.append(I18nUtils.get("interpretation.severity13"));
                        } else {
                            Object[] trendArgs = new Object[]{StringUtil.join(maxList, "、"), maxValue};
                            explain.append(String.format(I18nUtils.get("interpretation.severity14"), trendArgs));
                        }
                    } else {
                        Object[] trendArgs = new Object[]{StringUtil.join(maxList, "、"), maxValue};
                        explain.append(String.format(I18nUtils.get("interpretation.severity14"), trendArgs));
                    }
                    resultData.put("violationNum", explain.toString());
                }
                if (vo.getIsOperationalReport() == null || vo.getIsOperationalReport() == 0) {
                    itemValueMap.put("explanation", resultData);
                } else {
                    StringBuilder explanation = new StringBuilder();
                    explanation.append("<div style=\"text-indent: 2em\">" + resultData.get("ruleType") + resultData.get("violationNum") + "</div>");
                    explanation.append("<div style=\"text-indent: 2em\">" + resultData.get("deptDistribute") + resultData.get("termOrUserDistribute") + "</div>");
                    itemValueMap.put("explanation", explanation.toString());
                }
            ]]>
        </code>
    </interpretation>

    <interpretation id="13" name="敏感内容征兆报表解说" report-code="56">
        <code>
            <![CDATA[
                import com.tipray.dlp.util.StringUtil;
                import com.alibaba.fastjson.JSONObject;
                import com.tipray.dlp.bean.vo.OperationalReportVo;
                import com.tipray.dlp.bean.dto.EChartsOptionDTO;
                import cn.hutool.core.bean.BeanUtil;
                import com.tipray.dlp.report.util.ReportUtils;
                import com.alibaba.fastjson.JSONArray;
                import org.apache.commons.math3.stat.regression.SimpleRegression;
                import com.tipray.dlp.util.I18nUtils;

                StringBuilder explain = new StringBuilder();
                JSONObject itemValueMap = (JSONObject) data.get("itemValueMap");
                Long total = itemValueMap.getLong("violateTotal");

                /* 计算查询上一周期分布图数据的参数 */
                OperationalReportVo lastVo = cn.hutool.core.bean.BeanUtil.toBean(vo, OperationalReportVo.class);
                Long dimValue = ReportUtils.calcLastDate(lastVo.getDimBaseType(), lastVo.getDimValue());
                lastVo.setDimValue(dimValue);
                /* 获取上一周期数据，计算总和 */
                EChartsOptionDTO lastEchartsOption = reportPanelController.getEchartOptionDTO(imgCode, lastVo);
                JSONObject lastData = (JSONObject) lastEchartsOption.getData();
                StringBuilder temp;
                Map params = new HashMap();
                params.put("total", total);
                if (lastData != null) {
                    Long lastViolateTotal = lastData.getLong("violateTotal");
                    params.put("lastTotal", lastViolateTotal);
                }
                temp = com.tipray.dlp.report.util.ReportUtils.calcDataChange("interpretation.sensitiveContent1", params, 0, I18nUtils.get("pages.openTimes2"));
                explain.append(temp);

                JSONObject chartDataObjMap = data.getJSONObject("chartDataObjMap");
                Long seriousAndVerySerious = 0L;

                /* 风险行为占比分析 */
                JSONObject symptomPieChartData = chartDataObjMap.getJSONObject("symptomPieChartData");
                if (symptomPieChartData != null) {
                    JSONArray chartData = (JSONArray) symptomPieChartData.get("chartData");
                    List maxTermNames = new LinkedList();
                    List otherTermNames = new LinkedList();
                    JSONObject maxEntry = (JSONObject) ReportUtils.sortByValue(chartData, maxTermNames, otherTermNames, I18nUtils.get("pages.openTimes2")).get(0);
                    Integer maxEntryValue = (Integer) maxEntry.get("value");
                    Object[] args = new Object[]{StringUtil.join(maxTermNames, "、"), maxEntryValue, StringUtil.join(otherTermNames, "、")};
                    params.clear();
                    params.put("others", otherTermNames.isEmpty() ? "" : I18nUtils.get("interpretation.others"));
                    explain.append(String.format(I18nUtils.get("interpretation.sensitiveContent2", params), args));
                }
                resultData.put("riskPie", explain.toString());

                /* 部门风险行为情况分析 */
                JSONObject deptData = chartDataObjMap.getJSONObject("deptBarChartData");
                if (deptData != null && !deptData.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray seriesData = (JSONArray) deptData.get("seriesData");
                    JSONArray xaxisData = (JSONArray) deptData.get("xaxisData");
                    /* 部门风险行为触发情况排序, 获取触发风险最多的部门信息，和其余部门信息 */
                    List maxDeptNames = new LinkedList();
                    List otherDeptNames = new LinkedList();

                    /* 获取最大值的key(value)，再列出其余key(value) */
                    Long maxDeptCount = seriesData.getLong(0);
                    for (int j = 0; j < seriesData.size(); j++) {
                        Long count = seriesData.getLong(j);
                        if (maxDeptCount.equals(count)) {
                            maxDeptNames.add(xaxisData.getString(j));
                        } else {
                            otherDeptNames.add(xaxisData.getString(j) + "(" + count + ")");
                        }
                    }
                    Double deptProportion = maxDeptCount / (total * 0.01);
                    Object[] args = new Object[]{StringUtil.join(maxDeptNames, "、"), maxDeptCount, deptProportion, StringUtil.join(otherDeptNames, "、")};
                    params.clear();
                    params.put("attention", (deptProportion.compareTo(30.0) < 0) ? "" : I18nUtils.get("interpretation.attention"));
                    params.put("others", otherDeptNames.isEmpty() ? "" : I18nUtils.get("interpretation.others"));
                    String template = I18nUtils.get("interpretation.sensitiveContent4", params);
                    explain.append(String.format(template, args));
                    resultData.put("deptDistribute", explain.toString());
                }

                /* 终端风险行为触发情况分析 */
                Map termOrUserDistribute = chartDataObjMap.getJSONObject("terminalUserPieChartData");
                if (termOrUserDistribute != null && !termOrUserDistribute.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray chartData = (JSONArray) termOrUserDistribute.get("chartData");
                    List maxTermNames = new LinkedList();
                    List otherTermNames = new LinkedList();
                    JSONObject maxEntry = (JSONObject)ReportUtils.sortByValue(chartData, maxTermNames, otherTermNames, I18nUtils.get("pages.openTimes2")).get(0);
                    Integer maxEntryValue = (Integer) maxEntry.get("value");
                    Double proportion = maxEntryValue / (total * 0.01);
                    Object[] args = new Object[]{StringUtil.join(maxTermNames, "、"), proportion, maxEntryValue, StringUtil.join(otherTermNames, "、")};
                    params.clear();
                    params.put("userOrTerm", vo.getCountByObject() == 1 ? I18nUtils.get("pages.user") : I18nUtils.get("pages.terminal"));
                    params.put("others", otherTermNames.isEmpty() ? "" : I18nUtils.get("interpretation.others"));
                    explain.append(String.format(I18nUtils.get("interpretation.sensitiveContent6", params), args));
                    resultData.put("termOrUserDistribute", explain.toString());
                }
                /* 风险行为数量趋势分析  */
                Map trendData = chartDataObjMap.getJSONObject("trendLineChartData");
                if (trendData != null && !trendData.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray seriesData = (JSONArray) trendData.get("seriesData");
                    JSONArray xaxisData = (JSONArray) trendData.get("xaxisData");
                    SimpleRegression regression = new SimpleRegression();
                    /* 获取最大值和最小值 */
                    Long maxValue = seriesData.getLong(0);
                    List maxList  = new LinkedList();
                    for (int i = 0; i < seriesData.size(); i++) {
                        Long value = seriesData.getLong(i);
                        regression.addData(i, value);
                        if (value > maxValue) {
                            maxValue = value;
                            maxList.clear();
                            maxList.add(xaxisData.getString(i));
                        } else if (value.equals(maxValue)) {
                            maxList.add(xaxisData.getString(i));
                        }
                    }
                    Double slope = regression.getSlope();
                    Double rSquare = regression.getRSquare();
                    if (rSquare != null && rSquare > 0.8) {
                        if (slope > 0) {
                            Object[] trendArgs = new Object[]{StringUtil.join(maxList, "、"), maxValue};
                            explain.append(String.format(I18nUtils.get("interpretation.sensitiveContent8"), trendArgs));
                        } else if (slope < 0) {
                            explain.append(I18nUtils.get("interpretation.sensitiveContent9"));
                        } else {
                            Object[] trendArgs = new Object[]{StringUtil.join(maxList, "、"), maxValue};
                            explain.append(String.format(I18nUtils.get("interpretation.sensitiveContent10"), trendArgs));
                        }
                    } else {
                        Object[] trendArgs = new Object[]{StringUtil.join(maxList, "、"), maxValue};
                        explain.append(String.format(I18nUtils.get("interpretation.sensitiveContent10"), trendArgs));
                    }
                    resultData.put("violationNum", explain.toString());
                }
                if (vo.getIsOperationalReport() == null || vo.getIsOperationalReport() == 0) {
                    itemValueMap.put("explanation", resultData);
                } else {
                    StringBuilder explanation = new StringBuilder();
                    explanation.append("<div style=\"text-indent: 2em\">" + resultData.get("riskPie") + resultData.get("violationNum") + "</div>");
                    explanation.append("<div style=\"text-indent: 2em\">" + resultData.get("deptDistribute") + resultData.get("termOrUserDistribute") + "</div>");
                    itemValueMap.put("explanation", explanation.toString());
                }
            ]]>
        </code>
    </interpretation>
    <interpretation id="14" name="全盘扫描文件报表解说" report-code="57">
        <code>
            <![CDATA[
                import com.tipray.dlp.util.StringUtil;
                import com.alibaba.fastjson.JSONObject;
                import cn.hutool.core.bean.BeanUtil;
                import com.tipray.dlp.report.util.ReportUtils;
                import com.tipray.dlp.util.ReportUtil;
                import com.alibaba.fastjson.JSONArray;
                import com.tipray.dlp.bean.vo.DiskScanVO;
                import org.springframework.http.ResponseEntity;
                import com.alibaba.fastjson.JSON;
                import com.tipray.dlp.bean.vo.DiskScanSensitiveVO;
                import com.tipray.dlp.util.I18nUtils;

                StringBuilder explain = new StringBuilder();
                JSONObject itemValueMap = (JSONObject) data.get("itemValueMap");
                Long total = itemValueMap.getLong("all");

                /* 计算查询上一周期分布图数据的参数 */
                DiskScanVO lastDiskScanVo = new DiskScanVO();
                lastDiskScanVo.setGuid(vo.getGuid());
                ResponseEntity lastDateRes = ReportUtil.getDataJson(lastDiskScanVo, "/sensitiveControl/getTaskOptionStep2", new Object[]{});
                JSONArray lastDateArray = JSON.parseObject((String) lastDateRes.getBody()).getJSONArray("data");
                String lastBatchNo = null;
                for (int i = 1; i < lastDateArray.size(); i++) {
                    JSONObject item = (JSONObject)lastDateArray.get(i);
                    if (vo.getBatchNo().equals(item.get("key"))) {
                        lastBatchNo = lastDateArray.getJSONObject(i - 1).getString("key");
                    }
                }
                StringBuilder temp;
                Map params = new HashMap();
                params.put("total", total);
                if (lastBatchNo != null) {
                    DiskScanSensitiveVO lastVo = cn.hutool.core.bean.BeanUtil.toBean(vo, DiskScanSensitiveVO.class);
                    lastVo.setBatchNo(lastBatchNo);
                    ResponseEntity lastRes = ReportUtil.getDataJson(lastVo, "/sensitiveControl/getDiskScanSensitiveHomepageData", new Object[]{});
                    JSONObject lastData = JSON.parseObject((String)lastRes.getBody()).getJSONObject("data");
                    JSONObject lastItemValueMap = (JSONObject)lastData.get("itemValueMap");
                    Long lastViolateTotal = lastItemValueMap.getLong("all");
                    params.put("lastTotal", lastViolateTotal);
                }
                temp = ReportUtils.calcDataChange("interpretation.fullScan1", params, 0, I18nUtils.get("interpretation.fileUnit"));
                explain.append(temp);

                JSONObject chartDataObjMap = data.getJSONObject("chartDataObjMap");
                /* 终端敏感文件分析 */
                Map termSensitiveBar = chartDataObjMap.getJSONObject("termSensitiveBar");
                if (termSensitiveBar != null) {
                    JSONArray seriesData = (JSONArray) termSensitiveBar.get("seriesData");
                    JSONArray xaxisData = (JSONArray) termSensitiveBar.get("xaxisData");
                    Long maxTermValue = seriesData.getLongValue(0);
                    List maxTermNames = new LinkedList();
                    List otherTermNames = new LinkedList();
                    for (int i = 0; i < seriesData.size(); i++) {
                        if (seriesData.getLong(i).equals(maxTermValue)) {
                            maxTermNames.add(xaxisData.getString(i));
                        } else {
                            otherTermNames.add(xaxisData.getString(i) + "(" + seriesData.getLong(i) + ")");
                        }
                    }

                    Object[] trendArgs = new Object[]{StringUtil.join(maxTermNames, "、"), maxTermValue, maxTermNames.size() * maxTermValue * 100.0 / total, StringUtil.join(otherTermNames, "、")};
                    params.clear();
                    if (maxTermNames.size() > 1) {
                        params.put("respectively", I18nUtils.get("interpretation.respectively"));
                        params.put("total", I18nUtils.get("interpretation.total"));
                        params.put("others", "");
                    } else {
                        params.put("respectively", "");
                        params.put("total", "");
                        params.put("others", otherTermNames.isEmpty() ? "" : I18nUtils.get("interpretation.others"));
                    }
                    explain.append(String.format(I18nUtils.get("interpretation.fullScan2", params), trendArgs));
                    resultData.put("termSensitiveBar", explain.toString());
                }

                /* 部门敏感文件分析 */
                JSONObject deptData = chartDataObjMap.getJSONObject("groupSensitiveBar");
                if (deptData != null && !deptData.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray seriesData = (JSONArray) deptData.get("seriesData");
                    JSONArray xaxisData = (JSONArray) deptData.get("xaxisData");
                    /* 部门违规泄露情况排序, 获取触发风险最多的部门信息，和其余部门信息 */
                    List maxDeptNames = new LinkedList();
                    List otherDeptNames = new LinkedList();

                    /* 获取最大值的key(value)，再列出其余key(value) */
                    Long maxDeptCount = seriesData.getLong(0);
                    for (int j = 0; j < seriesData.size(); j++) {
                        Long count = seriesData.getLong(j);
                        if (maxDeptCount.equals(count)) {
                            maxDeptNames.add(xaxisData.getString(j));
                        } else {
                            otherDeptNames.add(xaxisData.getString(j) + "(" + count + ")");
                        }
                    }
                    Double deptProportion = maxDeptCount / (total * 0.01);
                    Object[] args = new Object[]{StringUtil.join(maxDeptNames, "、"), maxDeptCount, deptProportion, StringUtil.join(otherDeptNames, "、")};
                    params.clear();
                    params.put("attention", (deptProportion.compareTo(30.0) < 0) ? "" : I18nUtils.get("interpretation.attention"));
                    params.put("others", otherDeptNames.isEmpty() ? "" : I18nUtils.get("interpretation.others"));
                    explain.append(String.format(I18nUtils.get("interpretation.fullScan3", params), args));
                    resultData.put("deptDistribute", explain.toString());
                }
                /* 敏感文件规则分析 */
                JSONObject ruleTypeData = chartDataObjMap.getJSONObject("ruleTypePie");
                if (ruleTypeData != null) {
                    explain = new StringBuilder();
                    JSONArray chartData = (JSONArray) ruleTypeData.get("chartData");
                    JSONArray chartDataArray = new JSONArray();
                    for (Object item : chartData) {
                        JSONObject itemObj = (JSONObject) item;
                        JSONObject object = new JSONObject();
                        object.put("name", itemObj.getString("name"));
                        object.put("value", itemObj.getLong("value"));
                        chartDataArray.add(object);
                    }
                    List maxTermNames = new LinkedList();
                    List otherTermNames = new LinkedList();
                    JSONObject maxEntry = (JSONObject) ReportUtils.sortByValue(chartDataArray, maxTermNames, otherTermNames, I18nUtils.get("interpretation.fileUnit")).get(0);
                    Long maxEntryValue = (Long) maxEntry.get("value");
                    Object[] trendArgs = new Object[]{StringUtil.join(maxTermNames, "、"), maxEntryValue * 100.0 / total, StringUtil.join(otherTermNames, "、")};
                    params.clear();
                    params.put("others", otherTermNames.isEmpty() ? "" : I18nUtils.get("interpretation.others"));
                    explain.append(String.format(I18nUtils.get("interpretation.fullScan4", params), trendArgs));
                    resultData.put("sensRule", explain.toString());
                }
                /* 敏感文件严重程度分析 */
                JSONObject severityPieData = chartDataObjMap.getJSONObject("severityPie");
                if (severityPieData != null) {
                    explain = new StringBuilder();
                    JSONArray chartData = (JSONArray) severityPieData.get("chartData");
                    Long serious = 0L, verySerious = 0L;
                    Long slight = 0L, normal = 0L;
                    for (Object chartDatum : chartData) {
                        JSONObject chartDatumObj = (JSONObject) chartDatum;
                        if (chartDatumObj.getString("name").equals("非常严重")) {
                            verySerious = chartDatumObj.getLong("value");
                        } else if (chartDatumObj.getString("name").equals("严重")) {
                            serious = chartDatumObj.getLong("value");
                        } else if (chartDatumObj.getString("name").equals("一般")) {
                            normal = chartDatumObj.getLong("value");
                        } else {
                            slight = chartDatumObj.getLong("value");
                        }
                    }
                    Long seriousAndVerySerious = serious + verySerious;
                    if (serious.equals(0L) && verySerious.equals(0L)) {
                        if (slight.equals(0L) || normal.equals(0L)) {
                            Object[] severityArgs = new Object[]{ slight+normal, (slight + normal) * 100.0 / total };
                            params.clear();
                            params.put("lever", slight.equals(0L) ? I18nUtils.get("pages.severityOptions2") : I18nUtils.get("pages.severityOptions1"));
                            explain.append(String.format(I18nUtils.get("interpretation.fullScan5", params), severityArgs));
                        } else {
                            Object[] severityArgs = new Object[]{ slight, normal, slight * 100.0 / total, normal * 100.0 / total };
                            explain.append(String.format(I18nUtils.get("interpretation.fullScan6"), severityArgs));
                        }
                    } else if (!serious.equals(0L) && !verySerious.equals(0L)) {
                        Object[] severityArgs = new Object[]{ serious, verySerious, seriousAndVerySerious * 100.0 / total };
                        explain.append(String.format(I18nUtils.get("interpretation.fullScan7"), severityArgs));
                    } else {
                        Object[] severityArgs = new Object[]{ serious + verySerious, seriousAndVerySerious * 100.0 / total };
                        params.clear();
                        params.put("lever", serious.equals(0L) ? I18nUtils.get("pages.severityOptions4") : I18nUtils.get("pages.severityOptions3"));
                        explain.append(String.format(I18nUtils.get("interpretation.fullScan8", params), severityArgs));
                    }
                    resultData.put("sensLever", explain.toString());
                }
                itemValueMap.put("explanation", resultData);
            ]]>
        </code>
    </interpretation>

    <interpretation id="15" name="全盘扫描标签信息报表" report-code="94">
        <code>
            <![CDATA[
                import com.tipray.dlp.util.StringUtil;
                import com.alibaba.fastjson.JSONObject;
                import cn.hutool.core.bean.BeanUtil;
                import com.tipray.dlp.report.util.ReportUtils;
                import com.tipray.dlp.util.ReportUtil;
                import com.alibaba.fastjson.JSONArray;
                import com.tipray.dlp.bean.vo.DiskScanVO;
                import org.springframework.http.ResponseEntity;
                import com.alibaba.fastjson.JSON;
                import com.tipray.dlp.bean.vo.DiskScanSensitiveVO;
                import com.tipray.dlp.util.I18nUtils;

                StringBuilder explain = new StringBuilder();
                JSONObject itemValueMap = (JSONObject) data.get("itemValueMap");
                Long total = itemValueMap.getLong("violateTotal");
                Long scanTotal = itemValueMap.getLong("allViolateTotal");

                /* 计算查询上一周期分布图数据的参数 */
                DiskScanVO lastDiskScanVo = new DiskScanVO();
                lastDiskScanVo.setGuid(vo.getGuid());
                ResponseEntity lastDateRes = ReportUtil.getDataJson(lastDiskScanVo, "/sensitiveControl/getTaskOptionStep2", new Object[]{});
                JSONArray lastDateArray = JSON.parseObject((String) lastDateRes.getBody()).getJSONArray("data");
                String lastBatchNo = null;
                for (int i = 1; i < lastDateArray.size(); i++) {
                    JSONObject item = (JSONObject)lastDateArray.get(i);
                    if (vo.getBatchNo().equals(item.get("key"))) {
                        lastBatchNo = lastDateArray.getJSONObject(i - 1).getString("key");
                    }
                }
                StringBuilder temp;
                Map params = new HashMap();
                if (lastBatchNo != null) {
                    DiskScanSensitiveVO lastVo = cn.hutool.core.bean.BeanUtil.toBean(vo, DiskScanSensitiveVO.class);
                    lastVo.setBatchNo(lastBatchNo);
                    ResponseEntity lastRes = ReportUtil.getDataJson(lastVo, "/sensitiveControl/getDiskScanTagHomepageData", new Object[]{});
                    JSONObject lastData = JSON.parseObject((String)lastRes.getBody()).getJSONObject("data");
                    JSONObject lastItemValueMap = (JSONObject)lastData.get("itemValueMap");
                    Long lastViolateTotal = lastItemValueMap.getLong("violateTotal");
                    params.put("lastTotal", lastViolateTotal);
                    System.out.println("last" + lastViolateTotal.toString());
                }
                params.put("scanTotal", scanTotal);
                params.put("total", total);
                temp = ReportUtils.calcDataChange("interpretation.tagFullScan1", params, 0, I18nUtils.get("interpretation.fileUnit"));
                explain.append(temp);

                JSONObject chartDataObjMap = data.getJSONObject("chartDataObjMap");
                /* 终端文档标签分析 */
                Map termSensitiveBar = chartDataObjMap.getJSONObject("terminalUserBarChartData");
                if (termSensitiveBar != null) {
                    JSONArray seriesData = (JSONArray) termSensitiveBar.get("seriesData");
                    JSONArray xaxisData = (JSONArray) termSensitiveBar.get("xaxisData");
                    Long maxTermValue = seriesData.getLongValue(0);
                    List maxTermNames = new LinkedList();
                    List otherTermNames = new LinkedList();
                    for (int i = 0; i < seriesData.size(); i++) {
                        if (seriesData.getLong(i).equals(maxTermValue)) {
                            maxTermNames.add(xaxisData.getString(i));
                        } else {
                            otherTermNames.add(xaxisData.getString(i) + "(" + seriesData.getLong(i) + ")");
                        }
                    }

                    Object[] trendArgs = new Object[]{StringUtil.join(maxTermNames, "、"), maxTermValue, maxTermNames.size() * maxTermValue * 100.0 / total, StringUtil.join(otherTermNames, "、")};
                    params.clear();
                    if (maxTermNames.size() > 1) {
                        params.put("respectively", I18nUtils.get("interpretation.respectively"));
                        params.put("total", I18nUtils.get("interpretation.total"));
                        params.put("others", "");
                    } else {
                        params.put("respectively", "");
                        params.put("total", "");
                        params.put("others", otherTermNames.isEmpty() ? "" : I18nUtils.get("interpretation.others"));
                    }
                    explain.append(String.format(I18nUtils.get("interpretation.tagFullScan2", params), trendArgs));
                    resultData.put("termSensitiveBar", explain.toString());
                }

                /* 部门文档标签分析 */
                JSONObject deptData = chartDataObjMap.getJSONObject("deptPieChartData");
                if (deptData != null && !deptData.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray chartData = (JSONArray) deptData.get("chartData");
                    /* 部门违规泄露情况排序, 获取触发风险最多的部门信息，和其余部门信息 */
                    List maxDeptNames = new LinkedList();
                    List otherDeptNames = new LinkedList();

                    /* 获取最大值的key(value)，再列出其余key(value) */
                    JSONObject maxObject = chartData.getJSONObject(0);
                    Long maxDeptCount = maxObject.getLong("value");
                    for (int j = 0; j < chartData.size(); j++) {
                        JSONObject object = chartData.getJSONObject(j);
                        Long count = object.getLong("value");
                        if (maxDeptCount.equals(count)) {
                            maxDeptNames.add(object.getString("name"));
                        } else if (Double.compare(count.doubleValue(), maxDeptCount * 0.9) > 0) {
                            otherDeptNames.add(object.getString("name") + "(" + count + ")");
                        } else {
                            break;
                        }
                    }
                    Double deptProportion = maxDeptNames.size() * maxDeptCount / (total * 0.01);
                    Object[] args = new Object[]{StringUtil.join(maxDeptNames, "、"), maxDeptCount, deptProportion, StringUtil.join(otherDeptNames, "、")};
                    params.clear();
                    if (maxDeptNames.size() > 1) {
                        params.put("respectively", I18nUtils.get("interpretation.respectively"));
                        params.put("total", I18nUtils.get("interpretation.total"));
                        params.put("others", "");
                    } else {
                        params.put("respectively", "");
                        params.put("total", "");
                        params.put("others", otherDeptNames.isEmpty() ? "" : I18nUtils.get("interpretation.others"));
                    }
                    params.put("attention", (deptProportion.compareTo(30.0) < 0) ? "" : I18nUtils.get("interpretation.attention"));
                    explain.append(String.format(I18nUtils.get("interpretation.tagFullScan3", params), args));
                    resultData.put("deptDistribute", explain.toString());
                }
                /* 文档标签内容分析 */
                JSONObject tagContentData = chartDataObjMap.getJSONObject("tagContentBarChartData");
                List seriesData = (List) tagContentData.get("seriesData");
                List xaxisData = (List) tagContentData.get("xaxisData");
                if (!seriesData.isEmpty()) {
                    explain = new StringBuilder();
                    Integer maxValue = (Integer) seriesData.get(0);
                    List maxTagName = new LinkedList();
                    List otherTagName = new LinkedList();
                    for (int j = 0; j < seriesData.size(); j++) {
                        Integer v = (Integer) seriesData.get(j);
                        if (maxValue.equals(v)) {
                            maxTagName.add(xaxisData.get(j));
                        } else if (Double.compare(v.doubleValue(), maxValue * 0.9) > 0) {
                            otherTagName.add(xaxisData.get(j) + "(" + v + ")");
                        } else {
                            break;
                        }
                    }
                    Object[] trendArgs = new Object[]{StringUtil.join(maxTagName, "、"), maxTagName.size() * maxValue * 100.0 / total, StringUtil.join(otherTagName, "、")};
                    params.clear();
                    if (maxTagName.size() > 1) {
                        params.put("total", I18nUtils.get("interpretation.total"));
                        params.put("others", "");
                    } else {
                        params.put("total", "");
                        params.put("others", otherTagName.isEmpty() ? "" : I18nUtils.get("interpretation.others"));
                    }
                    explain.append(String.format(I18nUtils.get("interpretation.tagFullScan4", params), trendArgs));
                    resultData.put("tagContent", explain.toString());
                }
                /* 文档等级分析 */
                JSONObject severityPieData = chartDataObjMap.getJSONObject("trankPieChartData");
                if (severityPieData != null) {
                    explain = new StringBuilder();
                    JSONArray chartData = (JSONArray) severityPieData.get("chartData");
                    List maxLeverNames = new LinkedList();
                    List otherLeverNames = new LinkedList();
                    JSONArray jsonArray = ReportUtils.sortByValue(chartData, maxLeverNames, otherLeverNames, I18nUtils.get("interpretation.fileUnit") );
                    Long maxCountByLever = jsonArray.getJSONObject(0).getLong("value");
                    Object[] args = new Object[]{StringUtil.join(maxLeverNames, "、"), maxLeverNames.size() * maxCountByLever * 100.0 / total, StringUtil.join(otherLeverNames, "、")};
                    params.clear();
                    if (maxLeverNames.size() > 1) {
                        params.put("respectively", I18nUtils.get("interpretation.respectively"));
                        params.put("total", I18nUtils.get("interpretation.total"));
                        params.put("others", "");
                    } else {
                        params.put("respectively", "");
                        params.put("total", "");
                        params.put("others", otherLeverNames.isEmpty() ? "" : I18nUtils.get("interpretation.others"));
                    }
                    explain.append(String.format(I18nUtils.get("interpretation.tagFullScan5", params), args));
                    resultData.put("fileLever", explain.toString());
                }
                itemValueMap.put("explanation", resultData);
            ]]>
        </code>
    </interpretation>


    <interpretation id="16" name="智能备份报表" report-code="93">
        <code>
            <![CDATA[
                import com.tipray.dlp.util.StringUtil;
                import com.alibaba.fastjson.JSONObject;
                import cn.hutool.core.bean.BeanUtil;
                import com.tipray.dlp.report.util.ReportUtils;
                import com.tipray.dlp.util.ReportUtil;
                import com.alibaba.fastjson.JSONArray;
                import com.tipray.dlp.util.I18nUtils;
                import com.tipray.dlp.bean.vo.OperationalReportVo;
                import com.tipray.dlp.bean.dto.EChartsOptionDTO;
                import java.text.DecimalFormat;
                import org.apache.commons.math3.stat.regression.SimpleRegression;

                StringBuilder explain = new StringBuilder();
                JSONObject itemValueMap = (JSONObject) data.get("itemValueMap");
                Long total = itemValueMap.getLong("violateTotal");
                Long fileSizeTotal = itemValueMap.getLong("fileSizeTotal");

                /* 计算查询上一周期分布图数据的参数 */
                OperationalReportVo lastVo = BeanUtil.toBean(vo, OperationalReportVo.class);
                Long dimValue = ReportUtils.calcLastDate(lastVo.getDimBaseType(), lastVo.getDimValue());
                lastVo.setDimValue(dimValue);
                /* 获取上一周期数据，计算总和 */
                EChartsOptionDTO lastEchartsOption = reportPanelController.getEchartOptionDTO(imgCode, lastVo);
                JSONObject lastData = (JSONObject) lastEchartsOption.getData();
                StringBuilder temp;
                Map params = new HashMap();
                /* 备份文件总数 */
                params.put("total", total);
                if (lastData != null && !lastData.isEmpty()) {
                    Long lastViolateTotal = lastData.getLong("violateTotal");
                    params.put("lastTotal", lastViolateTotal);
                }
                temp = ReportUtils.calcDataChange("interpretation.smartBackup1", params, 0, I18nUtils.get("interpretation.fileUnit"));
                explain.append(temp);
                /* 备份文件大小总数 */
                params.clear();
                params.put("total", fileSizeTotal);
                if (lastData != null && !lastData.isEmpty()) {
                    Long lastViolateTotal = lastData.getLong("fileSizeTotal");
                    params.put("lastTotal", lastViolateTotal);
                }
                temp = ReportUtils.calcDataChange("interpretation.smartBackup2", params, 2, null);
                explain.append(temp);

                JSONObject chartDataObjMap = data.getJSONObject("chartDataObjMap");

                /* 备份业务类型分析 */
                JSONObject lossTypeBarChartData = chartDataObjMap.getJSONObject("businessTypeBarChartData");
                if (lossTypeBarChartData != null) {
                    JSONArray seriesData = (JSONArray) lossTypeBarChartData.get("seriesData");
                    JSONArray xaxisData = (JSONArray) lossTypeBarChartData.get("xaxisData");
                    Long maxValue = seriesData.getLong(0);
                    List typeName = new LinkedList();
                    for (int j = 0; j < seriesData.size(); j++) {
                        Long v = seriesData.getLong(j);
                        if (Double.compare(v.doubleValue(), maxValue * 0.9) > 0) {
                            typeName.add(xaxisData.getString(j) + "（" + new DecimalFormat("#.##%").format(v * 1.0 / total) + "）");
                        } else {
                            break;
                        }
                    }
                    explain.append(I18nUtils.get("interpretation.smartBackup3", new Object[]{StringUtil.join(typeName, "、")}));
                }
                resultData.put("backupType", explain.toString());

                /* 部门备份情况分析 */
                JSONObject deptData = chartDataObjMap.getJSONObject("deptBarChartData");
                if (deptData != null && !deptData.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray seriesData = (JSONArray) deptData.get("seriesData");
                    JSONArray xaxisData = (JSONArray) deptData.get("xaxisData");
                    /* 部门违规泄露情况排序, 获取触发风险最多的部门信息，和其余部门信息 */
                    List maxDeptNames = new LinkedList();
                    List otherDeptNames = new LinkedList();

                    /* 获取最大值的key(value)，再列出其余key(value) */
                    Long maxDeptCount = seriesData.getLong(0);
                    for (int j = 0; j < seriesData.size(); j++) {
                        Long count = seriesData.getLong(j);
                        if (maxDeptCount.equals(count)) {
                            maxDeptNames.add(xaxisData.getString(j));
                        } else {
                            otherDeptNames.add(xaxisData.getString(j) + "(" + count + ")");
                        }
                    }
                    Double deptProportion = maxDeptNames.size() * maxDeptCount * 100.0 / total;
                    Object[] args = new Object[]{StringUtil.join(maxDeptNames, "、"), maxDeptCount, deptProportion, StringUtil.join(otherDeptNames, "、")};
                    params.clear();
                    params.put("respectively", maxDeptNames.size() > 1 ? I18nUtils.get("interpretation.respectively") : "");
                    params.put("total", maxDeptNames.size() > 1 ? I18nUtils.get("interpretation.total") : "");
                    params.put("others", otherDeptNames.isEmpty() ? "" :I18nUtils.get("interpretation.others"));
                    String template = I18nUtils.get("interpretation.smartBackup4", params);
                    explain.append(String.format(template, args));
                    resultData.put("deptBackup", explain.toString());
                }

                /* 终端备份情况分析 */
                Map termOrUserDistribute = chartDataObjMap.getJSONObject("terminalUserPieChartData");
                if (termOrUserDistribute != null && !termOrUserDistribute.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray chartData = (JSONArray) termOrUserDistribute.get("chartData");
                    List maxTermNames = new LinkedList();
                    List otherTermNames = new LinkedList();
                    JSONArray sortedArray = ReportUtils.sortByValue(chartData, maxTermNames, otherTermNames, I18nUtils.get("interpretation.fileUnit") );
                    Long maxCount = ((JSONObject) sortedArray.get(0)).getLong("value");
                    Double proportion = maxTermNames.size() * maxCount * 100.0 / total;
                    Object[] args = new Object[]{StringUtil.join(maxTermNames, "、"), proportion, StringUtil.join(otherTermNames, "、")};
                    params.clear();
                    params.put("userOrTerm", vo.getCountByObject() == 1 ? I18nUtils.get("pages.user") : I18nUtils.get("pages.terminal"));
                    params.put("respectively", maxTermNames.size() > 1 ? I18nUtils.get("interpretation.respectively") : "");
                    params.put("total", maxTermNames.size() > 1 ? I18nUtils.get("interpretation.total") : "");
                    params.put("others", otherTermNames.isEmpty() ? "" :I18nUtils.get("interpretation.others"));
                    explain.append(String.format(I18nUtils.get("interpretation.smartBackup5", params), args));
                    resultData.put("termOrUserBackup", explain.toString());
                }
                /* 备份数量趋势分析  */
                Map trendData = chartDataObjMap.getJSONObject("backupCountTrendLineCharData");
                if (trendData != null && !trendData.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray seriesData = (JSONArray) trendData.get("seriesData");
                    JSONArray xaxisData = (JSONArray) trendData.get("xaxisData");
                    SimpleRegression regression = new SimpleRegression();
                    /* 获取最大值和最小值 */
                    Long maxValue = seriesData.getLong(0);
                    List maxList  = new LinkedList();
                    for (int i = 0; i < seriesData.size(); i++) {
                        Long value = seriesData.getLong(i);
                        regression.addData(i, value);
                        if (value > maxValue) {
                            maxValue = value;
                            maxList.clear();
                            maxList.add(xaxisData.getString(i));
                        } else if (value.equals(maxValue)) {
                            maxList.add(xaxisData.getString(i));
                        }
                    }
                    Double slope = regression.getSlope();
                    Double rSquare = regression.getRSquare();
                    String template = "interpretation.smartBackup8";
                    params.clear();
                    params.put("times", maxValue);
                    params.put("date", StringUtil.join(maxList, "、"));
                    if (rSquare != null && rSquare > 0.8) {
                        if (slope > 0) {
                            template = "interpretation.smartBackup6";
                        } else if (slope < 0) {
                            template = I18nUtils.get("interpretation.smartBackup7");
                        }
                    }
                    explain.append(I18nUtils.get(template, params));

                    resultData.put("backupTrend", explain.toString());
                }
                if (vo.getIsOperationalReport() == null || vo.getIsOperationalReport() == 0) {
                    itemValueMap.put("explanation", resultData);
                } else {
                    StringBuilder explanation = new StringBuilder();
                    explanation.append("<div style=\"text-indent: 2em\">" + resultData.get("backupType") + resultData.get("backupTrend") + "</div>");
                    explanation.append("<div style=\"text-indent: 2em\">" + resultData.get("deptBackup") + resultData.get("termOrUserBackup") + "</div>");
                    itemValueMap.put("explanation", explanation.toString());
                }
            ]]>
        </code>
    </interpretation>


    <interpretation id="17" name="文档标签记录报表" report-code="96">
        <code>
            <![CDATA[
                import com.tipray.dlp.util.StringUtil;
                import com.alibaba.fastjson.JSONObject;
                import cn.hutool.core.bean.BeanUtil;
                import com.tipray.dlp.report.util.ReportUtils;
                import com.tipray.dlp.util.ReportUtil;
                import com.alibaba.fastjson.JSONArray;
                import com.tipray.dlp.util.I18nUtils;
                import com.tipray.dlp.bean.vo.OperationalReportVo;
                import com.tipray.dlp.bean.dto.EChartsOptionDTO;
                import java.text.DecimalFormat;
                import org.apache.commons.math3.stat.regression.SimpleRegression;

                StringBuilder explain = new StringBuilder();
                JSONObject itemValueMap = (JSONObject) data.get("itemValueMap");
                Long total = itemValueMap.getLong("violateTotal");

                /* 计算查询上一周期分布图数据的参数 */
                OperationalReportVo lastVo = BeanUtil.toBean(vo, OperationalReportVo.class);
                Long dimValue = ReportUtils.calcLastDate(lastVo.getDimBaseType(), lastVo.getDimValue());
                lastVo.setDimValue(dimValue);
                /* 获取上一周期数据，计算总和 */
                EChartsOptionDTO lastEchartsOption = reportPanelController.getEchartOptionDTO(imgCode, lastVo);
                JSONObject lastData = (JSONObject) lastEchartsOption.getData();
                StringBuilder temp;
                Map params = new HashMap();
                /* 标签变更总次数 */
                params.put("total", total);
                if (lastData != null) {
                    Long lastViolateTotal = lastData.getLong("violateTotal");
                    params.put("lastTotal", lastViolateTotal);
                }
                temp = ReportUtils.calcDataChange("interpretation.documentTag2", params, 0, I18nUtils.get("pages.openTimes2"));
                explain.append(temp);

                JSONObject chartDataObjMap = data.getJSONObject("chartDataObjMap");

                /* 标签内容变更情况分析 */
                JSONObject tagContentData = chartDataObjMap.getJSONObject("tagContentTypeBarChartData");
                if (tagContentData != null) {
                    JSONArray seriesData = (JSONArray) tagContentData.get("seriesData");
                    JSONArray xaxisData = (JSONArray) tagContentData.get("xaxisData");

                    JSONArray insertData = seriesData.getJSONObject(0).getJSONArray("data");
                    JSONArray deleteData = seriesData.getJSONObject(1).getJSONArray("data");
                    JSONArray invalidData = seriesData.getJSONObject(2).getJSONArray("data");

                    Long maxValue = 0L;
                    List typeName = new LinkedList();
                    for (int j = 0; j < xaxisData.size(); j++) {
                        Long insertValue = insertData.getLong(j);
                        Long deleteValue = deleteData.getLong(j);
                        Long invalidValue = invalidData.getLong(j);
                        Long sum = insertValue + deleteValue + invalidValue;
                        if (maxValue.compareTo(sum) < 0) {
                            maxValue = sum;
                            typeName.add(xaxisData.getString(j) + "（"
                                    + "新增：" + insertValue + "次，"
                                    + "删除：" + deleteValue + "次，"
                                    + "失效：" + invalidValue + "次）");
                        } else {
                            break;
                        }
                    }
                    explain.append(I18nUtils.get("interpretation.documentTag3", new Object[]{StringUtil.join(typeName, "、")}));
                }
                resultData.put("backupType", explain.toString());

                /* 终端用户文档标签变更情况分析 */
                Map termOrUserDistribute = chartDataObjMap.getJSONObject("terminalUserBarChartData");
                if (termOrUserDistribute != null && !termOrUserDistribute.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray seriesData = (JSONArray) termOrUserDistribute.get("seriesData");
                    JSONArray xaxisData = (JSONArray) termOrUserDistribute.get("xaxisData");
                    List maxTermNames = new LinkedList();
                    List otherTermNames = new LinkedList();
                    Long maxCount = seriesData.getLong(0);
                    for (int j = 0; j < seriesData.size(); j++) {
                        Long count = seriesData.getLong(j);
                        if (maxCount.equals(count)) {
                            maxTermNames.add(xaxisData.getString(j));
                        } else {
                            otherTermNames.add(xaxisData.getString(j) + "(" + count + ")");
                        }
                    }
                    Double proportion = maxTermNames.size() * maxCount * 100.0 / total;
                    Object[] args = new Object[]{StringUtil.join(maxTermNames, "、"), maxCount, proportion, StringUtil.join(otherTermNames, "、")};
                    params.clear();
                    params.put("userOrTerm", vo.getCountByObject() == 1 ? I18nUtils.get("pages.user") : I18nUtils.get("pages.terminal"));
                    params.put("respectively", maxTermNames.size() > 1 ? I18nUtils.get("interpretation.respectively") : "");
                    params.put("total", maxTermNames.size() > 1 ? I18nUtils.get("interpretation.total") : "");
                    params.put("others", otherTermNames.isEmpty() ? "" :I18nUtils.get("interpretation.others"));
                    explain.append(String.format(I18nUtils.get("interpretation.documentTag5", params), args));
                    resultData.put("termOrUserBackup", explain.toString());
                }

                /* 部门文档标签变更情况分析 */
                JSONObject deptData = chartDataObjMap.getJSONObject("deptPieChartData");
                if (deptData != null && !deptData.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray chartData = (JSONArray) deptData.get("chartData");
                    /* 部门违规泄露情况排序, 获取触发风险最多的部门信息，和其余部门信息 */
                    List maxDeptNames = new LinkedList();
                    List otherDeptNames = new LinkedList();
                    JSONArray sortedArray = ReportUtils.sortByValue(chartData, maxDeptNames, otherDeptNames, I18nUtils.get("pages.openTimes2"));
                    Long maxDeptCount = ((JSONObject) sortedArray.get(0)).getLong("value");

                    Double deptProportion = maxDeptNames.size() * maxDeptCount * 100.0 / total;
                    Object[] args = new Object[]{StringUtil.join(maxDeptNames, "、"), maxDeptCount, deptProportion, StringUtil.join(otherDeptNames, "、")};
                    params.clear();
                    params.put("respectively", maxDeptNames.size() > 1 ? I18nUtils.get("interpretation.respectively") : "");
                    params.put("total", maxDeptNames.size() > 1 ? I18nUtils.get("interpretation.total") : "");
                    params.put("others", otherDeptNames.isEmpty() ? "" :I18nUtils.get("interpretation.others"));
                    String template = I18nUtils.get("interpretation.documentTag4", params);
                    explain.append(String.format(template, args));
                    resultData.put("deptBackup", explain.toString());
                }

                /* 文档等级变更情况分析 */
                Map trankBarChartData = chartDataObjMap.getJSONObject("trankBarChartData");
                if (trankBarChartData != null && !trankBarChartData.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray seriesData = (JSONArray) trankBarChartData.get("seriesData");
                    JSONArray xaxisData = (JSONArray) trankBarChartData.get("xaxisData");
                    List maxTermNames = new LinkedList();
                    List otherTermNames = new LinkedList();
                    Long maxCount = seriesData.getLong(0);
                    for (int j = 0; j < seriesData.size(); j++) {
                        Long count = seriesData.getLong(j);
                        if (maxCount.equals(count)) {
                            maxTermNames.add(xaxisData.getString(j));
                        } else if (Double.compare(count.doubleValue(), maxCount * 0.8) > 0){
                            otherTermNames.add(xaxisData.getString(j) + "(" + count + ")");
                        }
                    }
                    Double proportion = maxTermNames.size() * maxCount * 100.0 / total;
                    Object[] args = new Object[]{StringUtil.join(maxTermNames, "、"), maxCount, proportion, StringUtil.join(otherTermNames, "、")};
                    params.clear();
                    params.put("respectively", maxTermNames.size() > 1 ? I18nUtils.get("interpretation.respectively") : "");
                    params.put("total", maxTermNames.size() > 1 ? I18nUtils.get("interpretation.total") : "");
                    params.put("others", otherTermNames.isEmpty() ? "" :I18nUtils.get("interpretation.others"));
                    explain.append(String.format(I18nUtils.get("interpretation.documentTag1", params), args));
                    resultData.put("fileLeverBar", explain.toString());
                }

                /* 文档标签内容变更趋势分析  */
                Map trendData = chartDataObjMap.getJSONObject("dateLineCharData");
                if (trendData != null && !trendData.isEmpty()) {
                    explain = new StringBuilder();
                    JSONArray seriesData = (JSONArray) trendData.get("seriesData");
                    JSONArray xaxisData = (JSONArray) trendData.get("xaxisData");
                    SimpleRegression regression = new SimpleRegression();
                    /* 获取最大值和最小值 */
                    Long maxValue = seriesData.getLong(0);
                    List maxList  = new LinkedList();
                    for (int i = 0; i < seriesData.size(); i++) {
                        Long value = seriesData.getLong(i);
                        regression.addData(i, value);
                        if (value > maxValue) {
                            maxValue = value;
                            maxList.clear();
                            maxList.add(xaxisData.getString(i));
                        } else if (value.equals(maxValue)) {
                            maxList.add(xaxisData.getString(i));
                        }
                    }
                    Double slope = regression.getSlope();
                    Double rSquare = regression.getRSquare();
                    String template = "interpretation.documentTag8";
                    params.clear();
                    params.put("times", maxValue);
                    params.put("date", StringUtil.join(maxList, "、"));
                    if (rSquare != null && rSquare > 0.8) {
                        if (slope > 0) {
                            template = "interpretation.documentTag6";
                        } else if (slope < 0) {
                            template = I18nUtils.get("interpretation.documentTag7");
                        }
                    }
                    explain.append(I18nUtils.get(template, params));

                    resultData.put("backupTrend", explain.toString());
                }
                if (vo.getIsOperationalReport() == null || vo.getIsOperationalReport() == 0) {
                    itemValueMap.put("explanation", resultData);
                } else {
                    StringBuilder explanation = new StringBuilder();
                    explanation.append("<div style=\"text-indent: 2em\">" + resultData.get("backupType") + resultData.get("backupTrend") + "</div>");
                    explanation.append("<div style=\"text-indent: 2em\">" + resultData.get("deptBackup") + resultData.get("termOrUserBackup") + "</div>");
                    itemValueMap.put("explanation", explanation.toString());
                }
            ]]>
        </code>
    </interpretation>
</root>
