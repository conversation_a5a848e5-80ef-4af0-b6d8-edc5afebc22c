package com.TiprayAPI.cpp;

import com.sun.jna.Structure;

@Structure.FieldOrder({"m_dwFilter", "m_byVersion", "m_byEncLevel", "m_wUserId","m_wGroupId","m_szGUID"})
public class EncOption extends Structure {
    public int   m_dwFilter;
    public byte  m_byVersion;
    public byte  m_byEncLevel;
    public short m_wUserId;
    public short m_wGroupId;
    public byte[] m_szGUID = new byte[32];

    //内存对齐
    public EncOption() {
        super(ALIGN_NONE);
    }
    public static class ByReference extends EncOption implements Structure.ByReference {
        public ByReference() {}
    }
}
