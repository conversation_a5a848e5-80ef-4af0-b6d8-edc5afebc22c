package com.TiprayAPI.cpp;

import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.WString;
import com.sun.jna.ptr.IntByReference;
import com.tipray.dlp.cpp.Ldwebdf4Linux;
import com.tipray.dlp.util.ArmEnvUtil;
import com.tipray.dlp.util.SystemUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LdTiprayAPIDll {
	private static Lddf lddf;
	final static int ctAnsi = 0;
	final static int ctUnicode = 1;

	/*enum _enDecryptStatus
	{
		dsDecryptOK = 0,//成功
		dsFileError = 1,//找不到源文件等原因打不开源文件
		dsNotEncryptFile = 2,//已经是明文文件不需要再次解密
		dsPwdNotFound = 3,//未找到对应序号的密钥
		dsPwdError = 4,//密钥错误
		dsHeaderError = 5,//加密头错误，可能被篡改而校验不过、长度不对等
		dsNotSupport = 6,//未支持的加密文件版本，可能是本终端版本不支持的加密文件版本
		dsWriteError = 7,//写加、解密临时文件过程失败
		dsReadError = 8,//读取原文件失败
		dsMoveFileError1 = 9,//将源文件改名为【待删除的临时文件】失败
		dsMoveFileError2 = 10,//删除【待删除的临时文件】失败
		dsMoveFileError3 = 11,//将新产生的加、解密后的临时文件改名为源文件失败
		dsEncryptFile = 12,//已经是加密文件不需再次加密
		dsCreateDstFileErr = 13,//无法创建用于加解密的临时文件
		dsExtrCheckSumErr = 14,//拓展部分校验不对
		dsExtrCheckSumTypeNotSupport = 15,//拓展部分存在本版本组件不支持的类型
		dsSessionBufferTooSmall = 16,//传入的SessionBuffer太小，需要按返回的结构中的新大小申请
		dsEncStdVersionNotSupport = 17,//指定的加密算法-模式本版本组件不支持。

	};*/
	public interface Lddf extends Library {

		int DllInit();
		// file 文件路径；v_byCharType 路径编码， 0-ansi；1-unicode
		int DllEf(String file, int v_byCharType);
		// file 文件路径；v_byCharType 路径编码， 0-ansi；1-unicode
		int DllDf(byte[] file, int v_byCharType);

		//返回值：0成功 -1源文件路径为空 1打开源文件失败 5不是加密文件
		int DllMdyEFLv(String v_szSrcFile,String v_szDestFile,int v_dwSecretLevel,IntByReference v_dwSrcSecretLevel, int v_byCharType );

		//返回值：0成功 -1源文件路径为空 1打开源文件失败 5不是加密文件
		int DllQEFLv(String v_szSrcFile,IntByReference v_dwSrcSecretLevel, int v_byCharType );

		//返回值：0成功 -1源文件路径为空 1打开源文件失败 5不是加密文件
		int DllQUserID(String v_szSrcFile,IntByReference v_dwUserId,IntByReference v_dwGroupId, int v_byCharType );

		int DllEfEx(String file, int v_byCharType,EncOption.ByReference v_pEncOption);
		/*
              修改文件密级，如明文文件则v_pEncOption参数进行加密(可指定密级及操作员分组id)
              参数: v_szSrcFile   源文件路径
               v_szDestFile  目标文件路径（如要覆盖则传入与v_szSrcFile一样路径）
               v_dwSecretLevel   修改的密级
               v_dwSrcSecretLevel 返回源文件密级
               v_byCharType  路径字符编码 0-ansi 1-unicode
               v_pEncOption 传入要加密用户、分组id及密级等信息
            返回值 : 0 失败 1 成功 2 参数错误
       */
		int DllMdyEFLvEx(String v_szSrcFile,String v_szDestFile,int v_dwSecretLevel,IntByReference v_dwSrcSecretLevel, int v_byCharType,EncOption.ByReference v_pEncOption);
		/*
                检查是否可以加密，如果可以，则返回文件编号
                参数:
            v_byBuf      文件流头部部分
            v_iBufLen    传进去的文件流长度，不少于512byte
            v_pdwFileNo  返回文件编号
            v_pEncOption 传入要加密用户、分组id及密级等信息
              返回值: 98-不能工作，97-参数错误；其他值 enDecryptStatus(0-成功)
        */
		int DllCanEncryptByBuf(byte[] v_byBuf, int v_iBufLen, IntByReference v_pdwFileNo, EncOptionEx.ByReference v_pEncOptionEx );
		/*
            加密文件流(可指定密级及操作员分组id)
            参数: v_byNewBuf   用来接收加密后的文件流的内存(缓存)
              v_piNewLen   传入缓存大小，返回加密后的文件流长度（文件头 缓存大小必须比v_iOldLen大4096）
              v_byOldBuf   要加密的文件流
              v_iOldLen    要加密的文件流长度，如果分段加密，除非是文件尾传入真实的文件流长度，其他的文件流长度必须是512倍数
              v_ulFilePos  文件流起始位置对于文件头的偏移量，用于文件流分段加密用
              v_dwFileNo   DllCanEncryptByBuf()传出的文件编号
           返回值:  0 失败 1 成功 2 参数错误
        */
		int DllEncryptByBuf( byte[] v_byNewBuf, IntByReference v_piNewLen, byte[] v_byOldBuf, int v_iOldLen, long v_ulFilePos, int v_dwFileNo);

		/*
		加密文件流完成后，释放文件句柄
        参数 :  v_dwFileNo  文件编号
        返回值 : 0 失败  1 成功
        */
		int DllReleaseEncryptByBuf( int v_dwFileNo );

		/*
        检查是否可以解密，如果可以，则返回文件编号
        参数: v_byBuf    文件流头部部分
              v_iBufLen  传进去的文件流长度，不少于512byte
              v_pdwFileNo  返回文件编号
              返回值: 98-不能工作，97-参数错误；其他值 enDecryptStatus(0-成功)
       */
		int DllCanDecryptByBuf(byte[] v_byBuf, int v_iBufLen, IntByReference v_pdwFileNo);

		/*解密文件流
		参数: v_byNewBuf  用来接收解密后的文件流的内存
			  v_piNewLen  返回解密后的文件流长度
			  v_byOldBuf  要解密的文件流
			  v_iOldLen   要解密的文件流长度，如果分段解密，除非是文件尾传入真实的文件流长度，其他的文件流长度必须是512倍数
			  v_ulFilePos 文件流起始位置对于文件头的偏移量，用于文件流分段解密用
			  v_dwFileNo  DllCanDecryptByBuf()传出的文件编号
			  返回值: 0 失败 1 成功 2 参数错误
		*/
		int DllDecryptByBuf(byte[] v_byNewBuf, IntByReference v_piNewLen, byte[] v_byOldBuf,  int v_iOldLen,long v_ulFilePos, int v_dwFileNo);

		/*解密文件流完成后，释放文件句柄
		参数 :  v_dwFileNo  文件编号
		返回值 : 0 失败  1 成功
		*/
		int DllReleaseDecryptByBuf(int v_dwFileNo);

		int DllLdwebdfInit();

		int DllLdwebdfInitEx(String fileExt);

	}

	private static boolean getJdkIs64bits() {
		String bit = System.getProperty("sun.arch.data.model");
		if (bit != null && "32".equalsIgnoreCase(bit)) {
			return false;
		}else if (bit != null && "64".equalsIgnoreCase(bit)){
			return true;
		}else{
			return false;
		}
	}

	@SuppressWarnings("deprecation")
	private static Lddf getNative(){
		if(lddf == null){
			synchronized (LdTiprayAPIDll.class){
				if(lddf == null) {
					if (SystemUtil.isWindowOs()){
						lddf = getJdkIs64bits() ? (Lddf) Native.loadLibrary("lddf_64", Lddf.class) : (Lddf) Native.loadLibrary("lddf_32", Lddf.class);
					}else if (SystemUtil.isLinuxOs()) {
						String filePath = System.getProperty("catalina.home") + "/TRDLP/LdFile/Lddf_" + System.getProperty("sun.arch.data.model") + ".so";
						lddf = (Lddf) Native.loadLibrary(filePath, Lddf.class);
					}
				}
			}
		}
		return lddf;
	}


	public static int Init()
	{
		return getNative().DllInit();
	}

	public static int EncryptFile(String v_strFilePath)
	{
		return getNative().DllEf(v_strFilePath, ctAnsi);
	}

	public static int DecryptFile(byte[] v_strFilePath, int code) {
		return getNative().DllDf(v_strFilePath, code);
	}

	public static int CanDecryptByBuf(byte[] v_byBuf, int v_iBufLen, IntByReference v_pdwFileNo) {
		return getNative().DllCanDecryptByBuf(v_byBuf, v_iBufLen,v_pdwFileNo);
	}

	public static int DecryptByBuf(byte[] v_byNewBuf, IntByReference v_piNewLen, byte[] v_byOldBuf,  int v_iOldLen,long v_ulFilePos, int v_dwFileNo) {
		return getNative().DllDecryptByBuf(v_byNewBuf, v_piNewLen,v_byOldBuf,v_iOldLen,v_ulFilePos,v_dwFileNo);
	}

	public static int ReleaseDecryptByBuf(int v_dwFileNo)
	{
		return getNative().DllReleaseDecryptByBuf(v_dwFileNo);
	}

	public static int CanEncryptByBuf(byte[] v_byBuf, int v_iBufLen, IntByReference v_pdwFileNo, EncOptionEx.ByReference v_pEncOptionEx ) {
		return getNative().DllCanEncryptByBuf(v_byBuf,  v_iBufLen,  v_pdwFileNo, v_pEncOptionEx);
	}

	public static int EncryptByBuf( byte[] v_byNewBuf, IntByReference v_piNewLen, byte[] v_byOldBuf, int v_iOldLen, long v_ulFilePos, int v_dwFileNo) {
		return getNative().DllEncryptByBuf(v_byNewBuf,  v_piNewLen,v_byOldBuf,  v_iOldLen,  v_ulFilePos,  v_dwFileNo);
	}

	public static int ReleaseEncryptByBuf( int v_dwFileNo )
	{
		return getNative().DllReleaseEncryptByBuf(v_dwFileNo);
	}

	public static int MdyEFLv(String v_szSrcFile,String v_szDestFile,int v_dwSecretLevel,IntByReference v_dwSrcSecretLevel ) {
		return getNative().DllMdyEFLv(v_szSrcFile, v_szDestFile, v_dwSecretLevel, v_dwSrcSecretLevel, ctAnsi );
	}

	public static int QEFLv(String v_szSrcFile,IntByReference v_dwSrcSecretLevel ) {
		return getNative().DllQEFLv(v_szSrcFile, v_dwSrcSecretLevel, ctAnsi );
	}

	public static int QUserId(String v_szSrcFile,IntByReference v_dwUserId,IntByReference v_dwGroupId ) {
		return getNative().DllQUserID(v_szSrcFile, v_dwUserId,v_dwGroupId,ctAnsi );
	}

	public static int EfEx(String v_szSrcFile,EncOption.ByReference v_pEncOption){
		return getNative().DllEfEx(v_szSrcFile,  ctAnsi ,v_pEncOption);
	}

	public static int MdyEFLvEx(String v_szSrcFile,String v_szDestFile,int v_dwSecretLevel,IntByReference v_dwSrcSecretLevel,EncOption.ByReference v_pEncOption) {
		return getNative().DllMdyEFLvEx(v_szSrcFile,v_szDestFile,v_dwSecretLevel,v_dwSrcSecretLevel, ctAnsi,v_pEncOption);
	}

	public static int DllLdwebdfInit() {
		return getNative().DllLdwebdfInit();
	}

	public static int DllLdwebdfInitEx(String fileExt) {
		return getNative().DllLdwebdfInitEx(fileExt);
	}

}
