package com.TiprayAPI.cpp;

public class TiprayAPI {
    public TiprayAPI() {
    }

    public native int DllInit();

    public native int DllEf(String var1, int var2);

    public native int DllDf(String var1, int var2);

    public native int DllCeBuf(byte[] var1, int var2);

    public native int DllEfBuf(byte[] var1, byte[] var2, int var3, int var4, int var5);

    public native int DllReBuf(int var1);

    public native int DllCdBuf(byte[] var1, int var2);

    public native int DllDfBuf(byte[] var1, byte[] var2, int var3, int var4, int var5);

    public native int DllRdBuf(int var1);

    public native int DllMdyEFLv(String var1, String var2, int var3, Integer var4, int var5);

    public native int DllQEFLv(String var1, Integer var2, int var3);

    public native int DllQUserId(String var1, Integer var2, Integer var3, int var4);

    public native int DllEfEx(String var1, int var2, EncOption var3);

    public native int DllMdyEFLvEx(String var1, String var2, int var3, Integer var4, int var5, EncOption var6);

    public native int DllCanEncryptByBuf(byte[] var1, int var2, Integer var3, EncOptionEx var4);

    public native int DllEncryptByBuf(byte[] var1, Integer var2, byte[] var3, int var4, long var5, int var7, EncOptionEx var8);

    public native int DllReleaseEncryptByBuf(int var1);

    public native int DllCanDecryptByBuf(byte[] var1, int var2, Integer var3);

    public native int DllDecryptByBuf(byte[] var1, Integer var2, byte[] var3, int var4, long var5, int var7);

    public native int DllReleaseDecryptByBuf(int var1);

    public native int DllLdwebInit();

    public native int DllLdwebInitEx(String var1);
}
