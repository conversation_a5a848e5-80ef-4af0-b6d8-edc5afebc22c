package com.TiprayAPI.cpp;

import com.sun.jna.Structure;

@Structure.FieldOrder({"m_dwFilter", "m_byVersion", "m_byEncLevel", "m_dwUserId","m_dwGroupId","m_szGUID","m_byEncStdType"})
public class EncOptionEx extends Structure {
    /**标示传入了哪些参数,按位存   
     * eoEncVersion = 1(0x00000001),
     * eoEncLevel   = 2(0x00000002),
     * eoUserId     = 4(0x00000004),
     * eoGroupId    = 8(0x00000008),
     * eoGuid       = 16(0x00000010),
     * eoStdType    = 32(0x00000020),    
     **/
    public int m_dwFilter;
    // 加密版本，取值范围(04一代07二代,08三代,09四代）
    public byte m_byVersion;
    // 密级 取值范围(0-4)
    public byte m_byEncLevel;
    // 加密系统 上的操作员id
    public int m_dwUserId;
    // 加密系统 上的部门id
    public int m_dwGroupId;
    //文件GUID-长度32 一般不传，放空
    public byte[] m_szGUID = new byte[32];
    // 加密标准  取值范围(0-RC4,1-AES,23-祖冲之),一般放空可不传
    public byte	m_byEncStdType;//加密标准


    //内存对齐
    public EncOptionEx() {
        super(ALIGN_NONE);
    }

    public static class ByReference extends EncOptionEx implements Structure.ByReference {
        public ByReference() {}
    }
}
