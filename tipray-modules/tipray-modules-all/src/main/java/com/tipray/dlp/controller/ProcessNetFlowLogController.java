package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.ProcessNetFlowLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.NetFlowLogVO;
import com.tipray.dlp.service.ProcessNetFlowLogService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 流量统计Controller
 */
@Oplog(ProcessNetFlowLog.class)
@RestController
@RequestMapping("/log/processNetFlow")
public class ProcessNetFlowLogController {
    @Resource
    private ProcessNetFlowLogService service;

    /**
     * 查询流量统计日志
     * @param vo
     * @return
     */
    @Oplog(value = NetFlowLogVO.class,name = "route.netFlowLog")
    @PostMapping(value = "getPage")
    public GridPageDTO<ProcessNetFlowLog> getPage(@RequestBody NetFlowLogVO vo){
        return service.getLogPage(vo);
    }

    /**
     * 导出流量统计记录信息
     * 根据查询筛选条件导出Excel
     * @param vo
     * @return
     */
    @Oplog(value = NetFlowLogVO.class,name = "route.netFlowLog")
    @PostMapping(value = "export")
    public ResponseDTO exportExcelByLogVO(@RequestBody NetFlowLogVO vo){
        return ResponseDTO.success(service.exportExcel(vo));
    }
    /**
     * 删除日志
     * @param list
     * @return
     */
    @Oplog(name = "route.netFlowLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<ProcessNetFlowLog> list) {
        service.deleteBatch(list);
        return ResponseDTO.success();
    }
}
