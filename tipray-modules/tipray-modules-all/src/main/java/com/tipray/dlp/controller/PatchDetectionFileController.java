package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.PatchDetectionFile;
import com.tipray.dlp.bean.dict.OperationTypeDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.PatchDetectionFileVO;
import com.tipray.dlp.bean.vo.PatchVO;
import com.tipray.dlp.service.PatchDetectionService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 补丁检测包Controller
 */
@Oplog(PatchDetectionFile.class)
@RestController
@RequestMapping("/patchDetectionFile")
public class PatchDetectionFileController {
    @Resource
    private PatchDetectionService patchDetectionService;

    /** 查询补丁检测包*/
    @PostMapping(value = "/getPatchDetectionPage")
    public GridPageDTO<PatchDetectionFile> getPatchDetectionPage(@RequestBody PatchDetectionFileVO vo) {
        return patchDetectionService.getPatchDetectionPage(vo);
    }

    /** 更新补丁检测包*/
    @Oplog(name = "pages.patchDetectionPackage")
    @PostMapping(value = "/update")
    public void updatePatchDetection(PatchDetectionFile patchDetectionFile) {
        // 修改不改变下载状态
        patchDetectionFile.setDownloadStatus(null);
        patchDetectionService.updatePatchDetectionAndCycle(patchDetectionFile);
    }

    /** 导入补丁检测包*/
    @PostMapping(value = "/upload")
    public PatchDetectionFile importDetectionFile(MultipartFile uploadFile,Integer systemVersion, String taskId) throws IOException {
        return patchDetectionService.importDetectionFile(uploadFile,systemVersion, taskId);
    }

    /** 取消下载 */
    @PostMapping(value = "/cancelUpload")
    public void cancelUpload(@RequestParam("taskId") String taskId) {
        patchDetectionService.cancelImportDetectionFile(taskId);
    }

    /** 下载补丁检测包*/
    @Oplog(name = "pages.patchDetectionPackage", type = OperationTypeDict.DOWNLOAD)
    @PostMapping(value = "/clickToDownload")
    public PatchDetectionFile clickToDownload(@RequestBody PatchDetectionFile patchDetectionFile) {
        return patchDetectionService.clickToDownload();
    }

    /**导出补丁检测包*/
    @Oplog(value = PatchVO.class, name = "pages.detectionPackage")
    @PostMapping(value = "export")
    public ResponseDTO exportExcelBySelectId(@RequestBody PatchVO vo, HttpServletResponse response){
        return ResponseDTO.success(patchDetectionService.exportExcel(vo, response));
    }

    /** 测试检测包下载*/
    @GetMapping(value = "/testDownload")
    public void testDownload(String url) {
        if(StringUtils.isBlank(url)){
            url = "https://catalog.s.download.windowsupdate.com/microsoftupdate/v6/wsusscan/wsusscn2.cab";
        }
        patchDetectionService.testDownload(url);
    }
}
