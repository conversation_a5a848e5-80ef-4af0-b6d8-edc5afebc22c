package com.tipray.dlp.dao;

import com.tipray.dlp.bean.SecretLevelLogDetail;
import com.tipray.dlp.bean.vo.SecretLevelLogDetailVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * (SecretLevelLogDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-05-06 19:14:44
 */
@Repository
@ShardingDataSource
public interface SecretLevelLogDetailDao extends BaseDao<SecretLevelLogDetail>{

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    SecretLevelLogDetail getById(Long id);

    /**
     * 通过实体作为筛选条件查询
     *
     * @return 对象列表
     */
    List<SecretLevelLogDetail> list();
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(SecretLevelLogDetailVO vo);
     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<SecretLevelLogDetail> listByVO(SecretLevelLogDetailVO vo);
}
