package com.tipray.dlp.dao;

import com.tipray.dlp.bean.DenseTransLogDetail;
import com.tipray.dlp.bean.vo.DenseTransLogDetailVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import com.tipray.dlp.mybatis.bean.PageVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * (SecretLevelLogDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-05-06 19:14:44
 */
@Repository
@ShardingDataSource
public interface DenseTransLogDetailDao extends BaseDao<DenseTransLogDetail> {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    DenseTransLogDetail getById(Long id);

    /**
     * 通过实体作为筛选条件查询
     *
     * @return 对象列表
     */
    List<DenseTransLogDetail> list();
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(DenseTransLogDetailVO vo);
     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<DenseTransLogDetail> listByVO(DenseTransLogDetailVO vo);
}
