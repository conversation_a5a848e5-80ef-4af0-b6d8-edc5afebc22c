package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.LossTypeParam;
import com.tipray.dlp.bean.SensitiveAlarmDetailLog;
import com.tipray.dlp.bean.SensitiveAlarmLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.SensitiveAlarmLogVO;
import com.tipray.dlp.service.AlarmTypeParamService;
import com.tipray.dlp.service.LossTypeParamService;
import com.tipray.dlp.service.SensitiveAlarmLogService;
import com.tipray.dlp.util.PermissionUtil;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
/**
 * 敏感告警记录
 * @author: shensl
 * @date: 2020/6/6
 */
@RestController
@Oplog(SensitiveAlarmLog.class)
@RequestMapping("/log/SensitiveAlarmLog")
public class SensitiveAlarmLogController {

    @Resource
    private SensitiveAlarmLogService service;
    @Resource
    private AlarmTypeParamService alarmTypeParamService;
    @Resource
    private LossTypeParamService lossTypeParamService;

    /**
     * 分页查询敏感告警记录
     * @param vo
     * @return
     */
    @Oplog(value = SensitiveAlarmLogVO.class,name = "pages.sensitiveAlarmDetailLog")
    @PostMapping(value = "findPage")
    public GridPageDTO<SensitiveAlarmDetailLog> getPage(@RequestBody SensitiveAlarmLogVO vo) {

        GridPageDTO<SensitiveAlarmDetailLog> alarmDetailLogGridPageDTO = new GridPageDTO<>();
        Collection<SensitiveAlarmDetailLog> detailLogCollection = new ArrayList<>();
        GridPageDTO<SensitiveAlarmLog> alarmLogGridPageDTO = service.getPage(vo);

        for(SensitiveAlarmLog alarmLog : alarmLogGridPageDTO.getItems()){
            detailLogCollection.add(alarmLog.toDetail());
        }
        alarmDetailLogGridPageDTO.setTotal(alarmLogGridPageDTO.getTotal());
        alarmDetailLogGridPageDTO.setItems(detailLogCollection);
        alarmDetailLogGridPageDTO.setDlpTotal(alarmLogGridPageDTO.getDlpTotal());
        alarmDetailLogGridPageDTO.setHideTotal(alarmLogGridPageDTO.getHideTotal());
        return alarmDetailLogGridPageDTO;
    }

    /*获取敏感泄露方式（违规类型）列表
    @PostMapping(value = "getSensitiveLossType")
    public List<AlarmTypeParam> getAlarmType(@Param("type") Integer type) throws Exception {
        return alarmTypeParamService.getAlarmType(type);
    }*/

    /**
     * 删除日志
     * @param list
     * @return
     */
    @Oplog(name = "pages.sensitiveAlarmDetailLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<SensitiveAlarmLog> list) {
        service.deleteBatch(list);
        return ResponseDTO.success();
    }

    /**
     * 获取敏感泄露方式（违规类型）列表
     * @param type
     * @return
     * @throws Exception
     */
    @PostMapping(value = "getSensitiveLossType")
    public List<LossTypeParam> getAlarmType(@Param("type") Integer type) throws Exception {
        return lossTypeParamService.getAllowLossType(type);
    }

    /**
     * 获取所有的敏感泄露方式（违规类型）列表
     * @return
     */
    @PostMapping(value = "getAllSensitiveLossType")
    public List<LossTypeParam> getAllLossType(){
        LossTypeParam lossTypeParam = new LossTypeParam();
        return lossTypeParamService.queryAll(lossTypeParam);
    }

    /**
     * 导出敏感告警记录
     * @param vo
     * @return
     */
    @Oplog(value = SensitiveAlarmLogVO.class,name = "route.sensitiveAlarmLog")
    @PostMapping(value = "export")
    public ResponseDTO exportExcelByAlarmLogVO(@RequestBody SensitiveAlarmLogVO vo) {
        return ResponseDTO.success(service.exportExcel(vo));
    }
}
