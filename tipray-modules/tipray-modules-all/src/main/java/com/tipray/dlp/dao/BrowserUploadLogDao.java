package com.tipray.dlp.dao;

import com.tipray.dlp.bean.BrowserUploadLog;
import com.tipray.dlp.bean.vo.BrowserLogVO;
import com.tipray.dlp.bean.vo.LogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 浏览器文件上传日志数据库访问层
 *
 * <AUTHOR>
 * @since 2019-11-19 19:43:31
 */
@Repository
@ShardingDataSource
public interface BrowserUploadLogDao extends BaseDao<BrowserUploadLog> {
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(BrowserLogVO vo);

    /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<BrowserUploadLog> listByVO(BrowserLogVO vo);

    List<BrowserUploadLog> listByAttachmentGuid(LogVO vo);
}
