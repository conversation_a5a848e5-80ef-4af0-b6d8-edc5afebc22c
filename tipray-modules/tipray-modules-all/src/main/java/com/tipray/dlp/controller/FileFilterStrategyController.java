package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.FileFilterStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.FileFilterStrategyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 文件监视过滤策略Controller
 */
@Oplog(FileFilterStrategy.class)
@RestController
@RequestMapping("/fileFilterStrategy")
public class FileFilterStrategyController {
    @Resource
    private FileFilterStrategyService fileFilterStrategyService;

    /**
     * 添加文件监视过滤策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.fileFilterStrategy")
    @PostMapping(value = "add")
    public FileFilterStrategy addAppVersionBlock(@Valid @RequestBody FileFilterStrategy bean) {
        fileFilterStrategyService.insert(bean);
        return bean;
    }

    /**
     * 修改文件监视过滤策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.fileFilterStrategy")
    @PostMapping(value = "update")
    public FileFilterStrategy updateVersionStrategy(@Valid @RequestBody FileFilterStrategy bean) {
        fileFilterStrategyService.update(bean);
        return bean;
    }

    /**
     * 删除文件监视过滤策略
     * @param ids
     */
    @Oplog(name = "pages.fileFilterStrategy")
    @PostMapping(value = "delete")
    public void deleteVersionStrategy(String ids) {
        fileFilterStrategyService.deleteById(ids);
    }

    @PostMapping("getByName")
    public FileFilterStrategy getByName(String name) {
        return fileFilterStrategyService.getByName(name);
    }

    /** 得到指定目标的程序版本限制策略*/
    @PostMapping(value = "getPage")
    public GridPageDTO<FileFilterStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return fileFilterStrategyService.getStrategyPage(vo);
    }
}
