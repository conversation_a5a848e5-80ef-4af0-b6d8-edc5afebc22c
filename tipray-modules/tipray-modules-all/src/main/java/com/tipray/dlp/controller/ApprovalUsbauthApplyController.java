package com.tipray.dlp.controller;

import com.tipray.dlp.bean.dto.ApprovalUsbauthApplyDTO;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.mybatis.bean.PageVO;
import com.tipray.dlp.bean.ApprovalUsbauthApply;
import com.tipray.dlp.service.ApprovalUsbauthApplyService;
import com.tipray.dlp.annotation.Oplog;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Set;

/**
 * 终端存储usb认证申申请(ApprovalUsbauthApply)表控制层
 *
 * <AUTHOR>
 * @since 2020-07-31 11:02:36
 */
@Oplog(ApprovalUsbauthApply.class)
@RestController
@RequestMapping("/approvalUsbauthApply")
public class ApprovalUsbauthApplyController {
    /**
     * 服务对象
     */
    @Resource
    private ApprovalUsbauthApplyService approvalUsbauthApplyService;

    /**
     * USB设备审批
     * @param bean
     * @throws Exception
     */
    @Oplog(name = "pages.UsbApprovalAccess", value = ApprovalUsbauthApplyDTO.class)
    @PostMapping(value = "approval")
    public void save(@RequestBody ApprovalUsbauthApplyDTO bean) throws Exception {
        approvalUsbauthApplyService.approval(bean);
    }

    /**
     * 验证库中是否存在对应编码的usb设备
     * @param usbCode
     * @return
     */
    @PostMapping(value = "validUsbCode")
    public ResponseDTO validUsbCode(String usbCode) {
        Set<String> set = approvalUsbauthApplyService.validUsbCode(usbCode);
        return ResponseDTO.success(set);
    }

    /**
     * 查询USB设备审批
     * @param vo
     * @return
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<ApprovalUsbauthApply> getPage(@RequestBody PageVO vo) {
        return approvalUsbauthApplyService.getUsbPage(vo);
    }

    /**
     * 统计usb认证申请数目
     * @return
     */
    @PostMapping(value = "getCount")
    public Long getCount() {
        return approvalUsbauthApplyService.countUsb(new PageVO());
    }

}
