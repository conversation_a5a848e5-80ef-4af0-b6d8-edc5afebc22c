package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.EnhancesPlugStrategy;
import com.tipray.dlp.bean.ExceptStrategy;
import com.tipray.dlp.service.EnhancesPlugStrategyService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 增强型文档插件策略Controller
 */
@Oplog(ExceptStrategy.class)
@RestController
@RequestMapping("/enhancesPlugStrategy")
public class EnhancesPlugStrategyController {

    @Resource
    private EnhancesPlugStrategyService service;

    @RequestMapping(value = "update")
    public EnhancesPlugStrategy updateEnhancesPlugStrategy(@RequestBody EnhancesPlugStrategy bean){
        service.update(bean);
        return bean;
    }

    @GetMapping("getStrategy")
    public EnhancesPlugStrategy getEnhancesPlugStrategy() {
        return service.getStrategy();
    }
}
