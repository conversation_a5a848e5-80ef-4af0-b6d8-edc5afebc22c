package com.tipray.dlp.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tipray.dlp.bean.WebBrowseTimeLog;
import com.tipray.dlp.bean.vo.WebBrowseTimeLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import com.tipray.dlp.mybatis.annotation.WhereIn;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource
public interface WebBrowseTimeLogDao extends BaseMapper<WebBrowseTimeLog> {
    // Long countByVO(LogVO vo);
    @WhereIn(@WhereIn.Param(value = "hostList", numerical = false))
    List<WebBrowseTimeLog> listByVO(WebBrowseTimeLogVO vo);
}
