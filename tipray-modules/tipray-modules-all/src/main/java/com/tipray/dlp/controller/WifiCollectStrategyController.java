package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.WifiCollectStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.WifiCollectStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * wifi信息收集Controller
 * <AUTHOR>
 * @date 2023/2/24
 */
@Oplog(WifiCollectStrategy.class)
@RestController
@RequestMapping("/wifiCollectStrategy")
public class WifiCollectStrategyController {
    /**
     * 服务对象
     */
    @Resource
    private WifiCollectStrategyService wifiCollectStrategyService;

    /**
     * 新增wifi信息收集策略
     * @param beans
     * @return
     */
    @Oplog(name = "pages.wifiCollectStrategy")
    @PostMapping(value = "add")
    public WifiCollectStrategy[] add(@RequestBody WifiCollectStrategy[] beans) {
        wifiCollectStrategyService.insert(beans);
        return beans;
    }

    /**
     * 修改wifi信息收集策略
     * @param beans
     * @return
     */
    @Oplog(name = "pages.wifiCollectStrategy")
    @PostMapping(value = "update")
    public WifiCollectStrategy[] update(@RequestBody WifiCollectStrategy[] beans) {
        wifiCollectStrategyService.update(beans);
        return beans;
    }

    /**
     * 删除wifi信息收集策略
     * @param ids
     */
    @Oplog(name = "pages.wifiCollectStrategy")
    @PostMapping(value = "delete")
    public void deleteById(String ids) {
        wifiCollectStrategyService.deleteById(ids);
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("get/{id}")
    public WifiCollectStrategy getById(@PathVariable("id") Long id) {
        return wifiCollectStrategyService.getById(id);
    }

    @PostMapping("getByName")
    public WifiCollectStrategy getByName(String name) {
        return wifiCollectStrategyService.getByName(name);
    }

    /** 分页查询wifi信息收集策略*/
    @PostMapping(value = "getPage")
    public GridPageDTO<WifiCollectStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return wifiCollectStrategyService.getStrategyPage(vo);
    }

}
