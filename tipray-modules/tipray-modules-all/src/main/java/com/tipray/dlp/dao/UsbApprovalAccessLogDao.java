package com.tipray.dlp.dao;


import com.tipray.dlp.bean.UsbApprovalAccessLog;
import com.tipray.dlp.bean.vo.LogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import com.tipray.dlp.mybatis.datasource.DataSourceFactory;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource(datasourceId = DataSourceFactory.SHARD_MASTER_DATA_SOURCE)
public interface UsbApprovalAccessLogDao extends BaseDao<UsbApprovalAccessLog> {
    List<UsbApprovalAccessLog> listByVO(LogVO vo);
    Long countByVO(LogVO vo);
}
