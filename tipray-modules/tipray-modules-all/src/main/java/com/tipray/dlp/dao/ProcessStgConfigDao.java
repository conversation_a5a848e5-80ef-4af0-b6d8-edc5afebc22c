package com.tipray.dlp.dao;

import com.tipray.dlp.bean.ProcessStgConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 加密受控程序策略(自定义库)(ProcessStgConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-04-28 11:45:28
 */
@Repository
public interface ProcessStgConfigDao extends BaseSearchDao<ProcessStgConfig>{

    /**
     * 删除关联的配置
     * @param processIds
     */
    void deleteByProcessIds(@Param("ids") String processIds);
}
