package com.tipray.dlp.dao;

import com.tipray.dlp.bean.OfficeWatermarkLib;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: caitw
 * @date: 2024/9/27 16:05
 * @description: 基础数据库 Office水印模板 Dao
 */
@Repository
public interface OfficeWatermarkLibDao extends BaseDao<OfficeWatermarkLib> {

    @Select(
            "select id, lib_id from office_watermark_lib where lib_id in (${libIds})"
    )
    List<OfficeWatermarkLib> selectByLibId(String libIds);

}
