package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.FilePermissionControlData;
import com.tipray.dlp.bean.FilePermissionControlGroup;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.TreeNodeDTO;
import com.tipray.dlp.bean.vo.FilePermissionControlDataVO;
import com.tipray.dlp.service.FilePermissionControlDataService;
import com.tipray.dlp.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Oplog(FilePermissionControlData.class)
@RestController
@RequestMapping("filePermissionControlData")
public class FilePermissionControlDataController {
    @Resource
    private FilePermissionControlDataService service;

    /**
     * 根据类型，查询分组树节点
     */
    @PostMapping(value = "listGroupTreeNode")
    public List<TreeNodeDTO> listGroupTreeNode(@RequestBody FilePermissionControlGroup bean) {
        return service.listGroupTreeNode(bean);
    }

    /**
     * 查询所有分组
     */
    @GetMapping(value = "listAllGroup")
    public List<FilePermissionControlGroup> listAllGroup() {
        return service.listGroup(null);
    }

    /**
     * 新增分组
     */
    @Oplog(value = FilePermissionControlGroup.class, name = "table.group")
    @PostMapping(value = "insertGroup")
    public FilePermissionControlGroup insertGroup(@RequestBody FilePermissionControlGroup bean) {
        service.insertGroup(bean);
        return bean;
    }

    /**
     * 修改分组
     */
    @Oplog(value = FilePermissionControlGroup.class, name = "table.group")
    @PostMapping(value = "updateGroup")
    public FilePermissionControlGroup updateGroup(@RequestBody FilePermissionControlGroup bean) {
        service.updateGroup(bean);
        return bean;
    }

    /**
     * 删除分组
     */
    @Oplog(value = FilePermissionControlGroup.class, name = "table.group")
    @PostMapping(value = "deleteGroup")
    public void deleteGroup(Long id) {
        service.deleteGroupById(id);
    }

    /**
     * 根据类型、名称，查询是否存在同名分组
     */
    @PostMapping(value = "getGroupByName")
    public FilePermissionControlGroup getGroupByName(@RequestBody FilePermissionControlGroup bean) {
        return service.getGroupByName(bean);
    }

    /**
     * 返回被文件权限控制策略引用的策略名称集合，以及分组下存在数据数量
     */
    @GetMapping(value = "groupIdExistReference/{groupId}")
    public Map<String, Object> groupIdExistReference(@PathVariable("groupId") Long groupId) {
        return service.groupIdExistReference(groupId);
    }

    /**
     * 根据类型、分组ID，分页查询文件权限控制库配置
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<FilePermissionControlData> getPage(@RequestBody FilePermissionControlDataVO vo) {
        return service.getPage(vo);
    }

    /**
     * 添加文件权限控制库配置
     */
    @Oplog(name = "pages.filePermCtrlLibConfig")
    @PostMapping(value = "batchInsert")
    public void insert(@RequestBody List<FilePermissionControlData> beans) {
        if (CollectionUtil.isNotEmpty(beans)) {
            for (FilePermissionControlData bean : beans) {
                service.insert(bean);
            }
        }
    }

    /**
     * 修改文件权限控制库配置
     */
    @Oplog(name = "pages.filePermCtrlLibConfig")
    @PostMapping(value = "update")
    public FilePermissionControlData update(@RequestBody FilePermissionControlData bean) {
        service.update(bean);
        return bean;
    }

    /**
     * 删除文件权限控制库配置
     */
    @Oplog(name = "pages.filePermCtrlLibConfig")
    @PostMapping(value = "delete")
    public void batchDelete(String ids) {
        service.deleteByIds(ids);
    }

    /**
     * 根据类型、分组、值，获取文件权限控制库配置
     */
    @PostMapping(value = "getByValues")
    public FilePermissionControlData getByValues(@RequestBody FilePermissionControlDataVO vo) {
        return service.getByValues(vo);
    }

    @PostMapping(value = "getByTypeAndGroupId")
    public List<String> getByTypeAndGroupId(@RequestBody FilePermissionControlDataVO vo) {
        return service.getByTypeAndGroupId(vo);
    }
}
