package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.LandLabelingStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.LandLabelingStrategyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 落地打标签策略Controller
 */
@Oplog(LandLabelingStrategy.class)
@RestController
@RequestMapping("/landLabelingStrategy")
public class LandLabelingStrategyController {
    @Resource
    private LandLabelingStrategyService landLabelingStrategyService;

    /**
     * 添加落地加标签策略
     * @param beans
     * @return
     */
    @Oplog(name = "route.landLabeling")
    @PostMapping(value = "add")
    public LandLabelingStrategy[] addAppVersionBlock(@Valid @RequestBody LandLabelingStrategy[] beans) {
        landLabelingStrategyService.insert(beans);
        return beans;
    }

    /**
     * 修改落地加标签策略
     * @param beans
     * @return
     */
    @Oplog(name = "route.landLabeling")
    @PostMapping(value = "update")
    public LandLabelingStrategy[] updateVersionStrategy(@Valid @RequestBody LandLabelingStrategy[] beans) {
        landLabelingStrategyService.update(beans);
        return beans;
    }

    /**
     * 删除落地加标签策略
     * @param ids
     */
    @Oplog(name = "route.landLabeling")
    @PostMapping(value = "delete")
    public void deleteVersionStrategy(String ids) {
        landLabelingStrategyService.deleteById(ids);
    }

    @PostMapping("getByName")
    public LandLabelingStrategy getByName(String name) {
        return landLabelingStrategyService.getByName(name);
    }

    /** 得到落地加标签策略策略*/
    @PostMapping(value = "getPage")
    public GridPageDTO<LandLabelingStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return landLabelingStrategyService.getStrategyPage(vo);
    }
}
