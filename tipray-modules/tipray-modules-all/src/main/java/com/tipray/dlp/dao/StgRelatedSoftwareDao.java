package com.tipray.dlp.dao;

import com.tipray.dlp.bean.StgRelatedSoftware;
import com.tipray.dlp.mybatis.annotation.WhereIn;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/6
 */
@Repository
public interface StgRelatedSoftwareDao {
    void batchInsert(@Param("stgId") Long stgId, @Param("softwareList") List<StgRelatedSoftware> softwareList);
    void deleteByStgId(Long stgId);
    @WhereIn(@WhereIn.Param("stgIds"))
    void deleteByStgIds(String stgIds);
    List<StgRelatedSoftware> listSoftwareInfoByStgId(Long stgId);
}
