package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.AlarmBaseStrategy;
import com.tipray.dlp.bean.HardwareAssetAlarmSetup;
import com.tipray.dlp.service.AlarmBaseStrategyService;
import com.tipray.dlp.service.HardwareAssetAlarmSetupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 硬件资产变更报警与记录设置Controller
 */
@RestController
@Oplog(HardwareAssetAlarmSetup.class)
@RequestMapping("/hardwareAssetAlarmSetup")
@Slf4j
public class HardwareAssetAlarmSetupController {

    /**
     * 服务对象
     */
    @Resource
    private HardwareAssetAlarmSetupService hardwareAssetAlarmSetupService;
    @Resource
    private AlarmBaseStrategyService alarmBaseStrategyService;

    /**
     * 修改硬件资产变更报警与记录设置
     * @param bean
     * @return
     */
    @Oplog(name = "route.HardwareAssetChangeAlarmLogSettings")
    @PostMapping(value = "update")
    public HardwareAssetAlarmSetup update(@RequestBody HardwareAssetAlarmSetup bean){
        hardwareAssetAlarmSetupService.updateAssetAlarmSetup(bean);
        return bean;
    }

    /**
     * 得到硬件资产变更报警与记录设置
     *
     * @return 单条数据
     */
    @GetMapping("get")
    public HardwareAssetAlarmSetup getById() {
        HardwareAssetAlarmSetup assetAlarmSetup = hardwareAssetAlarmSetupService.getAssetAlarmSetup();
        if (assetAlarmSetup != null) {
            AlarmBaseStrategy stg = alarmBaseStrategyService.getByAlarmType(15);
            assetAlarmSetup.setRuleId(stg==null || stg.getRuleId() == 0 ?
                    null : stg.getRuleId());
        }
        return assetAlarmSetup;
    }
}
