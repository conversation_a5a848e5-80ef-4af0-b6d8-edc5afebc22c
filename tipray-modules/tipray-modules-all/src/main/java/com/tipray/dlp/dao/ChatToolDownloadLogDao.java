package com.tipray.dlp.dao;

import com.tipray.dlp.bean.ChatToolDownloadLog;
import com.tipray.dlp.bean.vo.ChatLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 通信工具下载文件记录(ChatToolDownloadLog)表数据库访问层
 *
 * <AUTHOR>
 * @date 2023/06/20
 */
@Repository
@ShardingDataSource
public interface ChatToolDownloadLogDao extends BaseDao<ChatToolDownloadLog> {
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(ChatLogVO vo);

    /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<ChatToolDownloadLog> listByVO(ChatLogVO vo);
}
