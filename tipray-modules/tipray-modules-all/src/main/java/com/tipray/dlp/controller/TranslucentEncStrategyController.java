package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.TranslucentEncStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.formatter.importFormatter.LDTranslucentEncImportFormatter;
import com.tipray.dlp.service.TranslucentEncStrategyService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * 半透明加密(TranslucentEncStrategy)表控制层
 *
 * <AUTHOR>
 * @since 2019-10-21 09:55:30
 */
@Oplog(TranslucentEncStrategy.class)
@RestController
@RequestMapping("/translucentEncStrategy")
public class TranslucentEncStrategyController {
    /**
     * 服务对象
     */
    @Resource
    private TranslucentEncStrategyService translucentEncStrategyService;

    @Oplog(name="pages.translucentEncStrategy")
    /** 新增半透明加密策略*/
    @PostMapping(value = "add")
    public TranslucentEncStrategy[] insert(@RequestBody TranslucentEncStrategy[] beans){
        translucentEncStrategyService.insert(beans);
        return beans;
    }

    @Oplog(name="pages.translucentEncStrategy")
    /** 修改半透明加密策略*/
    @PostMapping(value = "update")
    public TranslucentEncStrategy[] update(@RequestBody TranslucentEncStrategy[] beans){
        translucentEncStrategyService.update(beans);
        return beans;
    }

    @Oplog(name="pages.translucentEncStrategy")
    /** 删除半透明加密策略*/
    @PostMapping(value = "delete")
    public void deleteById(String ids){
        translucentEncStrategyService.deleteById(ids);
    }
    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("get/{id}")
    public TranslucentEncStrategy getById(@PathVariable("id") Long id) {
        return translucentEncStrategyService.getStrategyById(id);
    }

    @PostMapping("getByName")
    public List<TranslucentEncStrategy> getByName(String name) {
        return translucentEncStrategyService.listByName(name);
    }

    /** 分页查询*/
    @PostMapping(value = "getPage")
    public GridPageDTO<TranslucentEncStrategy> getPage(@ModelAttribute StrategyVO vo){
        return translucentEncStrategyService.getPage(vo);
    }

    @PostMapping(value = "/import")
    public void importData(@RequestParam(value = "uploadFile") List<MultipartFile> uploadFile,
                       @RequestParam(value = "importType")  Integer importType,
                       @RequestParam(value = "objectType")  Integer objectType,
                       @RequestParam(value = "objectIds")  List<Long> objectIds,
                       @RequestParam(value = "objectGroupIds")  List<Long> objectGroupIds) throws IOException {
        for (MultipartFile file : uploadFile) {
            new LDTranslucentEncImportFormatter(importType).format(file.getInputStream(), file.getOriginalFilename(), objectType, objectIds, objectGroupIds);
        }
    }
}