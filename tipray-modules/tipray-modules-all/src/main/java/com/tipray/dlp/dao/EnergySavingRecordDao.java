package com.tipray.dlp.dao;

import com.tipray.dlp.bean.EnergySavingRecord;
import com.tipray.dlp.bean.vo.EnergySavingRecordVO;
import com.tipray.dlp.bean.vo.LogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/6 14:41
 */
@Repository
@ShardingDataSource
public interface EnergySavingRecordDao extends BaseDao<EnergySavingRecord>  {
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(EnergySavingRecordVO vo);

    /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<EnergySavingRecord> listByVO(EnergySavingRecordVO vo);
}
