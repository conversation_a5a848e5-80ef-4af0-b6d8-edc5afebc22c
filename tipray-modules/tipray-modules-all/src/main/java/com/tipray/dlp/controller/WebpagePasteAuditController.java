package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.WebpagePasteAudit;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.WebpagePasteAuditService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网页粘贴审计策略Controller
 * <AUTHOR>
 * @Description
 * @CreateTime 2023年08月21日 09:05:00
 */
@RestController
@Oplog(WebpagePasteAudit.class)
@RequestMapping("/webpagePasteAudit")
public class WebpagePasteAuditController {
    @Resource
    public WebpagePasteAuditService webpagePasteAuditService;

    /**
     * 分页查询网页粘贴审计策略列表
     * @param vo
     * @return
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<WebpagePasteAudit> getPage(@RequestBody StrategyVO vo) {
        return webpagePasteAuditService.getStrategyPage(vo);
    }

    /**
     * 添加网页粘贴审计策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.webpagePasteAuditStrategy")
    @PostMapping(value = "add")
    public WebpagePasteAudit add(@RequestBody WebpagePasteAudit bean) {
        List<Map<String, Long>> rules = getRules(bean);
        bean.setRules(rules);
        webpagePasteAuditService.insert(bean);
        return bean;
    }

    /**
     * 修改网页粘贴审计策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.webpagePasteAuditStrategy")
    @PostMapping(value = "update")
    public WebpagePasteAudit update(@RequestBody WebpagePasteAudit bean) {
        List<Map<String, Long>> rules = getRules(bean);
        bean.setRules(rules);
        webpagePasteAuditService.update(bean);
        return bean;
    }
    public List<Map<String, Long>> getRules(WebpagePasteAudit bean){
        List<Long> webpageMatchRuleIds = bean.getWebpageMatchRuleIds();
        List<Map<String, Long>> rules = new ArrayList<>();
        for (Long webpageMatchRuleId : webpageMatchRuleIds) {
            Map<String, Long> map = new HashMap<>();
            map.put("ruleId", webpageMatchRuleId);
            rules.add(map);
        }
        return rules;
    }

    /**
     * 删除网页粘贴审计策略
     * @param ids
     */
    @Oplog(name = "pages.webpagePasteAuditStrategy")
    @PostMapping(value = "delete")
    public void deleteById(String ids) {
        webpagePasteAuditService.deleteById(ids);
    }

    /**
     * 根据名称查询策略  用于前端校验策略是否存在
     * @param name
     * @return
     */
    @PostMapping("getByName")
    public WebpagePasteAudit getByName(String name) {
        return webpagePasteAuditService.getByName(name);
    }
}
