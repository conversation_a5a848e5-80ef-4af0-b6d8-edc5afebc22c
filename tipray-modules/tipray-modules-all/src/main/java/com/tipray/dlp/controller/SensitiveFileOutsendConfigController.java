package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.SensitiveFileOutsendConfig;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.service.SensitiveFileOutsendConfigService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 敏感外发参数配置下发Controller
 * 敏感外发参数配置下发表(sensitive_file_outsend_config)表控制层
 *
 * <AUTHOR>
 * @since 2021-10-11 16:07:39
 */
@Oplog(SensitiveFileOutsendConfig.class)
@RestController
@RequestMapping("/sensitiveFileOutsendConfig")
public class SensitiveFileOutsendConfigController {
    /**
     * 服务对象
     */
    @Resource
    private SensitiveFileOutsendConfigService sensitiveFileOutsendConfigService;

    /**
     * 通过操作员显示方式
     * 查询敏感外发参数配置
     * @return 单条数据
     */
    @PostMapping("/get")
    public ResponseDTO get() {
        List<SensitiveFileOutsendConfig> list = sensitiveFileOutsendConfigService.queryAll();
        if (list.size() == 0) {
            return ResponseDTO.failure(new Throwable(),"敏感文件外发内置参数异常");
        }
        return ResponseDTO.success(list.get(0));
    }
    /**
     * 通过操作员显示方式
     * 修改敏感文件外发审批参数配置
     * @param bean
     * @return
     */
    @Oplog(name = "route.configwjwf")
    @PostMapping("/update")
    public ResponseDTO update(@RequestBody SensitiveFileOutsendConfig bean) {
        sensitiveFileOutsendConfigService.update(bean);
        return ResponseDTO.success();
    }

}
