package com.tipray.dlp.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tipray.dlp.bean.SensitiveFileLossType;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 敏感外发申请支持的类型下发表(SensitiveFileLossType)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-10-09 16:07:39
 */
@Repository
public interface SensitiveFileLossTypeDao extends BaseDao<SensitiveFileLossType>{

    /**
     * 通过实体作为筛选条件查询
     * @return 对象列表
     */
    @Override
    default List<SensitiveFileLossType> selectAll(){
        return this.selectList(new LambdaQueryWrapper<SensitiveFileLossType>().eq(SensitiveFileLossType::getDeleted, 0).orderByAsc(SensitiveFileLossType::getId));
    }

}
