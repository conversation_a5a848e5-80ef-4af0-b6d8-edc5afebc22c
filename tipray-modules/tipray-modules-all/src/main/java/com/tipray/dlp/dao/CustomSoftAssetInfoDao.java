package com.tipray.dlp.dao;

import com.tipray.dlp.bean.CustomSoftAssetInfo;
import com.tipray.dlp.mybatis.annotation.WhereIn;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/23
 */
@Repository
public interface CustomSoftAssetInfoDao extends BaseDao<CustomSoftAssetInfo>{

    void batchInsert(@Param("addList") List<CustomSoftAssetInfo> list);

    @WhereIn(@WhereIn.Param(value = "updateList.id"))
    void batchUpdate(@Param("updateList") List<CustomSoftAssetInfo> list);

    void deleteBySoftIds(String softIds);
}
