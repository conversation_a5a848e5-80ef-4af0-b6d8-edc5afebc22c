package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.LabelPermissionStrategy;
import com.tipray.dlp.bean.dict.MsgModuleDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.exception.ParamException;
import com.tipray.dlp.service.LabelPermissionStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 带标签文档管控策略Controller
 */
@RestController
@Oplog(LabelPermissionStrategy.class)
@RequestMapping("/labelPermissionStrategy")
@Slf4j
public class LabelPermissionStrategyController {
    @Autowired
    private LabelPermissionStrategyService labelPermissionStrategyService;

    /**
     * 添加带标签文档管控策略
     * @param beans
     * @return
     */
    @Oplog(name = "route.labelPermissionControl")
    @PostMapping(value = "add")
    public LabelPermissionStrategy[] addStrategy(@RequestBody LabelPermissionStrategy[] beans){
        for (LabelPermissionStrategy bean:beans) {
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.LABEL_PERMISSION.getCode());
        }
        labelPermissionStrategyService.insert(beans);
        return beans;
    }

    /**
     * 修改带标签文档管控策略
     * @param beans
     * @return
     */
    @Oplog(name = "route.labelPermissionControl")
    @PostMapping(value = "update")
    public LabelPermissionStrategy[] updateStrategy(@RequestBody LabelPermissionStrategy[] beans){
        for (LabelPermissionStrategy bean:beans) {
            if (bean.getId() == null) {
                throw new ParamException("参数错误");
            }
            bean.setSstType(0);
            bean.setAlarmBus(1);
            bean.setAlarmType(MsgModuleDict.LABEL_PERMISSION.getCode());
        }
        labelPermissionStrategyService.update(beans);
        return beans;
    }

    /**
     * 删除带标签文档管控策略
     * @param ids
     */
    @Oplog(name = "route.labelPermissionControl")
    @PostMapping(value = "delete")
    public void deleteAppBlock(String ids) {
        labelPermissionStrategyService.deleteById(ids);
    }

    /** 带标签文档管控分页列表*/
    @PostMapping(value = "getPage")
    public GridPageDTO<LabelPermissionStrategy> getStategyPage(@RequestBody StrategyVO vo) {
        return labelPermissionStrategyService.getStrategyPage(vo);
    }

    /**
     * 根据名称获取策略
     * @param name     策略名称
     * @return  策略内容
     */
    @PostMapping("getByName")
    public LabelPermissionStrategy getByName(String name) {
        return labelPermissionStrategyService.getStrategyByName(name);
    }
}
