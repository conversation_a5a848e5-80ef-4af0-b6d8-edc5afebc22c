package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.ContentStgDef;
import com.tipray.dlp.bean.ContentStrategy;
import com.tipray.dlp.bean.SmartEncStrategy;
import com.tipray.dlp.bean.dict.DeleteStateDict;
import com.tipray.dlp.bean.dict.StrategyDefType;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.ContentStrategyService;
import com.tipray.dlp.service.SmartEncStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 智能加密(SmartEncStrategy)表控制层
 *
 * <AUTHOR>
 * @since 2019-10-21 09:55:30
 */
@Oplog(SmartEncStrategy.class)
@RestController
@RequestMapping("/smartEncStrategy")
public class SmartEncStrategyController {
    /**
     * 服务对象
     */
    @Resource
    private SmartEncStrategyService smartEncStrategyService;
    @Resource
    private ContentStrategyService contentStrategyService;

    /**
     * 新增智能加密策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.smartEncStrategy")
    @PostMapping(value = "add")
    public SmartEncStrategy[] insert(@RequestBody SmartEncStrategy[] bean){
        smartEncStrategyService.insert(bean);
        return bean;
    }

    /**
     * 修改智能加密策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.smartEncStrategy")
    @PostMapping(value = "update")
    public SmartEncStrategy[] update(@RequestBody SmartEncStrategy[] bean){
        smartEncStrategyService.update(bean);
        return bean;
    }

    /**
     * 删除智能加密策略
     * @param ids
     */
    @Oplog(name = "pages.smartEncStrategy")
    @PostMapping(value = "delete")
    public void deleteById(String ids){
        smartEncStrategyService.deleteById(ids);
    }

    /**
     * 新增关联的内容检测策略
     * @param bean
     * @return
     */
    @Oplog(value = ContentStgDef.class, name = "route.smartEncStrategySensitive")
    @PostMapping(value = "addContentStg")
    public ContentStrategy insertContentStg(@RequestBody ContentStrategy bean) {
        if(StrategyDefType.prepare.getCode().equals(bean.getStrategyDefType())){
            // 预定义策略直接设置为已启用
            bean.setActive(true);
        }
        contentStrategyService.insertStrategy(bean);
        return bean;
    }
    /**
     * 修改关联的内容检测策略
     * @param bean
     * @return
     */
    @Oplog(value = ContentStgDef.class, name = "route.smartEncStrategySensitive")
    @PostMapping(value = "updateContentStg")
    public ContentStrategy updateContentStg(@RequestBody ContentStrategy bean) {
        contentStrategyService.updateStrategy(bean);
        return bean;
    }

    /**
     * 删除关联的内容检测策略
     * @param ids
     * @return
     */
    @Oplog(value = ContentStgDef.class, name = "route.smartEncStrategySensitive")
    @PostMapping(value = "deleteContentStg")
    public DeleteStateDict deleteContentStgById(String ids) {
        DeleteStateDict state = contentStrategyService.getDelState(ids);
        if(DeleteStateDict.enable.equals(state)){
            contentStrategyService.deleteStrategyById(ids);
        }
        return state;
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("get/{id}")
    public SmartEncStrategy getById(@PathVariable("id") Long id) {
        return smartEncStrategyService.getStrategyById(id);
    }

    @PostMapping("getByName")
    public List<SmartEncStrategy> getByName(String name) {
        return smartEncStrategyService.listByName(name);
    }

    @PostMapping(value = "getPage")
    public GridPageDTO<SmartEncStrategy> getPage(@RequestBody StrategyVO vo){
        return smartEncStrategyService.getPage(vo);
    }

}