package com.tipray.dlp.dao;

import com.tipray.dlp.bean.DiskScanSelfCheckLog;
import com.tipray.dlp.bean.vo.DiskScanLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 程序使用日志(AppLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2019-11-26 08:49:04
 */
@Repository
@ShardingDataSource
public interface DiskScanSelfCheckLogDao extends BaseDao<DiskScanSelfCheckLog> {

    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    List<Byte> countByVO(DiskScanLogVO vo);

    /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<DiskScanSelfCheckLog> listByVO(DiskScanLogVO vo);

    List<DiskScanSelfCheckLog> listScanStatus(DiskScanLogVO vo);
}
