package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.dto.ScreenInfoDTO;
import com.tipray.dlp.controller.socket.MonitorSocketMapper;
import com.tipray.dlp.controller.socket.dto.ProcessDetailResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 终端屏幕追踪管理Controller
 */
@RestController
@Oplog(ProcessDetailResponse.class)
@RequestMapping("/terminal/track")
public class TerminalScreenTrackController {
    @Resource
    private MonitorSocketMapper socketApi;

    /**
     * 屏幕追踪
     * @param bean
     * @return
     */
    @Oplog(name = "route.track")
    @PostMapping(value = "list")
    public Integer getTrack(@RequestBody ScreenInfoDTO bean) {
        return socketApi.updateTrack(bean.getTermId(), bean.getQualityType());
    }

    /** 如果调用此接口，那么不会记录管理员日志，避免定时执行时重复记录管理员日志*/
    @PostMapping(value = "listContinue")
    public Integer getProcessContinue(@RequestBody ScreenInfoDTO bean) {
        return getTrack(bean);
    }

    /**
     * 屏幕追踪，一次追踪多个终端
     */
    @Oplog(name = "route.track", value = ScreenInfoDTO.class)
    @PostMapping(value = "multipleTrack")
    public Integer getMultiPleTrack(@RequestBody ScreenInfoDTO bean) {
        return socketApi.updateMultiPleTrack(bean.getTermIds(), bean.getQualityType());
    }

    /**
     * 屏幕追踪，一次追踪多个终端, 不记录日志
     */
    @PostMapping(value = "multipleTrackContinue")
    public Integer getMultiPleTrackContinue(@RequestBody ScreenInfoDTO bean) {
        return getMultiPleTrack(bean);
    }
}
