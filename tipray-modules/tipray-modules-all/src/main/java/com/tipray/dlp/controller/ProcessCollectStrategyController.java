package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.ProcessCollectRuleStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.ProcessCollectRuleStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 进程指纹收集规则策略(ProcessCollectRuleStrategy)表控制层
 *
 * <AUTHOR>
 * @since 2019-09-25 11:22:13
 */
@Oplog(ProcessCollectRuleStrategy.class)
@RestController
@RequestMapping("processCollectRuleStrategy")
public class ProcessCollectStrategyController {
    @Resource
    private ProcessCollectRuleStrategyService service;

    /**
     * 新增进程指纹收集规则策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.processCollectStg")
    @PostMapping(value = "add")
    public ProcessCollectRuleStrategy insert(@RequestBody ProcessCollectRuleStrategy bean){
        service.insert(bean);
        return bean;
    }

    /**
     * 修改进程指纹收集规则策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.processCollectStg")
    @PostMapping(value = "update")
    public ProcessCollectRuleStrategy update(@RequestBody ProcessCollectRuleStrategy bean){
        service.update(bean);
        return bean;
    }

    /**
     * 删除进程指纹收集规则策略
     * @param ids
     */
    @Oplog(name = "pages.processCollectStg")
    @PostMapping(value = "delete")
    public void deleteById(String ids){
        service.deleteById(ids);
    }

    @PostMapping("getByName")
    public ProcessCollectRuleStrategy getByName(String name) {
        return service.getByName(name);
    }

    /** 分页查询*/
    @PostMapping(value = "getPage")
    public GridPageDTO<ProcessCollectRuleStrategy> getPage(@RequestBody StrategyVO vo){
        return service.getStrategyPage(vo);
    }

}
