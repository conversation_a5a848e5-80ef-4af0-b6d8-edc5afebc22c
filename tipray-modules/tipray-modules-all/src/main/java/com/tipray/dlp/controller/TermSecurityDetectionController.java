package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.TermSecurityDetectionStrategy;
import com.tipray.dlp.bean.TermSecurityDetectionStrategyDto;
import com.tipray.dlp.bean.dict.OperationTypeDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.TermSecurityDetectionService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

/**
 * 终端安全检测策略
 * <AUTHOR>
 * @date 2024/1/25 10:35
 */
@Oplog(TermSecurityDetectionStrategy.class)
@RestController
@RequestMapping("/termSecurityDetection")
public class TermSecurityDetectionController {

    @Resource
    private TermSecurityDetectionService termSecurityDetectionService;

    /** 终端安全检测*/
    @PostMapping(value = "getPage")
    public GridPageDTO<TermSecurityDetectionStrategyDto> getPage(@RequestBody StrategyVO vo){
        return termSecurityDetectionService.getStrategyPage(vo);
    }

    /** 根据Id获取终端安全检测配置信息*/
    @GetMapping(value = "getById/{id}")
    public TermSecurityDetectionStrategyDto getById(@PathVariable Long id){
        return termSecurityDetectionService.getById(id);
    }


    @Oplog(type = OperationTypeDict.ADD, value = TermSecurityDetectionStrategyDto.class,name = "route.termSecurityDetection")
    @PostMapping(value = "add")
    public TermSecurityDetectionStrategyDto addStrategy(@Valid @RequestBody TermSecurityDetectionStrategyDto bean) {
        termSecurityDetectionService.insertStrategy(bean);
        return bean;
    }

    @Oplog(type = OperationTypeDict.UPDATE, value = TermSecurityDetectionStrategyDto.class,name = "route.termSecurityDetection")
    @PostMapping(value = "update")
    public TermSecurityDetectionStrategyDto updateStrategy(@Valid @RequestBody TermSecurityDetectionStrategyDto bean) {
        termSecurityDetectionService.updateStrategy(bean);
        return bean;
    }

    @Oplog(type = OperationTypeDict.DELETE, value = TermSecurityDetectionStrategyDto.class, name = "route.termSecurityDetection")
    @PostMapping(value = "delete")
    public void deleteStrategy(String ids) {
        termSecurityDetectionService.deleteById(ids);
    }

    @GetMapping("getByName")
    public TermSecurityDetectionStrategyDto getByName(String name) {
        return termSecurityDetectionService.getStrategyByName(name);
    }

    /** 校验字符串是否为合法的正则表达式 **/
    @GetMapping("isValidRegex")
    public Boolean isValidRegex(String regular) {
        try {
            Pattern.compile(regular);
            return true;
        } catch (PatternSyntaxException e) {
            return false;
        }
    }
}
