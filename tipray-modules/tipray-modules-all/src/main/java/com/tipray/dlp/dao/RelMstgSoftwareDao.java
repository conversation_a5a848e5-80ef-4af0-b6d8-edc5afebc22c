package com.tipray.dlp.dao;

import com.tipray.dlp.bean.RelMstgSoftware;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

@Repository
public interface RelMstgSoftwareDao  {

    default void handleInsert(RelMstgSoftware rel) {
        if (rel == null) {
            return;
        }
        StringBuilder values = new StringBuilder();
        values.append('(').append(rel.getStgId()).append(',').append(rel.getObjId()).append(')');
        this.insert(values.toString());
    }


    @Insert("INSERT INTO rel_mstg_software(stg_id, obj_id) VALUES ${value}")
    void insert(String value);

    default void deleteByStgIds(List<Long> stgIds) {
        if (stgIds == null || stgIds.isEmpty()) {
            return;
        }
        if (stgIds.size() == 1) {
            this.deleteByStgId(stgIds.get(0));
            return;
        }
        String value = stgIds.stream().map(Long::toUnsignedString).collect(Collectors.joining(","));
        this.delete(value);
    }

    @Delete("DELETE FROM rel_mstg_software WHERE stg_id = #{stgId}")
    void deleteByStgId(Long stgId);

    @Delete("DELETE FROM rel_mstg_software WHERE stg_id IN (${value})")
    void delete(String stgIds);

    @Select("SELECT DISTINCT s.name FROM rel_mstg_software r, mstg_def s WHERE r.obj_id IN (${value}) AND r.stg_id = s.id")
    List<String> getStgNamesBySoftwareIds(String softwareIds);

}
