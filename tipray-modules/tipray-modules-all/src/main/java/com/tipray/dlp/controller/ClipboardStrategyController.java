package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.ClipboardStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.ClipboardStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;

/**
 * 剪切板策略Controller
 */
@RestController
@Oplog(ClipboardStrategy.class)
@RequestMapping("/clipboardStrategy")
public class ClipboardStrategyController {
    @Resource
    private ClipboardStrategyService service;

    /**
     * 添加剪切板策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.clipboardStrategy")
    @PostMapping(value = "add")
    public ClipboardStrategy[] insertStrategy(@Valid @RequestBody ClipboardStrategy[] bean) {
        service.insert(bean);
        return bean;
    }

    /**
     * 修改剪切板策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.clipboardStrategy")
    @PostMapping(value = "update")
    public ClipboardStrategy[] updateStrategy(@Valid @RequestBody ClipboardStrategy[] bean) {
        service.update(bean);
        return bean;
    }

    /**
     * 删除剪切板策略
     * @param ids
     */
    @Oplog(name = "pages.clipboardStrategy")
    @PostMapping(value = "delete")
    public void deleteStrategyById(String ids) {
        service.deleteById(ids);
    }

    @PostMapping("getByName")
    public Collection<ClipboardStrategy> getByName(String name) {
        return service.listByName(name);
    }


    /** 剪切板策略分页列表*/
    @PostMapping(value = "getPage")
    public GridPageDTO<ClipboardStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return service.getPage(vo);
    }
}
