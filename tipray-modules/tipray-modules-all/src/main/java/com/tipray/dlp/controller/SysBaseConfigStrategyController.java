package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.SysBaseConfig;
import com.tipray.dlp.bean.SysBaseConfigStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.TreeNodeDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.SysBaseConfigStrategyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统基础功能配置策略Controller
 */
@RestController
@Oplog(SysBaseConfigStrategy.class)
@RequestMapping("/sysBaseConfigStrategy")
public class SysBaseConfigStrategyController {
    @Autowired
    private SysBaseConfigStrategyService sysBaseConfigStrategyService;

    /**
     * 新增计算机设置策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.sysBaseConfigStrategy")
    @PostMapping(value = "add")
    public SysBaseConfigStrategy addStrategy(@RequestBody SysBaseConfigStrategy bean) {
        sysBaseConfigStrategyService.insert(bean);
        return bean;
    }

    /**
     * 修改计算机设置策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.sysBaseConfigStrategy")
    @PostMapping(value = "update")
    public SysBaseConfigStrategy updateStrategy(@RequestBody SysBaseConfigStrategy bean) {
        sysBaseConfigStrategyService.update(bean);
        return bean;
    }

    /**
     * 删除计算机设置策略
     * @param ids
     */
    @Oplog(name = "pages.sysBaseConfigStrategy")
    @PostMapping(value = "delete")
    public void deleteStrategy(String ids) {
        sysBaseConfigStrategyService.deleteById(ids);
    }

    @GetMapping("get/{id}")
    public SysBaseConfigStrategy getById(@PathVariable("id") Long id) {
        return sysBaseConfigStrategyService.getStrategyById(id);
    }

    @PostMapping("getByName")
    public SysBaseConfigStrategy getByName(String name) {
        return sysBaseConfigStrategyService.getByName(name);
    }

    /**获取系统基础功能配置策略*/
    @PostMapping(value = "getPage")
    public GridPageDTO<SysBaseConfigStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return sysBaseConfigStrategyService.getStrategyPage(vo);
    }

    /**获取系统基础功能下拉框字典值*/
    @GetMapping("getFuncOptions")
    public List<Map<String, Object>> getFuncOptions() {
        List<Map<String, Object>> res = new ArrayList<>();
        final List<SysBaseConfig> sysBaseConfigs = sysBaseConfigStrategyService.listFunctions();
        for (SysBaseConfig obj : sysBaseConfigs) {
            Map<String, Object> map = new HashMap<>();
            map.put("value", obj.getFuncId());
            map.put("label", obj.getFuncName());
            res.add(map);
        }
        return res;
    }

    /**获取系统基础功能树结构*/
    @GetMapping("getTree")
    public List<TreeNodeDTO> getTree() {
        List<TreeNodeDTO> res = sysBaseConfigStrategyService.getFuncTree();
        return res;
    }
}
