package com.tipray.dlp.dao;

import com.tipray.dlp.bean.SoftLimitProcess;
import com.tipray.dlp.bean.vo.SoftProcessVO;
import com.tipray.dlp.mybatis.annotation.WhereIn;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SoftLimitProcessDao extends BaseDao<SoftLimitProcess> {


    /**
     * 得到所有应用程序
     *
     * @return
     */
    @WhereIn(@WhereIn.Param(value = "processNames", numerical = false))
    List<SoftLimitProcess> listSoftProcess(SoftProcessVO vo);

    Long countByVO(SoftProcessVO vo);

    /**
     * 获取需要合并的进程个数
     * 需要合并的数据遵循：相同进程名称，最多只允许有两条数据，一条{开启防伪冒}的数据，一条{未开启防伪冒}的数据。存在重复数据即存在合并的数据
     * @return
     */
    Long countMergeProcess();

    /**
     * 查找需要合并的进程信息
     * @return
     */
    @WhereIn(@WhereIn.Param(value = "processNames", numerical = false))
    List<SoftLimitProcess> selectMergeProcess(SoftProcessVO vo);
}

