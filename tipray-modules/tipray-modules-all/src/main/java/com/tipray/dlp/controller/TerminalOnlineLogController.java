package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.TermLoginLogoutLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.TermLoginLogoutLogVO;
import com.tipray.dlp.service.TerminalOnlineLogService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 终端上下线记录Controller
 */
@Oplog(TermLoginLogoutLogVO.class)
@RestController
@RequestMapping("/log/online")
public class TerminalOnlineLogController {
    @Resource
    private TerminalOnlineLogService terminalOnlineLogService;

    /**
     * 查询终端上下线记录
     * @param vo
     * @return
     */
    @Oplog(value = TermLoginLogoutLogVO.class, name = "pages.termLoginLogoutLog")
    @PostMapping(value = "getPage")
    public GridPageDTO<TermLoginLogoutLog> getPage(@RequestBody TermLoginLogoutLogVO vo) {
        return terminalOnlineLogService.getLogPage(vo);
    }

    /**
     * 导出终端上下线记录
     * @param vo
     * @return
     */
    @Oplog(value = TermLoginLogoutLogVO.class, delayLog = true,name = "pages.termLoginLogoutLog")
    @PostMapping(value = "export")
    public ResponseDTO exportExcel(@RequestBody TermLoginLogoutLogVO vo){
        return ResponseDTO.success(terminalOnlineLogService.exportExcel(vo));
    }
    /**
     * 删除日志
     * @param list
     * @return
     */
    @Oplog(value = TermLoginLogoutLog.class, name = "pages.termLoginLogoutLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<TermLoginLogoutLog> list) {
        terminalOnlineLogService.deleteBatch(list);
        return ResponseDTO.success();
    }
}
