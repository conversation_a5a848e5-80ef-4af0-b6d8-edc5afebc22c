package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.ApplySecurityAccessStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.ApplySecurityAccessService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 应用安全接入服务器策略控制层
 *
 * <AUTHOR>
 * @since 2020-05-13 15:36:29
 */
@Oplog(ApplySecurityAccessStrategy.class)
@RestController
@RequestMapping("/applySecurityAccess")
public class ApplySecurityAccessController {
    /**
     * 服务对象
     */
    @Resource
    private ApplySecurityAccessService applySecurityAccessService;

    /**
     * 添加应用安全接入服务器
     * @param strategy
     * @return
     */
    @Oplog(name = "pages.applySecurityAccessServer")
    @PostMapping(value = "add")
    public ResponseDTO insert(@Valid @RequestBody ApplySecurityAccessStrategy strategy){
        applySecurityAccessService.insertApplySecurityAccess(strategy);
        return ResponseDTO.success();
    }

    /**
     * 修改应用安全接入服务器
     * @param strategy
     * @return
     */
    @Oplog(name = "pages.applySecurityAccessServer")
    @PostMapping(value = "update")
    public ResponseDTO update(@Valid @RequestBody ApplySecurityAccessStrategy strategy){
        applySecurityAccessService.updateApplySecurityAccess(strategy);
        return ResponseDTO.success();
    }

    /**
     * 删除应用安全接入服务器
     * @param ids
     */
    @Oplog(name = "pages.applySecurityAccessServer")
    @PostMapping(value = "delete")
    public void deleteById(String ids){
        applySecurityAccessService.deleteApplySecurityAccessById(ids);
    }

    /**
     * 分页查询应用安全接入设置
     * @param vo
     * @return
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<ApplySecurityAccessStrategy> getPage(@RequestBody StrategyVO vo){
        return applySecurityAccessService.getApplySecurityAccessPage(vo);
    }
}
