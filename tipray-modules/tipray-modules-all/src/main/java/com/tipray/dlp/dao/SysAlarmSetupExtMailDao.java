package com.tipray.dlp.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tipray.dlp.bean.SysAlarmSetupExtMail;
import com.tipray.dlp.util.CollectionUtil;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

/**
 * 系统预警设置：邮箱弹窗告警邮箱
 * <AUTHOR>
 * @date 2023/6/12
 */
@Repository
public interface SysAlarmSetupExtMailDao extends BaseDao<SysAlarmSetupExtMail> {
    default List<Long> listIdBySetupId(Long id){
        List<Object> userIds = id==null ? new LinkedList<>() :
                this.selectObjs(new LambdaQueryWrapper<SysAlarmSetupExtMail>().select(SysAlarmSetupExtMail::getId).eq(SysAlarmSetupExtMail::getSetupId, id));
        return CollectionUtil.toLongList(userIds);
    }

    default void deleteExtEmail(Collection<Long> mailIds){
        this.delete(new LambdaQueryWrapper<SysAlarmSetupExtMail>().in(SysAlarmSetupExtMail::getId, mailIds));
    }

    default List<SysAlarmSetupExtMail> listExtEmailBySetupId(Long id){
        return this.selectList(new LambdaQueryWrapper<SysAlarmSetupExtMail>().eq(SysAlarmSetupExtMail::getSetupId, id));
    }

    default SysAlarmSetupExtMail getExtEmailById(Long id, Long mailId){
        return this.selectOne(new LambdaQueryWrapper<SysAlarmSetupExtMail>()
                .eq(SysAlarmSetupExtMail::getSetupId, id)
                .eq(SysAlarmSetupExtMail::getId, mailId)
        );
    }
}
