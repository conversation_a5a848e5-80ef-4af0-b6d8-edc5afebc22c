package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.controller.socket.ExplorerSocketMapper;
import com.tipray.dlp.controller.socket.dto.ListenPortResponse;
import com.tipray.dlp.controller.socket.dto.TCPConnectionResponse;
import com.tipray.dlp.export.ExportDataset;
import com.tipray.dlp.export.ExportFileType;
import com.tipray.dlp.export.ExportHelper;
import com.tipray.dlp.export.call.SimpleExportCallable;
import com.tipray.dlp.formatter.exportFormatter.ListenPortExportFormatter;
import com.tipray.dlp.formatter.exportFormatter.TCPConnectionExportFormatter;
import com.tipray.dlp.util.CollectionUtil;
import com.tipray.dlp.util.SortUtil;
import com.tipray.dlp.util.StringUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * TCP连接和侦听端口Controller
 */
@RestController
@RequestMapping("/terminal/networkResourceMonitor")
public class NetworkResourceMonitorController {
    @Resource
    private ExplorerSocketMapper socketApi;

    /**
     * 查询TCP连接
     */
    @Oplog(value = TCPConnectionResponse.class, name = "pages.tcpConnection")
    @PostMapping(value = "listTCPConnection")
    public Map<String, Object> listTCPConnection(@RequestBody TCPConnectionResponse bean) {
        return socketApi.toGetTCPConnections(bean.getTermId());
    }

    /**
     * 导出TCP连接
     */
    @Oplog(value = TCPConnectionResponse.class, name = "pages.tcpConnection")
    @PostMapping(value = "exportTCPConnection")
    public ResponseDTO exportTCPConnection(@RequestBody TCPConnectionResponse bean) {
        TCPConnectionExportFormatter formatter = new TCPConnectionExportFormatter();
        ExportDataset<TCPConnectionResponse> dataset = ExportDataset.fromIterable(formatter, () -> {
            List<TCPConnectionResponse> list = (List<TCPConnectionResponse>) socketApi.toGetTCPConnections(bean.getTermId()).get("list");
            if (CollectionUtil.isNotEmpty(list) && StringUtil.isNotBlank(bean.getProcessName())) {
                String processName = bean.getProcessName().toLowerCase();
                list.removeIf(item -> StringUtil.isBlank(item.getProcessName()) || !item.getProcessName().toLowerCase().contains(processName));
            }
            return SortUtil.syncJsSort(list, Pair.of("processName", "desc"));
        });
        SimpleExportCallable callable = SimpleExportCallable.single(ExportFileType.xlsx, dataset);
        return ResponseDTO.success(ExportHelper.addTask(callable));
    }

    /**
     * 查询侦听端口列表
     */
    @Oplog(value = ListenPortResponse.class, name = "pages.listenPort")
    @PostMapping(value = "listListenPort")
    public Map<String, Object> listListenPort(@RequestBody ListenPortResponse bean) {
        return socketApi.toGetListenPorts(bean.getTermId());
    }

    /**
     * 导出侦听端口列表
     */
    @Oplog(value = ListenPortResponse.class, name = "pages.listenPort")
    @PostMapping(value = "exportListenPort")
    public ResponseDTO exportListenPort(@RequestBody ListenPortResponse bean) {
        ListenPortExportFormatter formatter = new ListenPortExportFormatter();
        ExportDataset<ListenPortResponse> dataset = ExportDataset.fromIterable(formatter, () -> {
            List<ListenPortResponse> list = (List<ListenPortResponse>) socketApi.toGetListenPorts(bean.getTermId()).get("list");
            if (CollectionUtil.isNotEmpty(list) && StringUtil.isNotBlank(bean.getProcessName())) {
                String processName = bean.getProcessName().toLowerCase();
                list.removeIf(item -> StringUtil.isBlank(item.getProcessName()) || !item.getProcessName().toLowerCase().contains(processName));
            }
            return SortUtil.syncJsSort(list, Pair.of("processName", "desc"));
        });
        SimpleExportCallable callable = SimpleExportCallable.single(ExportFileType.xlsx, dataset);
        return ResponseDTO.success(ExportHelper.addTask(callable));
    }

}
