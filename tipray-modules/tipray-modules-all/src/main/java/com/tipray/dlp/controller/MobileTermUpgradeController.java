package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.MobileTermUpgradeStrategy;
import com.tipray.dlp.bean.TermInstallerPackage;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.PackageVO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.MobileTermUpgradeService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 移动端升级提醒配置策略Controller
 */
@Oplog(MobileTermUpgradeStrategy.class)
@RestController
@RequestMapping("/mobileTermUpgrade")
public class MobileTermUpgradeController {

    @Resource
    private MobileTermUpgradeService mobileTermUpgradeService;

    /**
     * 上传安卓安装包
     * @param bean
     * @param uploadFile
     * @throws IOException
     */
    @Oplog(value = TermInstallerPackage.class,name = "pages.uploadAndroidInstallationPackage")
    @PostMapping(value = "upload")
    public void add(TermInstallerPackage bean, MultipartFile uploadFile) throws IOException {
        mobileTermUpgradeService.upload(bean,uploadFile);
    }

    /**
     * 删除终端安装包
     * @param ids
     */
    @Oplog(value = TermInstallerPackage.class,name = "pages.termInstallPackage")
    @PostMapping(value = "deletePack")
    public void deletePack(String ids) {
        mobileTermUpgradeService.deleteTermInstallerPackage(ids);
    }

    /**
     * 添加移动终端升级策略
     * @param strategy
     */
    @Oplog(name = "pages.mobileTermUpgradeStrategy")
    @PostMapping(value = "add")
    public void add(@RequestBody MobileTermUpgradeStrategy strategy) {
        mobileTermUpgradeService.insert(strategy);
    }

    /**
     * 修改移动终端升级策略
     * @param strategy
     */
    @Oplog(name = "pages.mobileTermUpgradeStrategy")
    @PostMapping(value = "update")
    public void update(@RequestBody MobileTermUpgradeStrategy strategy) {
        mobileTermUpgradeService.update(strategy);
    }

    /**
     * 删除移动终端升级策略
     * @param ids
     */
    @Oplog(name = "pages.mobileTermUpgradeStrategy")
    @PostMapping(value = "delete")
    public void delete(String ids) {
        mobileTermUpgradeService.deleteById(ids);
    }

    /** 得到移动终端升级策略分页列表*/
    @PostMapping(value = "getStrategyPage")
    public GridPageDTO<MobileTermUpgradeStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return mobileTermUpgradeService.getStrategyPage(vo);
    }

    @PostMapping("getByName")
    public MobileTermUpgradeStrategy getByName(String name) {
        return mobileTermUpgradeService.getByName(name);
    }

    /** 得到安卓安装包分页数据*/
    @PostMapping(value = "getPackagePage")
    public GridPageDTO<TermInstallerPackage> getPackagePage(@RequestBody PackageVO vo) {
        return mobileTermUpgradeService.getPackagePage(vo);
    }


}
