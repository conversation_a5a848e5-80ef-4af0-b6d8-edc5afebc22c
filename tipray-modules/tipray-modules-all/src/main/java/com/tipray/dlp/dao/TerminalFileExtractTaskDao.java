package com.tipray.dlp.dao;

import com.tipray.dlp.bean.BaseBean;
import com.tipray.dlp.bean.TerminalFileExtractTask;
import com.tipray.dlp.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class TerminalFileExtractTaskDao {
    private static final long DELETE_DURATION = 60 * 60 * 1000L;
    private static final AtomicLong ID = new AtomicLong();
    private static final Map<Integer, List<TerminalFileExtractTask>> CACHE = new ConcurrentHashMap<>();

    /**
     * 新增提取任务
     */
    public int insert(TerminalFileExtractTask entity) {
        if (!CACHE.containsKey(entity.getTerminalId())) {
            synchronized (CACHE) {
                if (!CACHE.containsKey(entity.getTerminalId())) {
                    CACHE.put(entity.getTerminalId(), Collections.synchronizedList(new LinkedList<>()));
                }
            }
        }
        synchronized (ID) {
            entity.setId(ID.getAndIncrement());
        }
        Date date = new Date();
        entity.setCreateTime(date);
        entity.setModifyTime(date);
        CACHE.get(entity.getTerminalId()).add(entity);
        return 1;
    }

    /**
     * 根据ID更新提取任务
     */
    public int updateById(TerminalFileExtractTask entity) {
        if (entity.getId() == null) {
            return 0;
        }
        List<TerminalFileExtractTask> list = CACHE.get(entity.getTerminalId());
        if (list == null || list.isEmpty()) {
            return 0;
        }
        for (int i = 0; i < list.size(); i++) {
            if (list.get(i).getId().equals(entity.getId())) {
                entity.setModifyTime(new Date());
                list.set(i, entity);
                return 1;
            }
        }
        return 0;
    }

    /**
     * 根据ID删除提取任务
     */
    public int deleteById(Long id) {
        if (id == null) {
            return 0;
        }
        AtomicBoolean removed = new AtomicBoolean(false);
        CACHE.forEach((termId, taskList) -> removed.set(taskList.removeIf(task -> id.equals(task.getId())) || removed.get()));
        return removed.get() ? 1 : 0;
    }

    /**
     * 根据终端ID和提取的文件名查询提取任务
     *
     * @param taskQuery 查询条件（终端ID、目录、是否压缩、文件名、文件夹名）
     * @return 提取任务拷贝对象
     */
    public TerminalFileExtractTask getByFile(TerminalFileExtractTask taskQuery) {
        return this.clone(getTaskByFile(taskQuery));
    }

    /**
     * 根据终端ID和终端提取GUID查询提取任务
     *
     * @param terminalId 终端ID
     * @param guid       终端提取GUID
     * @return 提取任务拷贝对象
     */
    public TerminalFileExtractTask getByGuid(int terminalId, String guid) {
        return this.clone(getTaskByGuid(terminalId, guid));
    }

    /**
     * 根据终端ID和0x0202协议消息流水号查询提取任务
     *
     * @param terminalId 终端ID
     * @param msgSn      消息流水号（0x0202协议）
     * @return 提取任务拷贝对象
     */
    public TerminalFileExtractTask getByMsgSn(int terminalId, Integer msgSn) {
        return this.clone(getTaskByMsgSn(terminalId, msgSn));
    }

    /**
     * 根据终端ID和0x0203协议消息流水号查询提取任务
     *
     * @param terminalId  终端ID
     * @param notifyMsgSn 消息流水号（0x0203协议）
     * @return 提取任务拷贝对象
     */
    public TerminalFileExtractTask getByNotifyMsgSn(int terminalId, Integer notifyMsgSn) {
        return this.clone(getTaskByNotifyMsgSn(terminalId, notifyMsgSn));
    }

    /**
     * 根据终端ID和文件MD5查询提取任务
     *
     * @param terminalId 终端ID
     * @param md5        文件MD5
     * @return 提取任务拷贝对象列表
     */
    public List<TerminalFileExtractTask> selectByMd5(int terminalId, String md5) {
        List<TerminalFileExtractTask> list = selectTaskByMd5(terminalId, md5);
        if (list.isEmpty()) {
            return list;
        }
        return list.stream().map(TerminalFileExtractTask::clone).collect(Collectors.toCollection(LinkedList::new));
    }

    /**
     * 更新上传状态
     *
     * @param task 提取任务
     */
    public void updateUploadStatus(TerminalFileExtractTask task) {
        List<TerminalFileExtractTask> list = this.selectTaskByMd5(task.getTerminalId(), task.getMd5());
        if (list != null) {
            list.forEach(item -> {
                item.setUploadStatus(task.getUploadStatus());
                item.setUploadUrl(task.getUploadUrl());
                item.setModifyTime(new Date());
            });
        }
    }

    /**
     * 更新上传片段
     *
     * @param task 提取任务
     */
    public void updateUploadChunk(TerminalFileExtractTask task) {
        List<TerminalFileExtractTask> list = this.selectTaskByMd5(task.getTerminalId(), task.getMd5());
        if (list != null) {
            list.forEach(item -> {
                item.setUploadStatus(task.getUploadStatus());
                item.setChunkIndex(task.getChunkIndex());
                item.setChunkLength(task.getChunkLength());
                item.setModifyTime(new Date());
            });
        }
    }

    /**
     * 删除失效的提取任务
     *
     * @return 待清理的提取任务
     */
    public TerminalFileExtractTask.ClearTasks deleteExpiredTask() {
        final long current = System.currentTimeMillis();
        List<String> all = new LinkedList<>();
        Map<String, TerminalFileExtractTask.ExpiredTask> result = new HashMap<>();
        CACHE.forEach((termId, taskList) -> taskList.removeIf(task -> {
            String fileKey = task.fileKey();
            all.add(fileKey);
            boolean isExpired = current - task.getModifyTime().getTime() > DELETE_DURATION;
            if (isExpired) {
                log.info("Delete expired task: {}", task);
                result.put(fileKey, task.toExpiredTask());
            }
            return isExpired;
        }));
        List<TerminalFileExtractTask.ExpiredTask> expired = new LinkedList<>(result.values());
        return new TerminalFileExtractTask.ClearTasks(all, expired);
    }

    /**
     * 根据失效任务统计提取任务数量
     *
     * @param task 失效任务
     * @return 提取任务数量
     */
    public int countByExpiredTask(TerminalFileExtractTask.ExpiredTask task) {
        return this.selectTaskByMd5(task.getTerminalId(), task.getMd5()).size();
    }

    private TerminalFileExtractTask getTaskByFile(TerminalFileExtractTask taskQuery) {
        if (taskQuery == null || taskQuery.getDir() == null) {
            return null;
        }
        final String dir = taskQuery.getDir();
        final boolean zip = taskQuery.isZip();
        final String files = StringUtil.defaultString(taskQuery.getFiles());
        final String folders = StringUtil.defaultString(taskQuery.getFolders());
        return getOne(taskQuery.getTerminalId(), task -> isSameFiles(dir, zip, files, folders, task));
    }

    private boolean isSameFiles(String dir, boolean zip, String files, String folders, TerminalFileExtractTask task) {
        return dir.equals(task.getDir())
                && zip == task.isZip()
                && equalsPaths(files, StringUtil.defaultString(task.getFiles()))
                && equalsPaths(folders, StringUtil.defaultString(task.getFolders()));
    }

    private TerminalFileExtractTask getTaskByGuid(int terminalId, String guid) {
        if (StringUtil.isBlank(guid)) {
            return null;
        }
        return getOne(terminalId, task -> guid.equals(task.getGuid()));
    }

    private TerminalFileExtractTask getTaskByMsgSn(int terminalId, Integer msgSn) {
        if (msgSn == null) {
            return null;
        }
        return getOne(terminalId, task -> msgSn.equals(task.getMsgSn()));
    }

    private TerminalFileExtractTask getTaskByNotifyMsgSn(int terminalId, Integer notifyMsgSn) {
        if (notifyMsgSn == null) {
            return null;
        }
        return getOne(terminalId, task -> notifyMsgSn.equals(task.getNotifyMsgSn()));
    }

    private TerminalFileExtractTask getOne(int terminalId, Predicate<TerminalFileExtractTask> filter) {
        List<TerminalFileExtractTask> list = CACHE.get(terminalId);
        if (list == null || list.isEmpty()) {
            return null;
        }
        return list.stream().filter(filter).max(Comparator.comparing(BaseBean::getId)).orElse(null);
    }

    private List<TerminalFileExtractTask> selectTaskByMd5(int terminalId, String md5) {
        if (StringUtil.isBlank(md5)) {
            return Collections.emptyList();
        }
        List<TerminalFileExtractTask> list = CACHE.get(terminalId);
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }
        return list.stream().filter(task -> md5.equals(task.getMd5())).collect(Collectors.toCollection(LinkedList::new));
    }

    private boolean equalsPaths(String paths1, String paths2) {
        if (paths1.length() != paths2.length()) {
            return false;
        }
        String[] pathArr1 = StringUtil.split(paths1, '|');
        String[] pathArr2 = StringUtil.split(paths2, '|');
        if (pathArr1.length != pathArr2.length) {
            return false;
        }
        for (String path : pathArr1) {
            if (!ArrayUtils.contains(pathArr2, path)) {
                return false;
            }
        }
        return true;
    }

    private TerminalFileExtractTask clone(TerminalFileExtractTask task) {
        if (task == null) {
            return null;
        }
        return task.clone();
    }

}
