package com.tipray.dlp.dao;

import com.tipray.dlp.bean.vo.DevServerAccessApproveVO;
import com.tipray.dlp.bean.DevServerAccessLog;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import com.tipray.dlp.mybatis.datasource.DataSourceFactory;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 服务设备审批接入日志Dao
 * <AUTHOR>
 * @date 2025-01-16 15:31
 */
@Repository
@ShardingDataSource(datasourceId = DataSourceFactory.SHARD_MASTER_DATA_SOURCE)
public interface DevServerAccessLogDao extends BaseDao<DevServerAccessLog> {

    /**
     *
     * @param condition
     * @return
     */
    Long countByVO(DevServerAccessApproveVO condition);

    /**
     * 查询数据
     * @param condition
     * @return
     */
    List<DevServerAccessLog> listByVO(DevServerAccessApproveVO condition);

}
