package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.MobileTermUpgradeStatusLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.LogVO;
import com.tipray.dlp.bean.vo.MobileTermUpgradeStatusLogVO;
import com.tipray.dlp.service.MobileTermUpgradeStatusLogService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 移动终端升级状态Controller
 */
@Oplog(MobileTermUpgradeStatusLog.class)
@RestController
@RequestMapping("/log/mobileTermUpgradeStatus")
public class MobileTermUpgradeStatusLogController {
    @Resource
    private MobileTermUpgradeStatusLogService mobileTermUpgradeStatusLogService;

    /** 分页查询*/
    @PostMapping(value = "getPage")
    @Oplog(value = MobileTermUpgradeStatusLogVO.class, name = "route.mobileTerminalUpgradeLog")
    public GridPageDTO<MobileTermUpgradeStatusLog> getPage(@RequestBody MobileTermUpgradeStatusLogVO vo) {
        return mobileTermUpgradeStatusLogService.getPage(vo);
    }

    @PostMapping(value = "getNewStatusPage")
    public GridPageDTO<MobileTermUpgradeStatusLog> getNewStatusPage(@RequestBody LogVO vo) {
        return mobileTermUpgradeStatusLogService.getNewStatusPage(vo);
    }

    /**
     * 导出移动终端升级记录: 根据查询筛选条件导出Excel
     * @param vo
     * @return
     */
    @Oplog(value = LogVO.class,name = "route.mobileTerminalUpgradeLog")
    @PostMapping(value = "export")
    public ResponseDTO exportExcelByLogVO(@RequestBody LogVO vo){
        return ResponseDTO.success(mobileTermUpgradeStatusLogService.exportExcel(vo));
    }

    /**
     * 删除日志
     * @param list
     * @return
     */
    @Oplog(name = "route.mobileTerminalUpgradeLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<MobileTermUpgradeStatusLog> list) {
        mobileTermUpgradeStatusLogService.deleteBatch(list);
        return ResponseDTO.success();
    }
}
