package com.tipray.dlp.dao;

import com.tipray.dlp.bean.ProcessCollectRule;
import com.tipray.dlp.mybatis.bean.PageVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 进程收集规则详细表(ProcessCollectRule)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-05-22 14:56:22
 */
@Repository
public interface ProcessCollectRuleDao extends BaseDao<ProcessCollectRule>{
    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ProcessCollectRule getById(Long id);

    /**
     * 通过实体作为筛选条件查询
     *
     * @return 对象列表
     */
    @Override
    List<ProcessCollectRule> selectAll();
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(PageVO vo);
     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<ProcessCollectRule> listByVO(PageVO vo);
}
