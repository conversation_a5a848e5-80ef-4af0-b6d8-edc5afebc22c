package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.TagFileOutSendLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.AddTagLogVO;
import com.tipray.dlp.service.TagFileOutSendLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 带标签文档外传记录Controller
 */
@Oplog(TagFileOutSendLog.class)
@RestController
@RequestMapping("/log/tagFileOutSendLog")
public class TagFileOutSendLogController {
    @Autowired
    private TagFileOutSendLogService service;

    /**
     * 删除带标签文档记录
     * @param list
     * @return
     */
    @Oplog(value = AddTagLogVO.class,name = "route.tagFileOutSendLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<TagFileOutSendLog> list) {
        service.deleteBatch(list);
        return ResponseDTO.success();
    }

    /**
     * 查询带标签文档记录
     * @param vo
     * @return
     */
    @Oplog(value = AddTagLogVO.class,name = "route.tagFileOutSendLog")
    @PostMapping(value = "getPage")
    public GridPageDTO<TagFileOutSendLog> getPage(@RequestBody AddTagLogVO vo) {
        return service.getPage(vo);
    }

    /**
     * 导出带标签文档记录
     * 根据查询筛选条件导出Excel
     * @param vo
     * @return
     */
    @Oplog(value = AddTagLogVO.class, name = "route.tagFileOutSendLog")
    @PostMapping(value = "export")
    public ResponseDTO exportExcelByLogVO(@RequestBody AddTagLogVO vo){
        return ResponseDTO.success(service.exportExcel(vo));
    }
}
