package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.MailWhiteListReceiver;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.TreeNodeDTO;
import com.tipray.dlp.bean.vo.MailWhiteListReceiverVO;
import com.tipray.dlp.service.MailWhiteListReceiverService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 收件人白名单Controller
 */
@RestController
@Oplog(MailWhiteListReceiver.class)
@RequestMapping("/mailWhiteListReceiver")
public class MailWhiteListReceiverController {
    @Autowired
    private MailWhiteListReceiverService mailWhiteListReceiverService;

    /** 添加收件人白名单*/
    @Oplog(name="pages.mailWhiteListStg")
    @PostMapping(value = "add")
    public MailWhiteListReceiver addMailWhiteListReceiver(@RequestBody MailWhiteListReceiver bean) {
        mailWhiteListReceiverService.insertMailWhiteListReceiver(bean);
        return bean;
    }

    /** 修改收件人白名单*/
    @Oplog(name="pages.mailWhiteListStg")
    @PostMapping(value = "update")
    public MailWhiteListReceiver updateMailWhiteListReceiver(@RequestBody MailWhiteListReceiver bean) {
        mailWhiteListReceiverService.updateMailWhiteListReceiver(bean);
        return bean;
    }

    /** 删除收件人白名单*/
    @Oplog(name="pages.mailWhiteListStg")
    @PostMapping(value = "delete")
    public void deleteMailWhiteListReceiver(String ids) {
        mailWhiteListReceiverService.deleteMailWhiteListReceiverById(ids);
    }

    @GetMapping("get/{id}")
    public MailWhiteListReceiver getById(@PathVariable("id") Long id) {
        return mailWhiteListReceiverService.getMailWhiteListReceiverById(id);
    }

    @PostMapping("getByMailAddress")
    public MailWhiteListReceiver getByMailAddress(String mailAddress) {
        return mailWhiteListReceiverService.getMailWhiteListReceiverByMailAddress(mailAddress);
    }

    /** 获取收件人白名单列表*/
    @PostMapping(value = "getPage")
    public GridPageDTO<MailWhiteListReceiver> getStrategyPage(@RequestBody MailWhiteListReceiverVO vo) {
        return mailWhiteListReceiverService.getMailWhiteListReceiverPage(vo);
    }

    /** 获取收件人树*/
    @GetMapping(value = "listTreeNode")
    public List<TreeNodeDTO> listMailWhiteListReceiverTreeNode(){
        return mailWhiteListReceiverService.listMailWhiteListReceiverTreeNode();
    }
}
