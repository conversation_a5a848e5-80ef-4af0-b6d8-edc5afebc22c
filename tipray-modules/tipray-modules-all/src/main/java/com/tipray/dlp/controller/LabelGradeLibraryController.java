package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.LabelGradeLibrary;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.LabelGradeLibraryVO;
import com.tipray.dlp.service.LabelGradeLibraryService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 文标签等级库Controller
 * <AUTHOR>
 * @date 2025/3/24
 */
@RestController
@Oplog(LabelGradeLibrary.class)
@RequestMapping("/labelGradeLibrary")
public class LabelGradeLibraryController {
    @Resource
    private LabelGradeLibraryService labelGradeLibraryService;
    /**
     * 添加标签等级
     * @param bean
     * @return
     */
    @Oplog(value = LabelGradeLibrary.class,name = "table.labelGrade")
    @PostMapping(value = "add")
    public LabelGradeLibrary add(@RequestBody LabelGradeLibrary bean) {
        labelGradeLibraryService.insert(bean);
        return bean;
    }

    /**
     * 修改标签等级
     * @param bean
     * @return
     */
    @Oplog(value = LabelGradeLibrary.class,name = "table.labelGrade")
    @PostMapping(value = "update")
    public LabelGradeLibrary update(@RequestBody LabelGradeLibrary bean) {
        labelGradeLibraryService.update(bean);
        return bean;
    }

    /**
     * 删除标签
     * @param vo
     * @return
     */
    @Oplog(value = LabelGradeLibraryVO.class,name = "table.labelGrade")
    @PostMapping(value = "delete")
    public ResponseDTO batchDelete(@RequestBody LabelGradeLibraryVO vo) {
        List<String> names = labelGradeLibraryService.listGradeNameByIds(vo.getIds());
        // 用于记录删除标签等级的管理员日志
        vo.setNames(names);
        labelGradeLibraryService.deleteByIds(vo.getIds(),vo.getGrade(),vo.getLangKey(),vo.getTargetLangKey());
        return ResponseDTO.success(vo);
    }

    @PostMapping(value = "getPage")
    public GridPageDTO<LabelGradeLibrary> getPage(@RequestBody LabelGradeLibraryVO vo) {
        return labelGradeLibraryService.getPage(vo);
    }

    @GetMapping(value = "getList")
    public List<LabelGradeLibrary> getList() {
        return labelGradeLibraryService.getList();
    }

    @GetMapping(value = "getAllList")
    public List<LabelGradeLibrary> getAllList() {
        return labelGradeLibraryService.getAllList();
    }

    @PostMapping(value = "saveEnableGrade")
    public void saveEnableGrade(Long gradeId) {
        labelGradeLibraryService.saveEnableGrade(gradeId);
    }

    @GetMapping(value = "getMaxGrade")
    public Integer getMaxGrade() {
        return labelGradeLibraryService.getMaxGrade();
    }

    @GetMapping(value = "getEnableGrade")
    public Long getEnableGradeId() {
        return labelGradeLibraryService.getEnableGradeId();
    }

    @PostMapping(value = "countByGrade")
    public Integer countByGrade(LabelGradeLibrary bean){
        List<Long> ids = labelGradeLibraryService.countByGrade(bean.getGrade());
        // 如果只有一个，同时等于当前id，那么说明不重复
        return ids.size() ==0 || ids.size()==1 && ids.contains(bean.getId()) ? 0 : ids.size();
    }


}
