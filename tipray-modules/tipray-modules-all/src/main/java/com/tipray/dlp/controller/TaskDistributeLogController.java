package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.TaskDistributeLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.TaskDistributeLogVO;
import com.tipray.dlp.service.TaskDistributeLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 任务分发执行日志Controller
 */
@Oplog(TaskDistributeLog.class)
@RestController
@RequestMapping("/log/taskDistribute")
public class TaskDistributeLogController {
    @Autowired
    private TaskDistributeLogService service;

    @Oplog(value = TaskDistributeLogVO.class,name = "route.taskDistributeLog")
    @PostMapping(value = "getPage")
    public GridPageDTO<TaskDistributeLog> getPage(@RequestBody TaskDistributeLogVO vo) {
        return service.getLogPage(vo);
    }

    /**
     * @param list
     * @return
     */
    @Oplog(name = "route.taskDistributeLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<TaskDistributeLog> list) {
        service.deleteBatch(list);
        return ResponseDTO.success();
    }

    /**
     * 导出任务分发记录
     * 根据查询筛选条件导出Excel
     * @param vo
     * @return
     */
    @Oplog(value = TaskDistributeLogVO.class, name = "route.taskDistributeLog")
    @PostMapping(value = "export")
    public ResponseDTO exportExcelByLogVO(@RequestBody TaskDistributeLogVO vo){
        return ResponseDTO.success(service.exportExcel(vo));
    }
}
