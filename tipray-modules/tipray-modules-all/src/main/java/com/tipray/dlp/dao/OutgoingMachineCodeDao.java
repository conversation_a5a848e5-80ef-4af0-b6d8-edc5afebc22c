package com.tipray.dlp.dao;

import com.tipray.dlp.bean.OutgoingMachineCode;
import com.tipray.dlp.bean.vo.MachineCodeInfoVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OutgoingMachineCodeDao extends BaseDao<OutgoingMachineCode> {
    List<OutgoingMachineCode> listByVO(MachineCodeInfoVO vo);

    Long countByVO(MachineCodeInfoVO vo);

    List<OutgoingMachineCode> list(MachineCodeInfoVO vo);
}
