package com.tipray.dlp.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tipray.dlp.bean.TermLoginLogoutLog;
import com.tipray.dlp.bean.vo.TermLoginLogoutLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
@ShardingDataSource
public interface TerminalOnlineLogDao extends BaseMapper<TermLoginLogoutLog> {
    Long countByVO(TermLoginLogoutLogVO vo);
    List<TermLoginLogoutLog> listByVO(TermLoginLogoutLogVO vo);
    List<Map<String, Object>> statisticGroupByCreateTime(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
}
