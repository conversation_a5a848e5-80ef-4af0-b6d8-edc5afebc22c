package com.tipray.dlp.dao;

import com.tipray.dlp.bean.TaskDistributeLog;
import com.tipray.dlp.bean.vo.TaskDistributeLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
@ShardingDataSource
public interface TaskDistributeLogDao extends BaseDao<TaskDistributeLog>{
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(TaskDistributeLogVO vo);

     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<TaskDistributeLog> listByVO(TaskDistributeLogVO vo);

}
