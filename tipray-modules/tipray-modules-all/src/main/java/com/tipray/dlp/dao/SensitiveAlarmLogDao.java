package com.tipray.dlp.dao;

import com.tipray.dlp.bean.SensitiveAlarmLog;
import com.tipray.dlp.bean.vo.SensitiveAlarmLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource
public interface SensitiveAlarmLogDao extends BaseDao<SensitiveAlarmLog> {

    Long countByVO(SensitiveAlarmLogVO vo);

    List<SensitiveAlarmLog> listByVO(SensitiveAlarmLogVO vo);

    SensitiveAlarmLog getById(Long id);

}
