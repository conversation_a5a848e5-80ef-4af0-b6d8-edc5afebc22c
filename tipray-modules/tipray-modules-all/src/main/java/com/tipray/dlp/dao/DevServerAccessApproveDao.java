package com.tipray.dlp.dao;

import com.tipray.dlp.bean.DevServerAccessApprove;
import com.tipray.dlp.bean.vo.DevServerAccessApproveVO;
import com.tipray.dlp.util.CollectionUtil;
import com.tipray.dlp.util.StringUtil;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 服务设备审批接入Dao
 * <AUTHOR>
 * @date 2025-01-16 15:31
 */
@Repository
public interface DevServerAccessApproveDao extends BaseDao<DevServerAccessApprove> {

    /**
     *
     * @param condition
     * @return
     */
    Long countByVO(DevServerAccessApproveVO condition);

    /**
     * 查询数据
     * @param condition
     * @return
     */
    List<DevServerAccessApprove> listByVO(DevServerAccessApproveVO condition);

    /**
     * 更新数据
     * @param bean
     * @return
     */
    int updateDataById(DevServerAccessApprove bean);

    /**
     * 更新数据
     * @param bean
     * @return
     */
    int updateDataByDevGuids(DevServerAccessApprove bean);

    /**
     * 根据设备Guid查询数据
     * @param devGuid
     * @return
     */
    default DevServerAccessApprove getByDevGuid(String devGuid) {
        if (StringUtil.isNotEmpty(devGuid)) {
            DevServerAccessApproveVO condition = new DevServerAccessApproveVO();
            condition.setDevGuid(devGuid);
            List<DevServerAccessApprove> list = listByVO(condition);
            if (CollectionUtil.isNotEmpty(list)) {
                return list.get(0);
            }
        }
        return null;
    }

}
