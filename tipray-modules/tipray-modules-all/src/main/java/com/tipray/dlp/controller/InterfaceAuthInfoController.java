package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.InterfaceAuthInfo;
import com.tipray.dlp.bean.dict.OperationTypeDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.controller.download.task.DownloadTaskFactory;
import com.tipray.dlp.controller.socket.PKeyInfoSocketMapper;
import com.tipray.dlp.mybatis.bean.PageVO;
import com.tipray.dlp.service.InterfaceAuthInfoService;
import com.tipray.dlp.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;

/**
 * 加解密接口授权Controller
 * <AUTHOR>
 * @date 2022/9/22
 */
@RestController
@Oplog(InterfaceAuthInfo.class)
@RequestMapping("/interfaceAuthInfo")
@Slf4j
public class InterfaceAuthInfoController {

    @Resource
    private InterfaceAuthInfoService interfaceAuthInfoService;
    @Resource
    private PKeyInfoSocketMapper socketApi;

    /** 新增加解密接口授权*/
    @Oplog(name="route.interfaceAuth")
    @PostMapping(value = "add")
    public InterfaceAuthInfo add(@RequestBody InterfaceAuthInfo bean) {
        interfaceAuthInfoService.insertInterfaceAuthInfo(bean);
        return bean;
    }

    /** 修改加解密接口授权*/
    @Oplog(name="route.interfaceAuth")
    @PostMapping(value = "update")
    public InterfaceAuthInfo update(@RequestBody InterfaceAuthInfo bean) {
        interfaceAuthInfoService.updateInterfaceAuthInfo(bean);
        return bean;
    }

    /** 删除加解密接口授权*/
    @Oplog(name="route.interfaceAuth")
    @PostMapping(value = "delete")
    public List<InterfaceAuthInfo> deleteInterfaceAuthInfo(String ids) {
        return interfaceAuthInfoService.deleteInterfaceAuthInfoByIds(ids);
    }

    /** 分页查询*/
    @PostMapping(value = "getPage")
    public GridPageDTO<InterfaceAuthInfo> getPage(@RequestBody PageVO vo){
        return interfaceAuthInfoService.getPage(vo);
    }

    @PostMapping("getByName")
    public InterfaceAuthInfo getByName(String name) {
        return interfaceAuthInfoService.getInterfaceAuthInfoByName(name);
    }

    /** 获取授权文件*/
    @GetMapping(value = "startDownload/{id}")
    public ResponseDTO downloadStart(@PathVariable("id") Long id) {
        try {
            InterfaceAuthInfo interfaceAuthInfo = interfaceAuthInfoService.getInterfaceAuthInfo(id);
            Map<String, Object> map = new HashMap<>();
            map.put("ver", interfaceAuthInfo.getVersion());
            map.put("type", interfaceAuthInfo.getType());
            map.put("encryptionVer", interfaceAuthInfo.getDecryptVersion());
            map.put("groupId", interfaceAuthInfo.getGroupId());
            map.put("encryptLevel", interfaceAuthInfo.getEncryptLevel());
            List<String> mcodes = CollectionUtil.toFieldList(interfaceAuthInfo.getMcodes(), "mcode", String.class);
            map.put("mcodes", mcodes);
            String startDate = DateUtil.formatDate(interfaceAuthInfo.getStartDate());
            map.put("startDate", startDate);
            String endDate = DateUtil.formatDate(interfaceAuthInfo.getEndDate());
            map.put("endDate", endDate);
            String fileGuid = StringUtil.replace(UUID.randomUUID().toString(), "-", "");
            map.put("fileGuid", fileGuid);
            Integer msgNo = socketApi.toGetAuthFile(map);
            log.info("获取加解密接口授权文件的消息流水号 ： {}" ,msgNo);
            Map<String, Object> cacheMap = new HashMap<>();
            cacheMap.put("fileGuid", fileGuid);
            cacheMap.put("authType", interfaceAuthInfo.getAuthType());
            CacheUtil.cache(msgNo.toString(), cacheMap, 5L);
            return ResponseDTO.requestId(msgNo);
        } catch (Exception e) {
            return ResponseDTO.failure(e);
        }
    }

    /** 下载授权文件*/
    @Oplog(value = Map.class, type = OperationTypeDict.DOWNLOAD, name = "pages.authorizationFile")
    @PostMapping(value = "downloadFile")
    public void downloadFile(@RequestBody Map<String, Object> params, HttpServletRequest request, HttpServletResponse response) {
        String files = null;
        if(params != null) {
            Object obj = params.get("files");
            if(obj != null) {
                files = (String) obj;
            }
        }

        String fileName = FileUtil.append(FileUtil.CACHE_FILE, files);
        File file = new File(fileName);
        if (!file.exists()) {
            log.warn("下载接加密接口授权文件 {} 不存在", fileName);
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            return;
        }
        String dateString = DateUtil.format(new Date(), "yyyyMMddHHmmss")+System.nanoTime();
        DownloadTaskFactory.addCommonTask(response, I18nUtils.get("route.interfaceAuth", "加解密接口授权") + "_" + dateString + ".lrc", file);
    }
}
