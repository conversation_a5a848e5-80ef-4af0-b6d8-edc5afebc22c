package com.tipray.dlp.dao;

import com.tipray.dlp.bean.SystemBackupLog;
import com.tipray.dlp.bean.vo.SystemBackupLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数据库备份日志表(SystemBackupLog)表数据库访问层
 *
 * <AUTHOR>
 * @date 2022/10/09
 */
@Repository
public interface SystemBackupLogDao extends BaseDao<SystemBackupLog> {

    /**
     * 通过条件查询满足条件的数据个数
     * @param vo
     * @return  满足条件的数据个数
     */
    Long countByVO(SystemBackupLogVO vo);

    /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<SystemBackupLog> listByVO(SystemBackupLogVO vo);

}
