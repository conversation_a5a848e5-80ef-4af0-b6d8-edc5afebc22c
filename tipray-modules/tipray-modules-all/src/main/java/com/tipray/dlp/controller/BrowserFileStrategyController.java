package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.BrowserFileStrategy;
import com.tipray.dlp.bean.dict.MsgModuleDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.BrowserFileStrategyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
/**
 * 网络上传文件管控策略Controller
 * <AUTHOR>
 * @since 2020-6-6
 */
@RestController
@Oplog(BrowserFileStrategy.class)
@RequestMapping("/browserFileStrategy")
public class BrowserFileStrategyController {
    @Autowired
    private BrowserFileStrategyService browserFileStrategyService;

    /**
     * 添加网络上传文件管控策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.browserFileStrategy")
    @PostMapping(value = "add")
    public BrowserFileStrategy addStrategy(@RequestBody BrowserFileStrategy bean) {
        bean.setSstType(0);
        bean.setAlarmType(MsgModuleDict.WEB_UPLOAD.getCode());
        browserFileStrategyService.insertStrategy(bean);
        return bean;
    }

    /**
     * 修改网络上传文件管控策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.browserFileStrategy")
    @PostMapping(value = "update")
    public BrowserFileStrategy updateStrategy(@RequestBody BrowserFileStrategy bean) {
        bean.setSstType(0);
        bean.setAlarmType(MsgModuleDict.WEB_UPLOAD.getCode());
        browserFileStrategyService.updateStrategy(bean);
        return bean;
    }

    /**
     * 删除网络上传文件管控策略
     * @param ids
     */
    @Oplog(name = "pages.browserFileStrategy")
    @PostMapping(value = "delete")
    public void deleteStrategy(String ids) {
        browserFileStrategyService.deleteStrategyById(ids);
    }

    @GetMapping("get/{id}")
    public BrowserFileStrategy getById(@PathVariable("id") Long id) {
        return browserFileStrategyService.getStrategyById(id);
    }

    @PostMapping("getByName")
    public BrowserFileStrategy getByName(String name) {
        return browserFileStrategyService.getStrategyByName(name);
    }

    /**
     * 获取网络上传文件管控策略
     * @param vo
     * @return
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<BrowserFileStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return browserFileStrategyService.getStrategyPage(vo);
    }

}
