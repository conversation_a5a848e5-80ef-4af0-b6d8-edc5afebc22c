package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.FtpControlConfig;
import com.tipray.dlp.bean.ManualLabelStrategy;
import com.tipray.dlp.bean.dict.MsgModuleDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.exception.ParamException;
import com.tipray.dlp.service.FtpControlConfigService;
import com.tipray.dlp.service.ManualLabelStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 手动加标签策略Controller
 */
@RestController
@Oplog(ManualLabelStrategy.class)
@RequestMapping("/manualLabelStrategy")
@Slf4j
public class ManualLabelStrategyController {
    @Autowired
    private ManualLabelStrategyService manualLabelStrategyService;

    /**
     * 添加手动加标签策略
     * @param beans
     * @return
     */
    @Oplog(name = "route.manualLabel")
    @PostMapping(value = "add")
    public ManualLabelStrategy[] addStrategy(@RequestBody ManualLabelStrategy[] beans){
        manualLabelStrategyService.insert(beans);
        return beans;
    }

    /**
     * 修改手动加标签策略
     * @param beans
     * @return
     */
    @Oplog(name = "route.manualLabel")
    @PostMapping(value = "update")
    public ManualLabelStrategy[] updateStrategy(@RequestBody ManualLabelStrategy[] beans){
        manualLabelStrategyService.update(beans);
        return beans;
    }

    /**
     * 删除手动加标签策略
     * @param ids
     */
    @Oplog(name = "route.manualLabel")
    @PostMapping(value = "delete")
    public void deleteAppBlock(String ids) {
        manualLabelStrategyService.deleteById(ids);
    }

    /** FTP文件管理分页列表*/
    @PostMapping(value = "getPage")
    public GridPageDTO<ManualLabelStrategy> getStategyPage(@RequestBody StrategyVO vo) {
        return manualLabelStrategyService.getStrategyPage(vo);
    }

    /**
     * 根据名称获取策略
     * @param name     策略名称
     * @return  策略内容
     */
    @PostMapping("getByName")
    public ManualLabelStrategy getByName(String name) {
        return manualLabelStrategyService.getStrategyByName(name);
    }
}
