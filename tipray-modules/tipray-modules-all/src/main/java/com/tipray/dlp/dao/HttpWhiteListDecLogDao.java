package com.tipray.dlp.dao;

import com.tipray.dlp.bean.HttpWhiteListDecLog;
import com.tipray.dlp.bean.vo.HttpWhiteListLogVo;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource
public interface HttpWhiteListDecLogDao extends BaseDao<HttpWhiteListDecLog> {

    Long countByVO(HttpWhiteListLogVo vo);

    List<HttpWhiteListDecLog> listByVO(HttpWhiteListLogVo vo);

}
