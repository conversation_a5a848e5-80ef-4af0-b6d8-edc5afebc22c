package com.tipray.dlp.dao;

import com.tipray.dlp.mybatis.annotation.WhereIn;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface DatabaseServerReflectDao {
    List<Long> listServerIds(Long serverId);

    void deleteByServerId(Long serverId);

    void deleteByRefServerId(Long serverId);

    @WhereIn(@WhereIn.Param("serverIds"))
    void deleteByServerIdOrRefServerId(String serverIds);

    void batchInsert(@Param("refServerId") Long refServerId, @Param("serverIds") List<Long> serverIds);

    void insert(Long serverId, Long refServerId);

    /**
     * 获取所有总部服务器ID
     * @return
     */
    List<Long> listAllRefServerId();

    List<Long> listAllServerId();
    List<Map<String, Object>> listAll();

    Long getRefServerIdByServerId(Long devId);

    /**
     * 根据数据库Id查询其关联关系
     * @return
     */
    Map<String, Integer> selectByExitsDevId(Integer dbId);

}
