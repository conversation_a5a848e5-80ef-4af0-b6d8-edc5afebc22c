package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.AlarmBaseStrategy;
import com.tipray.dlp.bean.SoftwareAssetAlarmSetup;
import com.tipray.dlp.service.AlarmBaseStrategyService;
import com.tipray.dlp.service.SoftwareAssetAlarmSetupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 软件资产变更报警与记录设置Controller
 */
@RestController
@Oplog(SoftwareAssetAlarmSetup.class)
@RequestMapping("/softwareAssetAlarmSetup")
@Slf4j
public class SoftwareAssetAlarmSetupController {

    /**
     * 服务对象
     */
    @Resource
    private SoftwareAssetAlarmSetupService softwareAssetAlarmSetupService;
    @Resource
    private AlarmBaseStrategyService alarmBaseStrategyService;

    /**
     * 修改软件资产变更报警与记录设置
     * @param bean
     * @return
     */
    @Oplog(name = "route.softwareAssetChangeAlarmLogSettings")
    @PostMapping(value = "update")
    public SoftwareAssetAlarmSetup update(@RequestBody SoftwareAssetAlarmSetup bean){
        softwareAssetAlarmSetupService.updateAssetAlarmSetup(bean);
        return bean;
    }

    /**
     * 得到软件资产变更报警与记录设置
     *
     * @return 单条数据
     */
    @GetMapping("get")
    public SoftwareAssetAlarmSetup getById() {
        SoftwareAssetAlarmSetup assetAlarmSetup = softwareAssetAlarmSetupService.getAssetAlarmSetup();
        if (assetAlarmSetup != null) {
            AlarmBaseStrategy stg = alarmBaseStrategyService.getByAlarmType(14);
            assetAlarmSetup.setRuleId(stg==null || stg.getRuleId() == 0 ?
                    null : stg.getRuleId());
        }
        return assetAlarmSetup;
    }
}
