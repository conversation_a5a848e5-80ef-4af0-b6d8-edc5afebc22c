package com.tipray.dlp.controller;

import com.alibaba.fastjson.JSONObject;
import com.tipray.dlp.annotation.EncryptDecrypt;
import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.BackupFileLog;
import com.tipray.dlp.bean.BackupRecoverPassword;
import com.tipray.dlp.bean.ManageDataBaseBack;
import com.tipray.dlp.bean.dict.DbBackupRecoverConstant;
import com.tipray.dlp.bean.dict.OperationTypeDict;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.ManageDataBaseBackVO;
import com.tipray.dlp.controller.socket.DataBaseBackSocketMapper;
import com.tipray.dlp.controller.websocket.DataBaseBackupWebSocketApi;
import com.tipray.dlp.exception.ServiceException;
import com.tipray.dlp.sdk.api.CloudDatabaseBackupApiService;
import com.tipray.dlp.service.DatabaseBackupInfoService;
import com.tipray.dlp.service.DatabaseRecoverLogService;
import com.tipray.dlp.util.*;
import com.tipray.dlp.websocket.annotation.WSocketRequestId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/9/20 14:55
 */
@Slf4j
@RestController
@RequestMapping("databaseBackupInfo")
public class DatabaseBackupInfoController {

    @Resource
    private DatabaseBackupInfoService databaseBackupInfoService;
    @Resource
    private DataBaseBackSocketMapper dataBaseBackSocketMapper;
    @Resource
    private CloudDatabaseBackupApiService databaseBackupApiService;
    @Resource
    private DatabaseRecoverLogService databaseRecoverLogService;
    @Resource
    private DataBaseBackupWebSocketApi dataBaseBackupWebSocketApi;


    /**
     * 获取当前数据库所在的磁盘信息
     * @return  /
     */
    @GetMapping("/getCurrentDataBaseServerInfo")
    @EncryptDecrypt(value = Map.class, name = "dataBaseBack.local.lpPassword", allowBlank = true)
    public ResponseDTO getServerInfo() {
        return ResponseDTO.success(databaseBackupInfoService.getServerInfo());
    }

    /**
     * 数据库备份配置
     */
    @Oplog(value = ManageDataBaseBack.class, name = "pages.databaseBackupConfiguration")
    @EncryptDecrypt(value = ManageDataBaseBackVO.class, name = "local.lpPassword", allowBlank = true)
    @PostMapping(value = "manageDataBaseBack")
    public void updateDataBaseBack(@RequestBody ManageDataBaseBackVO manageDataBaseBack) {
        if (manageDataBaseBack == null || manageDataBaseBack.getNotice() == null) {
            // 配置信息异常
            throw new ServiceException(I18nUtils.get("pages.dataBaseBackupInfoException10"));
        }
        databaseBackupInfoService.manageDataBaseBack(manageDataBaseBack, manageDataBaseBack.getNotice());
    }

    /** 根据本地备份地址查询备份列表*/
    @EncryptDecrypt(value = ManageDataBaseBack.Local.class, name = "lpPassword", allowBlank = true)
    @PostMapping("getLocalList")
    public void getLocalList(@RequestBody ManageDataBaseBack.Local local) {
        if (local == null) {
            // 参数为空
            throw new ServiceException(I18nUtils.get("pages.paramIsEmpty"));
        }
        dataBaseBackSocketMapper.scanPathBackupFileList(local);
    }

    /** 云平台备份查询*/
    @GetMapping(value = "getCloudList")
    public List<BackupFileLog> getCloudList() {
        return databaseBackupApiService.backupFileList();
    }

    /**
     * 云平台加密备份文件下载
     */
    @Oplog(type = OperationTypeDict.DOWNLOAD, name = "pages.cloudPlatformEncryptedBackupFile")
    @PostMapping(value = "downloadFile")
    public void downloadFile(HttpServletResponse response, String id, String fileName) {
        if (id == null || fileName == null) {
            log.error("云平台备份文件下载失败-原因：id参数为空或fileName为空 参数id：{}， 参数fileName: {}", id, fileName);
            // 备份文件下载失败
            throw new ServiceException(I18nUtils.get("pages.dataBaseBackupInfoException11"));
        }
        databaseBackupApiService.downLoadFile(response, id, fileName);
    }
    /**
     * 云平台明文备份文件下载
     */
    @Oplog(type = OperationTypeDict.DOWNLOAD, name = "pages.cloudPlatformPlaintextBackupFile")
    @PostMapping(value = "downloadProclaimedInWritingFile")
    public void downloadProclaimedInWritingFile(HttpServletResponse response, String id, String fileName) {
        if (id == null || fileName == null) {
            log.error("云平台备份文件下载失败-原因：id参数为空或fileName为空 参数id：{}， 参数fileName: {}", id, fileName);
            // 备份文件下载失败
            throw new ServiceException(I18nUtils.get("pages.dataBaseBackupInfoException11"));
        }
        databaseBackupInfoService.downloadProclaimedInWritingFile(response, id, fileName);
    }

    /**
     *加密备份文件解密(解密工具）
     * @param file  加密备份文件
     * @param response  /
     */
    @PostMapping(value = "fileDecode")
    public void fileDecode(MultipartFile file, HttpServletResponse response) {
        if (file == null) {
            log.error("加密备份文件解密 -> 文件上传失败");
            // 文件上传失败
            throw new ServiceException(I18nUtils.get("pages.dataBaseBackupInfoException12"));
        }
        log.info("加密策略库备份文件解密  管理员：{}， 备份文件名称：{}", ThreadVariable.getUserId(), file.getOriginalFilename());
        try {
            DbBackEncryptUtil.decodeFileAndExport(file.getInputStream(), response, file.getOriginalFilename());
        } catch (IOException e) {
            log.error("加密备份文件解密 -> 文件解密失败", e);
            // 文件解密失败
            throw new ServiceException(I18nUtils.get("pages.dataBaseBackupInfoException13"));
        }
    }

    /**
     * 检测登录用户是否具有一键恢复功能的权限
     * 功能逻辑说明：当超级管理员启用时，只有超级管理员具有一键恢复功能；
     * 当超级管理员禁用时，只有系统管理员才具有一键恢复的功能（必须输入审计管理员和安全管理员的密码）
     * @return
     * 0： 权限不足
     * 1： 超级管理员执行恢复
     * 2： 系统管理员执行恢复
     */
    @GetMapping("pretreatmentRecover")
    public ResponseDTO pretreatmentRecovered() {
        Integer auth = databaseBackupInfoService.pretreatmentRecover();
        if (auth == -1) {
            log.error("超级管理员账号不存在");
            // 系统异常
            throw new ServiceException(I18nUtils.get("pages.dataBaseBackupInfoException14"));
        } else {
            return ResponseDTO.success(auth);
        }
    }

    /**
     * 数据库恢复权限审核（需要安全和审计管理员的密码）
     */
    @Oplog(value = BackupRecoverPassword.class, name = "pages.backupRecoverByPassword", type = OperationTypeDict.UPDATE)
    @PostMapping("backupRecoverByPassword")
    public ResponseDTO backupRecoverByPassword(@RequestBody BackupRecoverPassword recoverPassword) {
        databaseBackupInfoService.backupRecoverByPassword(recoverPassword);
        return ResponseDTO.success(recoverPassword);
    }

    /**
     * 备份恢复（无需密码）
     * @param recover   /
     * @return  /
     */
    @Oplog(value = BackupRecoverPassword.class, name = "pages.backupRecover", type = OperationTypeDict.UPDATE, defaultArg = true)
    @PostMapping("backupRecover")
    public ResponseDTO backupRecover(@RequestBody BackupRecoverPassword recover) {
        databaseBackupInfoService.backupRecover(recover);
        return ResponseDTO.success(recover);
    }


    /** 测试是否能连接共享文件夹 **/
    @PostMapping("/testConnection")
    @EncryptDecrypt(value = ManageDataBaseBack.Local.class, name = "lpPassword", allowBlank = true)
    public ResponseDTO testConnection(@RequestBody ManageDataBaseBack.Local local) {
        databaseBackupInfoService.testConnection(local);
        // 连接成功
        return ResponseDTO.success("pages.connectSuccess");
    }


    /**
     * 获取本地备份地址， 包含之前的备份地址
     * @return  当前配置的本地备份地址 和 上次配置的本地备份地址
     */
    @GetMapping("getLocalBackupPath")
    @EncryptDecrypt(value = ManageDataBaseBack.Local.class, name = "lpPassword", allowBlank = true)
    public ResponseDTO getLocalBackupPath() {
        return ResponseDTO.success(databaseBackupInfoService.getLocalBackupPath());
    }

    /**
     * 获取数据库恢复状态
     * @param type 1: 查询恢复状态和恢复提示信息，  非 1：查询恢复状态
     * @return 0：未启用，1:成功，2：失败，3：正在恢复中
     */
    @GetMapping("getBackupRecovering")
    public ResponseDTO getBackupRecovering(@RequestParam Integer type) {
        //  查询该条恢复记录的状态码，如果是正在恢复，返回1    0:未启用
        if (Objects.equals(type, 1)) {
            return ResponseDTO.success(databaseBackupInfoService.getBackupStatusData());
        } else {
            Map<String, Object> result = new HashMap<>(1);
            result.put("status", Optional.ofNullable(CacheUtil.getCache(DbBackupRecoverConstant.RECOVER_STATUS_KEY)).orElse(DbBackupRecoverConstant.RECOVER_STATUS_NOT_ENABLED));
            return ResponseDTO.success(result);
        }
    }

    /**
     * 执行数据库恢复
     * 控制台向引擎服务发送是否进行恢复通知（生命周期：开始恢复之前）
     * @param recoverNotice 1:确认 2:取消
     * @param backupFileLocation: 1:本地，2：云平台，3：共享共享文件夹
     */
    @GetMapping("confirmRecover")
    public void confirmRecover(@RequestParam("recoverNotice")Integer recoverNotice,
                               @RequestParam("backupFileName") String backupFileName,
                               @RequestParam("backupFileLocation") Integer backupFileLocation) {
        if (recoverNotice == null || backupFileLocation == null) {
            log.error("参数错误");
            // 参数错误
            throw new ServiceException(I18nUtils.get("pages.reportServerException5"));
        }
        //  添加恢复日志（后续要改成引擎返回恢复后再添加恢复日志）
        databaseRecoverLogService.beginRecoverLog(backupFileName, backupFileLocation);
        //  通知引擎进行恢复数据库
        dataBaseBackSocketMapper.confirmNotification(1, recoverNotice);
    }

    /**
     * 获取恢复状态
     * socket
     */
    @MessageMapping("/databaseBackup/backupRecover")
    @WSocketRequestId
    public void getBackupStatus(String language) {
        if (StringUtil.isNotEmpty(language)) {
            language = JSONObject.parseObject(language).getString("language");
        }
        language = StringUtil.isEmpty(language) ? "zh" : language;
        ThreadVariable.setLang(language);
        Map<String, Object> result = databaseBackupInfoService.getBackupStatusData();
        result.put("language", language);
        dataBaseBackupWebSocketApi.backupRecover(DbBackupRecoverConstant.DB_BACKUP_TASK_ID, JSONObject.toJSONString(result));
    }


    /**
     * 重启服务
     * @param restartId
     */
    @GetMapping("/restartApp")
    public void restartApp(@RequestParam("restartId") String restartId) {
        synchronized (DbBackupRecoverConstant.RESTART_APP_KEY) {
            Object key = CacheUtil.getCache(DbBackupRecoverConstant.RESTART_APP_KEY);
            //  如果restartId和缓存中的值相同，进行重启
            if (!Objects.isNull(key) && StringUtil.equals(restartId, String.valueOf(key))) {
                Object number = CacheUtil.getCache(DbBackupRecoverConstant.RESTARTED_APP_END_KEY);
                if (number == null) {
                    dataBaseBackSocketMapper.responseReloadCache();
                    CacheUtil.cache(DbBackupRecoverConstant.RESTARTED_APP_END_KEY, "1");
                    CacheUtil.del(DbBackupRecoverConstant.RESTART_APP_KEY);
                    //  等待5秒后执行重启服务，同时清空缓存
                    ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(1, new CustomizableThreadFactory("app-restart-pool-t-"));
                    executor.scheduleAtFixedRate(() -> {
                        CacheUtil.del(DbBackupRecoverConstant.RECOVER_STATUS_OLD_KEY);
                        CacheUtil.del(DbBackupRecoverConstant.DELAY_RECOVER_STATUS_KEY);
                        CacheUtil.del(DbBackupRecoverConstant.RECOVERING_PROCESS);
                        CacheUtil.del(DbBackupRecoverConstant.RESTARTED_APP_END_KEY);
                        AppUtil.restart();
                        executor.shutdown();

                        //  等待5s， 若DLP该代码有执行，表示重启失败
                        try {
                            TimeUnit.SECONDS.sleep(5);
                            log.error("数据库恢复完成后，但DLP重启失败");
                            CacheUtil.cache(DbBackupRecoverConstant.RECOVER_STATUS_KEY, DbBackupRecoverConstant.RECOVER_DLP_RESTART_FAIL, DbBackupRecoverConstant.RECOVER_FAIL_TIME);
                        } catch (InterruptedException e) {
                            log.error("数据库恢复 => 检查控制台重启失败  失败原因：{}", e.getMessage());
                        }
                    }, 5, DbBackupRecoverConstant.RECOVER_TIME_INTERVAL, TimeUnit.SECONDS);
                }
            }
        }
    }

    /**
     * 清空恢复状态（恢复状态为失败时）
     */
    @PostMapping("/clearStatusByFailed")
    public ResponseDTO clearStatusByFailed() {
        Object status = CacheUtil.getCache(DbBackupRecoverConstant.RECOVER_STATUS_KEY);
        //  恢复失败时，可清空恢复状态
        if (Objects.equals(status, DbBackupRecoverConstant.RECOVER_STATUS_FAIL) || Objects.equals(status, DbBackupRecoverConstant.RECOVER_STATUS_FAIL_8)) {
            CacheUtil.del(DbBackupRecoverConstant.DELAY_RECOVER_STATUS_KEY);
            CacheUtil.del(DbBackupRecoverConstant.RECOVER_STATUS_KEY);
            CacheUtil.del(DbBackupRecoverConstant.RECOVER_STATUS_OLD_KEY);
        }
        return ResponseDTO.success();
    }

}
