package com.tipray.dlp.dao;

import com.tipray.dlp.bean.MailWhiteListAttachLog;
import com.tipray.dlp.bean.MailWhiteListLog;
import com.tipray.dlp.bean.vo.MailWhiteListLogVo;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * 邮件白名单审计日志数据访问层
 */
@Repository
@ShardingDataSource
public interface MailWhiteListLogDao extends BaseDao<MailWhiteListLog> {
    /**
     * 通过条件查询数据
     * @return 对象列表
     */
    List<MailWhiteListLog> listByVO(MailWhiteListLogVo vo);

    /**
     * 查询附件数量
     * @param vo
     * @return
     */
    Integer getAttachNum(MailWhiteListLogVo vo);

    /**
     * 通过条件查询附件数据
     * @param vo
     * @return
     */
    List<MailWhiteListAttachLog> attachListByVO(MailWhiteListLogVo vo);

}
