package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.ChatToolDownloadLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.ChatLogVO;
import com.tipray.dlp.service.ChatToolDownloadLogService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 通信工具下载文件记录(ChatToolDownloadLog)表控制层
 *
 * <AUTHOR>
 * @date 2023/6/20 14:24
 */
@Oplog(ChatToolDownloadLog.class)
@RestController
@RequestMapping("/log/chatToolDownloadLog")
public class ChatToolDownloadLogController {
    /**
     * 服务对象
     */
    @Resource
    private ChatToolDownloadLogService chatToolDownloadLogService;

    /** 删除下载文件记录*/
    @Oplog(name = "pages.chatToolDownloadLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<ChatToolDownloadLog> list) {
        chatToolDownloadLogService.deleteBatch(list);
        return ResponseDTO.success();
    }

    /** 查询通信工具下载文件记录*/
    @Oplog(value = ChatLogVO.class, name = "pages.chatToolDownloadLog")
    @PostMapping(value = "getPage")
    public GridPageDTO<ChatToolDownloadLog> getPage(@RequestBody ChatLogVO vo) {
        return chatToolDownloadLogService.getChatToolDownloadPage(vo);
    }

    /** 导出通信工具下载文件记录*/
    @PostMapping(value = "export")
    @Oplog(value = ChatLogVO.class, delayLog = true, name = "pages.chatToolDownloadLog")
    public ResponseDTO exportExcel(@RequestBody ChatLogVO vo) {
        return ResponseDTO.success(chatToolDownloadLogService.exportExcel(vo));
    }

}
