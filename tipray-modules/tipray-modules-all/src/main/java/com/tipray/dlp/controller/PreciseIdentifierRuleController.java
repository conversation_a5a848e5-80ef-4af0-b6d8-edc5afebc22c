package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.PreciseIdentifierRule;
import com.tipray.dlp.service.PreciseIdentifierRuleService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 精准数据标识符规则Controller
 * <AUTHOR>
 */
@RestController
@Oplog(PreciseIdentifierRule.class)
@RequestMapping("/preciseIdentifierRule")
public class PreciseIdentifierRuleController {
    @Resource
    private PreciseIdentifierRuleService service;

    /** 获取精准数据标识符规则*/
    @GetMapping(value = "listTree")
    public List<PreciseIdentifierRule> listRules() {
        return service.listRules();
    }
}
