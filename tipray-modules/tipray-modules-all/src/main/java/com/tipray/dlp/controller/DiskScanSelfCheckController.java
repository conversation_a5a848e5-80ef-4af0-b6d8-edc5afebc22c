package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.ContentStgDef;
import com.tipray.dlp.bean.ContentStrategy;
import com.tipray.dlp.bean.DiskScanSelfCheck;
import com.tipray.dlp.bean.dict.DeleteStateDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.ContentStrategyService;
import com.tipray.dlp.service.DiskScanSelfCheckService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 全盘扫描Controller
 */
@RestController
@Oplog(DiskScanSelfCheck.class)
@RequestMapping("/diskScanSelfCheck")
public class DiskScanSelfCheckController {

    @Resource
    private DiskScanSelfCheckService diskScanSelfCheckService;
    @Resource
    private ContentStrategyService contentStrategyService;

    /**
     * 添加敏感文件自检策略
     * @param bean
     * @return
     */
    @Oplog(name = "route.diskScanSelfCheck")
    @PostMapping(value = "add")
    public DiskScanSelfCheck[] insert(@RequestBody DiskScanSelfCheck[] bean){
        diskScanSelfCheckService.insertScan(bean);
        return bean;
    }

    /**
     * 新增文件自检内容检测策略
     * @param bean
     * @return
     */
    @Oplog(value= ContentStgDef.class, name = "route.diskScanSelfCheckSensitive")
    @PostMapping(value = "addContentStg")
    public ContentStrategy insertContentStg(@RequestBody ContentStrategy bean) {
        bean.setActive(true);
        contentStrategyService.insertStrategy(bean);
        return bean;
    }

    /**
     * 修改敏感文件自检策略
     * @param bean
     * @return
     */
    @Oplog(name = "route.diskScanSelfCheck")
    @PostMapping(value = "update")
    public DiskScanSelfCheck[] update(@RequestBody DiskScanSelfCheck[] bean){
        diskScanSelfCheckService.updateScan(bean);
        return bean;
    }

    /**
     * 修改文件自检内容检测策略
     * @param bean
     * @return
     */
    @Oplog(value= ContentStgDef.class, name = "route.diskScanSelfCheckSensitive")
    @PostMapping(value = "updateContentStg")
    public ContentStrategy updateContentStg(@RequestBody ContentStrategy bean) {
        bean.setActive(true);
        contentStrategyService.updateStrategy(bean);
        return bean;
    }

    /**
     * 删除敏感文件自检策略
     * @param ids
     */
    @Oplog(name = "route.diskScanSelfCheck")
    @PostMapping(value = "delete")
    public void delete(String ids) {
        diskScanSelfCheckService.deleteById(ids);
    }

    /**
     * 删除文件自检内容检测策略
     * @param ids
     * @return
     */
    @Oplog(value= ContentStgDef.class, name = "route.diskScanSelfCheckSensitive")
    @PostMapping(value = "deleteContentStg")
    public DeleteStateDict deleteContentStgById(String ids) {
        DeleteStateDict state = contentStrategyService.getDelState(ids);
        if(DeleteStateDict.enable.equals(state)){
            contentStrategyService.deleteStrategyById(ids);
        }
        return state;
    }

    /** 获取敏感文件自检策略信息*/
    @PostMapping(value = "getPage")
    public GridPageDTO<DiskScanSelfCheck> getStategyPage(@RequestBody StrategyVO vo) {
        return diskScanSelfCheckService.getStrategyPage(vo);
    }

}
