package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.WebBrowseTimeLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.LogVO;
import com.tipray.dlp.bean.vo.WebBrowseTimeLogVO;
import com.tipray.dlp.service.WebBrowseTimeLogService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 网页浏览时长记录
 * 网页浏览时间Controller
 * @author: chends
 * @date: 2020/6/6
 */
@Oplog(WebBrowseTimeLog.class)
@RestController
@RequestMapping("/log/webBrowseTime")
public class WebBrowseTimeLogController {
    @Resource
    private WebBrowseTimeLogService service;

    /**
     * 查询网页浏览时长记录
     * @param vo
     * @return
     */
    @Oplog(value = WebBrowseTimeLogVO.class, name = "pages.webBrowseTimeLog")
    @PostMapping(value = "getPage")
    public GridPageDTO<WebBrowseTimeLog> getPage(@RequestBody WebBrowseTimeLogVO vo) {
        return service.getPage(vo);
    }

    /**
     * 删除日志
     * @param list
     * @return
     */
    @Oplog(name = "pages.webBrowseTimeLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<WebBrowseTimeLog> list) {
        service.deleteBatch(list);
        return ResponseDTO.success();
    }

    /**
     * 导出网页浏览时长记录
     * @param vo
     * @return
     */
    @Oplog(value = LogVO.class,name = "pages.webBrowseTimeLog")
    @PostMapping(value = "export")
    public ResponseDTO exportExcel(@RequestBody WebBrowseTimeLogVO vo){
        return ResponseDTO.success(service.exportExcel(vo));
    }
}
