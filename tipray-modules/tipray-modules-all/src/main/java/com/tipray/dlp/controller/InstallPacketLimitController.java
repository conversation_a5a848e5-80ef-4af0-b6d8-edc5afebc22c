package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.*;
import com.tipray.dlp.bean.dict.MsgModuleDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.InstallVO;
import com.tipray.dlp.bean.vo.SoftProcessVO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.InstallPacketLimitStrategyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Collection;

/**
 * 软件安装卸载包限制Controller
 */
@RestController
@Oplog(InstallPacketLimitStrategy.class)
@RequestMapping("/installPacket")
public class InstallPacketLimitController {
    @Resource
    private InstallPacketLimitStrategyService service;

    /**
     * 根据id获取限制策略
     */
    @PostMapping(value = "/getById")
    public InstallPacketLimitStrategy getById(Long id) {
        return service.getById(id);
    }

    /**
     * 添加安装限制策略
     * @param beans
     */
    @Oplog(name = "pages.installPacketLimitStrategy")
    @PostMapping(value = "/install/add")
    public void addInstallLimit(@Valid @RequestBody InstallPacketLimitStrategy[] beans) {
        for (InstallPacketLimitStrategy bean : beans) {
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.ACCESSWEB_SITE_LIMIT.getCode());
            bean.setLimitType(1);
            bean.setEnableType(0);
        }
        service.insert(beans);
    }

    /**
     * 修改安装限制策略
     * @param beans
     */
    @Oplog(name = "pages.installPacketLimitStrategy")
    @PostMapping(value = "/install/update")
    public void updateInstallLimit(@Valid @RequestBody InstallPacketLimitStrategy[] beans) {
        for (InstallPacketLimitStrategy bean : beans) {
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.ACCESSWEB_SITE_LIMIT.getCode());
            bean.setLimitType(1);
            bean.setEnableType(0);
        }
        service.update(beans);
    }

    /**
     * 删除安装限制策略
     * @param ids
     */
    @Oplog(name = "pages.installPacketLimitStrategy")
    @PostMapping(value = "/install/delete")
    public void deleteInstallLimit(String ids) {
        service.deleteById(ids);
    }

    /**
     * 导出安装限制策略
     * @param vo
     */
    @Oplog(value = StrategyVO.class, name = "pages.installPacketLimitStrategy")
    @PostMapping(value = "/install/export")
    public void exportInstallLimit(@RequestBody StrategyVO vo, HttpServletResponse response) {
        service.exportInstallUninstallLimitStrategy(1, vo.getStrategyIds(), response);
    }

    /**
     * 得到安装限制策略列表
     */
    @PostMapping(value = "/install/findPage")
    public GridPageDTO<InstallPacketLimitStrategy> getInstallLimit(InstallVO bean) {
        bean.setLimitType(1);
        return service.findInstallPacketLimitStrategyPage(bean);
    }

    /**
     * 得到安装限制策略
     */
    @PostMapping(value = "/install/getByName")
    public Collection<InstallPacketLimitStrategy> getInstallByName(String name) {
        return service.listByName(name, 1);
    }

    /**
     * 添加卸载限制策略
     * @param beans
     */
    @Oplog(name = "pages.uninstallLimitStrategy")
    @PostMapping(value = "/uninstall/add")
    public void add(@Valid @RequestBody InstallPacketLimitStrategy[] beans) {
        for (InstallPacketLimitStrategy bean : beans) {
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.ACCESSWEB_SITE_LIMIT.getCode());
            bean.setLimitType(2);
            bean.setEnableType(0);
        }
        service.insert(beans);
    }

    /**
     * 修改卸载限制策略
     * @param beans
     */
    @Oplog(name = "pages.uninstallLimitStrategy")
    @PostMapping(value = "/uninstall/update")
    public void update(@Valid @RequestBody InstallPacketLimitStrategy[] beans) {
        for (InstallPacketLimitStrategy bean : beans) {
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.ACCESSWEB_SITE_LIMIT.getCode());
            bean.setLimitType(2);
            bean.setEnableType(0);
        }
        service.update(beans);
    }

    /**
     * 删除卸载限制策略
     * @param ids
     */
    @Oplog(name = "pages.uninstallLimitStrategy")
    @PostMapping(value = "/uninstall/delete")
    public void delete(String ids) {
        service.deleteById(ids);
    }

    /**
     * 导出卸载限制策略
     * @param vo
     */
    @Oplog(value = StrategyVO.class, name = "pages.uninstallLimitStrategy")
    @PostMapping(value = "/uninstall/export")
    public void exportUninstallLimit(@RequestBody StrategyVO vo, HttpServletResponse response) {
        service.exportInstallUninstallLimitStrategy(2, vo.getStrategyIds(), response);
    }

    /**
     * 得到卸载限制策略列表
     */
    @PostMapping(value = "/uninstall/findPage")
    public GridPageDTO<InstallPacketLimitStrategy> getUninstallLimit(InstallVO bean) {
        bean.setLimitType(2);
        return service.findInstallPacketLimitStrategyPage(bean);
    }

    /**
     * 得到卸载限制策略
     */
    @PostMapping(value = "/uninstall/getByName")
    public Collection<InstallPacketLimitStrategy> getUninstallByName(String name) {
        return service.listByName(name, 2);
    }

    /**
     * 得到特殊放行目录策略
     */
    @PostMapping(value = "getSpecialPath")
    public GridPageDTO<InstallPacketSpecialPathStrategy> getSpecialPath(StrategyVO vo) {
        return service.getSpecialPath(vo);
    }

    /**
     * 添加特殊放行目录策略
     * @param bean
     * @return
     */
    @Oplog(value = InstallPacketSpecialPathStrategy.class, name = "pages.specialReleasePathStg")
    @PostMapping(value = "saveSpecialPath")
    public ResponseDTO saveSpecialPath(@Valid @RequestBody InstallPacketSpecialPathStrategy bean) {
        try {
            service.saveSpecialPathStrategy(bean);
            return ResponseDTO.success();
        } catch (Exception e) {
            return ResponseDTO.failure(e);
        }
    }

    /**
     * 修改特殊放行目录策略
     * @param bean
     * @return
     */
    @Oplog(value = InstallPacketSpecialPathStrategy.class, name = "pages.specialReleasePathStg")
    @PostMapping(value = "updateSpecialPath")
    public ResponseDTO updateSpecialPath(@Valid @RequestBody InstallPacketSpecialPathStrategy bean) {
        try {
            service.updateSpecialPathStrategy(bean);
            return ResponseDTO.success();
        } catch (Exception e) {
            return ResponseDTO.failure(e);
        }
    }

    /**
     * 删除特殊放行目录策略
     * @param ids
     * @return
     */
    @Oplog(value = InstallPacketSpecialPathStrategy.class, name = "pages.specialReleasePathStg")
    @PostMapping(value = "deleteSpecialPath")
    public ResponseDTO deleteSpecialPath(String ids) {
        try {
            service.deleteSpecialPathStrategy(ids);
            return ResponseDTO.success();
        } catch (Exception e) {
            return ResponseDTO.failure(e);
        }
    }

    /**
     * 导出特殊放行目录策略
     * @param vo
     */
    @Oplog(value = StrategyVO.class, name = "pages.specialReleasePathStg")
    @PostMapping(value = "/exportSpecialPath")
    public void exportSpecialPath(@RequestBody StrategyVO vo, HttpServletResponse response) {
        service.exportSpecialPath(vo.getStrategyIds(), response);
    }

    /**
     * 得到应用程序树
     */
    @PostMapping(value = "getSoftTree")
    public Object findSoftLimitProcessTree(SoftProcessVO vo) {
        return service.findSoftLimitProcessTree(vo);
    }

    /**
     * 得到类型树
     */
    @PostMapping(value = "getTypeTree")
    public Object findTypeTree() {
        return service.getTypeTree();
    }

    /**
     * 查询终端上报程序列表
     */
    @PostMapping(value = "findApprovalSoftList")
    public Object findApprovalSoftList(ProcessInfoApproval bean) {
        return service.findApprovalSoftList(bean);
    }

    /**
     * 删除终端上报程序
     * @param ids
     * @return
     */
    @PostMapping(value = "deleteApprovalSoft")
    public ResponseDTO deleteApprovalSoft(String ids) {
        try {
            service.deleteApprovalSoft(ids);
            return ResponseDTO.success();
        } catch (Exception e) {
            return ResponseDTO.failure(e);
        }
    }

    /**
     * 得到终端上报程序数量
     */
    @PostMapping(value = "getApprovalSoftCount")
    public Long getApprovalSoftCount() {
        return 0L;
    }

}
