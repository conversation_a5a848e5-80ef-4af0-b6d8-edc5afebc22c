package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.UsbApprovalAccessLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.UsbApprovalLogVO;
import com.tipray.dlp.service.UsbApprovalAccessLogService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * USB接入审批记录Controller
 */
@RestController
@RequestMapping("log/usbApprovalAcessLog")
public class UsbApprovalAccessLogController {
    @Resource
    private UsbApprovalAccessLogService usbApprovalAccessLogService;

    /**
     * 分页查询USB接入日志
     * @param vo
     * @return
     */
    @Oplog(value = UsbApprovalLogVO.class,name = "pages.usbApprovalAccessLog")
    @PostMapping(value = "getPage")
    public GridPageDTO<UsbApprovalAccessLog> getPage(@RequestBody UsbApprovalLogVO vo){
        return usbApprovalAccessLogService.getPage(vo);
    }

    /**
     * 导出USB接入审批记录
     * @param vo
     * @return
     */
    @Oplog(value = UsbApprovalLogVO.class,name = "pages.usbApprovalAccessLog")
    @PostMapping(value = "export")
    public ResponseDTO exportExcel(@RequestBody UsbApprovalLogVO vo){
        return ResponseDTO.success(usbApprovalAccessLogService.exportExcel(vo));
    }
    /**
     * 删除日志
     * @param list
     * @return
     */
    @Oplog(value = UsbApprovalAccessLog.class, name = "pages.usbApprovalAccessLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<UsbApprovalAccessLog> list) {
        usbApprovalAccessLogService.deleteBatch(list);
        return ResponseDTO.success();
    }
}
