package com.tipray.dlp.controller;

import com.tipray.dlp.bean.dto.ResponseDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: caitw
 * @description: 消息推送报表模板controller
 **/
@RestController
@RequestMapping("/pushReportTemplate")
public class PushReportTemplateController {

    @PostMapping("reportTemplate")
    public ResponseDTO reportTemplate() {
        return ResponseDTO.success();
    }
}
