package com.tipray.dlp.dao;

import com.tipray.dlp.bean.LocalFileShareRecord;
import com.tipray.dlp.bean.vo.LogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * (LocalFileShareRecord)表数据库访问层
 *
 *
 * <AUTHOR>
 * @since 2021-01-11 14:47:52
 */
@Repository
@ShardingDataSource
public interface LocalFileShareRecordDao extends BaseDao<LocalFileShareRecord>{


    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(LogVO vo);
     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<LocalFileShareRecord> listByVO(LogVO vo);
}
