package com.tipray.dlp.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.SoftwareLimitStrategy;
import com.tipray.dlp.bean.dict.MsgModuleDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.exception.ParamException;
import com.tipray.dlp.service.SoftwareLimitStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * 应用软件限制Controller
 */
@Oplog(SoftwareLimitStrategy.class)
@RestController
@RequestMapping("/softwareLimit")
public class SoftwareLimitStrategyController {
    
    @Resource
    private SoftwareLimitStrategyService softwareLimitStrategyService;

    /**
     * 新增软件限制策略
     * @param beans
     * @return
     * @throws JsonProcessingException
     */
    @Oplog(name = "pages.softwareLimit")
    @PostMapping(value = "add")
    public SoftwareLimitStrategy[] add(@RequestBody SoftwareLimitStrategy[] beans) throws JsonProcessingException {
        for (SoftwareLimitStrategy bean : beans){
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.SOFT_WARE_LIMIT.getCode());
        }
        softwareLimitStrategyService.insert(beans);
        return beans;
    }

    /**
     * 修改软件限制策略
     * @param beans
     * @return
     */
    @Oplog(name = "pages.softwareLimit")
    @PostMapping(value = "update")
    public SoftwareLimitStrategy[] update(@RequestBody SoftwareLimitStrategy[] beans) {
        for (SoftwareLimitStrategy bean : beans){
            if(bean.getId() == null){
                throw new ParamException("参数错误");
            }
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.SOFT_WARE_LIMIT.getCode());
        }

        softwareLimitStrategyService.update(beans);
        return beans;
    }

    /**
     * 删除软件限制策略
     * @param ids
     */
    @Oplog(name = "pages.softwareLimit")
    @PostMapping(value = "delete")
    public void deleteAppBlock(String ids) {
        softwareLimitStrategyService.deleteById(ids);
    }

    @GetMapping("getById/{id}")
    public SoftwareLimitStrategy getById(@PathVariable("id") Long id) throws IOException {
        return softwareLimitStrategyService.getStrategyById(id);
    }

    @PostMapping("getByName")
    public List<SoftwareLimitStrategy> getByName(String name) {
        return softwareLimitStrategyService.listStrategyByName(name);
    }

    /** 软件限制策略分页列表*/
    @PostMapping(value = "getPage")
    public GridPageDTO<SoftwareLimitStrategy> getStategyPage(@RequestBody StrategyVO vo) throws IOException {
        return softwareLimitStrategyService.getStategyPage(vo);
    }
}
