package com.tipray.dlp.controller;

import cn.hutool.core.map.MapUtil;
import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.*;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.ViolationResponseRuleVO;
import com.tipray.dlp.service.*;
import com.tipray.dlp.util.I18nUtils;
import com.tipray.dlp.sync.bean.SyncSource;
import com.tipray.dlp.sync.service.SyncSourceService;
import com.tipray.dlp.util.StringUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 违规响应规则Controller
 */
@Oplog(ViolationResponseRule.class)
@RestController
@RequestMapping("/violationResponseRule")
public class ViolationResponseRuleController {
    @Resource
    private ViolationResponseRuleService violationResponseRuleService;
    @Resource
    private GlobalConfigService globalConfigService;
    @Resource
    private SyncSourceService syncSourceService;
    /**
     * 添加违规响应规则
     * @param bean
     * @return
     */
    @Oplog(value = ViolationResponseRule.class,name = "table.violationName")
    @PostMapping(value = "add")
    public ResponseDTO add(@Valid @RequestBody ViolationResponseRule bean) {
        //违规响应规则名同名验证
        if(violationResponseRuleService.countByName(bean.getName(),null) > 0){
            return ResponseDTO.failure(new Throwable(),"该违规响应规则名称已存在");
        }
        violationResponseRuleService.add(bean);
        return ResponseDTO.success();
    }

    /**
     * 删除违规响应规则
     * @param vo
     * @return
     */
    @Oplog(value = ViolationResponseRuleVO.class,name = "table.violationName")
    @PostMapping(value = "delete")
    public ResponseDTO batchDelete(@RequestBody ViolationResponseRuleVO vo) {
        if (null != vo.getUpdateQuery() && vo.getUpdateQuery()) {
            List<Long> ids = violationResponseRuleService.getPage(vo).getItems().stream().filter(t -> !vo.getBackupUnSelectedIds().contains(t.getId())).map(ViolationResponseRule::getId).collect(Collectors.toList());
            vo.setIds(StringUtil.toInSql(ids));
        }
        return violationResponseRuleService.delete(vo);
    }

    /**
     * 修改违规响应规则
     * @param bean
     * @return
     */
    @Oplog(value = ViolationResponseRule.class,name = "table.violationName")
    @PostMapping(value = "update")
    public ResponseDTO update(@Valid @RequestBody ViolationResponseRule bean) {
        //违规响应规则名同名验证
        violationResponseRuleService.update(bean);
        return ResponseDTO.success();
    }

    /** 违规响应规则分页列表*/
    @PostMapping(value = "findPage")
    public GridPageDTO<ViolationResponseRule> findPage(@RequestBody ViolationResponseRuleVO vo) {
        GridPageDTO<ViolationResponseRule> ruleGridPageDTO = violationResponseRuleService.getPage(vo);
        violationResponseRuleService.formatExtConfig(ruleGridPageDTO.getItems());
        violationResponseRuleService.formatSysUserName(ruleGridPageDTO.getItems());
        return ruleGridPageDTO;
    }


    /** 获取所有违规响应规则*/
    @PostMapping(value = "getAllRules")
    public List<ViolationResponseRule> getAllRules() {
        List<ViolationResponseRule> rules = violationResponseRuleService.allRule();
        // 获取违规响应规则关联的配置信息
        violationResponseRuleService.formatExtConfig(rules);
        return rules;
    }

    @GetMapping("getAllDingTalkApp")
    public List<Map<String, Object>> getDingTalkListForOptions() {
        List<SyncSource> list = syncSourceService.listSourceBySourceType(5);
        return list.stream()
                .map(s -> MapUtil.<String, Object>builder()
                        .put("id", s.getSyncId())
                        .put("name", s.getName()).build())
                .collect(Collectors.toList());
    }

    /** 得到违规响应规则*/
    @PostMapping(value = "get")
    public ViolationResponseRule get(Long id) {
        return violationResponseRuleService.getRule(id);
    }

    /** 判断该响应规则是否应用于离线锁屏*/
    @PostMapping(value = "validRuleId")
    public List<Long> validRuleId(Long id) {
        return violationResponseRuleService.validRuleId(id);
    }

    /**
     * 查询违规响应规则名称是否已存在
     * @param name
     * @return
     */
    @PostMapping("getByName")
    public ViolationResponseRule getByName(String name) {
        return violationResponseRuleService.getByName(name);
    }

    /**
     * 系统管理 -> 策略基础数据 -> 违规响应规则库-> 高级配置
     */
    @Oplog(value = GlobalConfig.class, name = "table.highConfig")
    @PostMapping("/updateConfig")
    public ResponseDTO updateGlobalConfig(@RequestBody List<GlobalConfig> globalConfigList) {
        for(GlobalConfig config : globalConfigList){
            if (StringUtil.isEmpty(config.getValue())){
                return ResponseDTO.failure(new Throwable(), I18nUtils.get("pages.regCode1", "参数错误"));
            }
        }
        globalConfigService.batchUpdate(globalConfigList);
        return ResponseDTO.success();
    }

}
