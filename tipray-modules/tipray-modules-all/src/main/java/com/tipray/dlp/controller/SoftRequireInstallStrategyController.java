package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.SoftRequireInstallStrategy;
import com.tipray.dlp.bean.dict.MsgModuleDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.exception.ParamException;
import com.tipray.dlp.service.SoftRequireInstallStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 必须安装软件设置Controller
 */
@RestController
@Oplog(SoftRequireInstallStrategy.class)
@RequestMapping("/softRequireInstallStrategy")
@Slf4j
public class SoftRequireInstallStrategyController {
    @Resource
    private SoftRequireInstallStrategyService softRequireInstallStrategyService;

    /**
     * 新增软件限制策略
     * @param beans
     * @return
     */
    @Oplog(name = "pages.softwareLimitStrategy")
    @PostMapping(value = "add")
    public SoftRequireInstallStrategy[] add(@RequestBody SoftRequireInstallStrategy[] beans) {
        for (SoftRequireInstallStrategy bean : beans){
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.SOFT_REQUIRED_INSTALL_LIMIT.getCode());
        }
        softRequireInstallStrategyService.insert(beans);
        return beans;
    }

    /**
     * 修改软件限制策略
     * @param beans
     * @return
     */
    @Oplog(name = "pages.softwareLimitStrategy")
    @PostMapping(value = "update")
    public SoftRequireInstallStrategy[] update(@RequestBody SoftRequireInstallStrategy[] beans) {
        for (SoftRequireInstallStrategy bean : beans){
            if(bean.getId() == null){
                throw new ParamException("参数错误");
            }
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.SOFT_REQUIRED_INSTALL_LIMIT.getCode());
        }

        softRequireInstallStrategyService.update(beans);
        return beans;
    }

    /**
     * 删除软件限制策略
     * @param ids
     */
    @Oplog(name = "pages.softwareLimitStrategy")
    @PostMapping(value = "delete")
    public void deleteAppBlock(String ids) {
        softRequireInstallStrategyService.deleteById(ids);
    }

    @GetMapping("getById/{id}")
    public SoftRequireInstallStrategy getById(@PathVariable("id") Long id) {
        return softRequireInstallStrategyService.getStrategyById(id);
    }

    @PostMapping("getByName")
    public List<SoftRequireInstallStrategy> getByName(String name) {
        return softRequireInstallStrategyService.listStrategyByName(name);
    }

    /**软件限制策略分页列表*/
    @PostMapping(value = "getPage")
    public GridPageDTO<SoftRequireInstallStrategy> getStategyPage(@RequestBody StrategyVO vo) {
        return softRequireInstallStrategyService.getStategyPage(vo);
    }
}
