package com.tipray.dlp.dao;

import com.tipray.dlp.bean.SensitiveDetectScreenRcdLog;
import com.tipray.dlp.bean.SensitiveOpLog;
import com.tipray.dlp.bean.vo.SensitiveDetectScreenRcdLogVO;
import com.tipray.dlp.bean.vo.SensitiveOpLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @version 1.0
 * @date 2022/1/10 15:53
 */
@Repository
@ShardingDataSource
public interface SensitiveDetectScreenRcdLogDao extends BaseDao<SensitiveDetectScreenRcdLog>{
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(SensitiveDetectScreenRcdLogVO vo);
    /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<SensitiveDetectScreenRcdLog> listByVO(SensitiveDetectScreenRcdLogVO vo);
}
