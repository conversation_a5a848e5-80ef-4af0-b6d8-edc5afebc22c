package com.tipray.dlp.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tipray.dlp.bean.SoftwareAuthorizedData;
import com.tipray.dlp.bean.vo.SoftWareVO;
import com.tipray.dlp.mybatis.annotation.WhereIn;
import com.tipray.dlp.mybatis.bean.PageVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SoftwareAuthorizedDataDao extends BaseMapper<SoftwareAuthorizedData> {
    @WhereIn(@WhereIn.Param(value = "softwareNames", numerical = false))
    List<Long> listTerminalIds(String softwareNames);

    @Override
    int insert(SoftwareAuthorizedData bean);

    void delete(SoftwareAuthorizedData bean);

    Integer countByTerminalId(Long terminalId);

    List<String> listSoftwareName(Long terminalId);

    Integer countBySoftwareName(String softwareName);

    void batchInsert(SoftwareAuthorizedData bean);

    /**
     * 删除卸载终端的软件授权信息
     * @param terminalIds 已卸载终端ID
     */
    @WhereIn(@WhereIn.Param("terminalIds"))
    void deleteUninstalled(String terminalIds);

    @WhereIn({@WhereIn.Param("terminalIds"), @WhereIn.Param(value = "softwareNames", numerical = false)})
    PageVO<SoftwareAuthorizedData> getPageByVO(SoftWareVO vo);

    @WhereIn(@WhereIn.Param("terminalIds"))
    List<SoftwareAuthorizedData> selectGroupByTerm(SoftWareVO vo);

    @WhereIn(@WhereIn.Param("terminalIds"))
    void deleteByTermIds(String terminalIds);
}
