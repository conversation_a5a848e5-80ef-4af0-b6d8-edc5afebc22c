package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.Department;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.TerminalGrpNickNameDTO;
import com.tipray.dlp.bean.vo.TerminalGrpNickAcquisitionVO;
import com.tipray.dlp.controller.socket.AccessSocketMapper;
import com.tipray.dlp.service.TerminalGroupNickNameService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 终端分组昵称收集Controller
 */
@RestController
@Oplog(Department.class)
@RequestMapping("/terminalGroupNickName")
public class TerminalGroupNickNameController {

    @Resource
    private TerminalGroupNickNameService terminalGroupNickNameService;
    @Resource
    private AccessSocketMapper accessSocketMapper;

    /**
     * 1、根据状态查询终端分组和昵称列表信息
     * @param vo
     * @return
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<TerminalGrpNickNameDTO> getPage(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        return terminalGroupNickNameService.getTerminalGroupNickNamePage(vo);
    }

    /**
     * 3、根据分组id或终端id查出终端树下所有终端（将终端添加到终端分组昵称采集表）
     * @param vo
     * @return
     */
    @PostMapping(value = "add")
    public void add(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        terminalGroupNickNameService.add(vo);
    }
    /**
     * 删除终端
     * @param vo
     * @return
     */
    @PostMapping(value = "delete")
    public void delete(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        terminalGroupNickNameService.deleteById(vo);
    }
    /**
     * 清空终端
     * @return
     */
    @PostMapping(value = "deleteAll")
    public void deleteAll() {
        terminalGroupNickNameService.deleteAll();
    }
    /**
     * 是否存在离线
     * @return
     */
    @PostMapping(value = "isExistOffline")
    public Boolean isExistOffline(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        return terminalGroupNickNameService.isExistOffline(vo);
    }
    /**
     * 开始收集终端
     * @return
     */
    @PostMapping(value = "startCollectTerminal")
    public void startCollectTerminal(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        TerminalGrpNickAcquisitionVO voo = terminalGroupNickNameService.filterOnlineTerminal(vo);
        accessSocketMapper.collectTermInfo(voo.getTermList());
        voo.setStatus(2);//已发出协议采集中
        terminalGroupNickNameService.updateGroupNickNameStatus(voo);
    }
    /**
     * 重新收集终端
     * @return
     */
    @PostMapping(value = "restartCollectTerminal")
    public void restartCollectTerminal(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        TerminalGrpNickAcquisitionVO voo = terminalGroupNickNameService.filterOnlineTerminal(vo);
        accessSocketMapper.collectTermInfo(voo.getTermList());
        voo.setStatus(2);//已发出协议采集中
        terminalGroupNickNameService.updateGroupNickNameStatus(voo);
    }

    /**
     * 修改分组和昵称
     * @return
     */
    @PostMapping(value = "updateGroupNickName")
    public void updateGroupNickName(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        terminalGroupNickNameService.updateGroupNickName(vo);
    }

    /**
     * 同步分组和昵称
     * @return
     */
    @PostMapping(value = "synGroupNickName")
    public Map<String,Object> synGroupNickName(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        return terminalGroupNickNameService.synGroupNickName(vo);
    }

}
