package com.tipray.dlp.dao;

import com.tipray.dlp.mybatis.annotation.WhereIn;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/9
 */
@Repository
public interface ADBStgRelatedSuffixDao {
    void batchInsert(Long stgId, List<Long> suffixIds);

    @WhereIn(@WhereIn.Param("suffixIds"))
    List<Long> listStgIdBySuffixIds(String suffixIds);

    @WhereIn(@WhereIn.Param("stgIds"))
    void deleteByStgIds(String stgIds);

    @WhereIn(@WhereIn.Param("suffixIds"))
    void deleteBySuffixIds(String suffixIds);
}
