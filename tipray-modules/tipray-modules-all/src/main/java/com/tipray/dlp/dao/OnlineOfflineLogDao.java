package com.tipray.dlp.dao;

import com.tipray.dlp.bean.OnlineOfflineLog;
import com.tipray.dlp.bean.vo.OnlineOfflineLogVo;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource
public interface OnlineOfflineLogDao extends BaseDao<OnlineOfflineLog> {
    Long countByVO(OnlineOfflineLogVo vo);

    List<OnlineOfflineLog> listByVO(OnlineOfflineLogVo vo);
}
