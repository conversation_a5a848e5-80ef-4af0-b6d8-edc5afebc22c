package com.tipray.dlp.controller;


import cn.hutool.core.collection.CollUtil;
import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.*;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.bean.vo.WaterMarkVO;
import com.tipray.dlp.service.OutgoingService;
import com.tipray.dlp.service.WaterMarkLibService;
import com.tipray.dlp.service.impl.OutgoingScreenWaterMarkServiceImpl;
import com.tipray.dlp.util.CollectionUtil;
import com.tipray.dlp.util.StringUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 外发屏幕水印Controller
 */
@Oplog(OutgoingScreenWaterMarkList.class)
@RestController
@RequestMapping("/outgoing/screenWaterMark")
public class OutgoingScreenWaterMarkController {
    @Resource(type= OutgoingScreenWaterMarkServiceImpl.class)
    private OutgoingService outgoingService;
    @Resource
    private WaterMarkLibService waterMarkLibService;

    /**
     * 新增外发屏幕水印策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.outgoingScreenWaterMarkList")
    @PostMapping(value = "add")
    public OutgoingScreenWaterMarkList add(@RequestBody OutgoingScreenWaterMarkList bean) {
        formatMark(bean);
        outgoingService.insert(bean);
        return bean;
    }

    /**
     * 获取数据库最新的模板设置回去，防止某些操作导致水印模板数据变更了，但是传过来的数据还是旧的
     * @param bean
     */
    private void formatMark(OutgoingScreenWaterMarkList bean) {
        if (CollUtil.isNotEmpty(bean.getList())) {
            List<Long> ids = CollectionUtil.toIdList(bean.getList());
            WaterMarkVO vo = new WaterMarkVO();
            vo.setSearchCount(false);
            vo.setIds(StringUtil.join(ids, ","));
            Collection<WaterMarkLib> list = waterMarkLibService.getWaterMarkPage(vo).getItems();
            List<ScreenWaterMark> newList = new ArrayList<>();
            for (WaterMarkLib waterMarkLib : list) {
                ScreenWaterMark mark = waterMarkLib.caseTo(ScreenWaterMark.class);
                mark.setId(waterMarkLib.getId());
                newList.add(mark);
            }
            bean.setList(newList);
        }
    }

    /**
     * 修改外发屏幕水印策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.outgoingScreenWaterMarkList")
    @PostMapping(value = "update")
    public OutgoingScreenWaterMarkList update(@RequestBody OutgoingScreenWaterMarkList bean) {
        formatMark(bean);
        outgoingService.update(bean);
        return bean;
    }

    /**
     * 删除外发屏幕水印策略
     * @param ids
     */
    @Oplog(name = "pages.outgoingScreenWaterMarkList")
    @PostMapping(value = "delete")
    public void delete(String ids) {
        outgoingService.deleteById(ids);
    }

    @PostMapping("getByName")
    public OutgoingScreenWaterMarkList getByName(String name) {
        return (OutgoingScreenWaterMarkList) outgoingService.getByName(name, OutgoingScreenWaterMarkList.class);
    }

    /** 策略分页列表*/
    @PostMapping(value = "getPage")
    public GridPageDTO<OutgoingScreenWaterMarkList> getPage(@RequestBody StrategyVO vo) {
        GridPageDTO page = outgoingService.getPage(vo, OutgoingScreenWaterMarkList.class);
        return page;
    }
}
