package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.UsbDevice;
import com.tipray.dlp.bean.UsbGroup;
import com.tipray.dlp.bean.dict.OperationTypeDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ImportResultDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.dto.TreeNodeDTO;
import com.tipray.dlp.bean.vo.GroupVO;
import com.tipray.dlp.bean.vo.UsbDevicePageVO;
import com.tipray.dlp.service.UsbConfigStrategyService;
import com.tipray.dlp.service.UsbDeviceService;
import com.tipray.dlp.util.FileUtil;
import com.tipray.dlp.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * usb存储设备认证限制Controller
 */
@RestController
@Oplog(UsbDevice.class)
@RequestMapping("/usbConfig")
public class UsbConfigStrategyController {
    @Autowired
    private UsbConfigStrategyService service;
    @Autowired
    private UsbDeviceService usbDeviceService;

    @GetMapping(value = "countByGroupId/{groupId}")
    public ResponseDTO countByGroupId(@PathVariable("groupId") Long groupId) {
        return ResponseDTO.success(service.countChildByGroupId(groupId));
    }

    @PostMapping(value = "getGroupByName")
    public UsbGroup getGroupByName(@RequestBody UsbGroup bean) {
        return usbDeviceService.getGroupByName(bean);
    }

    /**
     * 添加USB设备分组
     * @param bean
     * @return
     */
    @Oplog(value = UsbGroup.class,name = "pages.usbGroup", type = OperationTypeDict.ADD)
    @PostMapping(value = "addUsbGroup")
    public UsbGroup addUsbGroup(@Valid @RequestBody UsbGroup bean) {
        usbDeviceService.insertGroup(bean);
        return bean;
    }

    /**
     * 修改USB设备分组
     * @param bean
     * @return
     */
    @Oplog(value = UsbGroup.class,name = "pages.usbGroup", type = OperationTypeDict.UPDATE)
    @PostMapping(value = "updateUsbGroup")
    public UsbGroup updateUsbGroup(@Valid @RequestBody UsbGroup bean) {
        usbDeviceService.updateGroup(bean);
        return bean;
    }

    /**
     * 删除USB设备分组
     * @param bean
     */
    @Oplog(value = UsbGroup.class,name = "pages.usbGroup", type = OperationTypeDict.DELETE)
    @PostMapping(value = "deleteUsbGroup")
    public void deleteUsbGroup(UsbGroup bean) {
        String groupName = usbDeviceService.getGroupNameById(bean.getId());
        // 用于记录删除分组的管理员日志
        bean.setName(groupName);
        usbDeviceService.deleteGroupById(bean.getId());
    }

    /** 得到软件类别树形*/
    @PostMapping(value = "findUsbGroupTree")
    public List<TreeNodeDTO> findUsbGroupTree() {
        return usbDeviceService.listGroupTreeNode(new UsbGroup());
    }

    /** 得到所有USB设备类型*/
    @PostMapping(value = "findAllUsbGroup")
    public List<UsbGroup> listAllGroup() {
        return usbDeviceService.listGroup(null);
    }

    /**
     * 添加usb设备
     * @param bean
     * @return
     * @throws Exception
     */
    @Oplog(value = UsbDevice.class,name = "pages.usbDevice_text8", type = OperationTypeDict.ADD)
    @PostMapping(value = "addUsbDevice")
    public ResponseDTO addUsbDevice(@Valid @RequestBody UsbDevice bean) throws Exception {
        service.insertUsbDevice(bean);
        return ResponseDTO.success();
    }


    /**
     * 批量添加usb设备
     * @param list
     * @return
     * @throws Exception
     */
    @Oplog(value = UsbDevice.class,name = "pages.usbDevice_text8", type = OperationTypeDict.ADD)
    @PostMapping(value = "batchAddUsbDevice")
    public ResponseDTO batchAddUsbDevice(@Valid @RequestBody List<UsbDevice> list) throws Exception {
        service.batchInsertUsbDevice(list);
        return ResponseDTO.success();
    }


    /**
     * 修改usb设备
     * @param bean
     * @return
     */
    @Oplog(value = UsbDevice.class,name = "pages.usbDevice_text8", type = OperationTypeDict.UPDATE)
    @PostMapping(value = "updateUsbDevice")
    public ResponseDTO updateUsbDevice(@Valid @RequestBody UsbDevice bean) {
        try {
            service.updateUsbDevice(bean);
            return ResponseDTO.success();
        }catch (Exception e) {
            e.printStackTrace();
            return ResponseDTO.failure(e);
        }
    }

    /**
     * 移动分组
     * @param vo
     */
    @Oplog(value = UsbDevice.class,name = "button.moveGroup", type = OperationTypeDict.UPDATE)
    @PostMapping(value = "moveGroup")
    public void moveGroup(@RequestBody GroupVO vo) {
        usbDeviceService.moveGroup(vo.getParentId(),vo.getIds());
    }

    /**
     * 删除usb设备
     * @param vo
     * @return
     */
    @Oplog(value = UsbDevicePageVO.class,name = "pages.usbDevice_text8", type = OperationTypeDict.DELETE)
    @PostMapping(value = "deleteUsbDevice")
    public ResponseDTO batchDeleteUsbDevice(@RequestBody UsbDevicePageVO vo) {
        try {
            if (null != vo.getUpdateQuery() && vo.getUpdateQuery()) {
                List<Long> ids = usbDeviceService.getDevicePage(vo).getItems().stream().filter(t -> !vo.getBackupUnSelectedIds().contains(t.getId())).map(UsbDevice::getId).collect(Collectors.toList());
                vo.setIds(StringUtil.toInSql(ids));
            }

            // 过滤被策略使用的usb设备
            Boolean filterUsed = vo.getFilterUsed();
            if (filterUsed != null && filterUsed) {
                Set<Long> usedServers = usbDeviceService.findStrategyUsedUsbSet();
                if(usedServers != null){
                    String ids = vo.getIds();
                    if(StringUtils.isNotEmpty(ids)){
                        List<Long> resList = StringUtil.toLongList(ids).stream().filter(id -> !usedServers.contains(id)).collect(Collectors.toList());
                        vo.setIds(StringUtils.join(resList, ","));
                    }
                }
            }
            if (StringUtil.isNotEmpty(vo.getIds())) {
                List<String> names = usbDeviceService.listNameByIds(vo.getIds());
                vo.setNames(names);
                service.deleteUsbDevice(vo.getIds());
            }
            return ResponseDTO.success(vo);
        }catch (Exception e) {
            e.printStackTrace();
            return ResponseDTO.failure(e);
        }
    }

    /** 得到usb存储设备库分页数据*/
    @PostMapping(value = "listUsbDevice")
    public GridPageDTO<UsbDevice> findUsbDeviceList(@RequestBody UsbDevicePageVO vo) {
        return usbDeviceService.getDevicePage(vo);
    }

    @PostMapping(value = "listUsbDeviceByGroupIds")
    public List<UsbDevice> listUsbDeviceByGroupIds(@RequestBody UsbDevice usbDevice) {
        return usbDeviceService.listUsbDeviceByGroupIds(usbDevice);
    }

    /** 得到usb存储设备数据*/
    @GetMapping(value = "listAllUsbDevice")
    public List<UsbDevice> listAllUsbDevice() {
        return usbDeviceService.listAllDevice();
    }

    /** 得到usb存储设备库树形*/
    @PostMapping(value = "listUsbDeviceTree")
    public List<TreeNodeDTO> listUsbDeviceTree() {
        return usbDeviceService.listDeviceTree();
    }


    /** 得到指定id的usb存储设备库*/
    @PostMapping(value = "getUsbDeviceById")
    public UsbDevice getUsbDeviceById(Long id) {
        return usbDeviceService.getDeviceById(id);
    }

    /** 得到指定usCode的usb存储设备库集*/
    @PostMapping(value = "listDeviceByCode")
    public List<UsbDevice> listDeviceByCode(@RequestBody List<String> usbCodes) {
        return usbDeviceService.listDeviceByCode(usbCodes);
    }

    /** 得到指定Id的usb存储设备库集*/
    @PostMapping(value = "listDeviceById")
    public List<UsbDevice> listDeviceById(@RequestBody List<Long> ids) {
        return usbDeviceService.listDeviceById(ids);
    }

    /**
     * 删除usb设备分组
     * @param vo
     */
    @Oplog(value = UsbGroup.class,name = "pages.usbGroup", type = OperationTypeDict.DELETE)
    @PostMapping(value = "deleteGroupAndData")
    public void deleteGroupAndData(@RequestBody GroupVO vo) {
        String groupName = usbDeviceService.getGroupNameById(vo.getGroupId());
        // 用于记录删除分组的管理员日志
        vo.setGroupName(groupName);
        usbDeviceService.deleteGroupAndDevice(vo);
    }
    /**
     * 删除usb设备分组
     */
    @Oplog(value = GroupVO.class,name = "pages.usbGroup", type = OperationTypeDict.DELETE)
    @PostMapping(value = "moveGroupToOther")
    public void deleteGroupAndMove(@RequestBody GroupVO vo) {
        String groupName = usbDeviceService.getGroupNameById(vo.getGroupId());
        // 用于记录删除分组的管理员日志
        vo.setGroupName(groupName);
        usbDeviceService.updateGroupToOther(vo);
        usbDeviceService.deleteGroupById(vo.getGroupId());
    }

    /**
     * 导入USB设备信息库
     * @param file
     * @param importWay 1：重复覆盖， 0：不导入
     * @param taskId
     * @return
     * @throws IOException
     */
    @Oplog(value = ImportResultDTO.class, name = "route.usbLibrary", type = OperationTypeDict.IMPORT)
    @PostMapping(value="import")
    public ResponseDTO importData(@RequestParam(value = "file") MultipartFile file,
                              @RequestParam(value = "fileType") String fileType,
                              @RequestParam(value = "importWay") Integer importWay,
                              @RequestParam(value = "taskId") Long taskId) throws IOException {
        try {
            File destFile = FileUtil.transferImportFile(file);
            ImportResultDTO result = usbDeviceService.importUsbDevice(taskId, destFile, fileType, importWay);
            return ResponseDTO.success(result);
        } catch (Exception e) {
            return ResponseDTO.failure(e);
        }
    }

    /**
     * 导出USB设备信息库
     * @param bean
     * @param response
     * @return
     */
    @Oplog(value = UsbDevicePageVO.class, name = "route.usbLibrary", type = OperationTypeDict.EXPORT)
    @PostMapping(value = "export")
    public ResponseDTO exportExcelBySelectId(@RequestBody UsbDevicePageVO bean, HttpServletResponse response){
        return ResponseDTO.success(usbDeviceService.exportExcel(bean,response));
    }

}
