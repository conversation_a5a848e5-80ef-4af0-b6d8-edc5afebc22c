package com.tipray.dlp.dao;

import com.tipray.dlp.bean.SensitiveFileOutsendLog;
import com.tipray.dlp.bean.vo.SensitiveFileOutsendLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import com.tipray.dlp.mybatis.annotation.WhereIn;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 敏感文件外发审计日志(SensitiveFileOutsendLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-09-02 10:01:11
 */
@Repository
@ShardingDataSource
public interface SensitiveFileOutsendLogDao extends BaseDao<SensitiveFileOutsendLog> {
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    @WhereIn(@WhereIn.Param(value = "processIds", numerical = false))
    Long countByVO(SensitiveFileOutsendLogVO vo);

    /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    @WhereIn(@WhereIn.Param(value = "processIds", numerical = false))
    List<SensitiveFileOutsendLog> listByVO(SensitiveFileOutsendLogVO vo);
}
