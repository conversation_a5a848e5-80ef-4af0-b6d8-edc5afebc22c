package com.tipray.dlp.dao;

import com.tipray.dlp.bean.DiskScanSelfCheckSensLog;
import com.tipray.dlp.bean.vo.DiskScanLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 */
@Repository
@ShardingDataSource
public interface DiskScanSelfCheckSensLogDao extends BaseDao<DiskScanSelfCheckSensLog>{

    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(DiskScanLogVO vo);
     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<DiskScanSelfCheckSensLog> listByVO(DiskScanLogVO vo);
}
