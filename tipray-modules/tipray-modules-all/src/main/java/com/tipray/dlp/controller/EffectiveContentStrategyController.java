package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.ChatFileLog;
import com.tipray.dlp.bean.EffectiveContentConfig;
import com.tipray.dlp.bean.EffectiveContentStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.EffectiveContentStgVO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.EffectiveContentConfigService;
import com.tipray.dlp.service.EffectiveContentStrategyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 敏感内容检测配置Controller
 *
 * <AUTHOR>
 * @since 2019-11-20 14:19:28
 */
@Oplog(ChatFileLog.class)
@RestController
@RequestMapping("/effectiveContentStrategy")
public class EffectiveContentStrategyController {

    @Autowired
    private EffectiveContentStrategyService service;
    @Autowired
    private EffectiveContentConfigService configService;

    /**
     * 新增敏感内容检测配置
     * @param bean
     * @return
     */
    @Oplog(value = EffectiveContentStgVO.class,name = "route.effectiveContentStrategy")
    @PostMapping(value = "add")
    public EffectiveContentStgVO add(@RequestBody EffectiveContentStgVO bean) {
        service.insertStrategy(bean);
        return bean;
    }

    /**
     * 修改敏感内容检测配置
     * @param bean
     * @return
     */
    @Oplog(value = EffectiveContentStgVO.class,name = "route.effectiveContentStrategy")
    @PostMapping(value = "update")
    public EffectiveContentStgVO update(@RequestBody EffectiveContentStgVO bean) {
        service.updateStrategy(bean);
        return bean;
    }

    /**
     * 删除敏感内容检测配置
     * @param ids
     */
    @Oplog(value = EffectiveContentStgVO.class,name = "route.effectiveContentStrategy")
    @PostMapping(value = "delete")
    public void deleteById(String ids) {
        service.deleteStrategyById(ids);
    }

    @PostMapping(value = "getByName")
    public EffectiveContentStrategy getByName(String name) {
        return service.getStrategyByName(name);
    }

    /**分页查询*/
    @PostMapping(value = "getPage")
    public GridPageDTO<EffectiveContentStrategy> getPage(@RequestBody StrategyVO vo) {
        GridPageDTO<EffectiveContentStrategy> strategyPage = service.getStrategyPage(vo);
        return strategyPage;
    }

    @GetMapping("getEffectiveConfigBySuperior/{superiorId}")
    public EffectiveContentConfig getEffectiveConfigBySuperior(@PathVariable("superiorId") Long superiorId) {
        return superiorId==null ? null : configService.getByName(superiorId.toString());
    }
}
