package com.tipray.dlp.dao;

import com.tipray.dlp.bean.EncOrDecLogDetail;
import com.tipray.dlp.bean.vo.EncOrDecLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * (EncOrDecLogDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-05-06 19:19:36
 */
@Repository
@ShardingDataSource
public interface EncOrDecLogDetailDao extends BaseDao<EncOrDecLogDetail>{

    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(EncOrDecLogVO vo);
     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<EncOrDecLogDetail> listByVO(EncOrDecLogVO vo);
}