package com.tipray.dlp.dao;

import com.tipray.dlp.bean.MailWhiteListReceiver;
import com.tipray.dlp.bean.vo.MailWhiteListReceiverVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MailWhiteListReceiverDao extends BaseDao<MailWhiteListReceiver> {
    MailWhiteListReceiver getById(Long id);

    MailWhiteListReceiver getByMailAddress(String mailAddress);

    Long countByVO(MailWhiteListReceiverVO vo);

    List<MailWhiteListReceiver> listByVO(MailWhiteListReceiverVO vo);

    List<MailWhiteListReceiver> list();
}
