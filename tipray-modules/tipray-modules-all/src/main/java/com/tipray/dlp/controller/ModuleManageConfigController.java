package com.tipray.dlp.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.SysOptions;
import com.tipray.dlp.bean.dict.OperationTypeDict;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.ModuleManageConfigVO;
import com.tipray.dlp.service.SysOptionsService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 模块管理配置Controller
 * <AUTHOR>
 * @date 2022/12/14
 */
@RestController
@RequestMapping("/moduleManageConfig")
public class ModuleManageConfigController {

    @Resource
    private SysOptionsService sysOptionsService;

    /** 获取非自动分配模块*/
    @GetMapping(value = "listNonAutoModule")
    public List<Long> listNonAutoModule() {
        SysOptions sysOptions = sysOptionsService.getSysOptionsByOpt(2004L);
        if (sysOptions != null) {
            //将json字符串转换成json对象
            JSONObject jsonObj = JSON.parseObject(sysOptions.getOptStrVal());
            List<Long> lsit = jsonObj == null ? null : (List<Long>) jsonObj.get("nonAutoModuleIds");
            return lsit;
        }
        return null;
    }

    /**
     * 保存高级配置
     */
    @Oplog(value = ModuleManageConfigVO.class, type = OperationTypeDict.UPDATE, name = "pages.advancedConfiguration", defaultArg = true)
    @PostMapping(value = "saveNonAutoModule")
    public ResponseDTO saveNonAutoModule(@RequestBody ModuleManageConfigVO bean) {
        List<SysOptions> list = new ArrayList<>();
        SysOptions sysOptions = sysOptionsService.getSysOptionsByOpt(2004L);
        String optStrVal = null;
        JSONObject jsonObj = new JSONObject();
        List<Long> moduleIds = bean.getModuleIds();
        if (moduleIds != null && moduleIds.size() > 0) {
            jsonObj.put("nonAutoModuleIds", moduleIds);
            optStrVal = jsonObj.toString();
        } else {
            jsonObj.put("nonAutoModuleIds", new Long[0]);
            optStrVal = jsonObj.toString();
        }
        if (sysOptions == null) {
            sysOptions = new SysOptions();
            sysOptions.setOptId(2004L);
            sysOptions.setOptDesp("非自动分配模块");
            sysOptions.setOptVal(0L);
            sysOptions.setOptStrVal(optStrVal);
            list.add(sysOptions);
            sysOptionsService.insertSysOptions(list);
        } else {
            sysOptions.setOptStrVal(optStrVal);
            list.add(sysOptions);
            sysOptionsService.updateSysOptions(list);
        }

        return ResponseDTO.success();
    }
}
