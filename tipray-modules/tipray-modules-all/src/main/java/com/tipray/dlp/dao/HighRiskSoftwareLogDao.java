package com.tipray.dlp.dao;

import com.tipray.dlp.bean.HighRiskSoftwareLog;
import com.tipray.dlp.bean.vo.HighRiskSoftwareLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource
public interface HighRiskSoftwareLogDao extends BaseDao<HighRiskSoftwareLog> {
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(HighRiskSoftwareLogVO vo);

    /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<HighRiskSoftwareLog> listByVO(HighRiskSoftwareLogVO vo);

}
