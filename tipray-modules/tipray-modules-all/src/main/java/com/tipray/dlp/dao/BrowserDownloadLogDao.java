package com.tipray.dlp.dao;

import com.tipray.dlp.bean.BrowserDownloadLog;
import com.tipray.dlp.bean.vo.BrowserLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/1 13:57
 */
@Repository
@ShardingDataSource
public interface BrowserDownloadLogDao extends BaseDao<BrowserDownloadLog>{
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(BrowserLogVO vo);

    /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<BrowserDownloadLog> listByVO(BrowserLogVO vo);

}
