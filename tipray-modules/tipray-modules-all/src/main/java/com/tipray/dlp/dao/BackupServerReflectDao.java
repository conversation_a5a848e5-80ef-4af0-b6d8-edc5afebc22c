package com.tipray.dlp.dao;

import com.tipray.dlp.bean.BackupServer;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public interface BackupServerReflectDao {
    void deleteByRefServerId(Long serverId);

    Long getRefServerIdByServerId(Long serverId);

    BackupServer getMapByServerId(Long serverId);

    List<Long> listServerIds(Long serverId);

    List<Long> listAllRefServerId();

    void insert(@Param("refServerId") Long refServerId, @Param("serverId") Long serverId, @Param("resyncEnabled") Integer resyncEnabled,  @Param("syncType") Integer syncType, @Param("syncTypeEx") Integer syncTypeE, @Param("createTime") Date createTime);

    List<Long> listAllServerIds();

    void deleteByServerIds(String serverIds);

    void deleteByRefServerIds(String ids);

    List<Long> listAllServerIdByRefServerId(Long refServerId);
    List<Long> listServerIdByRefServerId(Long refServerId);

    List<BackupServer> listByRefServerId(Long refServerId);

    void updateNoDel(@Param("refServerId") Long refServerId, @Param("serverId") Long serverId, @Param("resyncEnabled") Integer resyncEnabled,  @Param("syncType") Integer syncType, @Param("syncTypeEx") Integer syncTypeE, @Param("createTime") Date createTime);

    List<Long> listServerIdByRefServerIds(String refServerIds);

    Date getRelatedCreateTime(@Param("refServerId") Long refServerId, @Param("serverId")Long serverId);

    Long getLastRelatedServerId(@Param("serverId") Long serverId, @Param("createTime") Date createTime);

    List<Map<String, Object>> listAllNotDeleted();
}
