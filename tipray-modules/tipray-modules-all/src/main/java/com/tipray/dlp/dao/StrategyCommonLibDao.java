package com.tipray.dlp.dao;

import com.tipray.dlp.bean.StrategyCommonLib;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
public interface StrategyCommonLibDao extends BaseDao<StrategyCommonLib>{

    /**
     * 接口想设计成通过，如果有后续扩展的常用库，可以根据libType来选择表，然后再left join
     * @param libType
     * @param libIds
     * @return
     */
    List<Map<String, Object>> listStgLibInfo(Integer libType, String libIds);
}
