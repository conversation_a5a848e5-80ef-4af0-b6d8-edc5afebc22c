package com.tipray.dlp.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.UnregisteredModule;
import com.tipray.dlp.bean.dict.BackTypeDict;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.controller.socket.RegisterSocketMapper;
import com.tipray.dlp.effective.module.RegisterInfo;
import com.tipray.dlp.exception.ServiceException;
import com.tipray.dlp.ftp.bean.FtpTask;
import com.tipray.dlp.service.UnregisteredModuleService;
import com.tipray.dlp.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;

/**
 * 非注册销售模块Controller
 */
@Oplog(UnregisteredModule.class)
@RestController
@RequestMapping("/unregisteredModule")
@Slf4j
public class UnregisteredModuleController {

    //@Value("${backup.moduleAuthFiles}")
    //private String moduleAuthFilesPath;

    @Resource
    private RegisterSocketMapper registerSocketMapper;

    @Resource
    private UnregisteredModuleService unregisteredModuleService;

    /** 上传授权文件*/
    @PostMapping(value = "uploadFile")
    public ResponseDTO uploadFile(@RequestParam(value = "file") MultipartFile file) {
        try {
            String fileName = FileUtil.append(FileUtil.getTempPath(), "module_auth_files", file.getOriginalFilename());
            FileUtil.writeFile(file.getInputStream(), fileName);
            return ResponseDTO.success(file.getOriginalFilename());
        } catch (Exception e) {
            return ResponseDTO.failure(e);
        }
    }

    @PostMapping(value = "updateModule")
    public ResponseDTO updateModule(String fileName) {
        try {
            String filePath = FileUtil.append(FileUtil.getTempPath(), "module_auth_files", fileName);
            String contents = FileUtil.readFileToList(new File(filePath)).get(0);
            String userNo = RegisterInfo.getInstance().getUserNo(); // 获取用户编号
            String plaintext = AesUtils.decryptByCbc(contents, userNo+"0000");
            //将json字符串转换成json对象
            JSONObject jsonObj = JSON.parseObject(plaintext);
            String userNoOfFile = jsonObj.getString("user_no");
            if (StringUtil.isEmpty(userNoOfFile)) {
                throw new ServiceException("该文件为非法文件");
            } else if (!userNo.equals(userNoOfFile)) {
                throw new ServiceException("该授权文件的授权用户非当前用户");
            }
            Integer authNum = jsonObj.getInteger("auth_num");
            if (authNum == 0) {
                UnregisteredModule bean = new UnregisteredModule();
                bean.setFileGuid("");
                bean.setModuleNo(60001);
                JSONObject ciphertextJson = new JSONObject();
                ciphertextJson.put("module_no", 60001);
                ciphertextJson.put("file_guid", "");
                ciphertextJson.put("auth_num", authNum);
                String jsonStr = ciphertextJson.toString();
                bean.setCiphertext(AesUtils.encryptByCbc(jsonStr, userNo+"0000"));
                unregisteredModuleService.update(bean);
                Integer msgNo = registerSocketMapper.notifyModuleChange();
                log.info("通知销售模块变更的消息流水号 ： {}" ,msgNo);
                return ResponseDTO.requestId(msgNo);
            } else {
                JSONObject json = new JSONObject();
                json.put("user_no", userNo);
                json.put("android_app_key", jsonObj.getString("android_app_key"));
                json.put("android_user_key", jsonObj.getString("android_user_key"));
                json.put("ios_app_key", jsonObj.getString("ios_app_key"));
                json.put("ios_user_key", jsonObj.getString("ios_user_key"));
                // 创建文件
                String toUploadFile = FileUtil.append(FileUtil.getTempPath(), "module_auth_files", System.currentTimeMillis() + ".txt");
                FileUtil.createFile(toUploadFile);
                FileUtil.appendText(toUploadFile, json.toString());
                //上传授权信息明文文件到文件服务器
//                TransferTaskDTO task = TransferUtil.addUploadTask(BackTypeDict.BACKUP_TYPE_SOFTWARE_PATCH.getCode(), toUploadFile);
//                if (TransferStatusDict.isErrorCode(task.getStatus())) {
//                    throw new ServiceException("文件传输到文件服务器过程中失败：" + TransferStatusDict.getValue(task.getStatus()));
//                }
                FtpTask task = JavaTransferUtil.syncUploadTask(
                        BackTypeDict.BACKUP_TYPE_SOFTWARE_PATCH.getCode(),
                        FileUtil.append(FileUtil.getTempPath(), "module_auth_files"),
                        System.currentTimeMillis() + ".txt"
                );
                if (task.getStatus().isErrorCode()) {
                    throw new ServiceException("文件传输到文件服务器过程中失败：" + JavaTransferUtil.formatStatusInfo(task.getStatus()));
                }
                //授权文件上传成功后，需要更新数据库
                UnregisteredModule bean = new UnregisteredModule();
                bean.setFileGuid(task.getFileGuid());
                bean.setModuleNo(60001);
                JSONObject ciphertextJson = new JSONObject();
                ciphertextJson.put("module_no", 60001);
                ciphertextJson.put("file_guid", task.getFileGuid());
                ciphertextJson.put("auth_num", authNum);
                String jsonStr = ciphertextJson.toString();
                bean.setCiphertext(AesUtils.encryptByCbc(jsonStr, userNo+"0000"));
                unregisteredModuleService.update(bean);
                Integer msgNo = registerSocketMapper.notifyModuleChange();
                log.info("通知销售模块变更的消息流水号 ： {}" ,msgNo);
                return ResponseDTO.requestId(msgNo);
            }
        } catch (Exception e) {
           return ResponseDTO.failure(e, "该文件为非法文件");
        }
    }

    /**
     * 文件流转换成字符串
     * @param is
     * @return
     */
    private String convertStreamToString(InputStream is) {
        BufferedReader reader = new BufferedReader(new InputStreamReader(is));
        StringBuilder sb = new StringBuilder();

        String line = null;
        try {
            while ((line = reader.readLine()) != null) {
                sb.append(line + "/n");
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return sb.toString();
    }

    public static void main(String[] args) throws Exception {
        String ipk = "[0x40,0x0,0x0,0x0,0xf,0x80,0x5a,0x97,0xbb,0x29,0x2b,0xfc,0xf4,0xd1,0xb1,0x0,0x69,0xdd,0x35,0xc9,0x2,0xf2,0x8f,0xcd,0x6d,0xe,0x28,0x23,0xf7,0x2f,0xc6,0xbf,0xd5,0x5,0xbc,0xd0,0x57,0xa9,0x34,0x76,0x7,0x4f,0x4,0x98,0xfa,0x6c,0x4d,0x63,0xd6,0x7c,0x87,0xed,0x48,0x41,0x1e,0x20,0x41,0x5b,0x19,0xa0,0xbd,0x5a,0x9e,0xb7,0x7b,0x3e,0x9e,0x72,0x63,0xa1,0x17,0x8d,0x3e,0x33,0xe7,0xc4,0xb,0x7,0xcf,0xe0,0x3f,0x25,0x84,0x8d,0x4a,0x90,0xf2,0xe9,0x4c,0x59,0x5d,0x2b,0x7f,0x36,0xac,0xac,0xe6,0x74,0x42,0xb3,0x7a,0xeb,0x8f,0x3d,0xd4,0x88,0x99,0x28,0x8a,0x59,0x74,0xb6,0x4c,0x70,0x72,0xcc,0xea,0x3d,0x3,0xb5,0x1c,0x43,0xf4,0x93,0x70,0x6f,0x7d,0xca,0x12,0x98,0x8c,0xa5,0x36,0x67,0x69,0x9e,0x15,0x95,0xa4,0x37,0xbd,0x1,0xfa,0x6b,0xdb,0xc4,0xd0,0x79,0x72,0x24,0x15,0xcd,0x6b,0x1e,0xb2,0x77,0xc6,0x10,0x6a,0x23,0xa0,0x7d,0x30,0xc3,0x51,0x36,0x2f,0x47,0xc5,0xe8,0x17,0xc3,0xa8,0x78,0xb8,0xbb,0xfb,0x4d,0xc1,0x23,0x7e,0xe6,0xd9,0x1e,0x1,0x28,0xec,0x52,0x6a,0x34,0x69,0xf9,0x68,0xb,0x86,0x80,0x29,0xd6,0xfa,0x4,0x41,0xe0,0xc2,0x22,0x9d,0x90,0x89,0x5,0x53,0xce,0x29,0xb0,0xb6,0xb9,0x76,0xd6,0x9f,0x8d,0xed,0x5d,0x63,0x37,0x57,0xf0,0x31,0xe1,0x20,0x7c,0xea,0x38,0xd3,0xed,0x9,0x7a,0x0,0x50,0x1d,0x9a,0xdc,0xfc,0x1a,0xf6,0x2e,0x4a,0x92,0x71,0xa1,0xb5,0x35,0x44,0x84,0x9c,0xb3,0x28,0xc9,0x96,0x77,0xc5,0x49,0xf8,0xb7,0x7b,0x83,0xb2,0xeb,0xa2,0x4e,0xca,0x68,0x6c,0xb1,0xc3,0xf9,0xa2,0xa1,0xf5,0x50,0xbf,0x65,0xf6,0x5b,0x76,0x18,0xc0,0x2a,0xe0,0x7b,0x2f,0xbb,0x86,0xcc,0xc6,0xdf,0x1b,0x11,0xde,0x4b,0xc9,0xf4,0x21,0xd6,0x47,0x5d,0x2,0x57,0xe0,0xba,0x3d,0xf7,0xf0,0xb0,0xa3,0xe9,0x6f,0x9b,0x3e,0xcd,0x9a,0x5e,0x2a,0x7f,0x42,0x5b,0x29,0x6d,0x9e,0x4a,0xf0,0x11,0x21,0xf8,0x1,0x48,0x2b,0xb4,0xca,0xc3,0x6a,0x12,0x7c,0x4c,0x8d,0xdc,0x15,0x7b,0xcb,0x7d,0x7d,0xf9,0xf0,0x9e,0xd2,0xdf,0x46,0xb3,0xd3,0x22,0xc9,0x49,0x5d,0x5f,0x47,0x4b,0xae,0xfe,0x15,0x85,0x8b,0xa0,0x84,0x88,0x9d,0x4c,0x8a,0x92,0xf5,0x83,0xcb,0x8a,0xe7,0x43,0xaf,0xb4,0x5f,0xa4,0xa1,0x8b,0x92,0xfd,0xb5,0x1c,0x6c,0x2d,0xb8,0xce,0x33,0x88,0xd4,0xf,0x7e,0xa6,0xb4,0xe9,0xbb,0x87,0x75,0xb9,0x8a,0xaa,0xf3,0x3e,0x5,0x9c,0xf2,0x45,0x76,0x6d,0x48,0x1d,0x4d,0xbf,0x8a,0x0,0xa9,0xf3,0x3b,0x99,0xe7,0xcf,0x16,0x46,0x2c,0x66,0xb3,0x8c,0x8b,0x40,0x53,0x9d,0x83,0x54,0xf0,0x79,0x98,0x43,0xea,0x53,0xf5,0xe4,0x2f,0xf3,0x7f,0x4d,0x61,0xde,0x56,0x96,0x47,0xc6,0x8a,0x36,0x9c,0xf5,0x54,0x27,0x6e,0x38,0x4b,0xe4,0x21,0xf1,0x39,0x77,0xa6,0x97,0xe4,0x5d,0xf9,0x2b,0xeb,0xf3,0x50,0xac,0x9f,0xc3,0xe6,0x75,0x39,0xd0,0xb0,0xee,0x0,0xba,0x2d,0xb7,0xc1,0x8d,0xf7,0x84,0xbf,0x65,0x3c,0xd1,0x30,0x45,0x9d,0x66,0x76,0x1a,0x85,0x8a,0x53,0x11,0xd8,0xeb,0x96,0x63,0x63,0x92,0x2c,0xb3,0x1d,0x60,0xf6,0x8c,0x8f,0x7f,0x34,0xb6,0x6f,0xc0,0x77,0xdf,0x96,0xb5,0x10,0x14,0xbb,0x92,0xd0,0xd2,0x16,0xcb,0x2,0xfd,0x93,0x54,0xd1,0x47,0xd2,0xc5,0xbe,0x7e,0xd7,0x42,0xde,0xfb,0xde,0x72,0x70,0xa6,0x8,0x6,0xf2,0x8a,0xe5,0x7b,0xfb,0x7,0x3,0x37,0x17,0x3f,0x6a,0x8f,0x10,0x12,0x1e,0x0,0x79,0x36,0x84,0x99,0x9e,0xfe,0xed,0x3c,0x11,0x57,0x2,0xfc,0x8b,0x1b,0x46,0xf3,0x82,0x40,0xdf,0xb2,0xe8,0xf9,0xa6,0x35,0xf9,0xd9,0xbc,0x25,0xdb,0xb6,0x95,0xa2,0xf2,0xeb,0xeb,0x2f,0x0,0x58,0x6b,0x80,0x85,0x9a,0x78,0xc2,0x3c,0xe6,0xbd,0xdd,0xc3,0xae,0xc8,0x8a,0xc0,0x2f,0xee,0x1f,0x5e,0x18,0x98,0x6e,0x99,0x55,0x66,0xdb,0x3f,0x15,0x26,0x54,0x23,0x8,0x6,0xd8,0x32,0x5b,0xb0,0x7d,0xcb,0xc2,0x43,0x63,0x49,0x4d,0xb3,0x7f,0x8a,0x64,0x57,0xbc,0x4f,0x80,0x96,0xe0,0x24,0xe6,0x2a,0x29,0x81,0x4d,0x30,0x4f,0xf3,0xfa,0xcc,0x48,0xaf,0x1c,0x1c,0x7,0x3b,0x78,0x63,0xab,0xf,0x63,0x1,0xd9,0xa5,0xd,0x9b,0xa9,0x72,0x7b,0xd2,0x32,0x32,0x4d,0x9a,0x79,0x40,0x8b,0xd8,0x58,0xeb,0xf3,0x10,0x32,0xf7,0xc7,0x18,0x5e,0x72,0x6b,0xc1,0x64,0x8c,0xc,0x45,0x1a,0xea,0xe8,0x24,0xe5,0xb5,0x97,0x29,0x21,0x7a,0x81,0x2,0x29,0xe3,0x21,0xff,0xe9,0x21,0xb0,0x58,0xb1,0xf1,0x62,0xdc,0x6b,0xe0,0x21,0xb0,0x6f,0x49,0x9a,0xe8,0x47,0xfc,0x2c,0xe7,0xd7,0x2c,0x72,0xf,0x91,0x31,0x44,0x9f,0xad,0xbf,0xfc,0xa6,0x15,0x39,0xa6,0xef,0xe0,0xe9,0x3a,0x38,0xc1,0x9d,0x41,0xad,0x11,0x6c,0x5e,0xe9,0xee,0x71,0x21,0x2,0x5e,0x86,0xff,0xd6,0x41,0x34,0x82,0x31,0xdc,0x45,0x2d,0xe3,0xdb,0x27,0x7f,0x7e,0xe7,0x6d,0x16,0x5,0xcd,0x7a,0xb3,0x31,0xfa,0x23,0x95,0x59,0x81,0x5,0xee,0x11,0xf6,0xed,0xa3,0xa7,0xb4,0x1,0x26,0x54,0x5,0x6d,0xe6,0x82,0x76,0x11,0x13,0x8a,0x1e,0xa9,0x3d,0x85,0x7d,0xc9,0x23,0xf3,0xb8,0x57,0x69,0x18,0xba,0x9,0x1f,0xc9,0x78,0x11,0x6e,0x63,0x96,0x5b,0xdd,0x81,0xef,0xec,0x72,0xcb,0x6f,0x1d,0x22,0x67,0x67,0xb4,0x16,0x47,0x10,0x6d,0x69,0x74,0xf7,0xd2,0xc4,0xd1,0x75,0x7b,0x5e,0xc9,0x5a,0xfc,0x54,0x7,0x28,0xd5,0xb4,0xc8,0x85,0x40,0xdc,0xde,0xb1,0x22,0x62,0xdd,0x6b,0x2c,0xbc,0x24,0x79,0x42,0xf8,0x75,0x20,0x61,0xea,0x7b,0x13,0x20,0xc1,0x93,0xab,0xd1,0xf8,0xf4,0x87,0x66,0xea,0xc4,0x98,0x1a,0x34,0x59,0x5d,0xf9,0x2e,0x9c,0x7c,0x5e,0x91,0x75,0xde,0x81,0x1a,0xe2,0x78,0x44,0xfb,0xa8,0x3f,0xc8,0xa4,0x48,0x83,0x75,0x47,0x32,0xd6,0xb9,0x6a,0x4b,0x55,0x6e,0x87,0x3a,0x57,0xf1,0xad,0xf9,0x9c,0x2d,0xdf,0x2f,0xfb,0xfa,0x90,0x31,0xf4,0xcf,0x69,0x2f,0x7e,0x37,0xea,0xce,0x93,0x7,0xd6,0x4c,0xea,0xd6,0x3d,0x0,0xa1,0x9c,0xb4,0x39,0xc0,0xa0,0x90,0xe5,0xa3,0xea,0xad,0x9b,0xd1,0x9a,0xb3,0x4,0xdf,0xe9,0xd8,0x16,0xdc,0xec,0xc,0x54,0xa,0x75,0x9b,0x6e,0x3f,0x91,0x2d,0xef,0x23,0xae,0xd4,0xa2,0x61,0x59,0x8f,0xbd,0xb6,0xf2,0x32,0x5d,0x90,0xcb,0x7e,0x3e,0xb7,0xed,0x76,0x98,0xb9,0x8e,0x56,0xa,0xaa,0xeb,0xed,0x30,0xf,0xc,0x3e,0x6c,0xea,0x47,0xd3,0xa6,0x5,0xd9,0x42,0x3b,0xa9,0x5e,0xa9,0x77,0x61,0xb6,0x62,0x7e,0x28,0x5d,0xd8,0x8,0x16,0x86,0x4e,0x84,0xb2,0x90,0x85,0x1b,0x0,0x5,0x4e,0x87,0x7d,0x1f,0x22,0xa0,0x3,0xae,0xb,0x48,0x83,0x3f,0x8e,0xbb,0x4,0x8b,0xf1,0xd5,0x8c,0xe8,0x1b,0x4f,0x28,0x7b,0x7d,0xd4,0xa9,0x28,0x19,0x24,0x9b,0xff,0xb5,0xa,0xdb,0xe5,0x18,0x8f,0x9a,0xd3,0x5,0x32,0x79,0x95,0x2f,0x3b,0xf1,0x0,0x6d,0x0,0xe2,0x80,0x82,0xe5,0x6b,0x5,0xf7,0x5c,0xb,0xb2,0x21,0xfe,0xba,0xe6,0x50,0x3,0x2,0x3e,0x83,0x1c,0x3a,0xb3,0x8b,0x42,0xbb,0xc5,0x1c,0xac,0x78,0x6,0xbc,0x18,0xf,0x72,0x17,0xb5,0x9c,0x64,0x56,0xf0,0x34,0x19,0xeb,0xee,0x6d,0xfc,0xc7,0xbc,0x0,0x5e,0xfd,0x4,0x49,0x2a,0x7d,0xa6,0x93,0x22,0x88,0xab,0xd0,0xf,0x66,0x52,0xeb,0x62,0xae,0xf1,0x1a,0x30,0x99,0x2d,0x4,0x68,0x6e,0x8a,0xb0,0x73,0x85,0x55,0x32,0x77,0xc7,0xb4,0xbb,0xcd,0x13,0x54,0xea,0xf3,0xa3,0xe1,0x73,0xe6,0x74,0x67,0xaf,0x48,0xf3,0xab,0xde,0x64,0x6d,0x3f,0x12,0xd4,0xe3,0x12,0x38,0x36,0x94,0x70,0xf9,0x1e,0xe2,0x7f,0x2d,0x13,0xe3,0xd7,0xb1,0x85,0xef,0x8c,0x4c,0x77,0xc7,0x31,0xa2,0x17,0x8,0x22,0xa4,0x12,0xd2,0x1d,0x17,0x20,0xa4,0xb5,0x13,0x37,0x8b,0xdd,0xbf,0xec,0x3a,0xcc,0x99,0xd4,0x41,0xd8,0x69,0x92,0xf6,0x6b,0xb8,0xcb,0x8d,0x97,0xc8,0xf7,0x43,0xa1,0xfe,0xda,0x22,0x65,0x66,0x3e,0x3,0x1e,0xf7,0x28,0xcf,0x7a,0x41,0x40,0x3e,0x3d,0x9c,0xeb,0xff,0x18,0xa6,0x7a,0x9e,0x7,0xc0,0xc9,0x3e,0xe4,0xcc,0x11,0xf1,0x9b,0xbc,0xe,0xbf,0x7,0x72,0x9b,0xd5,0x2a,0xe1,0x6b,0x72,0x67,0x4c,0x90,0xce,0x6f,0xf,0xca,0x54,0x32,0xe3,0xaf,0xbb,0x7c,0xe0,0x60,0x0,0x38,0x2a,0x7d,0x30,0x5,0x99,0xb6,0x10,0xcc,0x51,0x60,0x8d,0x90,0x64,0xc0,0x39,0x6d,0xa4,0xb7,0x56,0xc8,0xb1,0x29,0xfe,0xf8,0x67,0x32,0x66,0xf5,0x13,0xe5,0x2c,0x5e,0x3b,0x19,0xe5,0x8b,0xa,0x7d,0x6e,0xea,0x1f,0x14,0x7b,0x82,0xca,0x7e,0x1a,0x33,0xb2,0xf8,0x4a,0x7,0x92,0x4f,0xd,0x16,0x49,0xfc,0xa1,0x61,0x10,0x83,0xee,0x86,0x83,0x6e,0x6d,0x89,0xcf,0xb1,0x6c,0xa1,0xed,0x75,0xdb,0x10,0xac,0x4f,0xf8,0x73,0xec,0x3d,0xde,0x4e,0x8b,0x65,0xf2,0x4f,0x67,0x33,0xb8,0x3a,0xdb,0x90,0xa2,0x11,0xb6,0xf7,0x25,0xaa,0x54,0x2,0xf,0xc3,0x13,0x55,0x6d,0x95,0x4c,0xb0,0xb3,0x95,0xb3,0xe6,0xf3,0xb5,0x86,0xf,0x9b,0x65,0x8f,0x40,0x47,0xf4,0x67,0x1a,0x71,0x3e,0x9d,0xd1,0xc1,0x2,0x97,0x79,0x75,0xd9,0xe,0xce,0xab,0xa,0x3f,0x8b,0x77,0xb6,0xa8,0xc3,0x96,0x5b,0x14,0x1c,0x28,0x9b,0x2a,0x8d,0x1a,0x18,0xa9,0x3f,0x44,0x23,0x7,0x95,0xbc,0x4b,0x40,0xfa,0xc7,0x1b,0x1c,0xc0,0xd6,0x7f,0x22,0x2b,0x6f,0x3d,0xf0,0x64,0x40,0x62,0xd2,0xfc,0xc2,0xed,0xc6,0x1b,0x8f,0x49,0x7,0x65,0x2b,0x69,0x85,0x28,0x88,0x39,0x15,0xd,0x8e,0x8a,0x3b,0x21,0x31,0xc2,0xe,0xde,0x1c,0xc9,0xf7,0x50,0x46,0x9e,0x96,0xc7,0xe,0x5d,0x99,0x3e,0xa7,0xa3,0xf,0x38,0x24,0x4b,0x48,0x98,0xe4,0xc8,0x7a,0x93,0xe8,0xfd,0xaa,0x4d,0xe,0x89,0xcf,0xcf,0x49,0x77,0xc5,0x45,0xc7,0x49,0xcb,0xc7,0xfb,0x4b,0x61,0x98,0x5b,0xe3,0x91,0x42,0x34,0x98,0x28,0x5d,0x5e,0xa2,0xf5,0x84,0x29,0x2a,0x0,0x6,0xc8,0xd5,0x3d,0xe,0x11,0x2e,0xa8,0x6b,0x9d,0x7b,0x81,0xdf,0xf7,0x29,0x26,0x56,0x3a,0x8e,0x97,0x9b,0x37,0xb5,0xb1,0xd4,0xf1,0x65,0x42,0x1,0xc8,0x20,0x17,0x8,0xbb,0x25,0x4e,0xd8,0x6f,0xea,0x39,0x93,0xee,0xce,0x71,0x9b,0x7c,0xea,0xcc,0x30,0xcf,0x87,0xbe,0xfb,0xd7,0xad,0xd8,0x16,0xf0,0xee,0xa9,0xb,0xac,0x61,0x39,0x9c,0x30,0xb7,0x5,0x4,0x9c,0xb7,0x4c,0x30,0xd8,0x88,0xcd,0x48,0xa0,0x95,0x32,0xbb,0x61,0xcd,0xfb,0x76,0xbd,0xd2,0x29,0x73,0x3c,0xfd,0x25,0xb8,0xa7,0xa4,0x80,0x29,0x1a,0x87,0x9d,0xc,0xd3,0x8,0xd5,0x4e,0xa,0xb5,0x5c,0xc6,0x1f,0x60,0xc2,0xd8,0x5a,0x9e,0xb9,0xc1,0xc8,0x2c,0x72,0x73,0x11,0x63,0xdb,0x97,0x8b,0x93,0x94,0xc9,0xcc,0x7e,0x4b,0xe9,0x34,0x79,0x16,0x2a,0xcf,0xb7,0x4f,0xba,0x6,0xad,0xfe,0xce,0xb5,0xe0,0x95,0x72,0x62,0xd2,0xb4,0xf6,0x54,0x80,0x53,0x64,0xc2,0x98,0x8,0xd4,0x43,0xba,0x68,0x2c,0xe6,0xf,0x5c,0x79,0xda,0xf1,0x18,0x83,0x3b,0x77,0xac,0x92,0x3b,0xf,0x3e,0x21,0xf1,0x1,0xaa,0x8d,0x80,0x57,0xd6,0x6,0xad,0x2c,0x94,0xaa,0x26,0xf5,0x6a,0xe,0xf7,0xc4,0x74,0xcf,0x83,0xa1,0x61,0xf8,0xd4,0xcd,0xd6,0xb1,0x4,0xf1,0x6,0xe6,0xf7,0xc6,0x56,0xc8,0x3e,0xb3,0xd9,0x9f,0xc9,0xa5,0x3a,0xad,0x80,0x80,0xde,0x55,0xdc,0xe4,0x8d,0x31,0x81,0x56,0x1e,0x4f,0x82,0xe8,0x99,0xad,0x24,0xbb,0xf9,0x5e,0x6a,0xf2,0x50,0xd8,0xc1,0x33,0x3d,0xd7,0x5c,0x87,0x25,0xca,0x31,0x9a,0xb1,0xd,0xd5,0x31,0x7b,0x3b,0x70,0x90,0xeb,0xc7,0x17,0xa3,0x47,0xc9,0xd8,0x92,0x20,0x5f,0x3b,0xd0,0xa2,0x8,0xbc,0x29,0x12,0xb8,0xa2,0x59,0x58,0x3b,0xae,0x1d,0x6c,0x2,0x35,0xbb,0x14,0x68,0x8c,0xb,0xc7,0x13,0xf0,0xa0,0x12,0x7c,0x10,0xd7,0xa7,0xdb,0xe7,0xc8,0x3f,0xe4,0xc4,0x3a,0x9a,0x2a,0x44,0xa4,0x41,0x92,0x8d,0xbd,0x1,0x21,0xda,0x27,0x3f,0xf4,0x43,0x9,0xbd,0x9d,0x9b,0x66,0x80,0x88,0xe9,0x61,0x1c,0x63,0x32,0xcb,0x8a,0xc4,0x5b,0x41,0x43,0x95,0x9,0xfb,0x75,0x4f,0xcc,0xff,0x7d,0x31,0xbe,0xd5,0xdc,0xec,0xaa,0xbf,0xa5,0xc8,0x35,0xa5,0x91,0x66,0x6b,0x1b,0x2c,0xfd,0x12,0x92,0x5f,0xc,0x51,0xc0,0x14,0xba,0xca,0x7a,0xf1,0xf0,0x3f,0xbc,0x49,0x49,0x67,0xa6,0x2b,0x75,0x9d,0x93,0x9a,0x45,0xb,0xd6,0xd4,0xca,0x3,0x7f,0xda,0x83,0x1c,0x46,0xd2,0xee,0x5c,0xdf,0x3d,0x29,0x26,0x60,0xcb,0xb4,0xdc,0x7e,0x1f,0xdf,0x14,0x9c,0x6c,0x32,0x56,0x28,0xeb,0x8c,0x9e,0x2,0x40,0x6f,0xad,0x79,0x3b,0xad,0x89,0xe7,0xc6,0x21,0xd6,0x3d,0xf1,0x50,0xa,0xf4,0xd4,0x52,0x7f,0xcc,0xaf,0xbb,0x3f,0x4f,0xf3,0x5,0x1c,0x3b,0x5,0x59,0xd8,0x2b,0x82,0xdc,0x23,0xa4,0xa6,0xcf,0x5d,0xae,0x87,0x45,0xdc,0xd1,0x2e,0xb5,0x9b,0xaf,0x91,0x1b,0x8b,0x82,0x8f,0xe0,0x68,0xf,0x73,0x4b,0x40,0x33,0xd7,0xb5,0x98,0x68,0xa6,0x18,0x27,0x88,0x98,0xd9,0x54,0x4b,0xb1,0x6d,0x38,0x38,0x54,0x1e,0x5b,0xc8,0xd,0x71,0x2d,0x81,0x3c,0x5d,0x4f,0x1e,0xe4,0x6f,0xa4,0x53,0xa5,0x47,0xfa,0x4f,0xfe,0x2b,0x6a,0xa,0xd4,0xf3,0xd,0xf6,0xfa,0x70,0xda,0x3c,0x80,0x20,0x6a,0x60,0x2a,0xbb,0xd2,0xef,0xaa,0x12,0xcb,0x50,0x8a,0xe9,0x70,0x37,0x41,0x97,0xdb,0x7b,0x8b,0x1e,0xda,0xdb,0x7b,0x8e,0x70,0x5e,0x45,0x78,0xc,0x96,0x1c,0xef,0xe2,0xd6,0xc0,0x2d,0x27,0x31,0x24,0xc7,0xe7,0x3d,0x91,0x66,0x77,0x5b,0x45,0x69,0x18,0xc5,0x23,0x8a,0x75,0x7c,0x4b,0x74,0x56,0xb8,0x96,0xb3,0xa8,0x5e,0xb8,0x90,0xaf,0xda,0x62,0x68,0x33,0xf2,0xf0,0x1,0xb,0xb4,0xad,0x60,0x67,0x46,0xa4,0x75,0xa1,0xed,0xb9,0xfe,0xfb,0x8e,0x8a,0xdf,0x81,0xfc,0x31,0x4a,0x9f,0xd2,0x56,0xe5,0xfd,0xb4,0xb,0xce,0x7,0x83,0x1a,0xe1,0xb8,0x8e,0xe1,0x77,0xa7,0x64,0xd9,0xf2,0x3d,0xb6,0x18,0x43,0x60,0x56,0x9e,0xc2,0xc1,0x84,0x7e,0x55,0x20,0xac,0xff,0xc,0x68,0x34,0x19,0xdc,0x6d,0x78,0x7d,0xdc,0x19,0x1a,0x4f,0x19,0x9,0x5f,0xa4,0x49,0x57,0x99,0x64,0x39,0xf7,0x4,0xeb,0x3f,0x53,0xe7,0x95,0x90,0xf5,0x45,0xb,0x35,0x22,0xd1,0xdc,0x37,0x7,0x15,0xaa,0xba,0xf0,0x78,0x4b,0x6a,0xde,0x50,0x17,0x59,0xd7,0x14,0x75,0x9f,0x64,0x39,0xdd,0xf6,0x60,0xb9,0x6d,0x63,0x87,0x19,0x6a,0xf2,0x12,0xe0,0xe,0xd4,0x7b,0x7b,0x76,0x4f,0x70,0xaa,0x52,0x6b,0x13,0xe0,0xd,0xf4,0x76,0xe9,0x33,0x69,0xbd,0xdc,0xa0,0x33,0xd3,0xb4,0x46,0xd6,0x7f,0x1b,0xf7,0xe9,0x82,0x4e,0x2a,0xa7,0xc1,0x4a,0xf3,0x43,0x60,0x8d,0xcf,0xc,0xc7,0x23,0xd9,0xac,0x1d,0xfe,0xdf,0x57,0xb7,0x5,0x64,0xb5,0xeb,0x33,0x89,0x57,0x4d,0xb9,0x87,0x1d,0x86,0x4a,0xe,0xa,0x9c,0x9d,0xd4,0xd2,0x88,0x2f,0xf8,0x6b,0x30,0x4d,0xf2,0x6a,0x5e,0x61,0xe9,0xf6,0xd9,0x3d,0x3d,0xcb,0x61,0xb1,0xb4,0xbd,0x92,0x2e,0x50,0xfe,0x31,0xe7,0x11,0x7b,0xe1,0xcd,0x45,0x75,0x19,0xb0,0x7e,0xf7,0x46,0x4,0xca,0xfb,0x1e,0x25,0x6e,0xe2,0xd5,0xeb,0x69,0x4b,0xf6,0x84,0x2e,0xc7,0x3c,0x14,0x7b,0xfd,0x4a,0x68,0x96,0x4d,0x61,0x50,0x51,0x53,0xb1,0xac,0x14,0x17,0x55,0xc7,0x14,0x77,0x3d,0xdc,0x9c,0x31,0xf2,0x59,0xcb,0xc5,0x56,0x2d,0xd,0x1c,0xf3,0x49,0xed,0xab,0x56,0x2a,0x8,0x68,0xbe,0x4e,0x73,0x74,0xc,0x17,0x55,0x9a,0xae,0xbf,0x6e,0x25,0x8c,0xb0,0xd8,0x4a,0xaa,0xa6,0xc8,0x3c,0x4e,0x86,0xeb,0x25,0x3a,0xbd,0x5b,0x70,0x33,0xf8,0xf1,0x2c,0x72,0x74,0xe1,0x1a,0x0,0xcc,0xf8,0x72,0xe0,0x0,0xf9,0x14,0x3d,0xc8,0xc,0x6a,0x4f,0xfd,0x27,0x23,0x45,0x77,0x98,0x5f,0x0,0xda,0x63,0x75,0xbc,0xf1,0xa7,0xeb,0x1b,0x1,0x1d,0x19,0x69,0xcf,0x26,0xb9,0x62,0x8f,0x90,0xfb,0xa2,0x17,0xf2,0x90,0x43,0xdd,0x3c,0xcc,0xae,0x8a,0x10,0x98,0x22,0x92,0x83,0x45,0xa6,0xa4,0xa6,0xa2,0x72,0xe4,0x57,0x2f,0x10,0x2c,0x18,0x67,0x44,0x1c,0x80,0xfc,0x68,0xea,0x2f,0x55,0x96,0x73,0x4c,0x63,0xa5,0x4f,0x99,0x3b,0xfe,0x6a,0xd0,0xc2,0xe5,0x93,0xe2,0x87,0xce,0x50,0xdf,0xfd,0xb9,0xab,0x9,0x43,0xb4,0x28,0x54,0x77,0x39,0xd9,0x67,0x67,0x3,0xcf,0xfc,0xb7,0xd8,0x48,0x94,0x19,0x17,0x5e,0x69,0xc4,0x48,0x57,0x99,0x5d,0xb,0x41,0xde,0x46,0x3f,0xe7,0xf6,0xbe,0x9a,0x99,0x6c,0xeb,0x4c,0x9d,0x1b,0xdc,0xbc,0x3f,0x94,0x49,0x77,0xf7,0x4d,0xea,0x0,0x85,0x31,0x26,0x54,0xe5,0x80,0x69,0x93,0xa7,0xe4,0x8,0x6e,0x28,0x4d,0xc4,0x9c,0xc0,0x42,0xe,0x1a,0x94,0x14,0xb8,0xfe,0x40,0x8d,0x4b,0x6f,0x39,0x28,0xe3,0xbe,0xf0,0x38,0xe8,0xc6,0x76,0x31,0x1,0x16,0xc8,0x17,0x8e,0x3f,0x91,0x5e,0x82,0xc5,0xed,0x13,0xe2,0x7b,0xf2,0x7f,0x12,0x21,0x27,0xa7,0x93,0xdf,0x96,0xc6,0x4e,0x19,0xe4,0x88,0x40,0x85,0xde,0x51,0x58,0x88,0x11,0x9c,0x23,0x3c,0x8e,0x91,0x35,0xd9,0x2,0x95,0x23,0xdd,0x98,0xbe,0x2d,0x2,0x6c,0xac,0x88,0x5d,0xc2,0x36,0x5d,0xaa,0x39,0x94,0x51,0xf6,0xce,0x1c,0xcf,0x4,0x16,0xd,0x1e,0xd5,0x90,0x6f,0x6f,0x86,0x29,0x6e,0xc9,0x35,0xf5,0xbc,0xfc,0x6f,0x49,0x5e,0xa,0x26,0xb6,0xbb,0xa8,0xa5,0xe1,0xec,0xa5,0xc1,0x90,0x32,0xe6,0xb8,0x4b,0x3,0x8f,0x46,0x50,0x97,0x11,0x8d,0x95,0xa0,0x3,0xf7,0x1e,0x78,0x31,0xd1,0x2c,0xfe,0x70,0x40,0xd2,0x11,0xfa,0xd,0xda,0x2,0x20,0x9c,0x9,0x69,0x20,0xc9,0x85,0x88,0x28,0x0,0xf0,0x3f,0x13,0x62,0x19,0x87,0x70,0x1,0x9b,0xd9,0x9d,0xca,0x90,0x51,0xc8,0x3d,0xd8,0xb7,0xf,0xbf,0x3d,0x29,0x41,0xd1,0x2a,0x34,0x58,0x55,0x49,0x5d,0x81,0xe6,0xb6,0x95,0x52,0x9a,0x66,0xe1,0xdf,0xec,0x9f,0x3a,0x29,0xb3,0x47,0x48,0x48,0xc8,0x7d,0x66,0x82,0x9f,0x78,0x59,0xf7,0x1e,0x89,0x14,0x4b,0xa5,0x2c,0xa1,0x99,0xb8,0x0,0x1c,0x7,0x45,0x66,0xf1,0x5a,0xb3,0xad,0x0,0x87,0x5f,0xe6,0xf5,0xfe,0xe4,0x23,0x25,0x1,0xdc,0x43,0xb3,0xcc,0x9c,0x3b,0xa,0x2d,0xfb,0x9b,0xa6,0x7f,0x97,0x64,0xb3,0x11,0x9f,0xe6,0xd,0xd9,0x42,0x57,0x1,0xc0,0xb1,0xdb,0x22,0x9e,0x55,0xc4,0xe1,0x31,0xfd,0xad,0x26,0x8a,0x55,0xa2,0x71,0x22,0xf7,0x36,0xba,0x8a,0xe9,0xbc,0xdd,0x3d,0x4e,0x86,0x6b,0x55,0x67,0x68,0x1e,0x9a,0xfd,0xe,0xbd,0x72,0x61,0xdc,0xb6,0x81,0x54,0x6,0x83,0xf9,0x96,0x51,0xfd,0xb8,0x38,0x8c,0x34,0xbf,0xad,0xbe,0x21,0x66,0xef,0xa1,0x7f,0x3,0x35,0x6a,0xb3,0x7,0x57,0x6f,0x51,0x5d,0xb7,0x6d,0x6e,0x66,0x99,0xf6,0x9d,0x77,0xc,0x11,0x47,0xc,0x19,0x33,0x26,0xda,0x47,0x45,0x6,0xb,0xcb,0xe3,0xfb,0x15,0x39,0x9f,0x13,0xa5,0x84,0x76,0x79,0xb3,0x8d,0xf9,0x54,0xa9,0x94,0xfd,0xcc,0x4d,0xa,0x5d,0x16,0x5a,0x2c,0xf3,0x1f,0xe6,0xc3,0xb0,0x3,0xe6,0xff,0x1d,0x69,0x9c,0x47,0x60,0x84,0x16,0x2e,0x82,0x9e,0x73,0xfc,0xf5,0xe0,0x6d,0x16,0xd0,0xd5,0x78,0xe,0x1f,0xee,0xd,0xdd,0x73,0xe3,0xd3,0xc1,0x6,0x42,0xbd,0x7b,0xb5,0x1b,0x63,0x4b,0x72,0x92,0xcc,0x7b,0x3e,0x11,0x65,0x9d,0x7,0x7a,0xae,0x10,0xe6,0xe5,0x3b,0x63,0xb0,0xf,0x94,0xac,0x0,0xf3,0x34,0xec,0x42,0x70,0x36,0x27,0x19,0x4f,0xa4,0xb2,0x5,0xb5,0xc,0x84,0x6c,0x1f,0xdb,0x7b,0x54,0x9a,0x43,0x40,0x83,0x33,0xd3,0x10,0xdc,0x5,0xb,0x31,0x2b,0xcc,0x84,0x0,0x93,0x70,0x71,0x34,0x44,0x82,0x26,0xfa,0x40,0x4b,0x83,0x8d,0xda,0x40,0xc9,0xb7,0xd9,0x56,0xb5,0x17,0xd4,0x6e,0x37,0x61,0x62,0xc5,0x57,0xd9,0xc9,0xa3,0x64,0x78,0x4f,0x25,0xac,0x8f,0xdd,0xd2,0xd,0xca,0x63,0xe6,0x22,0x19,0xe4,0x4a,0x3f,0x3b,0x18,0x6,0x6b,0x88,0x5,0xc9,0x80,0x45,0xbd,0xc5,0xed,0xd6,0x4a,0x15,0xc,0xa0,0x7,0x65,0xc0,0x44,0x9c,0x66,0x3a,0xee,0x6e,0x9a,0x53,0xa3,0x2,0x4e,0xa2,0x5b,0x89,0xba,0xc6,0xc,0xa6,0xa2,0x10,0xf4,0x8a,0x16,0x1e,0x92,0x25,0xde,0x79,0x50,0x9a,0x6d,0x40,0xfb,0x2f,0x92,0x93,0x41,0x79,0xe2,0x88,0x99,0xea,0xe1,0x9c,0xbf,0x1f,0xce,0x73,0x4a,0xf6,0x4,0xc2,0xed,0x6e,0x51,0xd1,0x1a,0x34,0x20,0xf8,0x4,0x60,0x5f,0x8b,0x7b,0x85,0x97,0x23,0x11,0xd6,0x6,0xcf,0xf,0xb2,0x1c,0xe,0x87,0x1a,0x21,0x26,0xfa,0x21,0xd2,0xd9,0x64,0xb8,0xc3,0x4b,0x98,0x16,0x6b,0xc,0x78,0xfa,0x83,0xe0,0x5f,0x95,0x28,0x9a,0x2a,0xd0,0x51,0x69,0x18,0xdb,0x12,0x8c,0x10,0xf2,0x2e,0x7d,0xa7,0x25,0xc2,0xf6,0xb3,0xeb,0x52,0x53,0xcf,0x74,0xcc,0x51,0xf3,0xde,0xd9,0x7a,0x82,0xaf,0xff,0xff,0x39,0x50,0xc3,0xd5,0xdc,0x8a,0xe4,0xcb,0xaf,0xcf,0xe5,0x1f,0x28,0x28,0x3e,0x52,0x21,0x89,0xb8,0x23,0x2f,0x84,0xd1,0x6d,0xbd,0x2,0x98,0x50,0xb5,0x46,0xf5,0x58,0x2b,0x50,0xcd,0xab,0x20,0x9d,0x64,0x49,0x64,0xb1,0xd,0x9a,0xba,0xfc,0x5f,0x4a,0x83,0xcf,0xaf,0xf8,0x51,0xe,0xee,0x35,0x2f,0x11,0x98,0x7,0xa7,0x1c,0x4c,0x38,0x51,0xe0,0xc8,0xf9,0x3e,0x8f,0xcd,0xea,0xdd,0x34,0x5,0xdc,0x20,0x9a,0x25,0x85,0x12,0x8d,0xcc,0xef,0x93,0x92,0x95,0x10,0x28,0xa8,0xe9,0x4b,0xe6,0xf7,0x92,0xe2,0x79,0x41,0xd4,0xbf,0x7a,0xc1,0x81,0xcd,0x25,0x85,0x68,0x9,0x22,0xb5,0x6d,0x5f,0x75,0x4d,0xb7,0xd7,0xb7,0xb7,0xf0,0x9b,0x5e,0xba,0xab,0xe5,0x62,0x0,0x2,0xbd,0xaf,0x1d,0xb5,0x3f,0x74,0x8c,0xa7,0xdc,0x13,0x3c,0x61,0x43,0xd1,0x82,0x9d,0xd5,0xb4,0x13,0x56,0x45,0xef,0x21,0xa,0xa0,0x3c,0x6,0x94,0x3,0x19,0x82,0x58,0xca,0x7a,0xdd,0x1c,0x62,0x60,0x84,0x5f,0x9,0xa7,0x92,0x96,0xe3,0xef,0xc7,0x6a,0xb6,0x91,0x68,0x12,0xc3,0x1b,0x49,0xeb,0xe4,0x70,0x6a,0x59,0x80,0xa,0x14,0x9a,0x7d,0x92,0xd3,0xe7,0x7f,0xbe,0x62,0x6c,0x23,0xfa,0xa,0x9,0x8d,0x3d,0x96,0xd1,0x5b,0xbd,0x58,0x41,0x36,0x62,0xbe,0xcc,0x7a,0x54,0x6b,0xac,0xd2,0x77,0xd8,0xe1,0xfb,0x6f,0x6d,0xe1,0x30,0xec,0x90,0x33,0xe3,0x2f,0x5e,0xc5,0x1e,0x44,0xc4,0xb9,0xfa,0xc5,0x7e,0x74,0xb4,0x29,0xb6,0x2b,0x39,0xec,0xc7,0x48,0xe2,0xac,0x2a,0xf3,0xf,0x52,0xc2,0x96,0x4d,0x3a,0xdd,0x85,0xae,0xbb,0x0,0x7b,0x6c,0x15,0xfd,0xae,0x41,0x10,0xea,0xf7,0x1b,0xc8,0xe1,0x2d,0x11,0xb5,0x3b,0x31,0x23,0x88,0x12,0xbf,0xb0,0x85,0x72,0xfc,0x82,0x65,0x77,0xa6,0xbd,0x39,0x5f,0x2c,0xd9,0x72,0x65,0xf3,0x15,0x75,0xa9,0x86,0xb9,0x2d,0x4c,0x19,0xe5,0xcb,0xd5,0x41,0xea,0x97,0x8e,0x5a,0xc4,0xb9,0x35,0x67,0xa2,0x1f,0x3b,0x86,0xa2,0xf4,0x26,0x47,0x3d,0x9b,0x9e,0x6a,0xe8,0x23,0xca,0xe6,0x5b,0x65,0x9c,0x4e,0xdc,0x33,0xee,0xe6,0x38,0x2e,0x11,0xec,0x56,0x5b,0x91,0x87,0x50,0x8c,0xf7,0x1b,0xb7,0x8b,0xae,0x74,0xf8,0xcf,0x10,0x7b,0xc1,0x26,0x6,0x34,0x59,0xde,0xfa,0x8,0xd1,0x1,0xdb,0xa0,0x9f,0xa2,0xdc,0xa6,0x5e,0x90,0xc7,0xfb,0x5a,0xaf,0x10,0x9e,0xaa,0xa9,0x6d,0xc7,0x73,0xc4,0xb3,0x9e,0x2,0x8e,0xa9,0xa9,0x2d,0xbd,0xe,0xab,0xb9,0x97,0xda,0x1b,0xae,0x2c,0x86,0xd4,0x8,0x61,0xd,0x68,0xef,0xe6,0xc4,0xbc,0xca,0xb1,0x80,0xca,0x3a,0xe0,0x5b,0x4b,0x5e,0x4c,0xd2,0x9d,0xd6,0xf8,0x4,0xd3,0xfc,0x35,0xe3,0x24,0x7a,0x98,0x54,0x81,0x7b,0x4e,0x7,0xf8,0x90,0x6a,0xd3,0xc9,0x43,0xb9,0xec,0x6e,0x50,0xde,0x3c,0x5c,0x5,0x1b,0x4a,0xdc,0xce,0xaa,0x82,0xab,0xd7,0xc4,0x39,0xac,0xb0,0x3f,0x7e,0xdb,0x32,0x32,0x0,0xee,0xa0,0x42,0xb2,0xc1,0x51,0x45,0x4f,0x4,0x58,0x3e,0xe2,0x5f,0xfb,0xca,0x95,0xb2,0x57,0xb,0x2f,0xfd,0x72,0x6d,0x4c,0x6f,0xfc,0x7d,0x27,0xa2,0x56,0xde,0x83,0x8e,0x7,0x78,0x60,0x66,0xcd,0x48,0x5a,0x85,0x52,0xf3,0xa4,0xb3,0x28,0xac,0x19,0xeb,0x11,0x56,0x75,0xff,0x45,0xa4,0xbd,0x4e,0x24,0xb9,0x1c,0x6f,0x64,0xaa,0x12,0x3e,0x8e,0x4a,0x22,0xe4,0x95,0x53,0x5a,0x6f,0xc0,0x42,0x4a,0xa2,0x6a,0x56,0x33,0xf8,0x29,0xed,0x24,0x62,0x51,0xfb,0x3f,0x67,0xd7,0x69,0x98,0x93,0x38,0x54,0xd0,0xe8,0x0,0x91,0x96,0xbc,0x55,0x6,0x25,0xdd,0x78,0x3e,0xd3,0x3,0xf1,0xd9,0xc9,0x12,0xca,0xa5,0xbb,0x8e,0x17,0x68,0x4b,0x6c,0xc1,0x7f,0xf4,0xbf,0x7b,0xa,0x9a,0x97,0xdd,0x59,0x21,0xa6,0x48,0x1d,0xb9,0xa2,0x3d,0x49,0xa1,0x70,0x79,0x2,0x9,0x94,0x9b,0x40,0x82,0xe0,0x4c,0x6a,0x28,0x40,0xc0,0x39,0x1e,0x66,0xb7,0xbb,0xc3,0x7a,0xab,0x3f,0x18,0xd6,0xd2,0x81,0x1b,0x5f,0x61,0x1f,0x67,0x52,0x5c,0xe1,0x7d,0x8d,0x7e,0x58,0xa0,0xed,0x83,0x72,0x15,0xcb,0x6f,0x78,0x3d,0x38,0xbb,0xb8,0x3f,0xe9,0x26,0x3,0x9b,0x3d,0x8b,0xe8,0xb5,0x58,0x67,0xc,0xf5,0x11,0xb5,0xe6,0x9b,0x28,0x60,0x3b,0x27,0x88,0x39,0x70,0x90,0xb5,0x38,0x3d,0x36,0x31,0xe9,0x61,0xac,0x5,0x24,0x6a,0x51,0x51,0x42,0xdf,0x99,0x2b,0xc8,0x49,0xfb,0x8b,0xca,0x6c,0x40,0x8d,0xa9,0x85,0x5d,0x2a,0xf9,0x3e,0x8,0x68,0xf7,0xac,0x9d,0x46,0xf0,0xcb,0x4b,0x49,0xfd,0x9,0x1e,0x84,0x30,0x59,0x7f,0x24,0x3,0x63,0x2d,0xcf,0x75,0x6f,0xfb,0x18,0xdb,0x35,0x58,0x78,0xc9,0x6d,0x50,0xe1,0x58,0xef,0x1a,0x47,0xe9,0x73,0xc,0xb4,0x42,0x1e,0xc4,0x8f,0xa4,0xd5,0x1b,0x15,0x11,0xea,0x61,0x9b,0xeb,0xee,0x57,0xcf,0xf9,0x7a,0x62,0xa8,0xb6,0xf3,0x26,0x3d,0xef,0x5d,0x46,0x81,0x86,0xbe,0x4f,0xe8,0x9a,0x93,0x71,0x34,0x3b,0xf4,0x38,0x88,0x38,0xf4,0x69,0xa4,0x91,0x95,0x6c,0xdf,0x26,0x88,0x95,0x2c,0xc6,0x24,0xb1,0xed,0x8d,0x52,0xda,0xe8,0x49,0x3,0x50,0xed,0xec,0xad,0xe6,0xe7,0x36,0xcd,0x8a,0x38,0xa,0x63,0x5c,0xe5,0x5f,0x8,0xd7,0x96,0x71,0x98,0xed,0x97,0x33,0x5a,0x29,0xf4,0x9e,0x83,0x38,0x46,0x61,0xa0,0x9a,0xe7,0xd5,0xc8,0x44,0x95,0xc2,0x37,0x97,0x85,0x5,0x3a,0xac,0x35,0xb3,0x67,0x26,0xdf,0xee,0x82,0x79,0xbe,0xa8,0xd5,0x76,0x98,0x18,0x34,0xe3,0xdc,0x2c,0xd7,0xd6,0xbb,0x2e,0x41,0xa2,0xc3,0xb0,0x82,0xea,0x77,0x45,0xe8,0xd6,0x5d,0xf5,0x91,0xa0,0xb3,0xbf,0x8d,0x2e,0xfb,0x38,0x30,0x29,0x87,0xdb,0xa2,0x70,0x94,0x54,0xfb,0x55,0xee,0x37,0x2a,0xf6,0x23,0xb9,0x1b,0xed,0xe9,0xf2,0x16,0x50,0xd,0x4d,0x64,0xdf,0xed,0x5b,0x8f,0xa0,0xf0,0x7f,0x9b,0x68,0x49,0x0,0x94,0x3c,0xc9,0xcd,0xcb,0x60,0x53,0xa1,0x79,0xea,0xe6,0x36,0x9a,0x77,0x46,0x5,0xb,0xc3,0xa1,0xed,0x4,0xfc,0x23,0xf3,0x48,0xda,0x67,0x6e,0x70,0xd2,0x2c,0x6f,0x7,0xc1,0x45,0xe2,0xf5,0x5b,0x7b,0xd,0x76,0x71,0x86,0x1c,0x63,0x24,0xfc,0xa2,0xb2,0x86,0x5,0x7b,0x1e,0x1,0xa2,0xc1,0x28,0xb6,0xfc,0x41,0x84,0x24,0x60,0x4e,0x4b,0x81,0x43,0xc4,0x29,0xb4,0xc0,0x2,0x4c,0x4,0x60,0xd9,0xf0,0x12,0x93,0x35,0x26,0xe7,0x15,0x14,0x45,0xd,0x99,0xa6,0x91,0x5f,0x77,0x88,0xc,0xec,0xd,0x69,0x8e,0xa3,0x14,0x57,0x34,0xb5,0xa7,0x99,0x56,0x50,0x81,0x2c,0x5a,0x9f,0xd2,0xb9,0x2f,0x5c,0x5f,0x4d,0x9e,0x59,0xde,0xe8,0x4a,0x76,0x6b,0xde,0x11,0xc7,0x13,0xba,0x91,0x2b,0xb6,0xf7,0x4f,0x4d,0xdc,0x32,0xda,0x15,0xa3,0x39,0x64,0xae,0xda,0x3a,0x48,0xd5,0x1a,0x7e,0x27,0x88,0x38,0xa4,0x84,0xbf,0x70,0x94,0x83,0xff,0x64,0x32,0x62,0x90,0x81,0x64,0x71,0x0,0xa5,0x3d,0x2c,0x72,0x8d,0x83,0xc0,0xf9,0x86,0x88,0x7d,0x7,0xce,0x22,0x8c,0x33,0x5,0xee,0xa0,0xc4,0xc,0xa,0xa1,0xd8,0x16,0xd4,0xd7,0xe2,0xeb,0x7d,0xb5,0x16,0x52,0x43,0xba,0x79,0xf8,0x39,0x86,0x7d,0x8,0xb4,0xc4,0x8c,0x95,0xfc,0xd8,0xae,0x66,0xa8,0x74,0xbd,0xf6,0x74,0xb2,0xa9,0xa7,0x2d,0x87,0x33,0x40,0x74,0xbe,0xa5,0x46,0x8f,0x7e,0xe6,0x86,0x39,0xcc,0x9,0x40,0xa9,0x25,0xcc,0x2e,0x1f,0x8a,0x54,0x91,0x8d,0xc1,0x1,0x42,0x4c,0x14,0xa,0x58,0x42,0xbe,0xd0,0x50,0x33,0x43,0xaf,0xcd,0x19,0x22,0xa2,0x2e,0xd4,0x67,0xbd,0xb0,0xc4,0xdc,0xae,0xb6,0x6a,0x2,0x76,0xf8,0x79,0xff,0xbb,0xb2,0x84,0x87,0x73,0x48,0xed,0x5e,0x39,0xb7,0x42,0x86,0xa2,0xe2,0xe,0x2f,0x3,0xc6,0x9a,0x7b,0x95,0x87,0x4e,0xed,0xc,0x21,0x5e,0xd7,0x82,0x65,0x2,0x7f,0x93,0x27,0x5,0xa,0x54,0xaf,0xfb,0x94,0x19,0xb5,0x61,0x32,0x16,0x71,0x4d,0xea,0x39,0x68,0xe5,0xa2,0x41,0xf9,0x68,0x2c,0x7b,0x50,0x9e,0xfe,0x4b,0x1e,0x3b,0x32,0xb0,0xea,0x8a,0xb9,0x7f,0xd7,0x72,0x96,0x1b,0x70,0x63,0x54,0xf8,0xcf,0x4d,0x62,0x31,0x18,0xce,0x83,0x72,0x65,0x2,0x4e,0xc6,0xa9,0xa8,0x5a,0x4b,0xa2,0x4b,0xcb,0xe1,0x89,0x4f,0x96,0xfa,0x19,0x80,0xbb,0x76,0x7d,0x97,0xd7,0xe0,0x6e,0xf,0x33,0x2,0xe5,0xdd,0x70,0xd9,0xb4,0x9d,0x6e,0x7,0xa6,0xca,0xef,0xa7,0x50,0x7f,0x60,0x91,0xfc,0xc6,0x76,0x7e,0x8e,0x2f,0x1,0xc7,0xf,0x57,0x99,0xf3,0xf7,0xcb,0x45,0xd2,0x3,0x9b,0xc,0x39,0x3f,0x64,0x15,0xa6,0xc5,0x73,0x2,0x73,0x98,0x6a,0x13,0x97,0x10,0xe5,0x79,0x15,0xff,0x15,0x57,0xc3,0xbd,0x52,0x12,0x92,0x7,0x22,0xaf,0xba,0xdc,0xec,0xe8,0x4d,0x6c,0x1f,0x81,0x8f,0x4b,0xb5,0x5d,0xa,0xa2,0xff,0x3a,0x8d,0xf4,0x95,0xe7,0x64,0x63,0xf0,0xfe,0x5b,0x4e,0xb0,0x77,0x6e,0x5a,0x2d,0x73,0x33,0xf5,0x53,0xd5,0xa3,0x49,0xfa,0x54,0x5,0x94,0xbe,0x6d,0xd4,0xda,0xc6,0x81,0x62,0x3e,0x1b,0x2,0x8e,0x19,0xff,0x30,0x58,0x11,0x93,0x63,0x74,0x96,0x97,0x8d,0x95,0x4e,0x22,0x33,0x23,0xf8,0x74,0x6a,0x74,0x70,0x68,0x8a,0x56,0xdd,0xbd,0x46,0xd6,0xc1,0xfa,0xbc,0xd9,0xa3,0x12,0x79,0x69,0x73,0xf,0xd4,0x5a,0xa3,0x92,0x33,0x59,0xb,0xb3,0xf5,0xab,0x76,0x8b,0x8f,0xaa,0xbb,0x80,0x3f,0x32,0xac,0x1e,0x64,0xe0,0xdf,0xcf,0x39,0x3a,0x9b,0xd3,0x11,0x26,0x9d,0x24,0x90,0xb2,0x26,0xaa,0xb1,0xb0,0x35,0x21,0x7f,0xa3,0xe5,0x89,0xa1,0x3,0xb9,0x5f,0x2e,0xab,0xcb,0xd7,0x85,0xaf,0x6a,0xda,0xa3,0x30,0x34,0x72,0x2e,0x76,0x17,0x6b,0xeb,0x19,0x20,0x32,0xfa,0x14,0x8c,0xe3,0x53,0xc5,0xd6,0xe4,0x32,0xed,0x69,0xb4,0x37,0x64,0x5f,0xcf,0x30,0xca,0x14,0x75,0x3d,0x11,0x4d,0x58,0x69,0x72,0x94,0xd3,0x8,0xb3,0x92,0x56,0x96,0xad,0xd0,0x7a,0xea,0xdd,0xaa,0x4a,0x18,0x12,0xa8,0xf3,0xc2,0xa1,0x61,0x1f,0xf2,0xfe,0xf6,0x4,0x19,0xf0,0xb0,0x0,0x8,0x17,0x58,0x24,0x65,0x7f,0x7d,0xf5,0xc1,0x2a,0xc7,0xd7,0x70,0x93,0xb1,0x36,0x12,0xff,0x78,0xa8,0x47,0x4c,0xbe,0x31,0xef,0x70,0x6,0x7b,0xb8,0xa4,0xda,0x35,0x84,0x4a,0x36,0x16,0xcf,0x54,0x95,0xc0,0x15,0x7,0xc2,0xb0,0x64,0x8e,0xb2,0x28,0x87,0xae,0xfd,0x53,0x3,0x30,0x1b,0xe3,0xba,0x91,0xe5,0x5a,0xc5,0xae,0x3e,0xae,0x5d,0x7f,0x82,0x5f,0x9c,0x48,0x88,0x96,0xbf,0xbd,0x3b,0xf,0xd9,0x0,0x51,0x44,0xb1,0xcf,0x75,0x9d,0x2c,0x7e,0x6f,0x4a,0x44,0xdd,0x6f,0x8e,0x76,0x92,0x68,0x95,0xc,0x11,0xb6,0x3f,0x25,0x66,0xb7,0xb6,0xe5,0x38,0xb0,0x5c,0x23,0xa6,0x7f,0xbc,0xd1,0xba,0xca,0x24,0x85,0x6e,0x91,0x6e,0x22,0x90,0x84,0x6,0xbf,0x37,0xb6,0x86,0xcb,0xf3,0x3a,0x7,0xf6,0xa5,0xd6,0x75,0x3e,0x5f,0x2e,0x40,0xb4,0xcf,0x48,0xa2,0x9a,0x1b,0x6c,0x3d,0xa9,0x6a,0x65,0x9f,0x27,0x6d,0x7d,0x7e,0x7c,0x57,0xf9,0x9,0xb6,0xe2,0x18,0xd4,0x8e,0x4d,0x8c,0xe4,0x6e,0xb9,0xb9,0x32,0xab,0x1e,0x44,0x4e,0xbf,0xec,0x75,0x13,0xa1,0xc3,0x9b,0xa4,0xc4,0xb8,0xcc,0xe6,0xeb,0xed,0x5d,0xd4,0xf0,0x84,0x5,0xbf,0x2d,0xdf,0xbd,0x2c,0x4e,0xe,0x1a,0x18,0x7b,0x74,0x19,0x3,0x13,0x64,0xdd,0x1c,0xd0,0x67,0x44,0x8a,0xf7,0x27,0x6,0x65,0xca,0xb0,0x30,0xe2,0x7e,0xf,0x1e,0xc1,0xe6,0xaa,0xca,0xb3,0x50,0xb9,0xd1,0x4b,0xb6,0x23,0x6c,0x90,0x89,0xf8,0x8e,0xca,0x91,0xb9,0x14,0xe0,0xe8,0x39,0x9d,0x43,0x66,0x25,0xa1,0xa3,0x66,0x51,0x21,0x10,0xd2,0x53,0x31,0xe9,0x31,0x7a,0x69,0xc6,0xa6,0x46,0x7e,0xd,0xb5,0x24,0x0,0x83,0x9a,0x24,0xc6,0x40,0x93,0x40,0x4b,0x90,0x83,0xf3,0x58,0x3d,0x7,0xd3,0xe8,0x40,0x5,0x17,0xd4,0xbe,0x3d,0x20,0x7b,0x86,0xc3,0xb1,0xb8,0x53,0xcb,0x56,0x8,0x27,0x55,0x5e,0x60,0xa6,0xc0,0x26,0xd5,0xe8,0x98,0x9b,0x6d,0x74,0x7b,0xb3,0xef,0x23,0x35,0x11,0x39,0x70,0xe9,0xec,0x4b,0x35,0x15,0xc6,0x77,0x34,0x9c,0x76,0x11,0xfa,0x88,0x4f,0xe7,0xd6,0x37,0x10,0xbb,0x67,0x9,0x5f,0xf6,0xad,0x47,0x70,0xd1,0x79,0xd4,0x75,0x93,0xfb,0x33,0x5a,0x2b,0x91,0xdd,0x81,0xe6,0x26,0x30,0x21,0xa2,0xad,0x54,0x8e,0xb3,0x2f,0xb5,0xa4,0xac,0x3b,0xaf,0x82,0x18,0x84,0xac,0xa6,0x42,0xc1,0x77,0x89,0xa8,0x3,0x4,0xf1,0x48,0xa,0xa1,0xf9,0xbb,0xff,0x7d,0x38,0xa6,0xc6,0x22,0x35,0xdc,0x6,0x7,0x60,0x25,0x62,0x45,0x4a,0xe5,0x25,0x1c,0xe9,0x75,0x6d,0x31,0x2,0xa7,0x63,0x1f,0xbf,0x6d,0x3a,0x9d,0xc,0xc4,0xc8,0x3a,0xe7,0xfc,0x7b,0x6d,0x50,0xb7,0x93,0x26,0xdb,0x1,0xe8,0xfd,0xeb,0x4b,0x6c,0xb2,0xeb,0x51,0xe1,0x7c,0x24,0xe2,0xa1,0xc0,0x6c,0xcd,0xf7,0x72,0x2,0x52,0x49,0x86,0x6b,0xdf,0xb4,0x8f,0xfe,0x61,0xb,0x3a,0x5f,0x77,0x8e,0xee,0xe6,0xfc,0x74,0x2a,0x20,0xb6,0x24,0x9,0xc6,0xd5,0x8,0xe0,0x6,0x8d,0xf9,0xb9,0x31,0xb5,0x80,0xd0,0xb8,0x86,0xc0,0x1a,0xba,0xc8,0x32,0xfa,0x76,0x42,0xfa,0x92,0xb6,0x6f,0xfb,0x1b,0x4e,0x78,0x95,0xe0,0xe4,0xa6,0x7a,0x77,0xab,0xb3,0x96,0xa0,0xf2,0xf2,0x92,0xc6,0x84,0xa0,0x9b,0x9d,0x32,0xdf,0x60,0xa9,0xd3,0x90,0xee,0x7b,0x7d,0xb9,0xcc,0x78,0x34,0x2f,0xe5,0x99,0x5,0x4e,0x3f,0x8e,0x69,0x60,0xb,0xb3,0xc0,0xba,0x4a,0xd7,0x95,0xee,0xf9,0x68,0x6f,0x98,0xa2,0x29,0xb8,0x90,0x1f,0x9f,0xaf,0xdb,0xc7,0xaa,0xec,0xb1,0x5d,0x51,0x24,0xd7,0x63,0x23,0x2f,0xdf,0xa0,0x66,0xe7,0x74,0xa4,0xad,0x86,0xdc,0xae,0xfc,0xe0,0x38,0xa7,0xb9,0xe8,0x60,0xca,0x12,0x45,0x81,0x4b,0x85,0x6d,0x5e,0x45,0xce,0x76,0x48,0x4c,0x23,0x90,0xcb,0x6,0x10,0xf7,0x31,0xcd,0xcf,0x7f,0x1d,0xd9,0x8e,0x0,0x45,0x7f,0x14,0x73,0x4d,0xb1,0xbb,0x21,0xaa,0xd3,0x36,0x77,0xd5,0x97,0xd4,0x57,0x37,0xc2,0x2f,0xff,0xbe,0xa8,0xb5,0xc5,0xf5,0xd3,0x84,0x72,0xec,0x38,0xa9,0x4a,0x8b,0x11,0x6b,0x6b,0x92,0xe8,0x9d,0xd2,0x8,0xc1,0x7d,0x28,0x16,0xb7,0xa6,0x75,0x29,0xaf,0x9f,0xc9,0xcd,0x3,0xcb,0x92,0x85,0xe,0x90,0x12,0x97,0x6d,0xd5,0xd,0xbb,0x9a,0x68,0x5,0x2a,0x74,0xb4,0xca,0x55,0x8f,0xf0,0xb9,0x37,0x7e,0xa,0x12,0x94,0x14,0x40,0xd2,0xa3,0x48,0xb,0x74,0x9a,0xa0,0x2f,0xb8,0x67,0x71,0x66,0x54,0x75,0x58,0xc9,0x4b,0xee,0x90,0x4a,0x2b,0x8f,0x56,0xd6,0xc1,0x54,0xf,0x1a,0x2e,0x59,0xee,0xfe,0xad,0xd5,0xbb,0xb7,0x49,0x91,0x49,0x3d,0xbf,0xee,0xb6,0x1c,0x7c,0xa5,0xc4,0xc,0x25,0xd0,0xbf,0x64,0xa7,0xcb,0xd0,0xf8,0x62,0x96,0xf,0x87,0xc5,0xe3,0xe3,0xf4,0x68,0xe4,0x84,0x89,0xf6,0xc6,0xd,0x2f,0x9a,0xf3,0x4e,0xd8,0x86,0x36,0x1d,0xb7,0xef,0xae,0xed,0x10,0x89,0xb6,0x75,0x6b,0x39,0x31,0x50,0x7a,0xee,0x71,0x65,0xaa,0xa4,0x8a,0xc9,0x38,0x9b,0x72,0xd6,0x8c,0xea,0xa2,0xd2,0x9c,0x43,0xe6,0xb6]";
        String iuk = "[0x40,0x0,0x0,0x0,0x41,0x7,0x80,0xbf,0xe3,0xb2,0x43,0x78,0xc2,0x6d,0x26,0x31,0x4e,0x8e,0xd2,0xc,0x72,0x5d,0xa8,0xde,0xa6,0xc1,0x5e,0x36,0xbd,0x18,0x5f,0x5e,0xa7,0x12,0xc0,0x42,0xdf,0x28,0x8a,0x82,0xf6,0xd0,0xf2,0x2c,0xda,0x11,0x66,0x12,0xa2,0x58,0xa9,0x2d,0xeb,0xce,0x55,0x49,0x74,0xf4,0xed,0xee,0x89,0xc9,0xe,0x65,0x92,0x6e,0x10,0xb5,0xf8,0x3b,0x86,0x85,0xb0,0x33,0xea,0x98,0xff,0x7a,0xf6,0xd5,0xf7,0x10,0xfd,0x6a,0xd1,0xcb,0x42,0xf,0xef,0xed,0x8e,0x71,0xa7,0xe9,0x84,0x17,0xbf,0x50,0x8b,0xea,0xa4,0xc2,0x96,0x59,0x5b,0x76,0x3d,0x5e,0x80,0x0,0x9a,0x33,0x3e,0x56,0xcf,0xb3,0xb6,0x7a,0x68,0x2,0x83,0x8c,0xc6,0x17,0xef,0x9b,0x78,0xe9,0x7e,0xab,0x71,0x6,0xb,0xbb,0xd9,0x65,0x44,0x96,0x1,0xa6,0x37,0xbd,0xc2,0x5f,0x68,0x5c,0x6b,0xb7,0x56,0xb1,0xc9,0x75,0x9,0x8b,0x1e,0xbd,0x9,0xc5,0x5f,0x98,0x5c,0x62,0x6c,0xb3,0x6a,0x33,0xac,0x24,0x75,0x57,0x6c,0x97,0x3b,0xf6,0x85,0x27,0x13,0x8b,0xd9,0xd3,0x72,0xbb,0xbd,0xa,0x19,0x79,0x9d,0xa8,0x7,0xe4,0xa4,0x83,0xd4,0xb1,0x0,0x5a,0xf2,0x45,0xdc,0x49,0xd,0xbc,0xc4,0xc5,0x2f,0x25,0xb3,0xa4,0x7a,0xfc,0xd0,0xc6,0x11,0x62,0x95,0x35,0xb3,0xd,0xe5,0xc8,0x1a,0x80,0xdf,0x8f,0xcd,0x6e,0x74,0x6b,0x88,0x2c,0x81,0x7e,0x7,0x2a,0xef,0xdc,0xb4,0x20,0x41,0xb5,0x87,0xc4,0xb2,0xb4,0xe3,0x25,0x51,0x39,0x90,0xe5,0x48,0xef,0x6e,0x1c,0x4f,0x33,0x67,0xc5,0x45,0xa9,0xd8,0x3c,0x4a,0x7a,0x9f,0xd1,0x20,0xe6,0xcb,0x6a,0xb,0x31,0x9a,0xd,0xcd,0x95,0xca,0x5e,0x18,0x4b,0xdc,0x4a,0x9,0xf3,0x49,0x53,0xc1,0xe2,0x28,0xaf,0xec,0x6f,0x76,0x81,0x11,0x24,0xe8,0xcc,0x9d,0x7,0xf,0xa5,0xd1,0x43,0xea,0x1b,0x4a,0x35,0x3c,0xde,0x28,0xb7,0xf5,0xea,0x1,0x27,0xee,0x59,0xe1,0x4c,0x7e,0x91,0xe5,0xfd,0xd1,0x96,0x9e,0x85,0x8c,0xbc,0xb7,0x82,0x5a,0xba,0xae,0x9,0x26,0x7e,0x33,0x23,0xb3,0xda,0x9d,0x5d,0x7f,0x47,0x9d,0x28,0x36,0x97,0xc3,0xb7,0x7a,0xa0,0x7f,0x9e,0xc9,0xb2,0x3e,0xa8,0x39,0x99,0x57,0xf,0x63,0x6d,0x37,0x3c,0x3f,0x3a,0xa3,0xbd,0x15,0x9c,0x3e,0x36,0x63,0xda,0x1b,0x21,0x2d,0x34,0x74,0x70,0x3c,0xbb,0x69,0x53]";
        byte[] certbs = CertKeyUtil.getLicKey();
        StringBuffer certsb = new StringBuffer();
        certsb.append("[");
        for (byte b: certbs) {
            String str = "" + b;
            certsb.append("[".equals(certsb.toString())?"":",").append(str);
        }
        certsb.append("]");
        byte[] custbs = CustomerKeyUtil.getLicKey();
        StringBuffer custsb = new StringBuffer();
        custsb.append("[");
        for (byte b: custbs) {
            String str = "" + b;
            custsb.append("[".equals(custsb.toString())?"":",").append(str);
        }
        custsb.append("]");
        String userNo = "905159200027";
        JSONObject json = new JSONObject();
        json.put("user_no", userNo);
        json.put("android_app_key", certsb.toString());
        json.put("android_user_key", custsb.toString());
        json.put("ios_app_key", ipk);
        json.put("ios_user_key", iuk);
        json.put("auth_num", 5);
        String tt = json.toString();
        // 加密内容长度要求需要是16的倍数，不够填充空格
        //while (tt.length() % 16 != 0) {
        //    tt = jsonStr + " ";
        //}
        System.out.println("jsonlength:" + tt.length());
        String authFile = FileUtil.append("E:\\", System.currentTimeMillis()+".ldk");
        FileUtil.createFile(authFile);
        FileUtil.appendText(authFile, AesUtils.encryptByCbc(json.toString(), userNo+"0000"));
        System.out.println("jsonAeslength:" + AesUtils.encryptByCbc(json.toString(), userNo+"0000").length());

        String sss = AesUtils.encryptByCbc(json.toString(), userNo+"0000");
        System.out.println(sss);
        String ddd = AesUtils.decryptByCbc(sss, userNo+"0000");
        System.out.println(ddd);
        JSONObject ciphertextJson = new JSONObject();
        ciphertextJson.put("module_no", 60001);
        ciphertextJson.put("file_guid", "9A00F843-89C2-4577-B82A-626C89062932");
        ciphertextJson.put("auth_num", 0);
        String jsonStr = ciphertextJson.toString();
        String str = AesUtils.encryptByCbc(jsonStr, userNo+"0000");
        System.out.println(str);
    }

}
