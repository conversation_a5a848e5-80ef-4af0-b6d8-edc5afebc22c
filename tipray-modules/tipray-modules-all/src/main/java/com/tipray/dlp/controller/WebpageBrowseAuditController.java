package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.WebpageBrowseAudit;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.WebpageBrowseAuditService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网页浏览审计策略Controller
 * <AUTHOR>
 * @Description
 * @CreateTime 2023年09月05日 09:05:00
 */
@RestController
@Oplog(WebpageBrowseAudit.class)
@RequestMapping("/webpageBrowseAudit")
public class WebpageBrowseAuditController {
    @Resource
    public WebpageBrowseAuditService webpageBrowseAuditService;

    /**
     * 分页查询网页浏览审计策略列表
     * @param vo
     * @return
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<WebpageBrowseAudit> getPage(@RequestBody StrategyVO vo) {
        return webpageBrowseAuditService.getStrategyPage(vo);
    }

    /**
     * 添加网页浏览审计策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.webpageBrowseAuditStrategy")
    @PostMapping(value = "add")
    public WebpageBrowseAudit add(@RequestBody WebpageBrowseAudit bean) {
        List<Map<String, Long>> rules = getRules(bean);
        bean.setRules(rules);
        webpageBrowseAuditService.insert(bean);
        return bean;
    }

    /**
     * 修改网页浏览审计策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.webpageBrowseAuditStrategy")
    @PostMapping(value = "update")
    public WebpageBrowseAudit update(@RequestBody WebpageBrowseAudit bean) {
        List<Map<String, Long>> rules = getRules(bean);
        bean.setRules(rules);
        webpageBrowseAuditService.update(bean);
        return bean;
    }

    public List<Map<String, Long>> getRules(WebpageBrowseAudit bean){
        List<Long> webpageMatchRuleIds = bean.getWebpageMatchRuleIds();
        List<Map<String, Long>> rules = new ArrayList<>();
        for (Long webpageMatchRuleId : webpageMatchRuleIds) {
            Map<String, Long> map = new HashMap<>();
            map.put("ruleId", webpageMatchRuleId);
            rules.add(map);
        }
        return rules;
    }

    /**
     * 删除网页浏览审计策略
     * @param ids
     */
    @Oplog(name = "pages.webpageBrowseAuditStrategy")
    @PostMapping(value = "delete")
    public void deleteById(String ids) {
        webpageBrowseAuditService.deleteById(ids);
    }

    /**
     * 根据名称查询策略  用于前端校验策略是否存在
     * @param name
     * @return
     */
    @PostMapping("getByName")
    public WebpageBrowseAudit getByName(String name) {
        return webpageBrowseAuditService.getByName(name);
    }
}
