package com.tipray.dlp.dao;

import com.tipray.dlp.bean.TelnetMonitorLog;
import com.tipray.dlp.bean.vo.TelnetLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * (TelnetMonitorLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-06-02 16:43:56
 */
@Repository
@ShardingDataSource
public interface TelnetMonitorLogDao extends BaseDao<TelnetMonitorLog>{

    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(TelnetLogVO vo);
     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<TelnetMonitorLog> listByVO(TelnetLogVO vo);
}
