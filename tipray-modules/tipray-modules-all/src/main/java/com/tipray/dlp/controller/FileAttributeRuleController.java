package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.FileAttributeRule;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ImportResultDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.dto.TreeNodeDTO;
import com.tipray.dlp.bean.vo.RuleVO;
import com.tipray.dlp.exception.ParamException;
import com.tipray.dlp.service.RuleService;
import com.tipray.dlp.util.CollectionUtil;
import com.tipray.dlp.util.FileUtil;
import com.tipray.dlp.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 文件属性文件属性规则Controller
 */
@RestController
@Oplog(FileAttributeRule.class)
@RequestMapping("/fileAttributeRule")
public class FileAttributeRuleController {
    @Resource
    private RuleService<FileAttributeRule> ruleService;

    /**
     * 新增文件属性规则
     * @param rule
     * @return
     */
    @Oplog(name = "pages.fileRule")
    @PostMapping(value = "add")
    public FileAttributeRule addRule(@RequestBody FileAttributeRule rule) {
        ruleService.insertRule(rule);
        return rule;
    }

    /**
     * 修改文件属性规则
     * @param rule
     * @return
     */
    @Oplog(name = "pages.fileRule")
    @PostMapping(value = "update")
    public FileAttributeRule updateRule(@RequestBody FileAttributeRule rule) {
        ruleService.updateRule(rule);
        return rule;
    }

    /**
     * 删除文件属性规则
     */
    @Oplog(value = RuleVO.class, name = "pages.fileRule")
    @PostMapping(value = "delete")
    public ResponseDTO deleteRule(@RequestBody RuleVO vo) {
        if (null != vo.getUpdateQuery() && vo.getUpdateQuery()) {
            List<Long> ids = ruleService.getRulePage(FileAttributeRule.class, vo).getItems().stream()
                    .filter(t -> !vo.getBackupUnSelectedIds().contains(t.getId()))
                    .map(FileAttributeRule::getId).collect(Collectors.toList());
            vo.setIds(StringUtil.toInSql(ids));
        }
        Boolean filterUsed = vo.getFilterUsed();
        if (filterUsed != null && filterUsed) {
            // 查询被“规则集”引用的所有规则Id
            Set<Long> usedRules = ruleService.getAppliedRuleIdsFromRuleGroup();
            if (usedRules != null) {
                String ids = vo.getIds();
                if (StringUtils.isNotEmpty(ids)) {
                    List<String> strList = Arrays.asList(ids.split(","));
                    List<String> resList = strList.stream().filter(id -> !usedRules.contains(Long.valueOf(id))).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(resList)) {
                        String idStr = StringUtils.join(resList, ",");
                        vo.setIds(idStr);
                    } else {
                        vo.setIds("");
                    }
                }
            }
        }
        List<String> names = ruleService.listName(vo.getIds());
        // 用于记录删除文件属性规则的管理员日志
        vo.setNames(names);
        ruleService.deleteRuleByIds(vo.getIds());
        return ResponseDTO.success(vo);
    }

    @PostMapping("getByName")
    public FileAttributeRule getByName(String name) {
        return ruleService.getRuleByTypeName(FileAttributeRule.class, name);
    }
    @PostMapping(value = "listById")
    public List<FileAttributeRule> listById(String ids) {
        return ruleService.listRule(FileAttributeRule.class, ids);
    }

    @PostMapping(value = "getPage")
    public GridPageDTO<FileAttributeRule> getRulePage(@RequestBody RuleVO vo) {
        return ruleService.getRulePage(FileAttributeRule.class, vo);
    }

    /** 获取文件属性规则树*/
    @GetMapping(value = "listTree")
    public List<TreeNodeDTO> listTreeNode() {
        return ruleService.listRuleTreeNode(FileAttributeRule.class);
    }

    /**
     * 导出文件属性规则
     * @return
     */
    @Oplog(name = "pages.fileRule")
    @PostMapping(value = "export")
    public String exportRule() {
        return ruleService.exportRule(FileAttributeRule.class);
    }

    /**
     * 导入文件属性规则
     * @param file
     * @return
     */
    @Oplog(name = "pages.fileRule")
    @PostMapping(value = "import", produces = {"text/plain;charset=UTF-8"})
    public ImportResultDTO importRule(@RequestParam(value = "file", required = false) MultipartFile file,
                                      @RequestParam(value = "fileType") String fileType) {
        try {
            File destFile = FileUtil.transferImportFile(file);
            return ruleService.importRule(FileAttributeRule.class, destFile, fileType);
        } catch (IOException e) {
            throw new ParamException(e);
        }
    }
}
