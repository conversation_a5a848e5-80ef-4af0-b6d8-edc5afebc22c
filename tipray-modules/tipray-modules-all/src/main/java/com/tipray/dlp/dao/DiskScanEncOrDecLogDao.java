package com.tipray.dlp.dao;

import com.tipray.dlp.bean.BaseLog;
import com.tipray.dlp.bean.DiskScanEncOrDecLog;
import com.tipray.dlp.bean.vo.DiskScanLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * (EncOrDecLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-05-06 19:19:07
 */
@Repository
@ShardingDataSource
public interface DiskScanEncOrDecLogDao extends BaseDao<DiskScanEncOrDecLog>{

    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(DiskScanLogVO vo);
     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<DiskScanEncOrDecLog> listByVO(DiskScanLogVO vo);

    /**
     * 通过条件查询guid
     * @param vo
     * @return
     */
    List<BaseLog> listGuidByVO(DiskScanLogVO vo);
}