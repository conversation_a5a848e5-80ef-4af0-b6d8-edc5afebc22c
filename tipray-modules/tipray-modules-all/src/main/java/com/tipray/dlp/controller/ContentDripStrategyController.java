package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.ContentStgDef;
import com.tipray.dlp.bean.ContentStrategy;
import com.tipray.dlp.bean.dict.DeleteStateDict;
import com.tipray.dlp.bean.dict.StrategyDefType;
import com.tipray.dlp.bean.vo.ContentStrategyVO;
import com.tipray.dlp.service.ContentStrategyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 零星检测策略Controller
 * 零星检测策略(ContentStrategy)表控制层
 *
 * <AUTHOR>
 * @since 2022-09-28
 */
@Oplog(ContentStgDef.class)
@RestController
@RequestMapping("/contentDripStrategy")
public class ContentDripStrategyController {
    /**
     * 服务对象
     */
    @Resource
    private ContentStrategyService contentStrategyService;

    /**
     * 新增零星检测策略
     * @param bean
     * @return
     */
    @Oplog(name = "route.dripContent")
    @PostMapping(value = "add")
    public ContentStrategy insert(@RequestBody ContentStrategy bean) {
        if(StrategyDefType.prepare.getCode().equals(bean.getStrategyDefType())){
            // 预定义策略直接设置为已启用
            bean.setActive(true);
        }
        contentStrategyService.insertStrategy(bean);
        return bean;
    }

    /**
     * 修改零星检测策略
     * @param bean
     * @return
     */
    @Oplog(name = "route.dripContent")
    @PostMapping(value = "update")
    public ContentStrategy update(@RequestBody ContentStrategy bean) {
        contentStrategyService.updateStrategy(bean);
        return bean;
    }

    /**
     * 删除零星检测策略
     * @param ids
     * @return
     */
    @Oplog(name = "route.dripContent")
    @PostMapping(value = "delete")
    public DeleteStateDict deleteById(String ids) {
        DeleteStateDict state = contentStrategyService.getDelState(ids);
        if(DeleteStateDict.enable.equals(state)){
            contentStrategyService.deleteStrategyById(ids);
        }
        return state;
    }

    /**
     * 修改零星检测策略状态
     * @param vo
     */
    @Oplog(value = ContentStrategyVO.class, name = "pages.sporadicDetectionStrategyStatus", defaultArg = true)
    @PostMapping(value = "updateStatusBatch")
    public void updateStatusBatch(ContentStrategyVO vo) {
        contentStrategyService.updateStatusBatch(vo);
    }
}
