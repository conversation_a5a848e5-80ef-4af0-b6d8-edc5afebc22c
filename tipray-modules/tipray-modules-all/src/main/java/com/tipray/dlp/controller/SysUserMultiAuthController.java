package com.tipray.dlp.controller;

import com.alibaba.fastjson.JSONObject;
import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.SysUserMultiAuthForm;
import com.tipray.dlp.bean.dict.OperationTypeDict;
import com.tipray.dlp.bean.dict.SysUserMultiAuthConstant;
import com.tipray.dlp.bean.dto.MultiLoginAuthFormDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.dto.SysUserMultiAuthDTO;
import com.tipray.dlp.controller.websocket.SysUserMultiAuthWebSocketApi;
import com.tipray.dlp.exception.ServiceException;
import com.tipray.dlp.service.SysUserMultiAuthService;
import com.tipray.dlp.util.*;
import com.tipray.dlp.websocket.annotation.WSocketRequestId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/20 17:29
 */
@Slf4j
@RestController
@RequestMapping("/sysUserMultiAuth")
public class SysUserMultiAuthController {

    @Resource
    private SysUserMultiAuthService sysUserMultiAuthService;
    @Resource
    private SysUserMultiAuthWebSocketApi sysUserMultiAuthWebSocketApi;

    @GetMapping("/getMultiAuthDataBySysUserId")
    public SysUserMultiAuthDTO getMultiAuthDataBySysUserId(Long sysUserId) {
        if (sysUserId == null) {
            throw new ServiceException("参数错误");
        }
        return sysUserMultiAuthService.getMultiAuthData(sysUserId);
    }

    @GetMapping("/getMultiAuthData")
    public SysUserMultiAuthDTO getMultiAuthData() {
        return sysUserMultiAuthService.getMultiAuthData(ThreadVariable.getUserId());
    }

    /**
     * 配置多重登录认证信息
     * @param sysUserMultiAuthDTO
     * @return
     */
    @Oplog(value = SysUserMultiAuthDTO.class, type = OperationTypeDict.ADD, defaultArg = true, name = "pages.multiAuth")
    @PostMapping("/settingMultiAuthData")
    public ResponseDTO settingMultiAuthData(@RequestBody SysUserMultiAuthDTO sysUserMultiAuthDTO) {
        if (sysUserMultiAuthDTO == null || CollectionUtil.isEmpty(sysUserMultiAuthDTO.getSysUserIds())) {
            throw new ServiceException("配置信息错误");
        }
        sysUserMultiAuthService.settingMultiAuthData(sysUserMultiAuthDTO);
        return ResponseDTO.success();
    }

    /**
     * 获取当前用户可配置的多重登录认证用户
     * @return
     */
    @GetMapping("/getMultiAuthSysUser")
    public List<Map<String, Object>> getMultiAuthSysUser() {
        return sysUserMultiAuthService.getMultiAuthSysUser();
    }

    /**
     * 多重登录认证校验
     *
     * @param form
     * @return
     */
    @PostMapping("/verifyMultiLoginAuth")
    public MultiLoginAuthFormDTO verifyMultiLoginAuth(@RequestBody SysUserMultiAuthForm form) {
        if (form == null) {
            throw new ServiceException(I18nUtils.get("pages.multiLoginAuth_message15"));
        }
        try {
            // 前端对账号和密码进行特殊处理后才传给后端。
            String usernamePwd = AesUtils.decryptByCbc(form.getUsername(), "tr838408userpass");
            JSONObject json = JSONObject.parseObject(usernamePwd);
            form.setUsername(json.getString("username"));
            form.setPassword(json.getString("password"));
        } catch (Exception e) {
            log.error("多重登录认证校验错误 参数：{} 错误原因： {}", form, e.getMessage());
            throw new ServiceException(I18nUtils.get("pages.multiLoginAuth_message15"));
        }
        return sysUserMultiAuthService.verifyMultiLoginAuth(form);
    }


    /**
     * @param macId  唯一表示，代码当前缓存
     * @param macs   物理地址
     */
    @MessageMapping("/getMacAddress")
    @WSocketRequestId
    public void getMacAddress(String macId, String macs){
        //  保存物理地址
        CacheUtil.getCache(SysUserMultiAuthConstant.MAC_ADDRESS_KEY + macId, macs);
        sysUserMultiAuthWebSocketApi.toMacSuccess(macId, "success");
    }

}
