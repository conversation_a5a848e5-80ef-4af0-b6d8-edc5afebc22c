 package com.tipray.dlp.controller;

 import cn.hutool.core.map.MapUtil;
 import cn.hutool.core.util.StrUtil;
 import com.tipray.dlp.alqs.handler.EntityHandlerDelegate;
 import com.tipray.dlp.annotation.Oplog;
 import com.tipray.dlp.bean.AuditLogBean;
 import com.tipray.dlp.bean.AuditLogUnifiedResult;
 import com.tipray.dlp.bean.BurnLog;
 import com.tipray.dlp.bean.Scene;
 import com.tipray.dlp.bean.dict.AuditLogDict;
 import com.tipray.dlp.bean.dto.AuditLogDetailDTO;
 import com.tipray.dlp.bean.dto.GridPageDTO;
 import com.tipray.dlp.bean.dto.ResponseDTO;
 import com.tipray.dlp.bean.vo.AuditLogPolymerizationDetailQueryVO;
 import com.tipray.dlp.bean.vo.AuditLogPolymerizationQueryVO;
 import com.tipray.dlp.bean.vo.AuditLogSceneQueryVO;
 import com.tipray.dlp.bean.vo.BurnLogVO;
 import com.tipray.dlp.dao.BurnLogDao;
 import com.tipray.dlp.exception.ServiceException;
 import com.tipray.dlp.mybatis.export.ParameterHelper;
 import com.tipray.dlp.service.AuditLogQueryService;
 import com.tipray.dlp.support.scene.SceneManager;
 import com.tipray.dlp.util.BeanUtil;
 import com.tipray.dlp.util.DateUtil;
 import com.tipray.dlp.util.LogUtil;
 import com.tipray.dlp.util.SceneUtil;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.collections4.CollectionUtils;
 import org.apache.commons.lang3.StringUtils;
 import org.springframework.web.bind.annotation.*;

 import javax.annotation.Resource;
 import java.text.SimpleDateFormat;
 import java.util.Date;
 import java.util.HashMap;
 import java.util.List;
 import java.util.Map;
 import java.util.stream.Collectors;

/**
 * 审计日志统一查询Controller
 * <pre>
 * 本Controller 主要提供列表查询，详情，相关搜索等接口，在列表查询前一般需要统计各审计日志分类数量，
 * 详情请查看 {@link com.tipray.dlp.controller.websocket.AuditLogQueryWebSocketApi}
 * </pre>
 * <AUTHOR>
 * @date 2024/7/26
 */
@RestController
@RequestMapping("/auditLogUnifiedQuery")
@Slf4j
public class AuditLogUnifiedQueryController {

    @Resource
    private AuditLogQueryService auditLogQueryService;
    @Resource
    private EntityHandlerDelegate entityHandlerDelegate;
    @Resource
    private BurnLogDao burnLogDao;
    @Resource(name = "cacheUserSceneManager")
    private SceneManager sceneManager;

    /**
     * 聚合日志搜索 ，查询分类列表数据 <br/>
     * @param queryVO
     * @return
     */
    @PostMapping(value = "/getPage2")
    @Oplog(value= AuditLogPolymerizationQueryVO.class, name = "审计日志聚合搜索-查询某个审计类型")
    public GridPageDTO<AuditLogBean> searchV2(@RequestBody AuditLogPolymerizationQueryVO queryVO) {
        // sql注入处理
        String finalKeyword = ParameterHelper.escapeSqlInjection(queryVO.getKeyword());
        queryVO.setKeyword(finalKeyword);

        LogUtil.formatLogVO(queryVO);

        // 转化为 AuditLogSceneQueryVO
        AuditLogSceneQueryVO vo = new AuditLogSceneQueryVO();
        BeanUtil.copyProperties(queryVO, vo);

        return auditLogQueryService.query(vo);
    }

    /**
     * 获取场景描述
     * @param sceneName
     * @return
     */
    @GetMapping("/getSceneDescription/{sceneName}")
    public ResponseDTO getSceneDescription(@PathVariable String sceneName) {
        Scene scene = sceneManager.getScene(sceneName);
        if (scene == null) {
            throw new ServiceException("没有相应的场景配置");
        }
        String description = buildDescription(scene);
        return ResponseDTO.success(description);
    }

    private String buildDescription(Scene scene) {
        StringBuilder builder = new StringBuilder();
        builder.append("场景:").append("<b>").append(scene.getDescription()).append("</b>").append("<br/>");
        builder.append("-----------------------------------------------").append("<br/>");
        builder.append("查询以下菜单及对应字段信息:").append("<br/>");
        for (int i = 0; i < scene.getAudits().size(); i++) {
            Scene.Audit audit = scene.getAudits().get(i);
            builder.append(i + 1).append("、").append(audit.getDescription())
                    .append("(")
                    .append(String.join(",", SceneUtil.getFieldDescription(audit)))
                    .append(")")
                    .append("<br/>");
        }
        return builder.toString();
    }
    /**
     * 获取场景下拉框数据
     * @return
     */
    @GetMapping("/sceneOptions")
    public List<Map<String, String>> getSceneForOption() {
        Map<String, Scene> userSceneMap = sceneManager.getAllScenes();
        return userSceneMap.values().stream()
                .map(scene -> MapUtil.<String, String>builder()
                        .put("label", scene.getDescription())
                        .put("value", scene.getName())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 获取详情
     * @return
     */
    @PostMapping("/getDetail")
    @Oplog(value = AuditLogPolymerizationDetailQueryVO.class, name = "审计日志聚合搜索-查询审计日志详情")
    public AuditLogDetailDTO getDetail(@RequestBody AuditLogPolymerizationDetailQueryVO vo) {
        AuditLogDetailDTO result = new AuditLogDetailDTO();
        Map<String, Object> map = auditLogQueryService.queryOne(vo.getId());
        if (map == null) {
            throw new ServiceException("数据不存在或已被删除");
        }
        AuditLogDict dict = getDictByName(vo.getSceneDetail());
        Map<String, Object> finalMap = entityHandlerDelegate.handle(map, dict);
        result.setData(finalMap);
        result.setComponent(StrUtil.toCamelCase(dict.name().toLowerCase()) + "Detail");
        return result;
    }

    /**
     * 在跳转相关搜索前，需要整合跳转所需要的参数。
     * @param vo
     * @param sceneName
     * @param sceneDetail
     * @return
     */
    @PostMapping("/getParams/{sceneName}-{sceneDetail}")
    public Map<String, Object> getParams(@RequestBody AuditLogUnifiedResult.AuditLogUnifiedData vo,@PathVariable String sceneName, @PathVariable String sceneDetail) {
        AuditLogDict dict = getDictByName(sceneDetail);
        String id = vo.getId();
        // 查询数据
        Map<String, Object> map = auditLogQueryService.queryOne(id);
        // 通过委托处理得到最终实体的数据
        Map<String, Object> finalMap = entityHandlerDelegate.handle(map, dict);
        Scene scene = sceneManager.getScene(sceneName);
        Scene.Audit audit = SceneUtil.getAudit(scene, sceneDetail);

        List<Scene.SearchField> searchFields = audit.getSearchFields();

        Map<String, Object> params = new HashMap<>(16);
        params.put("objectType", 1);
        params.put("objectId", vo.getTermId());
        params.put("createDate", new SimpleDateFormat("yyyy-MM-dd").format(vo.getCreateDate()));

        if (CollectionUtils.isNotEmpty(searchFields)) {
            for (Scene.SearchField searchField : searchFields) {
                String fieldName = StrUtil.toCamelCase(searchField.getName());
                // 前端查询字段的名称
                String frontQueryName = StringUtils.isEmpty(searchField.getMapping()) ? fieldName : StrUtil.toCamelCase(searchField.getMapping());
                Object value = finalMap.get(fieldName);
                if (value != null) {
                    params.put(frontQueryName, value);
                }
            }
        }
        handleExtra(vo, sceneName, sceneDetail, finalMap, params);
        return MapUtil.<String, Object>builder()
                .put("viewPath", dict.getPath())
                .put("params", params).build();
    }

    /**
     * 这个方法是额外特殊处理的。
     * - 刻录机： 需要通过刻录机的burnId 查询出刻录机的id，再通过刻录机的id查询出刻录机的名称。
     * @param vo
     * @param sceneName
     * @param sceneDetail
     * @param data
     * @param params
     */
    private void handleExtra(AuditLogUnifiedResult.AuditLogUnifiedData vo, String sceneName, String sceneDetail, Map<String, Object> data, Map<String, Object> params) {
        try {
            // 如果是刻录机
            if (sceneDetail.equals(AuditLogDict.BURN_FILE_LOG.name())) {
                String burnId = (String) data.get("burnId");
                if (StringUtils.isNotEmpty(burnId)) {

                    String dateStr = (String) data.get("createTime");
                    Date date = null;
                    date = DateUtil.parse(dateStr, "yyyy-MM-dd HH:mm:ss");
                    Date createDate = DateUtil.getDayStart(date);
                    BurnLogVO burnLogVO = new BurnLogVO();
                    burnLogVO.setCreateDate(createDate);
                    burnLogVO.setBurnId(burnId);
                    burnLogVO.setIsTimes(false);
                    LogUtil.formatLogVO(burnLogVO);
                    List<BurnLog> burnLogs = burnLogDao.listByVO(burnLogVO);
                    if (CollectionUtils.isNotEmpty(burnLogs)) {
                        params.put("burnName", burnLogs.get(0).getBurner());
                    }
                }
            }
        } catch (Exception e) {
            // 抛出异常时，这里不处理。
            log.info("处理获取详情的额外信息时报错");
        }
    }

    private AuditLogDict getDictByName(String name) {
        return AuditLogDict.get(StrUtil.toUnderlineCase(name));
    }
}
