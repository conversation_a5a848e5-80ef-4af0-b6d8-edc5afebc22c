package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.SpecialSuffixStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.SpecialSuffixStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;

/**
 * 特殊文件后缀(SpecialFileExtStrategy)表控制层
 *
 * <AUTHOR>
 * @since 2019-10-21 09:55:30
 */
@Oplog(SpecialSuffixStrategy.class)
@RestController
@RequestMapping("/specialSuffixStrategy")
public class SpecialSuffixStrategyController {
    /**
     * 服务对象
     */
    @Resource
    private SpecialSuffixStrategyService specialFileExtStrategyService;

    /**
     * 新增特殊文件后缀过滤策略
     * @param bean
     * @return
     */
    @Oplog(name = "route.specialSuffix")
    @PostMapping(value = "add")
    public SpecialSuffixStrategy[] insert(@RequestBody SpecialSuffixStrategy[] bean){
        specialFileExtStrategyService.insert(bean);
        return bean;
    }

    /**
     * 修改特殊文件后缀过滤策略
     * @param bean
     * @return
     */
    @Oplog(name = "route.specialSuffix")
    @PostMapping(value = "update")
    public SpecialSuffixStrategy[] update(@RequestBody SpecialSuffixStrategy[] bean){
        specialFileExtStrategyService.update(bean);
        return bean;
    }

    /**
     * 删除特殊文件后缀过滤策略
     * @param ids
     */
    @Oplog(name = "route.specialSuffix")
    @PostMapping(value = "delete")
    public void deleteById(String ids){
        specialFileExtStrategyService.deleteById(ids);
    }
    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("get/{id}")
    public SpecialSuffixStrategy getById(@PathVariable("id") Long id) {
        return specialFileExtStrategyService.getStrategyById(id);
    }

    @PostMapping("getByName")
    public Collection<SpecialSuffixStrategy> getByName(String name) {
        return specialFileExtStrategyService.listByName(name);
    }

    @PostMapping(value = "getPage")
    public GridPageDTO<SpecialSuffixStrategy> getPage(@ModelAttribute StrategyVO vo){
        return specialFileExtStrategyService.getPage(vo);
    }

}