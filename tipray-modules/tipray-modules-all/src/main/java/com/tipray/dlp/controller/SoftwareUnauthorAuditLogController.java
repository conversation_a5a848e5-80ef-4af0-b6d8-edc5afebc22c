package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.SoftwareUnauthorAuditLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.SoftwareUnauthorAuditLogVO;
import com.tipray.dlp.service.SoftwareUnauthorAuditLogService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 软件未授权卸载记录Controller
 */
@Oplog(SoftwareUnauthorAuditLog.class)
@RestController
@RequestMapping("/log/softwareUnauthorAuditLog")
public class SoftwareUnauthorAuditLogController {
    @Resource
    private SoftwareUnauthorAuditLogService softwareUnauthorAuditLogService;

    /**
     * 分页查询软件/安装卸载限制记录
     * @param vo
     * @return
     */
    @Oplog(value = SoftwareUnauthorAuditLogVO.class,name = "pages.SoftwareUnauthorAuditLog")
    @PostMapping(value = "getPage")
    public GridPageDTO<SoftwareUnauthorAuditLog> getPage(@RequestBody SoftwareUnauthorAuditLogVO vo){
        return softwareUnauthorAuditLogService.getSoftwareUnauthorAuditLogPage(vo);
    }

    /**
     * 导出软件安装/卸载限制记录
     * @param vo
     * @return
     */
    @Oplog(value = SoftwareUnauthorAuditLogVO.class,name = "pages.SoftwareUnauthorAuditLog")
    @PostMapping(value = "export")
    public ResponseDTO exportExcel(@RequestBody SoftwareUnauthorAuditLogVO vo) {
        return ResponseDTO.success(softwareUnauthorAuditLogService.exportExcel(vo));
    }
    /**
     * 删除日志
     * @param list
     * @return
     */
    @Oplog(name = "pages.SoftwareUnauthorAuditLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<SoftwareUnauthorAuditLog> list) {
        softwareUnauthorAuditLogService.deleteBatch(list);
        return ResponseDTO.success();
    }
}
