package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.CustomSoftAssetInfo;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.service.CustomSoftAssetInfoService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 自定义软件资产属性信息Controller
 * <AUTHOR>
 * @date 2025/5/23
 */
@Oplog(CustomSoftAssetInfo.class)
@RestController
@RequestMapping("/customSoftAssetInfo")
public class CustomSoftAssetInfoController {

    @Resource
    private CustomSoftAssetInfoService customSoftAssetInfoService;

    /** 修改自定义软件资产信息*/
    @PostMapping(value = "save")
    public ResponseDTO save(@RequestBody List<CustomSoftAssetInfo> beans){
        if (beans == null || beans.isEmpty()) {
            return ResponseDTO.success();
        }
        return customSoftAssetInfoService.saveCustomSoftAssetInfo(beans);
    }
}
