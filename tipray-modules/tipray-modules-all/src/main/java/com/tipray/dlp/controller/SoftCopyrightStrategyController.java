package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.SoftwareCopyrightStrategy;
import com.tipray.dlp.bean.dict.MsgModuleDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.exception.ParamException;
import com.tipray.dlp.service.SoftCopyrightStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 软件版权管控设置Controller
 */
@RestController
@Oplog(SoftwareCopyrightStrategy.class)
@RequestMapping("/softwareCopyrightStrategy")
@Slf4j
public class SoftCopyrightStrategyController {

    @Resource
    private SoftCopyrightStrategyService softCopyrightStrategyService;

    /**
     * 新增软件限制策略
     * @param beans
     * @return
     */
    @Oplog(name = "pages.softwareLimitStrategy")
    @PostMapping(value = "add")
    public SoftwareCopyrightStrategy[] add(@RequestBody SoftwareCopyrightStrategy[] beans) {
        for (SoftwareCopyrightStrategy bean : beans){
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.SOFTWARE_CHARGE.getCode());
        }
        softCopyrightStrategyService.insert(beans);
        return beans;
    }

    /**
     * 修改软件限制策略
     * @param beans
     * @return
     */
    @Oplog(name = "pages.softwareLimitStrategy")
    @PostMapping(value = "update")
    public SoftwareCopyrightStrategy[] update(@RequestBody SoftwareCopyrightStrategy[] beans) {
        for (SoftwareCopyrightStrategy bean : beans){
            if(bean.getId() == null){
                throw new ParamException("参数错误");
            }
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.SOFTWARE_CHARGE.getCode());
        }

        softCopyrightStrategyService.update(beans);
        return beans;
    }

    /**
     * 删除软件限制策略
     * @param ids
     */
    @Oplog(name = "pages.softwareLimitStrategy")
    @PostMapping(value = "delete")
    public void deleteAppBlock(String ids) {
        softCopyrightStrategyService.deleteById(ids);
    }

    @GetMapping("getById/{id}")
    public SoftwareCopyrightStrategy getById(@PathVariable("id") Long id) {
        return softCopyrightStrategyService.getStrategyById(id);
    }

    @PostMapping("getByName")
    public SoftwareCopyrightStrategy getByName(String name) {
        return softCopyrightStrategyService.getStrategyByName(name);
    }

    /**软件限制策略分页列表*/
    @PostMapping(value = "getPage")
    public GridPageDTO<SoftwareCopyrightStrategy> getStategyPage(@RequestBody StrategyVO vo) {
        return softCopyrightStrategyService.getStategyPage(vo);
    }

}
