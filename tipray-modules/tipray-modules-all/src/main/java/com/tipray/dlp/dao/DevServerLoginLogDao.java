package com.tipray.dlp.dao;

import com.tipray.dlp.bean.DevServerLoginLog;
import com.tipray.dlp.bean.vo.DevServerLoginVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import com.tipray.dlp.mybatis.datasource.DataSourceFactory;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 服务器设备登录日志Dao
 * <AUTHOR>
 * @date 2025-03-12 15:31
 */
@Repository
@ShardingDataSource(datasourceId = DataSourceFactory.SHARD_MASTER_DATA_SOURCE)
public interface DevServerLoginLogDao extends BaseDao<DevServerLoginLog> {

    /**
     *
     * @param condition
     * @return
     */
    Long countByVO(DevServerLoginVO condition);

    /**
     * 查询数据
     * @param condition
     * @return
     */
    List<DevServerLoginLog> listByVO(DevServerLoginVO condition);

}
