package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.DeviceInfoLib;
import com.tipray.dlp.bean.LimitFunction;
import com.tipray.dlp.bean.OtherDeviceLimitStrategy;
import com.tipray.dlp.bean.dict.DeviceEffectTypeDict;
import com.tipray.dlp.bean.dict.MsgModuleDict;
import com.tipray.dlp.bean.dict.SstTypeDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.dto.TreeNodeDTO;
import com.tipray.dlp.bean.vo.DeviceInfoLibVO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.DeviceInfoLibService;
import com.tipray.dlp.service.LimitFunctionService;
import com.tipray.dlp.service.OtherDeviceLimitService;
import com.tipray.dlp.service.StgDeviceRelService;
import com.tipray.dlp.util.TreeUtil;
import lombok.extern.slf4j.Slf4j;
//import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 其他设备限制策略Controller
 */
@Oplog(OtherDeviceLimitStrategy.class)
@RestController
@RequestMapping("/otherDeviceLimit")
@Slf4j
public class OtherDeviceLimitController {
    @Autowired
    private LimitFunctionService limitFunctionService;
    @Autowired
    private OtherDeviceLimitService service;
    @Autowired
    private DeviceInfoLibService deviceInfoLibService;
    @Resource
    private StgDeviceRelService stgDeviceRelService;

    /**
     * 新增设备使用控制策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.otherDeviceLimit")
    @PostMapping(value = "add")
    public ResponseDTO add(@RequestBody OtherDeviceLimitStrategy[] bean) {
        List<String> group = Arrays.asList("1", "2", "3", "4");
        for (OtherDeviceLimitStrategy strategy : bean) {
            //  设置敏感类型
            strategy.setSstType(SstTypeDict.GENERAL_ALARM.getCode());
            //  设置告警类型
            strategy.setAlarmType(MsgModuleDict.OTHER_DEVICE_LIMIT.getCode());
            List<Map<String, Object>> limitFunctions = new ArrayList<>();
            //  若有配置响应规则，则额外添加functionId为0的配置，表示告警信息
            if (strategy.getAlarmStgId() != null) {
                Map<String, Object> map = new HashMap<>();
                map.put("functionId", 0);
                map.put("isAlarm", strategy.getIsAlarm());
                map.put("sstType", strategy.getSstType());
                map.put("alarmType", strategy.getAlarmType());
                map.put("alarmStgId", strategy.getAlarmStgId());
                limitFunctions.add(map);
            }
            for (String funId : strategy.getSelectedIdList()) {
                if (!group.contains(funId)) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("functionId", Integer.parseInt(funId));
                    map.put("timeId", strategy.getTimeId());
                    limitFunctions.add(map);
                }
            }
            strategy.setLimitFunctionList(limitFunctions);
            // 保存设备自定义和例外时，从设备库取最新的。(界面数据可能被修改但是未刷新)
            List<DeviceInfoLib> devList = deviceInfoLibService.listDeviceInfoLib();
            List<Map<String, Object>> customAndExceptionList = strategy.getCustomAndException();
            for(int i=0;i<customAndExceptionList.size();i++){
                Map<String, Object> map = customAndExceptionList.get(i);
                List<DeviceInfoLib> resList = devList.stream().filter(dev->map.get("id").toString().equalsIgnoreCase(dev.getId().toString()))
                        .collect(Collectors.toList());
                map.put("timeId", strategy.getTimeId());
                if(null!=resList && resList.size()>0) {
                    map.put("devInstanceId", resList.get(0).getDevInstanceId());
                    map.put("devName", resList.get(0).getDevName());
                    map.put("classGuid", resList.get(0).getClassGuid());
                    map.put("checkValue", DeviceEffectTypeDict.format(map));
                }
                customAndExceptionList.set(i,map);
            }
            // limitType 1 放行 2 限制
//            if(!strategy.getCustomLimitStatus()){
//                customAndExceptionList = customAndExceptionList.stream().filter(map -> "1".equalsIgnoreCase(map.get("limitType").toString())).collect(Collectors.toList());
//            }
//            if(!strategy.getExceptionConfigStatus()){
//                customAndExceptionList = customAndExceptionList.stream().filter(map -> "2".equalsIgnoreCase(map.get("limitType").toString())).collect(Collectors.toList());
//            }
            strategy.setCustomAndException(customAndExceptionList);
        }
        service.insert(bean);
        //
        return updateRelStgDevice(bean);
    }

//    @NotNull
    private ResponseDTO updateRelStgDevice(@RequestBody OtherDeviceLimitStrategy[] bean) {
        // 修改设备和策略关联关系
        for (OtherDeviceLimitStrategy strategy : bean) {
            List<Long> list = strategy.getCustomAndException().stream().map(map->Long.parseLong(map.get("id").toString())).collect(Collectors.toList());
            stgDeviceRelService.insertOrUpdateStgDeviceRelList(strategy.getId(),list);
        }
        return ResponseDTO.success();
    }

    /**
     * 修改设备使用控制策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.otherDeviceLimit")
    @PostMapping(value = "update")
    public ResponseDTO update(@RequestBody OtherDeviceLimitStrategy[] bean) {
        List<String> group = Arrays.asList("1", "2", "3", "4");
        for (OtherDeviceLimitStrategy strategy : bean) {
            //  设置敏感类型
            strategy.setSstType(SstTypeDict.GENERAL_ALARM.getCode());
            //  设置告警类型
            strategy.setAlarmType(MsgModuleDict.OTHER_DEVICE_LIMIT.getCode());
            List<Map<String, Object>> limitFunctions = new ArrayList<>();
            //  若有配置响应规则，则额外添加functionId为0的配置，表示告警信息
            if (strategy.getAlarmStgId() != null) {
                Map<String, Object> map = new HashMap<>();
                map.put("functionId", 0);
                map.put("isAlarm", strategy.getIsAlarm());
                map.put("sstType", strategy.getSstType());
                map.put("alarmType", strategy.getAlarmType());
                map.put("alarmStgId", strategy.getAlarmStgId());
                limitFunctions.add(map);
            }
            for (String funId : strategy.getSelectedIdList()) {
                if (!group.contains(funId)) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("functionId", Integer.parseInt(funId));
                    map.put("timeId", strategy.getTimeId());
                    limitFunctions.add(map);
                }
            }
            strategy.setLimitFunctionList(limitFunctions);
            // 保存设备自定义和例外时，从设备库去最新的。
            List<DeviceInfoLib> devList = deviceInfoLibService.listDeviceInfoLib();
            List<Map<String, Object>> customAndExceptionList = strategy.getCustomAndException();
            for(int i=0;i<customAndExceptionList.size();i++){
                Map<String, Object> map = customAndExceptionList.get(i);
                List<DeviceInfoLib> resList = devList.stream().filter(dev->map.get("id").toString().equalsIgnoreCase(dev.getId().toString()))
                        .collect(Collectors.toList());
                map.put("timeId", strategy.getTimeId());
                if(null!=resList && resList.size()>0) {
                    map.put("devInstanceId", resList.get(0).getDevInstanceId());
                    map.put("devName", resList.get(0).getDevName());
                    map.put("classGuid", resList.get(0).getClassGuid());
                    map.put("checkValue", DeviceEffectTypeDict.format(map));
                }
                customAndExceptionList.set(i,map);
            }
            // limitType 1 放行 2 限制
//            if(!strategy.getCustomLimitStatus()){
//                customAndExceptionList = customAndExceptionList.stream().filter(map -> "1".equalsIgnoreCase(map.get("limitType").toString())).collect(Collectors.toList());
//            }
//            if(!strategy.getExceptionConfigStatus()){
//                customAndExceptionList = customAndExceptionList.stream().filter(map -> "2".equalsIgnoreCase(map.get("limitType").toString())).collect(Collectors.toList());
//            }
            strategy.setCustomAndException(customAndExceptionList);
        }
        service.update(bean);
        return updateRelStgDevice(bean);
    }

    /**
     * 删除设备使用控制策略
     * @param ids
     */
    @Oplog(name = "pages.otherDeviceLimit")
    @PostMapping(value = "delete")
    public void delete(String ids) {
        service.deleteById(ids);
    }

    /** 查询其他设备限制策略分页列表*/
    @PostMapping(value = "findPage")
    public GridPageDTO<OtherDeviceLimitStrategy> findPage(@RequestBody StrategyVO vo) {
        return service.getStrategyPage(vo);
    }
    /** 查询其他设备限制策略*/
    @PostMapping(value = "listByName")
    public List<OtherDeviceLimitStrategy> listByName(String name) {
        List<OtherDeviceLimitStrategy> list = service.listByName(name);
        return list;
    }

    /** 获取其他设备树*/
    @GetMapping(value = "listTree")
    public List<TreeNodeDTO> listTreeNode() {
        List<LimitFunction> limitFunctions = limitFunctionService.listLimitFunction();
        return TreeUtil.toTreeNodes(limitFunctions);
    }

    /** 获取设备库列表*/
    @PostMapping(value = "getDeviceLibPage")
    public GridPageDTO<DeviceInfoLib> getDeviceLibPage(@RequestBody DeviceInfoLibVO vo) {
        return deviceInfoLibService.getDeviceLibPage(vo);
    }

    /**
     * 新增设备
     * @param deviceInfoLib
     * @return
     */
    @Oplog(value = DeviceInfoLib.class,name = "pages.device")
    @PostMapping(value = "addDevice")
    public Long addDevice(@RequestBody DeviceInfoLib deviceInfoLib) {
        return deviceInfoLibService.insertDeviceInfoLib(deviceInfoLib);
    }

    /**
     * 修改设备
     * @param deviceInfoLib
     * @return
     */
    @Oplog(value = DeviceInfoLib.class,name = "pages.device")
    @PostMapping(value = "updateDevice")
    public ResponseDTO updateDevice(@RequestBody DeviceInfoLib deviceInfoLib) {
        deviceInfoLibService.updateDeviceInfoLib(deviceInfoLib);
        return ResponseDTO.success();
    }

    /**
     * 删除设备
     * @param ids
     * @return
     */
    @Oplog(value = DeviceInfoLib.class,name = "pages.device")
    @PostMapping(value = "deleteDevice")
    public ResponseDTO deleteDevice(String ids) {
        deviceInfoLibService.deleteDeviceInfoLibById(ids);
        return ResponseDTO.success();
    }

    /** 根据设备实例路径获取设备*/
    @PostMapping(value = "getDevByInstanceId")
    public DeviceInfoLib getDevByInstanceId(@RequestBody DeviceInfoLibVO vo) {
        return deviceInfoLibService.getDevByInstanceId(vo);
    }
}
