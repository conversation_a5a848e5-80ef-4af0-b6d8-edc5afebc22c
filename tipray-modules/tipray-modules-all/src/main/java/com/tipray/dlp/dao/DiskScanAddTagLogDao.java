package com.tipray.dlp.dao;

import com.tipray.dlp.bean.DiskScanAddTagLog;
import com.tipray.dlp.bean.vo.DiskScanAddTagLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource
public interface DiskScanAddTagLogDao extends BaseDao<DiskScanAddTagLog>{
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(DiskScanAddTagLogVO vo);

     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<DiskScanAddTagLog> listByVO(DiskScanAddTagLogVO vo);
}
