package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.AppReportSetStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.AppReportSetStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 程序运行时长统计设置AppReportSetStrategy)表控制层
 *
 * <AUTHOR>
 * @since 2019-10-21 09:55:30
 */
@Oplog(AppReportSetStrategy.class)
@RestController
@RequestMapping("/appReportSetStrategy")
public class AppReportSetStrategyController {
    /**
     * 服务对象
     */
    @Resource
    private AppReportSetStrategyService appReportSetStrategyService;

    /**
     * 新增应用程序运行时长统计策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.appReportSetStrategy")
    @PostMapping(value = "add")
    public AppReportSetStrategy insert(@RequestBody AppReportSetStrategy bean){
        if(bean.getAllProcess() == 0){
            List<Map<String,Object>> appReportProcess = new ArrayList<>();
            Map<String,Object> appMap = new HashMap<>();
            appMap.put("id",1);
            appMap.put("processName","*.*");
            appReportProcess.add(appMap);
            bean.setAppReportProcess(appReportProcess);
        }else {
            if(bean.getAppReportProcess() == null || bean.getAppReportProcess().size() == 0){
                bean.setAllProcess(0);
            }
        }
        appReportSetStrategyService.insertStrategy(bean);
        return bean;
    }

    /**
     * 修改应用程序运行时长统计策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.appReportSetStrategy")
    @PostMapping(value = "update")
    public AppReportSetStrategy update(@RequestBody AppReportSetStrategy bean){
        if(bean.getAllProcess() == 0){
            List<Map<String,Object>> appReportProcess = new ArrayList<>();
            Map<String,Object> appMap = new HashMap<>();
            appMap.put("id",1);
            appMap.put("processName","*.*");
            appReportProcess.add(appMap);
            bean.setAppReportProcess(appReportProcess);
        }else {
            if(bean.getAppReportProcess() == null || bean.getAppReportProcess().size() == 0){
                bean.setAllProcess(0);
            }
        }
        appReportSetStrategyService.updateStrategy(bean);
        return bean;
    }

    /**
     * 删除应用程序运行时长统计策略
     * @param ids
     */
    @Oplog(name = "pages.appReportSetStrategy")
    @PostMapping(value = "delete")
    public void deleteById(String ids){
        appReportSetStrategyService.deleteStrategyById(ids);
    }
    /**
     * 根据主键ID获取策略
     *
     * @param id 主键
     * @return 策略内容
     */
    @GetMapping("get/{id}")
    public AppReportSetStrategy getById(@PathVariable("id") Long id) {
        return appReportSetStrategyService.getStrategyById(id);
    }

    /**
     * 根据名称获取策略
     * @param name 策略名称
     * @return 策略内容
     */
    @PostMapping("getByName")
    public AppReportSetStrategy getByName(String name) {
        return appReportSetStrategyService.getStrategyByName(name);
    }

    /**
     * 分页查询策略
     * @param vo
     * @return
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<AppReportSetStrategy> getPage(@ModelAttribute StrategyVO vo){
        return appReportSetStrategyService.getStrategyPage(vo);
    }

}
