package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.MailWhiteListSender;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.mybatis.bean.PageVO;
import com.tipray.dlp.service.MailWhiteListSenderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 发件人白名单库Controller
 */
@RestController
@Oplog(MailWhiteListSender.class)
@RequestMapping("/mailWhiteListSender")
public class MailWhiteListSenderController {

    @Autowired
    private MailWhiteListSenderService mailWhiteListSenderService;

    /** 添加发件人白名单*/
    @Oplog(name="pages.mailSender")
    @PostMapping(value = "add")
    public MailWhiteListSender addMailWhiteListSender(@RequestBody MailWhiteListSender bean) {
        mailWhiteListSenderService.insertMailWhiteListSender(bean);
        return bean;
    }

    /** 修改发件人白名单*/
    @Oplog(name="pages.mailSender")
    @PostMapping(value = "update")
    public MailWhiteListSender updateMailWhiteListSender(@RequestBody MailWhiteListSender bean) {
        mailWhiteListSenderService.updateMailWhiteListSender(bean);
        return bean;
    }

    /** 删除发件人白名单*/
    @Oplog(name="pages.mailSender")
    @PostMapping(value = "delete")
    public void deleteMailWhiteListSender(String ids) {
        mailWhiteListSenderService.deleteMailWhiteListSenderById(ids);
    }

    @GetMapping("get/{id}")
    public MailWhiteListSender getById(@PathVariable("id") Long id) {
        return mailWhiteListSenderService.getMailWhiteListSenderById(id);
    }

    @PostMapping("getByMailAddress")
    public MailWhiteListSender getByMailAddress(String mailAddress) {
        return mailWhiteListSenderService.getMailWhiteListSenderByMailAddress(mailAddress);
    }

    /** 获取发件人白名单列表*/
    @PostMapping(value = "getPage")
    public GridPageDTO<MailWhiteListSender> getStrategyPage(@RequestBody PageVO vo) {
        return mailWhiteListSenderService.getMailWhiteListSenderPage(vo);
    }
}
