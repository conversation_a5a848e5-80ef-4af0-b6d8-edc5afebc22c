package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.dict.BooleanDict;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.controller.socket.ExplorerSocketMapper;
import com.tipray.dlp.controller.socket.dto.*;
import com.tipray.dlp.export.ExportDataset;
import com.tipray.dlp.export.ExportFileType;
import com.tipray.dlp.export.ExportHelper;
import com.tipray.dlp.export.call.SimpleExportCallable;
import com.tipray.dlp.formatter.exportFormatter.LocalGroupExportFormatter;
import com.tipray.dlp.formatter.exportFormatter.LocalUserExportFormatter;
import com.tipray.dlp.util.CollectionUtil;
import com.tipray.dlp.util.SortUtil;
import com.tipray.dlp.util.StringUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 本地用户和组Controller
 */
@RestController
@RequestMapping("/terminal/localUserAndGroup")
public class LocalUserAndGroupController {
    @Resource
    private ExplorerSocketMapper socketApi;

    /**
     * 查询本地组信息
     */
    @Oplog(value = LocalGroupResponse.class, name = "pages.localGroup")
    @PostMapping(value = "listLocalGroup")
    public List<LocalGroupResponse> listLocalGroup(@RequestBody LocalGroupResponse bean) {
        return socketApi.toGetLocalGroups(bean.getTermId());
    }

    /**
     * 导出本地组
     */
    @Oplog(value = LocalGroupResponse.class,name = "pages.localGroup")
    @PostMapping(value = "exportLocalGroup")
    public ResponseDTO exportLocalGroup(@RequestBody LocalGroupResponse bean) {
        LocalGroupExportFormatter formatter = new LocalGroupExportFormatter();
        ExportDataset<LocalGroupResponse> dataset = ExportDataset.fromIterable(formatter, () -> {
            List<LocalGroupResponse> list = socketApi.toGetLocalGroups(bean.getTermId());
            if (CollectionUtil.isNotEmpty(list) && StringUtil.isNotEmpty(bean.getGroupName())) {
                String groupName = bean.getGroupName().toLowerCase();
                list.removeIf(item -> StringUtil.isBlank(item.getGroupName()) || !item.getGroupName().toLowerCase().contains(groupName));
            }
            return SortUtil.syncJsSort(list, Pair.of("groupName", "desc"));
        });
        SimpleExportCallable callable = SimpleExportCallable.single(ExportFileType.xlsx, dataset);
        return ResponseDTO.success(ExportHelper.addTask(callable));
    }

    /**
     * 查询本地用户信息
     */
    @Oplog(value = LocalUserResponse.class, name = "pages.localUser")
    @PostMapping(value = "listLocalUser")
    public List<LocalUserResponse> listLocalUser(@RequestBody LocalUserResponse bean) {
        return socketApi.toGetLocalUsers(bean.getTermId());
    }

    /**
     * 启用/禁用 本地用户
     */
    @Oplog(value = LocalUserResponse.class, name = "pages.localUser", defaultArg = true)
    @PostMapping(value = "updateLocalUser")
    public Integer updateLocalUser(@RequestBody LocalUserResponse bean) {
        if (bean.getTermId() == null || bean.getStatus() == null || bean.getUserNameLength() == null || StringUtil.isEmpty(bean.getUserName())) {
            return BooleanDict.FALSE.intValue();
        }
        return socketApi.toUpdateUser(bean.getTermId(), bean.getStatus(), bean.getUserNameLength(), bean.getUserName());
    }

    /**
     * 导出本地用户
     */
    @Oplog(value = LocalUserResponse.class,name = "pages.localUser")
    @PostMapping(value = "exportLocalUser")
    public ResponseDTO exportLocalUser(@RequestBody LocalUserResponse bean) {
        LocalUserExportFormatter formatter = new LocalUserExportFormatter();
        ExportDataset<LocalUserResponse> dataset = ExportDataset.fromIterable(formatter, () -> {
            List<LocalUserResponse> list = socketApi.toGetLocalUsers(bean.getTermId());
            if (CollectionUtil.isNotEmpty(list) && StringUtil.isNotEmpty(bean.getUserName())) {
                String userName = bean.getUserName().toLowerCase();
                list.removeIf(item -> StringUtil.isBlank(item.getUserName()) || !item.getUserName().toLowerCase().contains(userName));
            }
            return SortUtil.syncJsSort(list, Pair.of("userName", "desc"));
        });
        SimpleExportCallable callable = SimpleExportCallable.single(ExportFileType.xlsx, dataset);
        return ResponseDTO.success(ExportHelper.addTask(callable));
    }

}
