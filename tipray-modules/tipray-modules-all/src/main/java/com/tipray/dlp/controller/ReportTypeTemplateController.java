package com.tipray.dlp.controller;

import com.tipray.dlp.bean.ReportTemplateChip;
import com.tipray.dlp.bean.ReportTypeTemplate;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.ReportTypeTemplateVO;
import com.tipray.dlp.service.ReportTemplateChipService;
import com.tipray.dlp.service.ReportTypeTemplateService;
import com.tipray.dlp.util.StringUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @author: caitw
 * @description: 自定义报表模板controller(消息推送)
 **/
@RestController
@RequestMapping("/customizeReport")
public class ReportTypeTemplateController {

    @Resource
    private ReportTypeTemplateService reportTypeTemplateService;
    @Resource
    private ReportTemplateChipService reportTemplateChipService;

    @PostMapping(value = "getPage")
    public GridPageDTO<ReportTypeTemplate> getPage(@RequestBody ReportTypeTemplateVO pageVO) {
        return reportTypeTemplateService.getPage(pageVO);
    }

    /**
     * 获取消息推送任务配置的报表类型，去掉策略数量统计报表和计算机系统日志统计报表
     * @return
     */
    @GetMapping(value = "reportType")
    public ResponseDTO getReportType() {
        try {
            reportTypeTemplateService.initReportType();
        } catch (RuntimeException exception) {
            return ResponseDTO.failure(new Throwable(), exception.getMessage());
        }
        return ResponseDTO.success(reportTypeTemplateService.getReportType());
    }

    @PostMapping(value = "reportTemplate")
    public ResponseDTO addReportTemplate(@RequestBody ReportTypeTemplate reportTypeTemplate) {
        return ResponseDTO.success(reportTypeTemplateService.addReportTemplate(reportTypeTemplate));
    }

    @PostMapping(value = "updateTemplate")
    public ResponseDTO updateReportTemplate(@RequestBody ReportTypeTemplate reportTypeTemplate) {
        return ResponseDTO.success(reportTypeTemplateService.updateReportTemplate(reportTypeTemplate));
    }

    @PostMapping(value = "deleteTemplate")
    public ResponseDTO delReportTemplate(@RequestBody String templateIds) {
        reportTypeTemplateService.delReportTypeTemplateTemplate(templateIds);
        return ResponseDTO.success();
    }

    @PostMapping(value = "getByName")
    public ResponseDTO getByName(@RequestParam("name") String name) {
        return ResponseDTO.success(reportTypeTemplateService.getByName(name));
    }

    @GetMapping("reportTemplateChip")
    public ResponseDTO getReportTemplate() { return ResponseDTO.success(reportTypeTemplateService.getTemplateChipTree()); }

    @PostMapping(value = "updateTemplateChip")
    public ResponseDTO updateReportTemplateChipStr(@RequestBody ReportTemplateChip reportTemplateChip) {
        String str = reportTemplateChip.getStr();
        if (reportTemplateChip.getId() == null || StringUtil.isEmpty(str)) {
            return ResponseDTO.failure(new Throwable(), "模板碎片id不能为空");
        }
        int indexOf = str.indexOf("%s");
        if (indexOf < 0) { return ResponseDTO.failure(new Throwable(), "报表模板碎片修改的数据内容不包含%s字符串，请检查参数"); }
        return ResponseDTO.success(reportTemplateChipService.updateReportTemplateChip(reportTemplateChip));
    }
}