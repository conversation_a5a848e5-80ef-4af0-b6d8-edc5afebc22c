package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.OutgoingProcessStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.OutgoingProcessStrategyService;
import com.tipray.dlp.util.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 外发进程白名单策略Controller
 */
@RestController
@Oplog(OutgoingProcessStrategy.class)
@RequestMapping("/outgoingProcessStrategy")
public class OutgoingProcessStrategyController {
    @Autowired
    private OutgoingProcessStrategyService outgoingProcessStrategyService;

    /**
     * 新增软件白名单策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.outgoingProcessStrategy")
    @PostMapping(value = "add")
    public OutgoingProcessStrategy addStrategy(@RequestBody OutgoingProcessStrategy bean) {
        this.formatStrategy(bean);
        outgoingProcessStrategyService.insert(bean);
        return bean;
    }

    /**
     * 修改软件白名单策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.outgoingProcessStrategy")
    @PostMapping(value = "update")
    public OutgoingProcessStrategy updateStrategy(@RequestBody OutgoingProcessStrategy bean) {
        this.formatStrategy(bean);
        outgoingProcessStrategyService.update(bean);
        return bean;
    }

    private void formatStrategy(OutgoingProcessStrategy bean){
        if(bean==null|| CollectionUtil.isEmpty(bean.getSoftList())){
            return;
        }
        // 将非必要的字段置为空，较少策略的长度
        bean.getSoftList().forEach(item->{
            item.setCreateTime(null);
            item.setModifyTime(null);
        });
    }

    /**
     * 删除软件白名单策略
     * @param ids
     */
    @Oplog(name = "pages.outgoingProcessStrategy")
    @PostMapping(value = "delete")
    public void deleteStrategy(String ids) {
        outgoingProcessStrategyService.deleteById(ids);
    }

    @PostMapping("getByName")
    public OutgoingProcessStrategy getByName(String name) {
        return outgoingProcessStrategyService.getByName(name);
    }

    /** 获取外发进程白名单策略*/
    @PostMapping(value = "getPage")
    public GridPageDTO<OutgoingProcessStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return outgoingProcessStrategyService.getStrategyPage(vo);
    }

}
