package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.PatchCheckStrategy;
import com.tipray.dlp.bean.dict.OperationTypeDict;
import com.tipray.dlp.service.PatchCheckStrategyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 补丁检测文件配置表(PatchWsusscnConfig)表控制层
 *
 * <AUTHOR>
 * @since 2020-03-10 11:46:46
 */
@Oplog(PatchCheckStrategy.class)
@RestController
@RequestMapping("/patchWsusscnConfig")
public class PatchCheckStrategyController {
    /**
     * 服务对象
     */
    @Resource
    private PatchCheckStrategyService patchCheckStrategyService;

    /**
     * 补丁策略检测配置 和 保存安装策略 重复
     * 目的：保证管理员日志和铃铛消息的正确显示
     * 详情见：TRDLP-16399
     * @param bean
     * @return
     */
    @Oplog(name = "pages.detectingConfiguration", type = OperationTypeDict.UPDATE)
    @PostMapping(value = "saveSetting")
    public PatchCheckStrategy insert(@RequestBody PatchCheckStrategy bean){
        patchCheckStrategyService.insertPatchCheckStrategy(bean);
        return bean;
    }
    /**
     * 保存安装策略
     * 补丁策略检测配置 和 保存安装策略 重复
     * 目的：保证管理员日志和铃铛消息的正确显示
     * 详情见：TRDLP-16399
     * @param bean
     * @return
     */
    @Oplog(name = "route.installStrategy", type = OperationTypeDict.UPDATE)
    @PostMapping(value = "saveInstallStrategy")
    public PatchCheckStrategy save(@RequestBody PatchCheckStrategy bean){
        patchCheckStrategyService.insertPatchCheckStrategy(bean);
        return bean;
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @PostMapping("getSetting")
    public PatchCheckStrategy getSetting() {
        return patchCheckStrategyService.getPatchCheckStrategy();
    }

}
