package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.CompetitorInfo;
import com.tipray.dlp.bean.SeamlessReplaceStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.dto.TransferTaskDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.controller.download.task.DownloadTaskFactory;
import com.tipray.dlp.controller.socket.PKeyInfoSocketMapper;
import com.tipray.dlp.service.SeamlessReplaceStrategyService;
import com.tipray.dlp.util.FileUtil;
import com.tipray.dlp.util.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 兼容其他产品设置Controller
 */
@Oplog(SeamlessReplaceStrategy.class)
@RestController
@RequestMapping("/seamlessReplaceStrategy")
@Slf4j
public class SeamlessReplaceStrategyController {
    @Resource
    private SeamlessReplaceStrategyService seamlessReplaceStrategyService;
    @Resource
    private PKeyInfoSocketMapper socketApi;

    @Oplog(name="layout.compatibleSetting")
    /** 新增兼容其他产品设置策略*/
    @PostMapping(value = "add")
    public SeamlessReplaceStrategy[] add(@RequestBody SeamlessReplaceStrategy[] strategy) {
        seamlessReplaceStrategyService.insert(strategy);
        return strategy;
    }

    @Oplog(name="layout.compatibleSetting")
    /** 修改兼容其他产品设置策略*/
    @PostMapping(value = "update")
    public ResponseDTO update(@RequestBody SeamlessReplaceStrategy[] strategy) {
        seamlessReplaceStrategyService.update(strategy);
        return ResponseDTO.success(strategy);
    }

    @Oplog(name="layout.compatibleSetting")
    /** 删除兼容其他产品设置策略*/
    @PostMapping(value = "delete")
    public void delete(String ids) {
        seamlessReplaceStrategyService.deleteById(ids);
    }

    @PostMapping("getByName")
    public List<SeamlessReplaceStrategy> getByName(String name) {
        return seamlessReplaceStrategyService.listByName(name);
    }

    /** 移动兼容其他产品设置分页列表*/
    @PostMapping(value = "getPage")
    public GridPageDTO<SeamlessReplaceStrategy> getSeamlessReplaceStrategyPage(@RequestBody StrategyVO vo) {
        return seamlessReplaceStrategyService.getStrategyPage(vo);
    }

    /** 查询竞品信息列表*/
    @PostMapping(value = "listCompetitorInfo")
    public List<CompetitorInfo> listCompetitorInfo() {
        return seamlessReplaceStrategyService.listCompetitorInfo();
    }

    @PostMapping(value = "/upload")
    public TransferTaskDTO upload(CompetitorInfo info, MultipartFile uploadFile) throws IOException {
        return seamlessReplaceStrategyService.sendToFTP(uploadFile, info.getControlCode());
    }

    @PostMapping(value = "/restart")
    public ResponseDTO restart(String ids) {
        seamlessReplaceStrategyService.restart(ids);
        return ResponseDTO.success();
    }

    @PostMapping(value = "/stop")
    public ResponseDTO stop(String ids) {
        seamlessReplaceStrategyService.stop(ids);
        return ResponseDTO.success();
    }

    /**
     * 导出系统密钥
     * @return
     */
    @Oplog(name = "pages.exportSysKey")
    /** 导出密钥文件*/
    @PostMapping(value = "startDownload")
    @ResponseBody
    public ResponseDTO downloadStart() {
        try {
            Integer msgSn = socketApi.outPutMainKey();
            return ResponseDTO.requestId(msgSn);
        } catch (Exception e) {
            return ResponseDTO.failure(e);
        }
    }

    /** 导出密钥文件*/
    @PostMapping(value = "downloadFile")
    public void download(@RequestBody Map<String, Object> params, HttpServletResponse response) {
        String files = null;
        if(params != null) {
            Object obj = params.get("files");
            if(obj != null) {
                files = (String) obj;
            }
        }

        String fileName = FileUtil.append(FileUtil.CACHE_FILE, files);
        File file = new File(fileName);
        if (!file.exists()) {
            log.warn("导出密钥的文件 {} 不存在", fileName);
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            return;
        }
        String dateString = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String registerName = seamlessReplaceStrategyService.getRegisterName();
        DownloadTaskFactory.addCommonTask(response, registerName +
                I18nUtils.get("pages.pkeyExportFileName1", "密钥备份") + "_" + dateString + ".ldk", file);
    }
}
