package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.ShortOfflineStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.ShortOfflineStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 短期离线设置策略(ShortOfflineStrategy)表控制层
 *
 * <AUTHOR>
 * @since 2019-10-21 09:55:30
 */
@Oplog(ShortOfflineStrategy.class)
@RestController
@RequestMapping("/shortOfflineStrategy")
public class ShortOfflineStrategyController {
    /**
     * 服务对象
     */
    @Resource
    private ShortOfflineStrategyService shortOfflineStrategyService;

    /**
     * 新增短期离线设置策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.shortOfflineStrategy")
    @PostMapping(value = "add")
    public ShortOfflineStrategy insert(@RequestBody ShortOfflineStrategy bean){
        shortOfflineStrategyService.insertStrategy(bean);
        return bean;
    }

    /**
     * 修改短期离线设置策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.shortOfflineStrategy")
    @PostMapping(value = "update")
    public ShortOfflineStrategy update(@RequestBody ShortOfflineStrategy bean){
        shortOfflineStrategyService.updateStrategy(bean);
        return bean;
    }

    /**
     * 删除短期离线设置策略
     * @param ids
     */
    @Oplog(name = "pages.shortOfflineStrategy")
    @PostMapping(value = "delete")
    public void deleteById(String ids){
        shortOfflineStrategyService.deleteStrategyById(ids);
    }
    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("get/{id}")
    public ShortOfflineStrategy getById(@PathVariable("id") Long id) {
        return shortOfflineStrategyService.getStrategyById(id);
    }

    @PostMapping("getByName")
    public ShortOfflineStrategy getByName(String name) {
        return shortOfflineStrategyService.getStrategyByName(name);
    }

    @PostMapping(value = "getPage")
    public GridPageDTO<ShortOfflineStrategy> getPage(@ModelAttribute StrategyVO vo){
        return shortOfflineStrategyService.getStrategyPage(vo);
    }

}