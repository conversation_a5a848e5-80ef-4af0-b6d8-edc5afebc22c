package com.tipray.dlp.controller;


import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.OutgoingTemplateStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.OutgoingService;
import com.tipray.dlp.service.impl.OutgoingTemplateStrategyServiceImpl;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 文件外发——直接外发模板Controller
 */
@Oplog(OutgoingTemplateStrategy.class)
@RestController
@RequestMapping("/outgoing/template")
public class OutgoingTemplateStrategyController {
    @Resource(type = OutgoingTemplateStrategyServiceImpl.class)
    private OutgoingService outgoingService;

    /**
     * 新增直接外发模板设置
     * @param bean
     * @return
     */
    @Oplog(name = "pages.outgoingTemplateStrategy")
    @PostMapping(value = "add")
    public OutgoingTemplateStrategy add(@RequestBody OutgoingTemplateStrategy bean) {
        outgoingService.insert(bean);
        return bean;
    }

    /**
     * 修改直接外发模板设置
     * @param bean
     * @return
     */
    @Oplog(name = "pages.outgoingTemplateStrategy")
    @PostMapping(value = "update")
    public OutgoingTemplateStrategy update(@RequestBody OutgoingTemplateStrategy bean) {
        outgoingService.update(bean);
        return bean;
    }

    /**
     * 删除直接外发模板设置
     * @param ids
     */
    @Oplog(name = "pages.outgoingTemplateStrategy")
    @PostMapping(value = "delete")
    public void delete(String ids) {
        outgoingService.deleteById(ids);
    }

    @PostMapping("getByName")
    public OutgoingTemplateStrategy getByName(String name) {
        return (OutgoingTemplateStrategy) outgoingService.getByName(name, OutgoingTemplateStrategy.class);
    }

    /** 策略分页列表*/
    @PostMapping(value = "getPage")
    public GridPageDTO<OutgoingTemplateStrategy> getPage(@RequestBody StrategyVO vo) {
        return outgoingService.getPage(vo, OutgoingTemplateStrategy.class);
    }

}
