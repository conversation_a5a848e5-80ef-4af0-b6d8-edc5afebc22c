package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.GlobalConfig;
import com.tipray.dlp.bean.HttpWhiteListStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.GlobalConfigService;
import com.tipray.dlp.service.HttpWhiteListStrategyService;
import com.tipray.dlp.util.I18nUtils;
import com.tipray.dlp.util.StringUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 服务器白名单设置Controller
 */
@Oplog(HttpWhiteListStrategy.class)
@RestController
@RequestMapping("/httpWhiteListStrategy")
public class HttpWhiteListStrategyController {

    @Resource
    private HttpWhiteListStrategyService httpWhiteListStrategyService;
    @Resource
    private GlobalConfigService globalConfigService;

    /**
     * 新增服务器白名单
     * @param bean
     * @return
     */
    @Oplog(name = "pages.httpWhiteListStrategy")
    @PostMapping(value = "addServer")
    public HttpWhiteListStrategy addServer(@RequestBody HttpWhiteListStrategy bean) {
        httpWhiteListStrategyService.insertStrategy(true,bean);
        return bean;
    }

    /**
     * 修改服务器白名单
     * @param bean
     * @return
     */
    @Oplog(name = "pages.httpWhiteListStrategy")
    @PostMapping(value = "updateServer")
    public HttpWhiteListStrategy updateServer(@RequestBody HttpWhiteListStrategy bean) {
        httpWhiteListStrategyService.updateStrategy(true,bean);
        return bean;
    }

    /**
     * 删除服务器白名单
     * @param ids
     */
    @Oplog(name = "pages.httpWhiteListStrategy")
    @PostMapping(value = "deleteServer")
    public void deleteServer(String ids) {
        httpWhiteListStrategyService.deleteStrategyById(true, ids);
    }

    @PostMapping("getServerByName")
    public HttpWhiteListStrategy getServerByName(String name) {
        return httpWhiteListStrategyService.getStrategyByName(true,name);
    }

    /**服务器白名单列表*/
    @PostMapping(value = "getServerPage")
    public GridPageDTO<HttpWhiteListStrategy> getServerPage(@RequestBody StrategyVO vo) {
        return httpWhiteListStrategyService.getStrategyPage(true,vo);
    }

    /**
     * 新增进程过滤配置
     * @param bean
     * @return
     */
    @Oplog(name = "pages.processFilterConfig")
    @PostMapping(value = "addProcess")
    public HttpWhiteListStrategy addProcess(@RequestBody HttpWhiteListStrategy bean) {
        httpWhiteListStrategyService.insertStrategy(false, bean);
        return bean;
    }

    /**
     * 修改进程过滤配置
     * @param bean
     * @return
     */
    @Oplog(name = "pages.processFilterConfig")
    @PostMapping(value = "updateProcess")
    public HttpWhiteListStrategy updateProcess(@RequestBody HttpWhiteListStrategy bean) {
        httpWhiteListStrategyService.updateStrategy(false, bean);
        return bean;
    }

    /**
     * 删除进程过滤配置
     * @param ids
     */
    @Oplog(name = "pages.processFilterConfig")
    @PostMapping(value = "deleteProcess")
    public void deleteProcess(String ids) {
        httpWhiteListStrategyService.deleteStrategyById(false, ids);
    }

    @PostMapping("getProcessByName")
    public HttpWhiteListStrategy getProcessByName(String name) {
        return httpWhiteListStrategyService.getStrategyByName(false, name);
    }

    /**进程过滤配置分页列表*/
    @PostMapping(value = "getProcessPage")
    public GridPageDTO<HttpWhiteListStrategy> getProcessPage(@RequestBody StrategyVO vo) {
        return httpWhiteListStrategyService.getStrategyPage(false, vo);
    }

    /**
     * 保存邮件白名单配置
     * @param bean
     * @return
     */
    @Oplog(name = "pages.emailWhitelistConfig")
    @PostMapping(value = "save")
    public HttpWhiteListStrategy insert(@RequestBody HttpWhiteListStrategy bean){
        httpWhiteListStrategyService.insertHttpWhiteListStrategy(bean);
        return bean;
    }

    /**
     * 分页查询策略
     *
     * @return 单条数据
     */
    @PostMapping("getHttpWhiteList")
    public HttpWhiteListStrategy getStrategyPage(@RequestBody StrategyVO vo) {
        return httpWhiteListStrategyService.getHttpWhiteListStrategyPage(vo);
    }


    /**
     * 服务器白名单设置 -> 高级设置 -> 上网模式设置
     * 服务器白名单设置 -> 高级设置 -> 加解密设置
     */
    @Oplog(value = GlobalConfig.class, name = "table.highConfig")
    @PostMapping("/updateConfig")
    public ResponseDTO updateGlobalConfig(@RequestBody List<GlobalConfig> globalConfigList) {
        for(GlobalConfig config : globalConfigList){
            if (StringUtil.isEmpty(config.getValue())){
                return ResponseDTO.failure(new Throwable(), I18nUtils.get("pages.regCode1", "参数错误"));
            }
        }
        globalConfigService.batchUpdate(globalConfigList);
        return ResponseDTO.success();
    }
}
