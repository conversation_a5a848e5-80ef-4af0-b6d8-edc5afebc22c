package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.WebpageMatchRule;
import com.tipray.dlp.bean.WebpageMatchRuleGroup;
import com.tipray.dlp.bean.dict.OperationTypeDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ImportResultDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.dto.TreeNodeDTO;
import com.tipray.dlp.bean.vo.WebpageMatchRuleVO;
import com.tipray.dlp.service.WebpageMatchRuleService;
import com.tipray.dlp.util.FileUtil;
import com.tipray.dlp.util.PermissionUtil;
import com.tipray.dlp.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 网页匹配规则Controller
 * <AUTHOR>
 * @Description 网页匹配规则Controller
 * @CreateTime 2023年08月30日 14:57:00
 */
@Slf4j
@Oplog(WebpageMatchRule.class)
@RestController
@RequestMapping("webpageMatchRule")
public class WebpageMatchRuleController {
    @Resource
    private WebpageMatchRuleService webpageMatchRuleService;

    /**
     * 获取网页匹配规则分组
     *
     * @return
     */
    @GetMapping(value = "listGroupTreeNode")
    public List<TreeNodeDTO> listGroupTreeNode() {
        return webpageMatchRuleService.listGroupTreeNode(new WebpageMatchRuleGroup());
    }

    /**
     * 新增网页匹配规则分组
     *
     * @param bean
     * @return
     */
    @Oplog(value = WebpageMatchRuleGroup.class, name = "pages.webpageMatchRuleGroup")
    @PostMapping(value = "insertGroup")
    public WebpageMatchRuleGroup insertGroup(@Valid WebpageMatchRuleGroup bean) {
        webpageMatchRuleService.insertGroup(bean);
        return bean;
    }

    /**
     * 修改网页匹配规则分组
     *
     * @param bean
     * @return
     */
    @Oplog(value = WebpageMatchRuleGroup.class, name = "pages.webpageMatchRuleGroup")
    @PostMapping(value = "updateGroup")
    public WebpageMatchRuleGroup updateGroup(@Valid WebpageMatchRuleGroup bean) {
        webpageMatchRuleService.updateGroup(bean);
        return bean;
    }

    /**
     * 删除网页匹配规则分组
     *
     * @param bean
     */
    @Oplog(value = WebpageMatchRuleGroup.class, name = "pages.webpageMatchRuleGroup")
    @PostMapping(value = "deleteGroup")
    public void deleteGroup(WebpageMatchRuleGroup bean) {
        String name = webpageMatchRuleService.getGroupNameById(bean.getId());
        bean.setName(name);
        //判断是否有导入任务未完成
        webpageMatchRuleService.checkImportFinished();
        webpageMatchRuleService.deleteGroupById(bean.getId());
    }

    /**
     * 批量移动分组
     *
     * @param vo
     */
    @PostMapping(value = "moveGroup")
    public void moveGroup(@RequestBody WebpageMatchRuleVO vo) {
        webpageMatchRuleService.moveGroup(vo.getParentId(), vo.getIds());
    }

    @PostMapping(value = "getGroupByName")
    public WebpageMatchRuleGroup getGroupByName(WebpageMatchRuleGroup bean) {
        return webpageMatchRuleService.getGroupByName(bean);
    }

    @GetMapping(value = "countByGroupId/{groupId}")
    public Long countByGroupId(@PathVariable("groupId") Long groupId) {
        return webpageMatchRuleService.countWebpageMatchRuleByGroupId(groupId);
    }

    /**
     * 分页查询网页匹配规则
     *
     * @param vo
     * @return
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<WebpageMatchRule> getPage(@RequestBody WebpageMatchRuleVO vo) {
        return webpageMatchRuleService.getWebpageMatchRulePage(vo);
    }

    /**
     * 添加网页匹配规则
     *
     * @param bean
     * @return
     */
    @Oplog(name = "table.webpageMatchRule")
    @PostMapping(value = "insert")
    public WebpageMatchRule insert(@Valid WebpageMatchRule bean) {
        this.webpageMatchRuleService.insertWebpageMatchRule(bean);
        return bean;
    }

    /**
     * 修改网页匹配规则
     *
     * @param bean
     * @return
     */
    @Oplog(name = "table.webpageMatchRule")
    @PostMapping(value = "update")
    public WebpageMatchRule update(@Valid WebpageMatchRule bean) {
        this.webpageMatchRuleService.updateWebpageMatchRule(bean);
        return bean;
    }

    /**
     * 删除网页匹配规则
     *
     * @param vo
     * @return
     */
    @Oplog(value = WebpageMatchRuleVO.class, name = "table.webpageMatchRule")
    @PostMapping(value = "delete")
    public ResponseDTO batchDelete(@RequestBody WebpageMatchRuleVO vo) {
        if (null != vo.getUpdateQuery() && vo.getUpdateQuery()) {
            List<Long> ids = webpageMatchRuleService.getWebpageMatchRulePage(vo).getItems().stream()
                    .filter(t -> !vo.getBackupUnSelectedIds().contains(t.getId())).
                            map(WebpageMatchRule::getId).collect(Collectors.toList());
            vo.setIds(StringUtil.toInSql(ids));
        }
        Boolean filterUsed = vo.getFilterUsed();
        if(filterUsed != null && filterUsed) {
            Set<Long> usedRules = webpageMatchRuleService.findStrategyUsedRuleSet(vo);
            if (usedRules != null) {
                String ids = vo.getIds();
                if (StringUtils.isNotEmpty(ids)) {
                    List<Long> resList = StringUtil.toLongList(ids).stream().filter(id -> !usedRules.contains(id)).collect(Collectors.toList());
                    vo.setIds(StringUtils.join(resList, ","));
                }
            }
        }
        List<String> names = webpageMatchRuleService.listNameByIds(vo.getIds());
        // 用于记录删除网页匹配规则的管理员日志
        vo.setNames(names);
        //判断是否有导入任务未完成
        webpageMatchRuleService.checkImportFinished();
        webpageMatchRuleService.deleteWebpageMatchRuleByIds(vo.getIds());
        return ResponseDTO.success(vo);
    }

    /**
     * 删除网页匹配规则分分组以及分组下的所有数据
     *
     * @param vo
     */
    @Oplog(value = WebpageMatchRuleVO.class, name = "pages.webpageMatchRuleGroup")
    @PostMapping(value = "deleteGroupAndData")
    public void deleteGroupAndData(@RequestBody WebpageMatchRuleVO vo) {
        String groupName = webpageMatchRuleService.getGroupNameById(vo.getGroupId());
        // 用于记录删除分组的管理员日志
        vo.setGroupName(groupName);
        webpageMatchRuleService.checkImportFinished();
        webpageMatchRuleService.deleteGroupAndData(vo);
    }

    /**
     * 删除网页匹配规则分组，分组下的数据移至另一处网页匹配规则分组
     *
     * @param dto
     */
    @Oplog(value = WebpageMatchRuleVO.class, name = "pages.webpageMatchRuleGroup", type = OperationTypeDict.DELETE)
    @PostMapping(value = "moveGroupToOther")
    public void moveGroupToOther(@RequestBody WebpageMatchRuleVO dto) {
        String groupName = webpageMatchRuleService.getGroupNameById(dto.getGroupId());
        // 用于记录删除分组的管理员日志
        dto.setGroupName(groupName);
        webpageMatchRuleService.checkImportFinished();
        webpageMatchRuleService.moveGroupToOther(dto);
        webpageMatchRuleService.deleteGroupById(dto.getGroupId());
    }

    /**
     * 移动网页匹配规则分组
     *
     * @param groupId
     * @param vo
     * @return
     */
    @Oplog(value = WebpageMatchRule.class, name = "pages.webpageMatchRuleGroup")
    @PostMapping(value = "batchUpdateGroup/{groupId}")
    public ResponseDTO batchUpdateGroup(@PathVariable("groupId") Long groupId, @RequestBody WebpageMatchRuleVO vo) {
        webpageMatchRuleService.moveGroup(groupId, vo.getIds());
        vo.setGroupId(groupId);
        return ResponseDTO.success(vo);
    }

    /**
     * 移动网页匹配规则分组
     */
    @Oplog(value = WebpageMatchRule.class, name = "pages.webpageMatchRuleGroup")
    @PostMapping(value = "batchUpdateAllGroup/{groupId}")
    public ResponseDTO batchUpdateAllGroup(@PathVariable("groupId") Long groupId, @RequestBody WebpageMatchRuleVO vo) {
        if (vo.getGroupId() == null && StringUtil.isBlank(vo.getGroupIds())) {
            String groupIds = StringUtil.join(PermissionUtil.listPermissionGroupId(), ",");
            vo.setGroupIds(groupIds);
        }
        vo.setLimit(null);
        List<WebpageMatchRule> suffixs = (List) webpageMatchRuleService.getWebpageMatchRulePage(vo).getItems();
        List<Long> ids = new LinkedList<>();
        suffixs.forEach(item -> {
            if (vo.getBackupUnSelectedIds() == null || !vo.getBackupUnSelectedIds().contains(item.getId())) {
                ids.add(item.getId());
            }
        });
        webpageMatchRuleService.moveGroup(groupId, StringUtil.toInSql(ids));
        vo.setIds(StringUtil.toInSql(ids));
        vo.setGroupId(groupId);
        return ResponseDTO.success(vo);
    }


    /**
     * 导出网页匹配规则库
     *
     * @param bean
     * @param response
     * @return
     */
    @Oplog(value = WebpageMatchRuleVO.class, name = "route.webpageMatchRuleLib")
    @PostMapping(value = "export")
    public ResponseDTO exportExcelBySelectId(@RequestBody WebpageMatchRuleVO bean, HttpServletResponse response) {
        return ResponseDTO.success(webpageMatchRuleService.exportExcel(bean, response));
    }

    /**
     * 根据name获取网页匹配规则
     * @param name
     * @return
     */
    @GetMapping(value = "getByName")
    public WebpageMatchRule getByName(String name) {
        WebpageMatchRule bean = this.webpageMatchRuleService.getByName(name);
        return bean;
    }
    /**
     * 根据分组id获取网页匹配规则
     * @param
     * @return
     */
    @PostMapping(value = "listByGroupId")
    public List<WebpageMatchRule> listByGroupId(@RequestBody WebpageMatchRuleVO vo) {
        List<Long> groupIdList = StringUtil.toLongList(vo.getGroupIds());
        return webpageMatchRuleService.listWebpageMatchRuleByGroupId(groupIdList);
    }
    /**
     * 根据ids获取网页匹配规则列表
     * @param
     * @return
     */
    @PostMapping(value = "getByIds")
    public List<WebpageMatchRule> getByIds(@RequestBody WebpageMatchRuleVO vo) {
        return webpageMatchRuleService.getWebpageMatchRuleListByIds(vo.getIds());
    }

    /**
     * 导入网页匹配规则
     *
     * @param file
     * @param importWay
     * @param taskId
     * @return
     * @throws IOException
     */
    @Oplog(value = ImportResultDTO.class, name = "table.webpageMatchRule")
    @PostMapping(value = "import")
    public ResponseDTO importData(@RequestParam(value = "file") MultipartFile file,
                              @RequestParam(value = "fileType") String fileType,
                              @RequestParam(value = "importWay") Integer importWay,
                              @RequestParam(value = "taskId") Long taskId) throws IOException {
        try {
            File destFile = FileUtil.transferImportFile(file);
            ImportResultDTO result = webpageMatchRuleService.importWebpageMatchRule(taskId, destFile, fileType, importWay);
            return ResponseDTO.success(result);
        } catch (Exception e) {
            return ResponseDTO.failure(e);
        }
    }
}
