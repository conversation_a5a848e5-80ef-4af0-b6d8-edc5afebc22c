package com.tipray.dlp.dao;

import com.tipray.dlp.bean.MailWhiteListSender;
import com.tipray.dlp.mybatis.bean.PageVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MailWhiteListSenderDao extends BaseDao<MailWhiteListSender> {
    MailWhiteListSender getById(Long id);

    MailWhiteListSender getByMailAddress(String mailAddress);

    Long countByVO(PageVO vo);

    List<MailWhiteListSender> listByVO(PageVO vo);
}
