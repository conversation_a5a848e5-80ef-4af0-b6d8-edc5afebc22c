package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.AddTagLog;
import com.tipray.dlp.bean.DiskScanGetTagLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.DiskScanGetTagLogVO;
import com.tipray.dlp.service.DiskScanGetTagLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 全盘扫描获取标签日志Controller
 */
@Oplog(DiskScanGetTagLog.class)
@RestController
@RequestMapping("/log/diskScanGetTagLog")
public class DiskScanGetTagLogController {
    @Autowired
    private DiskScanGetTagLogService service;

    /**
     * 删除全盘扫描获取标签日志
     * @param list
     * @return
     */
    @Oplog(value = DiskScanGetTagLog.class, name = "pages.tagGetLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<DiskScanGetTagLog> list) {
        service.deleteBatch(list);
        return ResponseDTO.success();
    }

    /**
     * 查询全盘扫描获取标签记录
     * @param vo
     * @return
     */
    @Oplog(value = DiskScanGetTagLogVO.class, name = "pages.tagGetLog")
    @PostMapping(value = "getPage")
    public GridPageDTO<DiskScanGetTagLog> getPage(@RequestBody DiskScanGetTagLogVO vo) {
        return service.getPage(vo);
    }
}
