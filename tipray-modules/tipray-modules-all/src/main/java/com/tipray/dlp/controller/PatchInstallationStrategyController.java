package com.tipray.dlp.controller;

import cn.hutool.core.bean.BeanUtil;
import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.PatchInstallStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.PatchInstallStrategyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
/**
 * 自动补丁策略Controller
 * <AUTHOR>
 * @since 2020-6-6
 */
@RestController
@Oplog(PatchInstallStrategy.class)
@RequestMapping("/installStrategy")
public class PatchInstallationStrategyController {
    @Autowired
    private PatchInstallStrategyService patchInstallStrategyService;

    /**
     * 添加补丁安装策略
     * @param bean
     * @return
     */
    @Oplog(name="route.installStrategy")
    @PostMapping(value = "add")
    public PatchInstallStrategy addStrategy(@RequestBody PatchInstallStrategy bean) {
        bean.setPatchList(null);
//        bean.setActive(true);
        PatchInstallStrategy patchInstallStrategy = new PatchInstallStrategy();
        // 参数修改导致管理员日志记录有误，因此拷贝一份
        BeanUtil.copyProperties(bean, patchInstallStrategy);
        patchInstallStrategyService.savePatchInstallStrategy(patchInstallStrategy,false);
        return bean;
    }

    /**
     * 修改补丁安装策略
     * @param bean
     * @return
     */
    @Oplog(name="route.installStrategy")
    @PostMapping(value = "update")
    public PatchInstallStrategy updateStrategy(@RequestBody PatchInstallStrategy bean) {
//        bean.setActive(true);
        PatchInstallStrategy patchInstallStrategy = new PatchInstallStrategy();
        // 参数修改导致管理员日志记录有误，因此拷贝一份
        BeanUtil.copyProperties(bean, patchInstallStrategy);
        patchInstallStrategyService.savePatchInstallStrategy(patchInstallStrategy,true);
        return bean;
    }

    /**
     * 删除自动补丁限制策略
     * @param ids
     */
    @Oplog(name="route.installStrategy")
    @PostMapping(value = "delete")
    public void deleteStrategy(String ids) {
        patchInstallStrategyService.deleteStrategyById(ids);
    }

    @GetMapping(value = "get/{id}")
    public PatchInstallStrategy getById(@PathVariable("id") Long id){
        return patchInstallStrategyService.getStrategyAndPatchInfoById(id);
    }

    /**
     * 根据名称获取策略
     * @param name
     * @return
     */
    @PostMapping("getByName")
    public PatchInstallStrategy getByName(String name) {
        return patchInstallStrategyService.getStrategyByName(name);
    }

    /**
     * 获取自动补丁策略
     * @param vo
     * @return
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<PatchInstallStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return patchInstallStrategyService.getStrategyPage(vo);
    }

}
