package com.tipray.dlp.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tipray.dlp.bean.DepartmentPosition;
import com.tipray.dlp.bean.UserRelRole;
import com.tipray.dlp.util.StringUtil;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

@Repository
public interface DepartmentPositionDao extends BaseDao<DepartmentPosition> {
    /**
     * 根据操作员id删除对应部门职务
     * @param userId 操作员id
     */
    default void deleteByUserId(Long userId) {
        if (null == userId) {
            return;
        }
        List<DepartmentPosition> departmentPositionList = this.listByUserId(userId);
        if (null == departmentPositionList || departmentPositionList.isEmpty()) {
            return;
        }
        List<Long> idList = departmentPositionList.stream().map(DepartmentPosition::getId).collect(Collectors.toList());
        this.deleteByIds(StringUtil.toInSql(idList));
    }

    /**
     * 根据操作员id获取操作员部门职务
     * @param userId 操作员id
     * @return 操作员与部门职务的关联表数据
     */
    default List<DepartmentPosition> listByUserId(Long userId) {
        if (null == userId) {
            return null;
        }
        List<DepartmentPosition> departmentPositions = this.selectList(new QueryWrapper<DepartmentPosition>().eq("user_id", userId));
        if (null == departmentPositions || departmentPositions.isEmpty()) {
            return null;
        }
        return departmentPositions;
    }
}
