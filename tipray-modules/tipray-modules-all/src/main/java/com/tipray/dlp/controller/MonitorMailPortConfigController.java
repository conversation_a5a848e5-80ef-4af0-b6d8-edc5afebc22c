package com.tipray.dlp.controller;

import com.alibaba.fastjson.JSON;
import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.GlobalConfig;
import com.tipray.dlp.bean.MonitorMailPortConfig;
import com.tipray.dlp.service.GlobalConfigService;
import com.tipray.dlp.util.JsonUtil;
import com.tipray.dlp.util.StringUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 邮件监控端口配置Controller
 * <AUTHOR>
 * @date 2024/1/7
 */
@RestController
@Oplog(MonitorMailPortConfig.class)
@RequestMapping("/monitorMailPortConfig")
public class MonitorMailPortConfigController {

    @Resource
    private GlobalConfigService globalConfigService;

    private final String CONFIG_KEY = "monitorMailPort";

    /**
     * 查询邮件监控端口配置
     * @return
     */
    @PostMapping(value = "list")
    public List<MonitorMailPortConfig> listMonitorMailPortConfig() throws IOException {
        GlobalConfig globalConfig = globalConfigService.getByKey(CONFIG_KEY);
        return StringUtil.isNotEmpty(globalConfig.getValue()) ? JsonUtil.toList(globalConfig.getValue(), MonitorMailPortConfig.class) : new ArrayList<>();
    }

    /**
     * 添加邮件监控端口
     * @param bean
     * @return
     */
    @Oplog(name = "table.monitorMailPort")
    @PostMapping(value = "add")
    public MonitorMailPortConfig add(@RequestBody MonitorMailPortConfig bean) throws IOException {
        GlobalConfig globalConfig = globalConfigService.getByKey(CONFIG_KEY);
        List<MonitorMailPortConfig> list = StringUtil.isNotEmpty(globalConfig.getValue()) ? JsonUtil.toList(globalConfig.getValue(), MonitorMailPortConfig.class) : new ArrayList<>();
        list.add(bean);
        globalConfig.setValue(JsonUtil.toJson(list));
        globalConfigService.update(globalConfig);
        return bean;
    }

    /**
     * 修改邮件监控端口
     * @param bean
     * @return
     */
    @Oplog(name = "table.monitorMailPort")
    @PostMapping(value = "update")
    public MonitorMailPortConfig update(@RequestBody MonitorMailPortConfig bean) throws IOException {
        GlobalConfig globalConfig = globalConfigService.getByKey(CONFIG_KEY);
        List<MonitorMailPortConfig> list = JsonUtil.toList(globalConfig.getValue(), MonitorMailPortConfig.class);
        list.forEach(item -> {
            if (bean.getId().equals(item.getId())) {
                item.setProtocol(bean.getProtocol());
                item.setPort(bean.getPort());
                item.setRemark(bean.getRemark());
            }
        });
        globalConfig.setValue(JsonUtil.toJson(list));
        globalConfigService.update(globalConfig);
        return bean;
    }

    /**
     * 删除邮件监控端口
     * @param vo
     * @return
     */
    @Oplog(name = "table.monitorMailPort")
    @PostMapping(value = "delete")
    public void batchDelete(MonitorMailPortConfig vo) throws IOException {
        GlobalConfig globalConfig = globalConfigService.getByKey(CONFIG_KEY);
        List<MonitorMailPortConfig> list = JsonUtil.toList(globalConfig.getValue(), MonitorMailPortConfig.class);
        List<MonitorMailPortConfig> toDeleteMonitorMailPortConfig = list.stream().filter(config -> StringUtil.toLongList(vo.getIds()).contains(config.getId())).collect(Collectors.toList());
        // 记录管理员日志
        vo.setToDeleteData(JSON.toJSONString(toDeleteMonitorMailPortConfig));
        List<MonitorMailPortConfig> toUpdateList = list.stream().filter(config-> !StringUtil.toLongList(vo.getIds()).contains(config.getId())).collect(Collectors.toList());
        if (toUpdateList.isEmpty()) {
            globalConfig.setValue("");
        } else {
            globalConfig.setValue(JsonUtil.toJson(toUpdateList));
        }
        globalConfigService.update(globalConfig);
    }
}
