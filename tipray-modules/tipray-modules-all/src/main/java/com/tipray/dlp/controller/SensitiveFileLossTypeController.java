package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.SensitiveFileLossType;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.service.SensitiveFileLossTypeService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 敏感外发申请支持的类型下发Controller
 * 敏感外发申请支持的类型下发表(sensitive_file_loss_type)表控制层
 *
 * <AUTHOR>
 * @since 2021-10-11 16:07:39
 */
@Oplog(SensitiveFileLossType.class)
@RestController
@RequestMapping("/sensitiveFileLossType")
public class SensitiveFileLossTypeController {
    /**
     * 服务对象
     */
    @Resource
    private SensitiveFileLossTypeService sensitiveFileLossTypeService;

    /**
     * 通过操作员显示方式
     * 查询敏感外发申请支持的类型
     * @return 单条数据
     */
    @PostMapping("/getPage")
    public List<SensitiveFileLossType> getOperatorDisplayMode() {
        return sensitiveFileLossTypeService.queryAll();
    }

    /**
     * 通过操作员显示方式
     * 修改敏感文件外发申请支持的类型
     * @return 单条数据
     */
    @Oplog(name = "route.outsendConfig")
    @PostMapping("/update")
    public ResponseDTO update(@RequestBody List<SensitiveFileLossType> sensitiveFileLossTypeList) {
        sensitiveFileLossTypeService.batchUpdate(sensitiveFileLossTypeList);
        return ResponseDTO.success();
    }

}
