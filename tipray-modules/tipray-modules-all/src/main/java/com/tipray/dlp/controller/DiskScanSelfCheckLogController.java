package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.DiskScanEncOrDecLog;
import com.tipray.dlp.bean.DiskScanSelfCheckLog;
import com.tipray.dlp.bean.DiskScanSelfCheckSensLog;
import com.tipray.dlp.bean.EncOrDecLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.DiskScanLogVO;
import com.tipray.dlp.service.DiskScanSelfCheckLogService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * 敏感文件自检日志Controller
 */
@Oplog(EncOrDecLog.class)
@RestController
@RequestMapping("/log/diskScanSelfCheck")
public class DiskScanSelfCheckLogController {
    @Resource
    private DiskScanSelfCheckLogService diskScanSelfCheckLogService;

    @PostMapping(value = "getDetailPage")
    public GridPageDTO<DiskScanSelfCheckLog> getLogPage(@RequestBody DiskScanLogVO vo) {
        return diskScanSelfCheckLogService.getScanLogPage(vo);
    }

    /** 导出敏感文件自检记录*/
    @Oplog(name="route.diskScanSelfCheckLog")
    @PostMapping(value = "export")
    public ResponseDTO exportExcelByLogVO(@RequestBody DiskScanLogVO vo, HttpServletResponse response){
        return ResponseDTO.success(diskScanSelfCheckLogService.exportExcel(vo,response));
    }

    /** 删除全盘加解密日志*/
    @Oplog(name="pages.entireEncAndDecLog")
    @PostMapping(value = "delete")
    public void deleteEncOrDecLogById(String ids){
        diskScanSelfCheckLogService.deleteEncOrDecLogById(ids);
    }

    /** 分页查询全盘加解密日志*/
    @PostMapping(value = "getPage")
    public GridPageDTO<DiskScanEncOrDecLog> getEncOrDecLogPage(@ModelAttribute DiskScanLogVO vo){
        return diskScanSelfCheckLogService.getEncOrDecLogPage(vo);
    }

    /** 删除全盘扫描敏感文件详情日志*/
    @Oplog(name="pages.diskScanSensLog")
    @PostMapping(value = "deleteSensitive")
    public void deleteSensLogById(String ids){
        diskScanSelfCheckLogService.deleteSensLogById(ids);
    }

    /** 分页查询全盘扫描敏感文件详情日志*/
    @PostMapping(value = "getSensitivePage")
    public GridPageDTO<DiskScanSelfCheckSensLog> getSensLogPage(@ModelAttribute DiskScanLogVO vo){
        return diskScanSelfCheckLogService.getSensLogPage(vo);
    }
}
