package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.MailWhiteListStgConfig;
import com.tipray.dlp.bean.MailWhiteListStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.MailWhiteListStgConfigService;
import com.tipray.dlp.service.MailWhiteListStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 邮件白名单设置Controller
 */
@Oplog(MailWhiteListStrategy.class)
@RestController
@RequestMapping("/mailWhiteListStrategy")
public class MailWhiteListStrategyController {

    @Resource
    private MailWhiteListStrategyService mailWhiteListStrategyService;
    @Resource
    private MailWhiteListStgConfigService mailWhiteListConfigService;

    /**
     * 新增收件人白名单策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.mailWhiteListStrategy")
    @PostMapping(value = "add")
    public MailWhiteListStrategy add(@RequestBody MailWhiteListStrategy bean) {
        mailWhiteListStrategyService.insertStrategy(bean);
        return bean;
    }

    /**
     * 修改收件人白名单策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.mailWhiteListStrategy")
    @PostMapping(value = "update")
    public MailWhiteListStrategy update(@RequestBody MailWhiteListStrategy bean) {
        mailWhiteListStrategyService.updateStrategy(bean);
        return bean;
    }

    /**
     * 删除收件人白名单策略
     * @param ids
     */
    @Oplog(name = "pages.mailWhiteListStrategy")
    @PostMapping(value = "delete")
    public void deleteRecv(String ids) {
        mailWhiteListStrategyService.deleteStrategyById(ids);
    }

    @PostMapping("getByName")
    public MailWhiteListStrategy getByName(String name) {
        return mailWhiteListStrategyService.getStrategyByName(name);
    }

    /** 收件人白名单分页列表*/
    @PostMapping(value = "getPage")
    public GridPageDTO<MailWhiteListStrategy> getPage(@RequestBody StrategyVO vo) {
        return mailWhiteListStrategyService.getStrategyPage(vo);
    }

    /** 获取邮箱白名单系统设置*/
    @GetMapping(value = "getConfig")
    public MailWhiteListStgConfig getMailWhiteListStgConfig(){
        return mailWhiteListConfigService.getMailWhiteListStgConfig();
    }

    /**
     * 保存邮箱白名单系统设置
     * @param bean
     */
    @Oplog(value = MailWhiteListStgConfig.class,name = "pages.configMailWhiteListStgConfig")
    @PostMapping(value = "saveConfig")
    public void updateMailWhiteListStgConfig(@RequestBody MailWhiteListStgConfig bean){
        mailWhiteListConfigService.saveMailWhiteListStgConfig(bean);
    }
}
