package com.tipray.dlp.dao;

import com.tipray.dlp.bean.DatabaseRecoverLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2022-11-02
 */
@Repository
public interface DatabaseRecoverLogDao {

    int insert(DatabaseRecoverLog bean);

    List<DatabaseRecoverLog> list(DatabaseRecoverLog bean);

    int updateById(DatabaseRecoverLog bean);

    DatabaseRecoverLog selectById(@Param("id") Long id);

    int deleteById(@Param("id") Long id);

}
