package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.TaskDistribute;
import com.tipray.dlp.bean.Terminal;
import com.tipray.dlp.bean.TerminalGroupNameTask;
import com.tipray.dlp.bean.TerminalGrpNickAcquisition;
import com.tipray.dlp.bean.dict.OperationTypeDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.TerminalGrpNickNameDTO;
import com.tipray.dlp.bean.vo.TerminalGrpNickAcquisitionVO;
import com.tipray.dlp.service.TerminalGroupNickNameService;
import com.tipray.dlp.util.PermissionUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 终端分组昵称收集Controller
 */
@RestController
@Oplog(TerminalGrpNickAcquisition.class)
@RequestMapping("/terminalGroupName")
public class TerminalGroupNameController {

    @Resource
    private TerminalGroupNickNameService terminalGroupNickNameService;

    /**
     * 根据状态查询终端分组名称列表信息
     * @param vo
     * @return
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<TerminalGrpNickNameDTO> getPage(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        if (vo.getGroupId() == null) {
            vo.setGroupList(PermissionUtil.listPermissionGroupId());
        }
        return terminalGroupNickNameService.getTaskPage(vo, true);
    }

    /**
     * 根据状态查询收集完成的任务
     * @param vo
     * @return
     */
    @PostMapping(value = "getToSncPage")
    public GridPageDTO<TerminalGrpNickNameDTO> getToSncPage(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        if (vo.getGroupId() == null) {
            vo.setGroupList(PermissionUtil.listPermissionGroupId());
        }
        return terminalGroupNickNameService.getToSncTaskPage(vo);
    }

    /**
     * 删除规范任务
     * @param vo
     * @return
     */
    @Oplog(value = TerminalGrpNickAcquisitionVO.class, name = "pages.specificationTask")
    @PostMapping(value = "delete")
    public void delete(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        terminalGroupNickNameService.deleteTask(vo.getGroupId(), vo.getTermList());
    }
    /**
     * 删除规范任务
     * @param vo
     * @return
     */
    @Oplog(value = TerminalGrpNickAcquisitionVO.class, name = "pages.specificationTask")
    @PostMapping(value = "deleteV2")
    public void deleteV2(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        terminalGroupNickNameService.deleteTaskV2(vo.getGroupId(), vo.getTermList());
    }
    /**
     * 清空终端
     * @return
     */
    @Oplog(name = "table.terminal")
    @PostMapping(value = "deleteAll")
    public void deleteAll() {
        terminalGroupNickNameService.deleteAll();
    }

    /**
     * 开始收集终端
     * @return
     */
    @Oplog(value = TerminalGrpNickAcquisitionVO.class, name = "pages.specificationTask", type = OperationTypeDict.ADD, defaultArg = true)
    @PostMapping(value = "startCollectTerminal")
    public void startCollectTerminal(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        terminalGroupNickNameService.startCollectTask(vo);
    }
    @Oplog(value = TerminalGrpNickAcquisitionVO.class, name = "pages.specificationTask", type = OperationTypeDict.ADD, defaultArg = true)
    @PostMapping(value = "add")
    public void addStrategy(@RequestBody TerminalGrpNickAcquisitionVO bean){
        terminalGroupNickNameService.addStrategy(bean);
    }

    /**
     * 修改分组和名称
     * @return
     */
    @PostMapping(value = "updateGroupNickName")
    public void updateGroupNickName(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        terminalGroupNickNameService.updateGroupNickName2(vo);
    }

    /**
     * 规范分组和名称
     * @return
     */
    @Oplog(value = TerminalGrpNickAcquisitionVO.class, name = "pages.syncGroupNickName", type = OperationTypeDict.UPDATE, defaultArg = true)
    @PostMapping(value = "synGroupNickName")
    public Map<String, Object> synGroupNickName(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        if (vo.getGroupId() == null) {
            vo.setGroupList(PermissionUtil.listPermissionGroupId());
        }
        return terminalGroupNickNameService.synGroupNickName2(vo);
    }

    /**
     *
     * 获取终端状态：在线状态和任务状态
     * @deprecated 待废弃：协议下发终端收集
     * @param vo
     * @return
     */
    @PostMapping(value = "getTerminalStatus")
    public Map<String, Integer> getTerminalStatus(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        return terminalGroupNickNameService.getTerminalStatus(vo.getGroupId(), vo.getTermList());
    }

    /**
     *
     * 获取终端状态：在线状态和任务状态
     *
     * @param vo
     * @return
     */
    @PostMapping(value = "getTerminalStatusV2")
    public Map<String, Integer> getTerminalStatusV2(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        return terminalGroupNickNameService.getTerminalStatusV2(vo.getGroupId(), vo.getTermList());
    }

    /**
     * 获取有效终端
     * @deprecated 待废弃：协议下发
     * @param vo
     * @return
     */
    @PostMapping(value = "getActiveTermPage")
    public GridPageDTO<Terminal> getActiveTermPage(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        return terminalGroupNickNameService.getActiveTermPage(vo);
    }

    /**
     * 获取有效终端
     * @deprecated 待废弃：协议下发
     * @param vo
     * @return
     */
    @PostMapping(value = "getActiveTermPageV2")
    public GridPageDTO<Terminal> getActiveTermPageV2(@RequestBody TerminalGrpNickAcquisitionVO vo) {
        return terminalGroupNickNameService.getActiveTermPageV2(vo);
    }

}
