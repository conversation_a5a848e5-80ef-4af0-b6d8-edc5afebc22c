package com.tipray.dlp.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tipray.dlp.bean.ProcessNetFlowLog;
import com.tipray.dlp.bean.vo.LogVO;
import com.tipray.dlp.bean.vo.NetFlowLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource
public interface ProcessNetFlowLogDao extends BaseMapper<ProcessNetFlowLog> {
    Long countByVO(NetFlowLogVO vo);

    List<ProcessNetFlowLog> listByVO(NetFlowLogVO vo);
}
