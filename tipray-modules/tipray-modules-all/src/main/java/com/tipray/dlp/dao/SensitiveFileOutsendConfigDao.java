package com.tipray.dlp.dao;

import com.tipray.dlp.bean.SensitiveFileOutsendConfig;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 敏感外发参数配置下发表(sensitive_file_outsend_config)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-10-09 16:07:39
 */
@Repository
public interface SensitiveFileOutsendConfigDao extends BaseDao<SensitiveFileOutsendConfig>{

    /**
     * 通过实体作为筛选条件查询
     * @return 对象列表
     */
    @Override
    List<SensitiveFileOutsendConfig> selectAll();
}
