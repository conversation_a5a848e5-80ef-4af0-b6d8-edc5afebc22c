package com.tipray.dlp.dao;

import com.tipray.dlp.bean.AlarmRecscreenLog;
import com.tipray.dlp.bean.vo.AlarmRecscreenLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * (AlarmRecscreenLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-05-06 19:19:07
 */
@Repository
@ShardingDataSource
public interface AlarmRecscreenLogDao extends BaseDao<AlarmRecscreenLog>{

    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(AlarmRecscreenLogVO vo);
     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<AlarmRecscreenLog> listByVO(AlarmRecscreenLogVO vo);
}
