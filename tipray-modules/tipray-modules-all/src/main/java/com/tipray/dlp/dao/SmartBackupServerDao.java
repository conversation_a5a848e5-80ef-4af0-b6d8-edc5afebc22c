package com.tipray.dlp.dao;

import com.tipray.dlp.backup.bean.SmartBackupServer;
import org.springframework.stereotype.Repository;

/**
 * 智能备份服务器DAO
 * <AUTHOR>
 * @date 2025-03-27 15:09
 */
@Repository
public interface SmartBackupServerDao {

    /**
     * 根据文件服务器设备Id获取绑定的智能备份服务器信息
     * @param fileDevId
     * @return
     */
    SmartBackupServer getSmartBackupByBindFileServerDevId(Long fileDevId);
}
