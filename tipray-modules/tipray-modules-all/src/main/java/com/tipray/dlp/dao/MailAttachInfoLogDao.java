package com.tipray.dlp.dao;
;
import com.tipray.dlp.bean.MailAttachInfoLog;
import com.tipray.dlp.bean.vo.MailAttachInfoLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource
public interface MailAttachInfoLogDao extends BaseDao<MailAttachInfoLog> {

    List<MailAttachInfoLog> listByVO(MailAttachInfoLogVO vo);

}
