package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.DiskScanTagDetailLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.DiskScanTagDetailLogVO;
import com.tipray.dlp.service.DiskScanTagDetailLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 全盘扫描获取标签内容Controller
 */
@Oplog(DiskScanTagDetailLog.class)
@RestController
@RequestMapping("/log/diskScanTagDetailLog")
public class DiskScanTagDetailLogController {
    @Autowired
    private DiskScanTagDetailLogService service;

    /**
     * 删除标签内容
     * @param list
     * @return
     */
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<DiskScanTagDetailLog> list) {
        service.deleteBatch(list);
        return ResponseDTO.success();
    }

    /**
     * 查询标签内容
     * @param vo
     * @return
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<DiskScanTagDetailLog> getPage(@RequestBody DiskScanTagDetailLogVO vo) {
        return service.getPage(vo);
    }

    /**
     * 导出打标签记录
     * 根据查询筛选条件导出Excel
     * @param vo
     * @return
     */
    @PostMapping(value = "export")
    public ResponseDTO exportExcelByLogVO(@RequestBody DiskScanTagDetailLogVO vo){
        return ResponseDTO.success(service.exportExcel(vo));
    }
}
