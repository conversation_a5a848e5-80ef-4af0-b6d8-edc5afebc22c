package com.tipray.dlp.controller;

import com.google.common.collect.Lists;
import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.BaseGroup;
import com.tipray.dlp.bean.PersonalizePolicy;
import com.tipray.dlp.bean.PictureLib;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.PersonalizePolicyService;
import com.tipray.dlp.service.PictureLibService;
import com.tipray.dlp.service.StgBusinessRelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 壁纸策略Controller
 */
@Oplog(PersonalizePolicy.class)
@RestController
@RequestMapping("/personalizePolicy")
@Slf4j
public class PersonalizePolicyController {

    @Autowired
    private PersonalizePolicyService service;
    @Resource
    private PictureLibService pictureLibService;
    @Autowired
    private StgBusinessRelService stgBusinessRelService;

    /**
     * 添加计算机个性化策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.personalizePolicy")
    @PostMapping(value = "add")
    public ResponseDTO add(@RequestBody PersonalizePolicy bean) {
        formateBean(bean);
        service.insert(bean);
        return ResponseDTO.success();
    }

    /**
     * 修改计算机个性化策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.personalizePolicy")
    @PostMapping(value = "update")
    public ResponseDTO update(@RequestBody PersonalizePolicy bean) {
        formateBean(bean);
        service.update(bean);
        return ResponseDTO.success();
    }

    private void formateBean(@RequestBody PersonalizePolicy bean) {
        // 终端要求：屏保图片+锁屏图片 合并存储在锁屏图片里。
        // 所以清理锁屏图片数据，避免数据库存储垃圾数据。
        bean.setLockScreenImg(Lists.newArrayList());
        List<PictureLib> picList = pictureLibService.listPictureLib();
        // 查询所有分组-->Map<分组id,分组名称>
        List<?> groups = pictureLibService.listGroup(null);
        Map<Long,String> groupMap = groups.stream()
                .map(g->(BaseGroup)g)
                .collect(Collectors.toMap(BaseGroup::getId,BaseGroup::getName,(key1,key2)->key2));
        // 将契合度设置到每一张图片
        Integer style = bean.getWallPaperStauts() == 1 ? bean.getFit(): 0;
        bean.setStyle(style);
        List<Map<String, Object>> list = bean.getPictureList();
        for (Map<String, Object> map : list) {
            // 根据id取图片库最新数据
            List<PictureLib> resList = picList.stream().filter(pic->map.get("id").toString().equalsIgnoreCase(pic.getId().toString()))
                    .collect(Collectors.toList());
            map.put("style", style);
            if(null!=resList && resList.size()>0){
                map.put("md5", resList.get(0).getMd5());
                map.put("name", resList.get(0).getName());
                map.put("resolution", resList.get(0).getResolution());
                map.put("ext", resList.get(0).getExt());
                map.put("guid", resList.get(0).getGuid());
                map.put("ftpId", resList.get(0).getFtpId());
                Long groupId = resList.get(0).getGroupId();
                map.put("groupId", groupId);
                map.put("groupName", groupMap.get(groupId));
            }
        }
        bean.setPictureList(list);
        List<Map<String, Object>> list2 = bean.getScreenSaverImgPolicy();
        for (Map<String, Object> map : list2) {
            if("2".equalsIgnoreCase(map.get("imgType").toString())){
                // 锁屏图片
                continue;
            }
            // 根据id取图片库最新数据
            List<PictureLib> resList = picList.stream().filter(pic->map.get("id").toString().equalsIgnoreCase(pic.getId().toString()))
                    .collect(Collectors.toList());
            map.put("style", style);
            if(null!=resList && resList.size()>0){
                map.put("md5", resList.get(0).getMd5());
                map.put("name", resList.get(0).getName());
                map.put("resolution", resList.get(0).getResolution());
                map.put("ext", resList.get(0).getExt());
                map.put("guid", resList.get(0).getGuid());
                map.put("ftpId", resList.get(0).getFtpId());
                Long groupId = resList.get(0).getGroupId();
                map.put("groupId", groupId);
                map.put("groupName", groupMap.get(groupId));
                // 屏保图片类型
                map.put("imgType", 1);
            }
        }
        bean.setScreenSaverImgPolicy(list2);
    }

    /**
     * 删除计算机个性化策略
     * @param ids
     */
    @Oplog(name = "pages.personalizePolicy")
    @PostMapping(value = "delete")
    public void delete(String ids) {
        service.deleteById(ids);
        stgBusinessRelService.deleteRel(ids);
    }

    /**查询计算机个性化策略分页列表*/
    @PostMapping(value = "findPage")
    public GridPageDTO<PersonalizePolicy> findPage(@RequestBody StrategyVO vo) {
        return service.getStrategyPage(vo);
    }

    /**通过策略名称查询计算机个性化策略*/
    @PostMapping(value = "listByName")
    public List<PersonalizePolicy> listByName(String name) {
        return service.listByName(name);
    }

}
