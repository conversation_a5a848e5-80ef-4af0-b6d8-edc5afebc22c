package com.tipray.dlp.controller;

import com.tipray.dlp.bean.dict.DbAcctDevTypeDict;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.service.DatabaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Objects;

/**
 * 数据库密码同步，接口
 * <AUTHOR>
 * @date 2024-10-09 14:47
 */
@RestController
@RequestMapping("dbAcct")
@Slf4j
public class DbAcctExternalApiController {

    @Resource
    private DatabaseService databaseService;

    /**
     * 获取数据库账号密码 -- 供nac,报表使用
     * type: 187: 报表， 33：NAC
     * @return
     */
    @PostMapping(value = "getDbServerPasswords")
    public ResponseDTO getDbServerPasswords(Integer type) {
        if (Objects.isNull(type) || !Arrays.asList(DbAcctDevTypeDict.DlpAnalysis.getDevType(), DbAcctDevTypeDict.NacConsole.getDevType()).contains(type)) {
            log.error("获取数据库账号密码失败！参数错误:{}", type);
            return ResponseDTO.failure(403, "参数错误");
        }
        return ResponseDTO.success(databaseService.getDbServerPasswords(type));
    }
}
