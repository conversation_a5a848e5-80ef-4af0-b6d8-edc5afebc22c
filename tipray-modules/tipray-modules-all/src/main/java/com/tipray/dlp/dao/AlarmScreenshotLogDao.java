package com.tipray.dlp.dao;

import com.tipray.dlp.bean.AlarmScreenshotLog;
import com.tipray.dlp.bean.vo.AlarmScreenshotLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * (AlarmScreenshotLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-05-06 19:19:07
 */
@Repository
@ShardingDataSource
public interface AlarmScreenshotLogDao extends BaseDao<AlarmScreenshotLog>{

    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(AlarmScreenshotLogVO vo);
     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<AlarmScreenshotLog> listByVO(AlarmScreenshotLogVO vo);
}
