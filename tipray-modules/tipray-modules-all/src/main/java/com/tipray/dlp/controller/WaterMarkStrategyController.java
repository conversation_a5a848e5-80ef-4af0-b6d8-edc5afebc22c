package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.*;
import com.tipray.dlp.bean.dict.OperationTypeDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.bean.vo.WpSignatureProcessVO;
import com.tipray.dlp.mybatis.bean.PageVO;
import com.tipray.dlp.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.awt.*;
import java.io.IOException;
import java.util.List;
import java.util.*;

/**
 * 打印(屏幕)水印策略(ContentStrategy)表控制层
 *
 * @since 2019-10-08 09:35:07
 */
@Oplog(PrintWaterMarkStrategy.class)
@RestController
@RequestMapping("/waterMark")
public class WaterMarkStrategyController {
    @Autowired
    private PrintWaterMarkStgService printWaterService;
    @Autowired
    private ScreenWaterMarkStgService screenWaterService;
    @Autowired
    private OfficeWaterMarkStgService officeWaterService;
    @Autowired
    private WaterMarkLibService waterMarkLibService;

    /** 得到水印策略分页*/
    @PostMapping(value = "getPage")
    public Object getPage(StrategyVO vo) {
        if(vo!=null && vo.getStgTypeNumber()!=null){
            if(vo.getStgTypeNumber().equals(PrintWaterMarkStgService.STG_TYPE_NUMBER)){
                return printWaterService.getPage(vo);
            } else if(vo.getStgTypeNumber().equals(ScreenWaterMarkStgService.STG_TYPE_NUMBER)){
                return screenWaterService.getPage(vo);
            } else if(vo.getStgTypeNumber().equals(OfficeWaterMarkStgService.STG_TYPE_NUMBER)){
                return officeWaterService.getPage(vo);
            }
        }
        return new GridPageDTO<>();
    }

    @PostMapping("getByName")
    public List<? extends BaseStrategy> getByName(Integer stgTypeNumber, String name) {
        if(stgTypeNumber!=null){
            if(stgTypeNumber.equals(PrintWaterMarkStgService.STG_TYPE_NUMBER)){
                return printWaterService.listByName(name);
            } else if(stgTypeNumber.equals(ScreenWaterMarkStgService.STG_TYPE_NUMBER)){
                return screenWaterService.listByName(name);
            } else if(stgTypeNumber.equals(OfficeWaterMarkStgService.STG_TYPE_NUMBER)){
                return officeWaterService.listByName(name);
            }
        }
        return Collections.emptyList();
    }

    /**
     * 删除打印水印策略
     * @param ids
     */
    @Oplog(value=PrintWaterMarkStrategy.class, name = "pages.printWaterMarkStrategy")
    @PostMapping(value = "deletePrint")
    public void deletePrint(String ids) {
        printWaterService.deleteById(ids);
    }

    /**
     * 删除屏幕水印策略
     * @param ids
     */
    @Oplog(value = ScreenWaterMarkStrategy.class,name = "pages.screenWaterMarkStrategy")
    @PostMapping(value = "deleteScreen")
    public void deleteScreen(String ids) {
        screenWaterService.deleteById(ids);
    }

    /**
     * 删除Office文档水印策略
     * @param ids
     */
    @Oplog(value=OfficeWaterMarkStg.class, name = "pages.OfficeWaterMarkStg")
    @PostMapping(value = "deleteOffice")
    public void deleteOffice(String ids) {
        officeWaterService.deleteById(ids);
    }

    /**
     * 新增Office文档水印策略
     * @param bean
     */
    @Oplog(value=OfficeWaterMarkStg.class, name = "pages.OfficeWaterMarkStg")
    @PostMapping(value = "saveOffice")
    public void saveOffice(@RequestBody OfficeWaterMarkStg[] bean) {
        officeWaterService.insert(bean);
    }
    /**
     * 修改Office文档水印策略
     * @param bean
     */
    @Oplog(value=OfficeWaterMarkStg.class, name = "pages.OfficeWaterMarkStg")
    @PostMapping(value = "updateOffice")
    public void updateOffice(@RequestBody OfficeWaterMarkStg[] bean) {
        officeWaterService.update(bean);
    }

    @GetMapping(value = "officeAdvanceOpts")
    public ResponseDTO getOfficeAdvanceOpts() {
        return ResponseDTO.success(officeWaterService.getAdvanceOpts());
    }

    @PostMapping(value = "saveOfficeAdvanceOpts")
    @Oplog(value = OfficeWaterMarkAdvanceOpts.class, name = "table.highConfig", type = OperationTypeDict.UPDATE)
    public ResponseDTO saveOfficeAdvanceOpts(@RequestBody OfficeWaterMarkAdvanceOpts advanceOpts) {
        return ResponseDTO.success(officeWaterService.saveAdvanceOpts(advanceOpts));
    }

    /**
     * 新增打印水印策略
     * @param bean
     * @throws IOException
     */
    @Oplog(value=PrintWaterMarkStrategy.class, name = "pages.printWaterMarkStrategy")
    @PostMapping(value = "savePrint")
    public void savePrint(@RequestBody PrintWaterMarkStrategy[] bean) throws IOException {
        for (PrintWaterMarkStrategy strategy : bean) {
            PrintWaterMark waterMark = strategy.getWaterMark();
            if(waterMark!=null){
                PrintWaterMark newMark = waterMarkLibService.getWaterMarById(waterMark.getId()).caseTo(PrintWaterMark.class);
                newMark.setId(waterMark.getId());
                newMark.setWaterType(strategy.getWaterType());
                strategy.setWaterMark(newMark);
            }
        }
        printWaterService.insert(bean);
    }

    /**
     * 修改打印水印策略
     * @param bean
     * @throws IOException
     */
    @Oplog(value=PrintWaterMarkStrategy.class, name = "pages.printWaterMarkStrategy")
    @PostMapping(value = "updatePrint")
    public void updatePrint(@RequestBody PrintWaterMarkStrategy[] bean) throws IOException {
        for (PrintWaterMarkStrategy strategy : bean) {
            PrintWaterMark waterMark = strategy.getWaterMark();
            if (waterMark != null) {
                PrintWaterMark newMark = waterMarkLibService.getWaterMarById(waterMark.getId()).caseTo(PrintWaterMark.class);
                newMark.setId(waterMark.getId());
                newMark.setWaterType(strategy.getWaterType());
                strategy.setWaterMark(newMark);
            }
        }
        printWaterService.update(bean);
    }

    /**
     * 新增屏幕水印策略
     * @param bean
     */
    @Oplog(value = ScreenWaterMarkStrategy.class,name = "pages.screenWaterMarkStrategy")
    @PostMapping(value = "saveScreen")
    public void saveScreen(@RequestBody ScreenWaterMarkStrategy[] bean) {
        for (ScreenWaterMarkStrategy strategy : bean) {
            ScreenWaterMark waterMark = strategy.getWaterMark();
            Integer waterType = strategy.getWaterType();
            if(waterMark!=null){
                ScreenWaterMark newMark = waterMarkLibService.getWaterMarById(waterMark.getId()).caseTo(ScreenWaterMark.class);
                newMark.setId(waterMark.getId());
                newMark.setWaterType(waterType);
                strategy.setWaterMark(newMark);
            }
            if (waterType <= 1) {
                strategy.setUrlList(Collections.emptyList());
            } else if (waterType >= 2 && waterType <= 3) {
                strategy.setProcessList(Collections.emptyList());
            }
        }
        screenWaterService.insert(bean);
    }

    /**
     * 修改屏幕水印策略
     * @param bean
     * @throws IOException
     */
    @Oplog(value = ScreenWaterMarkStrategy.class,name = "pages.screenWaterMarkStrategy")
    @PostMapping(value = "updateScreen")
    public void updateScreen(@RequestBody ScreenWaterMarkStrategy[] bean) throws IOException {
        for (ScreenWaterMarkStrategy strategy : bean) {
            ScreenWaterMark waterMark = strategy.getWaterMark();
            if(waterMark!=null){
                WaterMarkLib waterMarkLib = waterMarkLibService.getWaterMarById(waterMark.getId());
                if (waterMarkLib != null) {
                    ScreenWaterMark newMark = waterMarkLib.caseTo(ScreenWaterMark.class);
                    newMark.setId(waterMark.getId());
                    newMark.setWaterType(strategy.getWaterType());
                    strategy.setWaterMark(newMark);
                }
            }
        }
        screenWaterService.update(bean);
    }

    /**
     * 新增水印程序库
     * @param bean
     * @return
     */
    @Oplog(value = PrintWpSignatureProcess.class,name = "pages.waterMarkProcess")
    @PostMapping(value = "savePrintApp")
    public PrintWpSignatureProcess savePrintApp(PrintWpSignatureProcess bean) {
        printWaterService.savePrintApp(bean);
        return bean;
    }

    /**
     * 批量新增屏幕水印程序库
     * @param list
     * @return
     */
    @PostMapping(value = "savePrintAppBatch")
    public List<PrintWpSignatureProcess> savePrintAppBatch(@RequestBody List<PrintWpSignatureProcess> list) {
        printWaterService.savePrintAppBatch(list);
        return list;
    }

    /**
     * 修改屏幕水印程序库
     * @param bean
     */
    @Oplog(value = PrintWpSignatureProcess.class,name = "pages.waterMarkProcess")
    @PostMapping(value = "editPrintApp")
    public void editPrintApp(PrintWpSignatureProcess bean) {
        printWaterService.editPrintApp(bean);
    }

    /**
     * 删除屏幕水印程序库
     * @param ids
     */
    @Oplog(value = PrintWpSignatureProcess.class,name = "pages.waterMarkProcess")
    @PostMapping(value = "removePrintAppBatch")
    public void removePrintAppBatch(String ids) {
        printWaterService.removePrintAppBatch(ids);
    }

    /** 得到屏幕水印程序库列表*/
    @PostMapping(value = "findPrintAppList")
    public GridPageDTO<PrintWpSignatureProcess> findPrintAppList(WpSignatureProcessVO bean) {
        return new GridPageDTO<>(printWaterService.findPrintAppList(bean));
    }
    /** 得到指定屏幕水印程序库列表*/
    @PostMapping(value = "getBatchPrintAppList")
    public GridPageDTO<PrintWpSignatureProcess> getBatchPrintAppList(String ids) {
        return new GridPageDTO<>(printWaterService.getBatchPrintAppList(ids));
    }
    /** 分页获取屏幕水印网址库*/
    @PostMapping(value = "getWatermarkUrlPage")
    public ResponseDTO getScreenWatermarkUrlPage(@RequestBody PageVO pageVO) {
        return ResponseDTO.success(screenWaterService.getUrlPage(pageVO));
    }

    /**
     * 新增屏幕水印网址库
     * @param screenWatermarkUrl
     * @return
     */
    @Oplog(value = ScreenWatermarkUrl.class,name = "pages.screenWatermarkUrl")
    @PostMapping(value = "screenWatermarkUrl")
    public ResponseDTO insertScreenWatermarkUrl(@RequestBody ScreenWatermarkUrl screenWatermarkUrl) {
        return ResponseDTO.success(screenWaterService.insertScreenWatermarkUrl(screenWatermarkUrl));
    }

    /**
     * 修改屏幕水印网址库
     * @param screenWatermarkUrl
     * @return
     */
    @Oplog(value = ScreenWatermarkUrl.class,name = "pages.screenWatermarkUrl")
    @PostMapping(value = "updateScreenWatermarkUrl")
    public ResponseDTO updateScreenWatermarkUrl(@RequestBody ScreenWatermarkUrl screenWatermarkUrl) {
        return ResponseDTO.success(screenWaterService.updateScreenWatermarkUrl(screenWatermarkUrl));
    }

    /**
     * 删除屏幕水印网址库
     * @param ids
     * @return
     */
    @Oplog(value = ScreenWatermarkUrl.class, type = OperationTypeDict.DELETE,name = "pages.screenWatermarkUrl")
    @PostMapping(value = "deleteScreenWatermarkUrl")
    public ResponseDTO deleteScreenWatermarkUrl(String ids) {
        screenWaterService.deleteScreenWatermarkUrl(ids);
        return ResponseDTO.success();
    }
    /** 获取屏幕水印网址*/
    @PostMapping(value = "getWatermarkUrlList")
    public ResponseDTO getUrlListByIds(@RequestBody PageVO pageVO) {
        return ResponseDTO.success(screenWaterService.getUrlListByIds(pageVO.getIds()));
    }
    /** 获取屏幕水印网址信息*/
    @PostMapping(value = "getWatermarkUrlByName")
    public ResponseDTO getUrlByName(String name) {
        return ResponseDTO.success(screenWaterService.getWatermarkUrlByName(name));
    }


    /** 将未保存到屏幕水印网址信息的网址信息库列表进行保存*/
    @PostMapping(value = "fromLibToWatermarkUrl")
    public ResponseDTO fromLibToWatermarkUrl(@RequestBody List<Url> urls) {
        return ResponseDTO.success(screenWaterService.fromLibToWatermarkUrl(urls));
    }

    /** 根据程序名称查询同名app*/
    @PostMapping(value = "getPrintAppByName")
    public PrintWpSignatureProcess getPrintAppByName(PrintWpSignatureProcess bean) {
        return printWaterService.getPrintAppByName(bean);
    }

    /** 根据程序名称查询同名app*/
    @PostMapping(value = "getPrintAppByNames")
    public List<PrintWpSignatureProcess> getPrintAppByNames(Integer appType, String processNames) {
        return printWaterService.getPrintAppByNames(appType, processNames);
    }

    /** 获取系统安装的字体*/
    @PostMapping(value = "getFontOptions")
    public List<Map<String, Object>> getFontOptions() {
        List<Map<String, Object>> fontoptions = new ArrayList<>();
        GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
        String[] fonts = ge.getAvailableFontFamilyNames();
        for (String font : fonts) {
            Map<String, Object> map = new HashMap<>();
            map.put("value", font);
            map.put("label", font);
            fontoptions.add(map);
        }
        return fontoptions;
    }
}
