package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.OfflineLockScreen;
import com.tipray.dlp.bean.dict.MsgModuleDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.exception.ParamException;
import com.tipray.dlp.service.OfflineLockScreenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 离线锁屏策略Controller
 */
@RestController
@Oplog(OfflineLockScreen.class)
@RequestMapping("/offlineLockScreen")
@Slf4j
public class OfflineLockScreenController {
    @Autowired
    private OfflineLockScreenService offlineLockScreenService;

    /** 添加离线锁屏策略*/
    @Oplog(name="pages.offlineLockScreenStg")
    @PostMapping(value = "add")
    public OfflineLockScreen[] addStrategy(@RequestBody OfflineLockScreen[] beans){
        for (OfflineLockScreen bean:beans) {
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.OFFLINE_LOCK_SCREEN.getCode());
        }
        offlineLockScreenService.insert(beans);
        return beans;
    }

    /** 修改离线锁屏策略*/
    @Oplog(name="pages.offlineLockScreenStg")
    @PostMapping(value = "update")
    public OfflineLockScreen[] updateStrategy(@RequestBody OfflineLockScreen[] beans){
        for (OfflineLockScreen bean:beans) {
            if (bean.getId() == null) {
                throw new ParamException("参数错误");
            }
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.OFFLINE_LOCK_SCREEN.getCode());
        }
        offlineLockScreenService.update(beans);
        return beans;
    }

    /** 删除离线锁屏策略*/
    @Oplog(name="pages.offlineLockScreenStg")
    @PostMapping(value = "delete")
    public void deleteAppBlock(String ids) {
        offlineLockScreenService.deleteById(ids);
    }

    @PostMapping("getByName")
    public OfflineLockScreen getByName(String name) {
        return offlineLockScreenService.getStrategyByName(name);
    }

    /** 离线锁屏策略分页查询*/
    @PostMapping(value = "getPage")
    public GridPageDTO<OfflineLockScreen> getStategyPage(@RequestBody StrategyVO vo) {
        return offlineLockScreenService.getStrategyPage(vo);
    }

}
