package com.tipray.dlp.dao;

import com.tipray.dlp.bean.DiskScanTagDetailLog;
import com.tipray.dlp.bean.vo.DiskScanTagDetailLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource
public interface DiskScanTagDetailLogDao extends BaseDao<DiskScanTagDetailLog>{
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(DiskScanTagDetailLogVO vo);

     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<DiskScanTagDetailLog> listByVO(DiskScanTagDetailLogVO vo);
}
