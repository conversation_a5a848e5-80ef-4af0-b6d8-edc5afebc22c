package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.ArpFireWallStrategy;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.ArpFireWallStrategyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
/**
 * ARP防火墙策略Controller
 * <AUTHOR>
 * @since 2020-6-6
 */
@RestController
@Oplog(ArpFireWallStrategy.class)
@RequestMapping("/arpFireWallStrategy")
public class ArpFireWallStrategyController {
    @Autowired
    private ArpFireWallStrategyService arpFireWallStrategyService;

    /**
     * 添加ARP防火墙策略
     * @param bean
     * @return
     */
    @Oplog(name="route.firewallSettings")
    @PostMapping(value = "add")
    public ArpFireWallStrategy addStrategy(@RequestBody ArpFireWallStrategy bean) {
        arpFireWallStrategyService.insertStrategy(bean);
        return bean;
    }

    /**
     * 修改ARP防火墙策略
     * @param bean
     * @return
     */
    @Oplog(name="route.firewallSettings")
    @PostMapping(value = "update")
    public ArpFireWallStrategy updateStrategy(@RequestBody ArpFireWallStrategy bean) {
        arpFireWallStrategyService.updateStrategy(bean);
        return bean;
    }

    /**
     * 删除ARP防火墙限制策略
     * @param ids
     */
    @Oplog(name="route.firewallSettings")
    @PostMapping(value = "delete")
    public void deleteStrategy(String ids) {
        arpFireWallStrategyService.deleteStrategyById(ids);
    }

    /**
     * 根据主键ID获取策略
     * @param id
     * @return
     */
    @GetMapping("get/{id}")
    public ArpFireWallStrategy getById(@PathVariable("id") Long id) {
        return arpFireWallStrategyService.getStrategyById(id);
    }

    /**
     * 根据名称获取策略
     * @param name
     * @return
     */
    @PostMapping("getByName")
    public ArpFireWallStrategy getByName(String name) {
        return arpFireWallStrategyService.getStrategyByName(name);
    }

    /**
     * 获取ARP防火墙策略
     * @param vo
     * @return
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<ArpFireWallStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return arpFireWallStrategyService.getStrategyPage(vo);
    }

}
