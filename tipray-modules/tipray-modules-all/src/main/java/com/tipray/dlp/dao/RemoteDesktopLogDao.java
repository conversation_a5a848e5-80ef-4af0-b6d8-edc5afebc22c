package com.tipray.dlp.dao;

import com.tipray.dlp.bean.RemoteDesktopLog;
import com.tipray.dlp.bean.vo.RemoteDesktopLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: caitw
 * @date: 2024/7/19 10:18
 * @description: 远程桌面审计表Dao
 */
@Repository
@ShardingDataSource
public interface RemoteDesktopLogDao extends BaseDao<RemoteDesktopLog> {

    Long countByVO(RemoteDesktopLogVO vo);

    /**
     * 查询审计日志数据
     * @param vo 请求体
     * @return 查询结果
     */
    List<RemoteDesktopLog> listByVO(RemoteDesktopLogVO vo);
}
