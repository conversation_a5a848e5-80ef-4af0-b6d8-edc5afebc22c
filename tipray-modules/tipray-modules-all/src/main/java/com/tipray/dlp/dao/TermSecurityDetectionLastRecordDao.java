package com.tipray.dlp.dao;

import com.tipray.dlp.bean.TermSecurityDetectionLastRecord;
import com.tipray.dlp.bean.vo.TermSecurityDetectionLastRecordVO;
import com.tipray.dlp.bean.vo.TermSecurityDetectionRecordVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/6 14:41
 */
@Repository
@ShardingDataSource
public interface TermSecurityDetectionLastRecordDao extends BaseDao<TermSecurityDetectionLastRecord>  {
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(TermSecurityDetectionRecordVO vo);

    /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<TermSecurityDetectionLastRecord> listByVO(TermSecurityDetectionLastRecordVO vo);

    TermSecurityDetectionLastRecord getById(TermSecurityDetectionLastRecordVO vo);
}
