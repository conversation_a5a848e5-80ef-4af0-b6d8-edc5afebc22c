package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.ProcessCollectRule;
import com.tipray.dlp.bean.dict.OperationTypeDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.ProcessCollectRuleVO;
import com.tipray.dlp.mybatis.bean.PageVO;
import com.tipray.dlp.service.ProcessCollectRuleService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 进程收集规则详细表(ProcessCollectRule)表控制层
 *
 * <AUTHOR>
 * @since 2020-05-22 14:56:22
 */
@Oplog(ProcessCollectRule.class)
@RestController
@RequestMapping("/processCollectRule")
public class ProcessCollectRuleController {
    /**
     * 服务对象
     */
    @Resource
    private ProcessCollectRuleService processCollectRuleService;

    /**
     * 新增进程收集规则
     * @param bean
     * @return
     */
    @Oplog(value = ProcessCollectRuleVO.class,name = "pages.processCollectRule")
    @PostMapping(value = "add")
    public ProcessCollectRule insert(@Valid ProcessCollectRuleVO bean) {
        this.formatName(bean);
        processCollectRuleService.insertProcessCollectRule(bean);
        return bean;
    }

    /**
     * 新增进程收集规则
     * @param bean
     * @return
     */
    @Oplog(value = ProcessCollectRuleVO.class, type = OperationTypeDict.ADD,name = "pages.processCollectRule")
    @PostMapping(value = "batchAdd")
    public List<ProcessCollectRule> batchInsert(@Valid ProcessCollectRuleVO bean) {
        this.formatName(bean);
        return processCollectRuleService.batchInsertProcessCollectRule(bean);
    }


    /**
     * 格式化stgIssue里面记录的名称
     * @param bean
     */
    private void formatName(ProcessCollectRule bean) {
        if (bean.getType() == null) {
            bean.setName("");
        }
        bean.setName(bean.getType() == 1 ? "程序名称" : bean.getType() == 2 ? "程序路径" : bean.getType() == 3 ? "产品名称" : "数字签名");
    }

    /**
     * 修改进程收集规则
     * @param bean
     * @return
     */
    @Oplog(name = "pages.processCollectRule")
    @PostMapping(value = "update")
    public ResponseDTO update(@Valid ProcessCollectRule bean) {
        try {
            this.formatName(bean);
            processCollectRuleService.updateProcessCollectRule(bean);
            return ResponseDTO.success(bean);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseDTO.failure(e);
        }
    }
    /**
     * 删除进程收集规则
     * @param ids
     * @return
     */
    @Oplog(name = "pages.processCollectRule")
    @PostMapping(value = "delete")
    public ResponseDTO deleteById(String ids) {
        try {
            processCollectRuleService.deleteProcessCollectRuleById(ids);
            return ResponseDTO.success();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseDTO.failure(e);
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("get/{id}")
    public ProcessCollectRule getById(@PathVariable("id") Long id) {
        return processCollectRuleService.getProcessCollectRuleById(id);
    }

    /** 查询全部数据*/
    @PostMapping(value = "list")
    public List<ProcessCollectRule> list() {
        return processCollectRuleService.listProcessCollectRule();
    }

    /** 根据策略id查询数据*/
    @PostMapping(value = "listByStgId")
    public List<ProcessCollectRule> listByStgId(Long stgId) {
        return processCollectRuleService.listByStgId(stgId);
    }

    /** 查询进程收集规则*/
    @PostMapping(value = "getPage")
    public GridPageDTO<ProcessCollectRule> getPage(@ModelAttribute PageVO vo) {
        return processCollectRuleService.getProcessCollectRulePage(vo);
    }

}
