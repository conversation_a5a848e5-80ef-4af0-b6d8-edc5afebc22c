package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.EffectiveContentConfig;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.EffectiveContentConfigService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 敏感检测高级配置Controller
 * <AUTHOR>
 */
@Oplog(EffectiveContentConfig.class)
@RestController
@RequestMapping("/effectiveContentConfig")
public class EffectiveContentConfigController {
    @Resource
    private EffectiveContentConfigService effectiveContentConfigService;

    /**
     * 新增配置
     * @param bean
     * @return
     */
    @Oplog(value = EffectiveContentConfig.class,name = "route.effectiveContentConfig")
    @PostMapping(value = "add")
    public EffectiveContentConfig add(@RequestBody EffectiveContentConfig bean) {
        effectiveContentConfigService.insert(bean);
        return bean;
    }

    /**
     * 修改配置
     * @param bean
     * @return
     */
    @Oplog(value = EffectiveContentConfig.class,name = "route.effectiveContentConfig")
    @PostMapping(value = "update")
    public EffectiveContentConfig update(@RequestBody EffectiveContentConfig bean) {
        effectiveContentConfigService.update(bean);
        return bean;
    }

    /**
     * 删除配置
     * @param ids
     */
    @Oplog(value = EffectiveContentConfig.class,name = "route.effectiveContentConfig")
    @PostMapping(value = "delete")
    public void deleteById(String ids) {
        effectiveContentConfigService.deleteById(ids);
    }

    @PostMapping(value = "getByName")
    public EffectiveContentConfig getByName(String name) {
        return effectiveContentConfigService.getByName(name);
    }

    /**分页查询*/
    @PostMapping(value = "getPage")
    public GridPageDTO<EffectiveContentConfig> getPage(@RequestBody StrategyVO vo) {
        return effectiveContentConfigService.getPage(vo);
    }
}