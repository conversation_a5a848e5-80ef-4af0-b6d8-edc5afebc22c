package com.tipray.dlp.dao;

import com.tipray.dlp.bean.SysUserMultiAuth;
import com.tipray.dlp.mybatis.annotation.WhereIn;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 管理员登录多重认证
 * <AUTHOR>
 * @date 2023/2/17 13:28
 */
@Repository
public interface SysUserMultiAuthDao  extends BaseDao<SysUserMultiAuth>{
    /**
     * 取消管理员所有登录认证
     * @param sysUserIds 管理员id
     */
    @WhereIn(@WhereIn.Param("sysUserIds"))
    void disableMultiLoginAuthBySysUserIds(List<Long> sysUserIds);
}
