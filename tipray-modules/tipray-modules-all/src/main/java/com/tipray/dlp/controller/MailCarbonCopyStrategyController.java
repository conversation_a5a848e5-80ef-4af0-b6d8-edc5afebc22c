package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.MailCarbonCopyStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.MailCarbonCopyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 邮件抄送Controller
 */
@Oplog(MailCarbonCopyStrategy.class)
@RestController
@RequestMapping("/mailCarbonCopyStrategy")
public class MailCarbonCopyStrategyController {
    @Resource
    private MailCarbonCopyService mailCarbonCopyService;

    /**
     * 新增邮件抄送策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.mailCarbonCopyStrategy")
    @PostMapping(value = "add")
    public MailCarbonCopyStrategy add(@RequestBody MailCarbonCopyStrategy bean) {
        mailCarbonCopyService.insertStrategy(bean);
        return bean;
    }

    /**
     * 修改邮件抄送策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.mailCarbonCopyStrategy")
    @PostMapping(value = "update")
    public MailCarbonCopyStrategy update(@RequestBody MailCarbonCopyStrategy bean) {
        mailCarbonCopyService.updateStrategy(bean);
        return bean;
    }

    /**
     * 删除邮件抄送策略
     * @param ids
     */
    @Oplog(name = "pages.mailCarbonCopyStrategy")
    @PostMapping(value = "delete")
    public void deleteRecv(String ids) {
        mailCarbonCopyService.deleteStrategyById(ids);
    }

    @PostMapping("getByName")
    public MailCarbonCopyStrategy getByName(String name) {
        return mailCarbonCopyService.getStrategyByName(name);
    }

    /** 邮件抄送策略分页列表*/
    @PostMapping(value = "getPage")
    public GridPageDTO<MailCarbonCopyStrategy> getPage(@RequestBody StrategyVO vo) {
        return mailCarbonCopyService.getStrategyPage(vo);
    }

}
