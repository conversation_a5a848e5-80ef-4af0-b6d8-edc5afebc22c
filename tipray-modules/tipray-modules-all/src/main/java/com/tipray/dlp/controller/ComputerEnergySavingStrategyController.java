package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.ComputerEnergySavingStrategy;
import com.tipray.dlp.bean.dict.OperationTypeDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.ComputerEnergySavingStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 计算机节能设置Controller
 * <AUTHOR>
 * @date 2023/7/5 14:50
 */
@Oplog(ComputerEnergySavingStrategy.class)
@RestController
@RequestMapping("/computerEnergySaving")
public class ComputerEnergySavingStrategyController {

    @Resource
    private ComputerEnergySavingStrategyService computerEnergySavingStrategyService;

    /** 计算机节能设置*/
    @PostMapping(value = "getPage")
    public GridPageDTO<ComputerEnergySavingStrategy> getPage(@RequestBody StrategyVO vo){
        return computerEnergySavingStrategyService.getStrategyPage(vo);
    }


    @Oplog(type = OperationTypeDict.ADD, value = ComputerEnergySavingStrategy.class,name = "pages.energySavingStrategy")
    @PostMapping(value = "add")
    public ComputerEnergySavingStrategy addStrategy(@Valid @RequestBody ComputerEnergySavingStrategy bean) {
        computerEnergySavingStrategyService.insertStrategy(bean);
        return bean;
    }

    @Oplog(type = OperationTypeDict.UPDATE, value = ComputerEnergySavingStrategy.class,name = "pages.energySavingStrategy")
    @PostMapping(value = "update")
    public ComputerEnergySavingStrategy updateStrategy(@Valid @RequestBody ComputerEnergySavingStrategy bean) {
        computerEnergySavingStrategyService.updateStrategy(bean);
        return bean;
    }

    @Oplog(type = OperationTypeDict.DELETE,name = "pages.energySavingStrategy")
    @PostMapping(value = "delete")
    public void deleteStrategy(String ids) {
        computerEnergySavingStrategyService.deleteStrategyById(ids);
    }

    @GetMapping("getByName")
    public ComputerEnergySavingStrategy getByName(String name) {
        return computerEnergySavingStrategyService.getStrategyByName(name);
    }
}
