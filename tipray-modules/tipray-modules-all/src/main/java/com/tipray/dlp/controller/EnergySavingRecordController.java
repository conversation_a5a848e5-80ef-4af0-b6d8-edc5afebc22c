package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.EnergySavingRecord;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.EnergySavingRecordVO;
import com.tipray.dlp.service.EnergySavingRecordService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.List;

/**
 * 计算机节能记录
 * <AUTHOR>
 * @date 2023/7/6 15:27
 */
@Oplog(EnergySavingRecord.class)
@RestController
@RequestMapping("/log/energySavingRecord")
public class EnergySavingRecordController {

    @Resource
    private EnergySavingRecordService energySavingRecordService;

    /** 分页查询计算机节能记录*/
    @Oplog(value = EnergySavingRecordVO.class, name = "export.energySavingRecord")
    @PostMapping(value = "getPage")
    public GridPageDTO<EnergySavingRecord> getPage(@RequestBody EnergySavingRecordVO vo){
        return energySavingRecordService.getEnergySavingRecordPage(vo);
    }

    /** 导出计算机节能记录*/
    @PostMapping(value = "export")
    @Oplog(value = EnergySavingRecordVO.class, delayLog = true, name = "export.energySavingRecord")
    public ResponseDTO exportExcel(@RequestBody EnergySavingRecordVO vo) {
        return ResponseDTO.success(energySavingRecordService.exportExcel(vo));
    }

    /**
     * 删除日志
     * @param list
     * @return
     */
    @Oplog(name = "export.energySavingRecord")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<EnergySavingRecord> list) {
        energySavingRecordService.deleteBatch(list);
        return ResponseDTO.success();
    }
}
