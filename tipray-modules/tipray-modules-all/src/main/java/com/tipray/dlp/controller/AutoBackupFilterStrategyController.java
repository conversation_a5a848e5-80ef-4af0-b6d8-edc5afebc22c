package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.AutoBackupFilterStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.AutoBackupFilterStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 自动备份过滤策略(AutoBackupFilterStrategy)表控制层
 *
 * <AUTHOR>
 * @since 2019-10-21 09:55:30
 */
@Oplog(AutoBackupFilterStrategy.class)
@RestController
@RequestMapping("/autoBackupFilterStrategy")
public class AutoBackupFilterStrategyController {
    /**
     * 服务对象
     */
    @Resource
    private AutoBackupFilterStrategyService autoBackupFilterStrategyService;

    /**
     * 添加自动备份过滤策略
     * @param bean
     * @return
     */
    @Oplog(name="pages.autoBackupStg")
    @PostMapping(value = "add")
    public AutoBackupFilterStrategy insert(@RequestBody AutoBackupFilterStrategy bean){
        autoBackupFilterStrategyService.insertStrategy(bean);
        return bean;
    }

    /**
     * 修改自动备份过滤策略
     * @param bean
     * @return
     */
    @Oplog(name="pages.autoBackupStg")
    @PostMapping(value = "update")
    public AutoBackupFilterStrategy update(@RequestBody AutoBackupFilterStrategy bean){
        autoBackupFilterStrategyService.updateStrategy(bean);
        return bean;
    }

    /**
     * 删除自动备份过滤策略
     * @param ids
     */
    @Oplog(name="pages.autoBackupStg")
    @PostMapping(value = "delete")
    public void deleteById(String ids){
        autoBackupFilterStrategyService.deleteStrategyById(ids);
    }
    /**
     * 根据主键ID获取策略
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("get/{id}")
    public AutoBackupFilterStrategy getById(@PathVariable("id") Long id) {
        return autoBackupFilterStrategyService.getStrategyById(id);
    }

    /**
     * 根据名称获取策略
     * @param name
     * @return
     */
    @PostMapping("getByName")
    public AutoBackupFilterStrategy getByName(String name) {
        return autoBackupFilterStrategyService.getStrategyByName(name);
    }

    /**
     * 分页获取策略
     * @param vo
     * @return
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<AutoBackupFilterStrategy> getPage(@ModelAttribute StrategyVO vo){
        return autoBackupFilterStrategyService.getStrategyPage(vo);
    }

}