package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.WebBrowseURLFilter;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.WebBrowseURLFilterService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 网页浏览网址过滤Controller
 */
@RestController
@Oplog(WebBrowseURLFilter.class)
@RequestMapping("/webBrowseURLFilter")
public class WebBrowseURLFilterController {
    @Resource
    private WebBrowseURLFilterService webBrowseUrlFilterService;

    /** 添加网页浏览网址过滤策略*/
    @Oplog(name="route.webBrowsingUrlFiltering")
    @PostMapping(value = "add")
    public WebBrowseURLFilter add(@RequestBody WebBrowseURLFilter bean) {
        webBrowseUrlFilterService.insert(bean);
        return bean;
    }

    /** 修改网页浏览网址过滤策略*/
    @Oplog(name="route.webBrowsingUrlFiltering")
    @PostMapping(value = "update")
    public WebBrowseURLFilter update(@RequestBody WebBrowseURLFilter bean) {
        webBrowseUrlFilterService.update(bean);
        return bean;
    }

    /** 删除网页浏览网址过滤策略*/
    @Oplog(name="route.webBrowsingUrlFiltering")
    @PostMapping(value = "delete")
    public void deleteById(String ids) {
        webBrowseUrlFilterService.deleteById(ids);
    }

    @GetMapping("get/{id}")
    public WebBrowseURLFilter getById(@PathVariable("id") Long id) {
        return webBrowseUrlFilterService.getStrategyById(id);
    }

    @PostMapping("getByName")
    public WebBrowseURLFilter getByName(String name) {
        return webBrowseUrlFilterService.getByName(name);
    }

    /** 得到网页浏览网址过滤策略分页列表*/
    @PostMapping(value = "getPage")
    public GridPageDTO<WebBrowseURLFilter> getPage(@RequestBody StrategyVO vo) {
        return webBrowseUrlFilterService.getStrategyPage(vo);
    }
}
