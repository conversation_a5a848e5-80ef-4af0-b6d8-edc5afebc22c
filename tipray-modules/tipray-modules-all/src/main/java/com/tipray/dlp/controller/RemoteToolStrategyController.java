package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.RemoteToolStrategy;
import com.tipray.dlp.bean.dict.MsgModuleDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.RemoteToolStrategyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 远程工具上传管控策略Controller
 */
@RestController
@Oplog(RemoteToolStrategy.class)
@RequestMapping("/remoteToolStrategy")
public class RemoteToolStrategyController {
    @Autowired
    private RemoteToolStrategyService RemoteToolStrategyService;

    /** 获取远程工具上传管控策略*/
    @PostMapping(value = "getPage")
    public GridPageDTO<RemoteToolStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return RemoteToolStrategyService.getStrategyPage(vo);
    }

    /**
     * 添加远程工具上传管控策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.RemoteToolStrategy")
    @PostMapping(value = "add")
    public RemoteToolStrategy addStrategy(@RequestBody RemoteToolStrategy bean) {
        bean.setSstType(0);
        bean.setAlarmType(MsgModuleDict.REMOTE_TOOL_LIMIT.getCode());
        RemoteToolStrategyService.insertStrategy(bean);
        return bean;
    }

    /**
     * 修改远程工具上传管控策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.RemoteToolStrategy")
    @PostMapping(value = "update")
    public RemoteToolStrategy updateStrategy(@RequestBody RemoteToolStrategy bean) {
        bean.setSstType(0);
        bean.setAlarmType(MsgModuleDict.REMOTE_TOOL_LIMIT.getCode());
        RemoteToolStrategyService.updateStrategy(bean);
        return bean;
    }

    /**
     * 删除远程工具上传管控策略
     * @param ids
     */
    @Oplog(name = "pages.RemoteToolStrategy")
    @PostMapping(value = "delete")
    public void deleteStrategy(String ids) {
        RemoteToolStrategyService.deleteStrategyById(ids);
    }

    @GetMapping("get/{id}")
    public RemoteToolStrategy getById(@PathVariable("id") Long id) {
        return RemoteToolStrategyService.getStrategyById(id);
    }

    @PostMapping("getByName")
    public RemoteToolStrategy getByName(String name) {
        return RemoteToolStrategyService.getStrategyByName(name);
    }



}
