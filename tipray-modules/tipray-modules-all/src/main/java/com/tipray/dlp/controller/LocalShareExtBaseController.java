package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.FileSuffixGroup;
import com.tipray.dlp.bean. LocalShareExtBase;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ImportResultDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.dto.TreeNodeDTO;
import com.tipray.dlp.bean.vo. LocalShareExtBaseVO;
import com.tipray.dlp.service.LocalShareExtBaseService;
import com.tipray.dlp.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * 本地文件后缀管理Controller
 *
 * <AUTHOR>
 */
@RestController
@Oplog(FileSuffixGroup.class)
@RequestMapping("/localShareExtBase")
@Slf4j
public class LocalShareExtBaseController {
    
    @Resource
    private LocalShareExtBaseService localShareExtBaseService;


    /**
     * 添加文件后缀
     * @param bean
     * @return
     */
    @Oplog(value = LocalShareExtBase.class,name = "table.localShareExtBase")
    @PostMapping(value = "add/{canCover}")
    public  ResponseDTO add(@PathVariable("canCover") Boolean canCover, @RequestBody LocalShareExtBase bean) {
        try {
            String[] strs = bean.getSuffix().split("\\|");
            Map<String, String> suffixMap = new HashMap<>();
            Map<String, LocalShareExtBase> existMap = localShareExtBaseService.getBySuffixList(Arrays.asList(strs));
            List<LocalShareExtBase> addList = new ArrayList<>();
            List<LocalShareExtBase> updateList = new ArrayList<>();
            for (String str : strs) {
                LocalShareExtBase suffix = new LocalShareExtBase();
                String suffixTemp;
                if (str.length() > 60) {
                    str = str.substring(0, 60);
                }
                if (!str.startsWith(".")) {
                    suffixTemp = "." + str;
                } else {
                    suffixTemp = str;
                }
                if (suffixMap.get(suffixTemp) == null) {
                    suffixMap.put(suffixTemp, suffixTemp);
                    suffix.setSuffix(suffixTemp);
                    suffix.setRemark(bean.getRemark());
                    if (!existMap.isEmpty() && canCover && existMap.get(suffixTemp) != null) {
                        suffix.setId(existMap.get(suffixTemp).getId());
                        updateList.add(suffix);
                    } else {
                        addList.add(suffix);
                    }
                }
            }
            if (!addList.isEmpty()) {
                localShareExtBaseService.batchInsert(addList);
            }
            if (!updateList.isEmpty()) {
                localShareExtBaseService.batchUpdate(updateList);
            }
        } catch (Exception e) {
            return ResponseDTO.failure(e);
        }
        return ResponseDTO.success();
    }

    /**
     * 修改文件后缀
     * @param bean
     * @return
     */
    @Oplog(value = LocalShareExtBase.class,name = "table.localShareExtBase")
    @PostMapping(value = "update")
    public  ResponseDTO update(@RequestBody LocalShareExtBase bean) {
        try {
            localShareExtBaseService.update(bean);
        } catch (Exception e) {
            return ResponseDTO.failure(e);
        }
        return ResponseDTO.success();
    }

    /**
     * 删除文件后缀
     * @param ids
     * @return
     */
    @Oplog(value = LocalShareExtBaseVO.class,name = "table.localShareExtBase")
    @PostMapping(value = "delete")
    public void delete(String ids) {
        //判断是否有导入任务未完成
        localShareExtBaseService.checkImportFinished();
        localShareExtBaseService.delete(ids);
    }

    @PostMapping(value = "getPage")
    public GridPageDTO<LocalShareExtBase> getPage(@RequestBody LocalShareExtBaseVO vo) {
        return  localShareExtBaseService.getPage(vo);
    }

    /** 查询文件后缀是否已存在*/
    @PostMapping("getBySuffix")
    public List<LocalShareExtBase> getBySuffix(@RequestBody List<String> suffixes) {
        List<String> list = new ArrayList<>(suffixes.size());
        for (String suffix : suffixes) {
            suffix = suffix.toLowerCase();
            if (!suffix.startsWith(".")) {
                suffix = "." + suffix;
            }
            if (suffix.length() > 60) {
                suffix = suffix.substring(0, 60);
            }
            list.add(suffix);
        }
        Map<String, LocalShareExtBase> map = localShareExtBaseService.getBySuffixList(list);
        return new ArrayList<>(map.values());
    }

    @GetMapping("listTree")
    public List<TreeNodeDTO> listLocalShareExtBaseTree() {
        return localShareExtBaseService.listTree();
    }

    /**
     * 导出本地共享文件后缀
     * @param bean
     * @param response
     * @return
     */
    @Oplog(value = LocalShareExtBase.class,name = "route.localShareExtBase")
    @PostMapping(value = "export")
    public ResponseDTO exportExcelBySelectId(@RequestBody LocalShareExtBaseVO bean, HttpServletResponse response){
        return ResponseDTO.success(localShareExtBaseService.exportExcel(bean, response));
    }

    /**
     * 导入本地共享文件后缀
     * @param file
     * @param importWay
     * @param taskId
     * @return
     * @throws IOException
     */
    @Oplog(value = ImportResultDTO.class,name = "route.localShareExtBase")
    @PostMapping(value="import")
    public ResponseDTO importData(@RequestParam(value = "file") MultipartFile file,
                                  @RequestParam(value = "fileType") String fileType,
                                  @RequestParam(value = "importWay") Integer importWay,
                                  @RequestParam(value = "taskId") Long taskId) throws IOException {
        try {
            File destFile = FileUtil.transferImportFile(file);
            ImportResultDTO result = localShareExtBaseService.importLocalShareExtBase(taskId, destFile, fileType, importWay);
            return ResponseDTO.success(result);
        } catch (Exception e) {
            return ResponseDTO.failure(e);
        }
    }

}
