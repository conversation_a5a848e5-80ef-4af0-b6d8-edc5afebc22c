package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.DirectOutgoingWhiteListStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.StrategyService;
import com.tipray.dlp.service.impl.DirectOutgoingWhiteListStrategyService;
import com.tipray.dlp.util.StrategyUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 直接外发程序白名单Controller
 * <AUTHOR>
 * @since 2023-06-19
 */
@Oplog(DirectOutgoingWhiteListStrategy.class)
@RestController
@RequestMapping("/directOutgoingWhiteListStrategy")
public class DirectOutgoingWhiteListStrategyController {

    @Resource
    private DirectOutgoingWhiteListStrategyService directOutgoingWhiteListStrategyService;

    @Resource
    private StrategyService strategyService;

    /**
     * 新增直接外发程序白名单策略
     * @param beans
     * @return
     */
    @PostMapping("add")
    @Oplog(name = "pages.directOutgoingWhiteListStrategy")
    public DirectOutgoingWhiteListStrategy[] add(@RequestBody DirectOutgoingWhiteListStrategy[] beans) {
        directOutgoingWhiteListStrategyService.insert(beans);
        return beans;
    }

    /**
     * 更新直接外发程序白名单策略
     * @param beans
     * @return
     */
    @Oplog(name = "pages.directOutgoingWhiteListStrategy")
    @PostMapping("update")
    public DirectOutgoingWhiteListStrategy[] update(@RequestBody DirectOutgoingWhiteListStrategy[] beans) {
        directOutgoingWhiteListStrategyService.update(beans);
        return beans;
    }

    @PostMapping("getByName")
    public List<DirectOutgoingWhiteListStrategy> getByName(String name) {
        return strategyService.listStrategyByName(DirectOutgoingWhiteListStrategyService.STRATEGY_TYPE_NUMBER, name, DirectOutgoingWhiteListStrategy.class);
    }

    /** 分页查询直接外发程序白名单*/
    @PostMapping(value = "getPage")
    public GridPageDTO<DirectOutgoingWhiteListStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        String typeName = StrategyUtil.getStrategyType(DirectOutgoingWhiteListStrategyService.STRATEGY_TYPE_NUMBER);
        vo.setStrategyType(typeName);
        return strategyService.getStrategyPage(vo, DirectOutgoingWhiteListStrategy.class);
    }

    /** 删除直接外发程序白名单*/
    @Oplog(name = "pages.directOutgoingWhiteListStrategy")
    @PostMapping("delete")
    public void deleteStrategy(String ids) {
        strategyService.deleteStrategyById(ids);
    }
}
