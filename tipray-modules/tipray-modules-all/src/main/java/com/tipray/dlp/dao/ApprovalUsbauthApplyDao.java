package com.tipray.dlp.dao;

import com.tipray.dlp.bean.ApprovalUsbauthApply;
import com.tipray.dlp.bean.dto.ApprovalUsbauthApplyDTO;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.tipray.dlp.mybatis.bean.PageVO;

/**
 * 终端存储usb认证申申请(ApprovalUsbauthApply)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-07-31 11:02:36
 */
@Repository
public interface ApprovalUsbauthApplyDao extends BaseDao<ApprovalUsbauthApply>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @return 对象列表
     */
    List<ApprovalUsbauthApply> list(ApprovalUsbauthApplyDTO bean);
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(PageVO vo);
     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<ApprovalUsbauthApply> listByVO(PageVO vo);
}
