package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.ScreenshotStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.ScreenshotStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;

/**
 * 截屏设置策略(ScreenshotStrategy)表控制层
 *
 * <AUTHOR>
 * @since 2019-10-21 09:55:30
 */
@Oplog(ScreenshotStrategy.class)
@RestController
@RequestMapping("/screenshotStrategy")
public class ScreenshotStrategyController {
    /**
     * 服务对象
     */
    @Resource
    private ScreenshotStrategyService screenshotStrategyService;

    /**
     * 新增截屏设置策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.screenshotStrategy")
    @PostMapping(value = "add")
    public ScreenshotStrategy[] insert(@RequestBody ScreenshotStrategy[] bean){
        screenshotStrategyService.insert(bean);
        return bean;
    }

    /**
     * 修改截屏设置策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.screenshotStrategy")
    @PostMapping(value = "update")
    public ScreenshotStrategy[] update(@RequestBody ScreenshotStrategy[] bean){
        screenshotStrategyService.update(bean);
        return bean;
    }

    /**
     * 删除截屏设置策略
     * @param ids
     */
    @Oplog(name = "pages.screenshotStrategy")
    @PostMapping(value = "delete")
    public void deleteById(String ids){
        screenshotStrategyService.deleteById(ids);
    }
    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("get/{id}")
    public ScreenshotStrategy getById(@PathVariable("id") Long id) {
        return screenshotStrategyService.getStrategyById(id);
    }

    @PostMapping("getByName")
    public Collection<ScreenshotStrategy> getByName(String name) {
        return screenshotStrategyService.listByName(name);
    }

    @PostMapping(value = "getPage")
    public GridPageDTO<ScreenshotStrategy> getPage(@ModelAttribute StrategyVO vo){
        return screenshotStrategyService.getPage(vo);
    }

}