package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.SysAlarmConfigStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.SysAlarmConfigStrategyService;
import com.tipray.dlp.util.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 计算机告警配置策略Controller
 */
@RestController
@Oplog(SysAlarmConfigStrategy.class)
@RequestMapping("/sysAlarmConfigStrategy")
public class SysAlarmConfigStrategyController {

    @Autowired
    private SysAlarmConfigStrategyService sysAlarmConfigStrategyService;

    /**
     * 添加计算机告警配置策略
     * @param beans
     * @return
     */
    @Oplog(name = "pages.sysAlarmConfigStrategy")
    @PostMapping(value = "add")
    public SysAlarmConfigStrategy addStrategy(@RequestBody SysAlarmConfigStrategy[] beans) {
        this.formatSubmitBean(beans);
        sysAlarmConfigStrategyService.insert(beans[0]);
        return beans[0];
    }

    /**
     * 修改计算机告警配置策略
     * @param beans
     * @return
     */
    @Oplog(name = "pages.sysAlarmConfigStrategy")
    @PostMapping(value = "update")
    public SysAlarmConfigStrategy updateStrategy(@RequestBody SysAlarmConfigStrategy[] beans) {
        this.formatSubmitBean(beans);
        sysAlarmConfigStrategyService.update(beans[0]);
        return beans[0];
    }

    /**
     * 删除计算机告警配置策略
     * @param ids
     */
    @Oplog(name = "pages.sysAlarmConfigStrategy")
    @PostMapping(value = "delete")
    public void deleteStrategy(String ids) {
        sysAlarmConfigStrategyService.deleteById(ids);
    }

    @GetMapping("get/{id}")
    public SysAlarmConfigStrategy getById(@PathVariable("id") Long id) {
        return sysAlarmConfigStrategyService.getStrategyById(id);
    }

    @PostMapping("getByName")
    public SysAlarmConfigStrategy getByName(String name) {
        return sysAlarmConfigStrategyService.getByName(name);
    }

    /** 获取计算机告警配置策略*/
    @PostMapping(value = "getPage")
    public GridPageDTO<SysAlarmConfigStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return sysAlarmConfigStrategyService.getStrategyPage(vo);
    }

    private void formatSubmitBean(SysAlarmConfigStrategy[] beans){
        if(beans != null){
            for (SysAlarmConfigStrategy bean : beans) {
                if(bean.getTimeId()==null || CollectionUtil.isEmpty(bean.getConfigs())){
                    continue;
                }
                bean.getConfigs().forEach(item->item.setTimeId(bean.getTimeId()));
            }
        }
    }
}
