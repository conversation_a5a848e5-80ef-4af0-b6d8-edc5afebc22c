package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.*;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.TreeNodeDTO;
import com.tipray.dlp.bean.vo.GroupPolicyVO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.GroupPolicyStrategyService;
import com.tipray.dlp.util.TreeUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 组策略Controller
 */
@RestController
@Oplog(GroupPolicyStrategy.class)
@RequestMapping("/groupPolicyStrategy")
public class GroupPolicyStrategyController {

    /**
     * 服务对象
     */
    @Resource
    private GroupPolicyStrategyService groupPolicyStrategyService;

    /**
     * 添加计算机组策略
     * @param beans
     * @return
     */
    @Oplog(name = "pages.groupPolicyStrategy")
    @PostMapping(value = "add")
    public GroupPolicyStrategy[] addStrategy(@RequestBody GroupPolicyStrategy[] beans) {
        groupPolicyStrategyService.insertStrategy(beans);
        return beans;
    }

    /**
     * 修改计算机组策略
     * @param beans
     * @return
     */
    @Oplog(name = "pages.groupPolicyStrategy")
    @PostMapping(value = "update")
    public GroupPolicyStrategy[] updateStrategy(@RequestBody GroupPolicyStrategy[] beans) {
        groupPolicyStrategyService.updateStrategy(beans);
        return beans;
    }

    @PostMapping("getByName")
    public GroupPolicyStrategy getByName(String name) {
        return groupPolicyStrategyService.getStrategyByName(name);
    }

    /** 分页查询计算机组策略*/
    @PostMapping(value = "getPage")
    public GridPageDTO<GroupPolicyStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return groupPolicyStrategyService.getStrategyPage(vo);
    }

    /**
     * 获取计算机组策略配置项
     *
     * @return
     */
    @GetMapping("listGroupTree")
    public List<TreeNodeDTO> listGroupPolicyFuncGroupTree() {
        List<GroupPolicyFuncGroup> list = groupPolicyStrategyService.listGroupPolicyFuncGroup();
        return TreeUtil.toTreeNodes(list);
    }

    /** 查询功能项*/
    @PostMapping(value = "listFunction")
    public List<GroupPolicyFunction> listGroupPolicyFunction(@RequestBody GroupPolicyVO vo) {
        return groupPolicyStrategyService.listGroupPolicyFunction(vo);
    }

    /**
     * 删除计算机组策略
     * @param ids
     */
    @Oplog(name = "pages.groupPolicyStrategy")
    @PostMapping(value = "delete")
    public void deleteStrategy(String ids) {
        groupPolicyStrategyService.deleteStrategy(ids);
    }

    @GetMapping("listFunctionMap")
    public Map<String, List<GroupPolicyFunction>> listFunctionMap() {
        return groupPolicyStrategyService.listFunctionMap();
    }
}
