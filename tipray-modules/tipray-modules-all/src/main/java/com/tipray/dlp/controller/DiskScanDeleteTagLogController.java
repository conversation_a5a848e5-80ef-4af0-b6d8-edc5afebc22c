package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.DiskScanDeleteTagLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.DiskScanAddTagLogVO;
import com.tipray.dlp.service.DiskScanDeleteTagLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 全盘扫描清除标签Controller
 */
@Oplog(DiskScanDeleteTagLog.class)
@RestController
@RequestMapping("/log/diskScanDeleteTagLog")
public class DiskScanDeleteTagLogController {
    @Autowired
    private DiskScanDeleteTagLogService service;

    /**
     * 删除清除标签日志
     * @param list
     * @return
     */
    @Oplog(value = DiskScanDeleteTagLog.class, name = "pages.tagGetLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<DiskScanDeleteTagLog> list) {
        service.deleteBatch(list);
        return ResponseDTO.success();
    }

    /**
     * 查询清除标签记录
     * @param vo
     * @return
     */
    @Oplog(value = DiskScanAddTagLogVO.class, name = "pages.tagDeleteLog")
    @PostMapping(value = "getPage")
    public GridPageDTO<DiskScanDeleteTagLog> getPage(@RequestBody DiskScanAddTagLogVO vo) {
        return service.getPage(vo);
    }
}
