package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.DecToolLimit;
import com.tipray.dlp.bean.HighRiskSoftwareLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.HighRiskSoftwareLogVO;
import com.tipray.dlp.service.HighRiskSoftwareLogService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Oplog(HighRiskSoftwareLog.class)
@RestController
@RequestMapping("/log/highRiskSoftwareLog")
public class HighRiskSoftwareLogController {
    @Resource
    private HighRiskSoftwareLogService service;

    @Oplog(value = HighRiskSoftwareLogVO.class, name = "pages.softBlackList_Msg12")
    @PostMapping(value = "getPage")
    public GridPageDTO<HighRiskSoftwareLog> getPage(@RequestBody HighRiskSoftwareLogVO vo) {
        return service.getLogPage(vo);
    }

    @Oplog(name = "pages.softBlackList_Msg12")
    @PostMapping(value = "delete")
    public ResponseDTO deleteBy(@RequestBody List<HighRiskSoftwareLog> list) {
        service.deleteBatch(list);
        return ResponseDTO.success();
    }

    @Oplog(value = HighRiskSoftwareLogVO.class, name = "pages.softBlackList_Msg12")
    @PostMapping(value = "export")
    public ResponseDTO exportExcelByLogVO(@RequestBody HighRiskSoftwareLogVO vo) {
        return ResponseDTO.success(service.exportExcel(vo));
    }

    /**
     * 检测存在于高危软件库的高危软件信息
     * @param vo
     */
    @PostMapping(value = "checkExistInLibrary")
    public List<String> checkExistInLibrary(@RequestBody DecToolLimit vo) {
        return service.checkExistInLibrary(vo);
    }

    @Oplog(value = DecToolLimit.class, name = "button.addToHighRiskSoftLibrary")
    @PostMapping(value = "batchAddToLibrary")
    public void addToLibrary(@RequestBody DecToolLimit vo) {
        service.addToLibrary(vo);
    }
}
