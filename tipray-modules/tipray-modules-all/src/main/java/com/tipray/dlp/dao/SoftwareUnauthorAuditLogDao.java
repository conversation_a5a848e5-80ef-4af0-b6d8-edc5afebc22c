package com.tipray.dlp.dao;

import com.tipray.dlp.bean.SoftLimitLog;
import com.tipray.dlp.bean.SoftwareUnauthorAuditLog;
import com.tipray.dlp.bean.vo.LogVO;
import com.tipray.dlp.bean.vo.SoftwareUnauthorAuditLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource
public interface SoftwareUnauthorAuditLogDao extends BaseDao<SoftwareUnauthorAuditLog> {
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(LogVO vo);

    /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<SoftwareUnauthorAuditLog> listByVO(LogVO vo);

}