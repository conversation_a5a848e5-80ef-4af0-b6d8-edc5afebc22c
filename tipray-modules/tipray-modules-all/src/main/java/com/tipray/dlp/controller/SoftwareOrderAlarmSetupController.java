package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.SoftwareOrderAlarmSetup;
import com.tipray.dlp.service.SoftwareOrderAlarmSetupService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 软件订单预警设置Controller
 * <AUTHOR>
 * @date 2023/6/9
 */
@RestController
@Oplog(SoftwareOrderAlarmSetup.class)
@RequestMapping("/softwareOrderAlarmSetup")
public class SoftwareOrderAlarmSetupController {

    @Resource
    private SoftwareOrderAlarmSetupService softwareOrderAlarmSetupService;

    /**
     * 保存软件订单预警设置
     * @param bean
     * @return
     */
    @Oplog(name = "pages.softwareOrderAlarmSetup")
    @PostMapping(value = "save")
    public SoftwareOrderAlarmSetup save(@RequestBody SoftwareOrderAlarmSetup bean) {
        softwareOrderAlarmSetupService.update(bean);
        return bean;
    }

    /** 获取软件订单预警设置*/
    @GetMapping(value = "getAlarmSetup")
    public SoftwareOrderAlarmSetup getAlarmSetup() {
     return softwareOrderAlarmSetupService.getSoftwareOrderAlarmSetup();
    }

    /** 触发预警扫描*/
    @GetMapping(value = "scanTriggerAlarm")
    public void scanTriggerAlarm() {
        softwareOrderAlarmSetupService.scanTriggerAlarm();
    }
}
