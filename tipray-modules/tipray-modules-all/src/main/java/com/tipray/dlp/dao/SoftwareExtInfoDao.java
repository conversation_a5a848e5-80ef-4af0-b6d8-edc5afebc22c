package com.tipray.dlp.dao;

import com.tipray.dlp.bean.SoftwareExtInfo;
import com.tipray.dlp.bean.SoftwareStatistics;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SoftwareExtInfoDao extends BaseDao<SoftwareExtInfo> {
    SoftwareExtInfo getBySoftwareName(String softwareName);

    void deleteBySoftwareNames(String softwareNames);

    List<String> listSoftwareNames(SoftwareExtInfo bean);

    List<String>  listSoftwareTypes();

    List<String>  listChargeTypes();

    List<String>  listIndustryTypes();

    List<SoftwareExtInfo> listSimpleExtInfo(String softNames);

    List<SoftwareStatistics> listSoftwareTypeNum();

    List<SoftwareExtInfo> listBySoftwareName(String softNames);
}
