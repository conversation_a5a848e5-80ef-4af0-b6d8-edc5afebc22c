package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.TermSecurityDetectionLastRecord;
import com.tipray.dlp.bean.TermSecurityDetectionRecord;
import com.tipray.dlp.bean.TermSecurityStatisticalResult;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.TermSecurityDetectionLastRecordPermissionVO;
import com.tipray.dlp.bean.vo.TermSecurityDetectionRecordVO;
import com.tipray.dlp.service.TermSecurityDetectionRecordService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 终端安全检测记录
 * <AUTHOR>
 * @date 2023/7/6 15:27
 */
@Oplog(TermSecurityDetectionRecord.class)
@RestController
@RequestMapping("/log/termSecurityDetection")
public class TermSecurityDetectionRecordController {

    @Resource
    private TermSecurityDetectionRecordService termSecurityDetectionRecordService;

    /** 分页查询终端安全检测记录*/
    @Oplog(value = TermSecurityDetectionRecordVO.class, name = "route.termSecurityLog")
    @PostMapping(value = "getPage")
    public GridPageDTO<TermSecurityDetectionRecord> getPage(@RequestBody TermSecurityDetectionRecordVO vo){
        return termSecurityDetectionRecordService.getPage(vo);
    }

    /** 分页查询最终终端安全检测状态*/
    @Oplog(value = TermSecurityDetectionLastRecordPermissionVO.class, name = "route.termSecurityStatus")
    @PostMapping(value = "getLastRecordPage")
    public GridPageDTO<TermSecurityDetectionLastRecord> getLastRecordPage(@RequestBody TermSecurityDetectionLastRecordPermissionVO vo){
        return termSecurityDetectionRecordService.getLastRecordPage(vo.toTermSecurityDetectionLastRecordVO());
    }

    /** 获取检测统计结果*/
    @GetMapping(value = "getStatisticalResult")
    public TermSecurityStatisticalResult getStatisticalResult(){
        return termSecurityDetectionRecordService.getStatisticalResult();
    }

    /** 导出终端安全检测记录*/
    @PostMapping(value = "export")
    @Oplog(value = TermSecurityDetectionRecordVO.class, delayLog = true, name = "route.termSecurityLog")
    public ResponseDTO exportExcel(@RequestBody TermSecurityDetectionRecordVO vo) {
        return ResponseDTO.success(termSecurityDetectionRecordService.exportExcel(vo));
    }

    /** 导出终端安全检测-检测报告*/
    @PostMapping(value = "exportDetails")
    @Oplog(value = TermSecurityDetectionRecordVO.class, delayLog = true, name = "pages.termSecurityDetectionReport")
    public void exportDetails(@RequestBody TermSecurityDetectionRecordVO vo, HttpServletResponse response) {
        termSecurityDetectionRecordService.exportDetails(vo, response);
    }

    /**
     * 删除日志
     * @param list
     * @return
     */
    @Oplog(name = "route.termSecurityLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<TermSecurityDetectionRecord> list) {
        termSecurityDetectionRecordService.deleteBatch(list);
        return ResponseDTO.success();
    }

    @PostMapping("/getById")
    public ResponseDTO getById(@RequestBody TermSecurityDetectionRecordVO vo) {
        return ResponseDTO.success(termSecurityDetectionRecordService.getById(vo));
    }

    @PostMapping("/getLastById")
    public ResponseDTO getLastById(@RequestBody TermSecurityDetectionRecordVO vo) {
        return ResponseDTO.success(termSecurityDetectionRecordService.getLastById(vo));
    }

}
