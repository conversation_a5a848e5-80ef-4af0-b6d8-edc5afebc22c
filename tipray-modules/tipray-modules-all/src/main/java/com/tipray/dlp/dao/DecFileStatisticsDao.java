package com.tipray.dlp.dao;

import com.tipray.dlp.bean.DecFileStatistics;
import com.tipray.dlp.bean.vo.LogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * (DecFileStatistics)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-05-21 10:18:59
 */
@Repository
@ShardingDataSource
public interface DecFileStatisticsDao extends BaseDao<DecFileStatistics>{

    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(LogVO vo);
     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<DecFileStatistics> listByVO(LogVO vo);
}
