package com.tipray.dlp.dao;

import com.tipray.dlp.bean.InstallPkgMakeLog;
import com.tipray.dlp.bean.dto.InstallPkgCount;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import com.tipray.dlp.mybatis.datasource.DataSourceFactory;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 安装包制作日志
 * <AUTHOR>
 */
@Repository
@ShardingDataSource(datasourceId = DataSourceFactory.SHARD_MASTER_DATA_SOURCE)
public interface InstallPkgMakeLogDao {

    List<InstallPkgCount> countCodeCount(String codes);

    int insertLog(InstallPkgMakeLog log);
}
