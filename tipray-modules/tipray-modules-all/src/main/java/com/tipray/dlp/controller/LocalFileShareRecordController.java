package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.LocalFileShareRecord;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.LocalFileShareLogVO;
import com.tipray.dlp.service.LocalFileShareRecordService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 本地文件共享记录Controller
 * (LocalFileShareRecord)表控制层
 *
 * <AUTHOR>
 * @since 2021-01-11 14:47:52
 */
@Oplog(LocalFileShareRecord.class)
@RestController
@RequestMapping("/log/localFileShare")
public class LocalFileShareRecordController {
    /**
     * 服务对象
     */
    @Resource
    private LocalFileShareRecordService localFileShareRecordService;

    /**
     * 分页查询本地共享记录
     * @param vo
     * @return
     */
    @Oplog(value = LocalFileShareLogVO.class,name = "route.localFileBackupRecord")
    @PostMapping(value = "getPage")
    public GridPageDTO<LocalFileShareRecord> getPage(@RequestBody LocalFileShareLogVO vo){
        return localFileShareRecordService.getLocalFileShareRecordPage(vo);
    }

    /**
     * 导出本地共享记录信息
     * 根据查询筛选条件导出Excel
     * @param vo
     * @return
     */
    @Oplog(value = LocalFileShareLogVO.class,name = "route.localFileBackupRecord")
    @PostMapping(value = "export")
    public ResponseDTO exportExcelByLogVO(@RequestBody LocalFileShareLogVO vo){
        return ResponseDTO.success(localFileShareRecordService.exportExcel(vo));
    }

    /**
     * 删除本地文件共享记录
     * @param list
     * @return
     */
    @Oplog(name = "route.localFileBackupRecord")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<LocalFileShareRecord> list) {
        localFileShareRecordService.deleteBatch(list);
        return ResponseDTO.success();
    }
}
