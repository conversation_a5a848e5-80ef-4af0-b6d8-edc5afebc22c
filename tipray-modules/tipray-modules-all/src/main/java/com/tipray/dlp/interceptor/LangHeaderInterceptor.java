package com.tipray.dlp.interceptor;

import com.tipray.dlp.util.GlobalConfigUtil;
import com.tipray.dlp.util.StringUtil;
import com.tipray.dlp.util.ThreadVariable;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023-06-08
 */
public class LangHeaderInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String lang = request.getHeader("Lang");
        if (StringUtil.isEmpty(lang) && ArrayUtils.isNotEmpty(request.getCookies())) {
            for (Cookie cookie : request.getCookies()) {
                if (Objects.equals("language", cookie.getName())) {
                    lang = cookie.getValue();
                    break;
                }
            }
        }
        // 当请求无语言辨识时，使用全局默认的语言（全局配置中配置）
        ThreadVariable.setLang(StringUtil.isEmpty(lang) ? GlobalConfigUtil.getDefaultLanguage().getValue() : lang);
        return true;
    }
}
