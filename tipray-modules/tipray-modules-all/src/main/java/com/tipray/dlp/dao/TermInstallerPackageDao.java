package com.tipray.dlp.dao;

import com.tipray.dlp.bean.TermInstallerPackage;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.tipray.dlp.mybatis.bean.PageVO;

/**
 * 终端安装包数据(TermInstallerPackage)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-09-01 15:45:44
 */
@Repository
public interface TermInstallerPackageDao extends BaseDao<TermInstallerPackage>{

    List<TermInstallerPackage> getByMd5(@Param("md5") String md5, @Param("bussId")Integer bussId);
    /**
     * 通过实体作为筛选条件查询
     *
     * @return 对象列表
     */
    List<TermInstallerPackage> list(TermInstallerPackage bean);
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(PageVO vo);
     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<TermInstallerPackage> listByVO(PageVO vo);

    /**
     * 更新安装包的文件服务器上传状态
     * @param bean
     */
    void updateStatus(TermInstallerPackage bean);
}
