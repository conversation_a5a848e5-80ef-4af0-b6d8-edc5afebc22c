package com.tipray.dlp.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tipray.dlp.bean.DbBackupNoticeMsg;
import com.tipray.dlp.bean.vo.DbBackupNoticeMsgVO;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2022-11-02
 */
@Repository
public interface DbBackupNoticeMsgDao extends BaseMapper<DbBackupNoticeMsg> {
    List<DbBackupNoticeMsg> listByVo(DbBackupNoticeMsgVO vo);
    Long countByVo(DbBackupNoticeMsgVO vo);
    Long deleteByVo(DbBackupNoticeMsgVO vo);
}
