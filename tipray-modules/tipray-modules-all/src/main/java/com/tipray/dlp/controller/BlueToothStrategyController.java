package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.BlueToothStrategy;
import com.tipray.dlp.bean.dict.MsgModuleDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.BlueToothStrategyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
/**
 * 蓝牙文件管控策略Controller
 * <AUTHOR>
 * @since 2020-6-6
 */
@Oplog(BlueToothStrategy.class)
@RestController
@RequestMapping("/blueToothStrategy")
public class BlueToothStrategyController {
    @Resource
    private BlueToothStrategyService blueToothStrategyService;

    /**
     * 添加蓝牙文件管控策略
     * @param strategy
     * @return
     */
    @Oplog(name = "pages.blueToothStrategy")
    @PostMapping(value = "add")
    public BlueToothStrategy addAppVersionBlock(@Valid @RequestBody BlueToothStrategy strategy) {
        strategy.setSstType(0);
        strategy.setAlarmType(MsgModuleDict.BLUETOOTH_FILE_LIMIT.getCode());
        // 统一样式，当不勾选备份复选框时，备份的阀值设置为0
        if (strategy.getDisable() == 1) {
            strategy.setFileBackUpLimit(0);
        }
        blueToothStrategyService.insertStrategy(strategy);
        return strategy;
    }

    /**
     * 修改蓝牙文件管控策略
     * @param strategy
     * @return
     */
    @Oplog(name = "pages.blueToothStrategy")
    @PostMapping(value = "update")
    public ResponseDTO updateVersionStrategy(@Valid @RequestBody BlueToothStrategy strategy) {
        if(strategy.getId() == null){
            return ResponseDTO.failure(new Throwable(),"参数错误");
        }
        strategy.setSstType(0);
        strategy.setAlarmType(MsgModuleDict.BLUETOOTH_FILE_LIMIT.getCode());
        // 统一样式，当不勾选备份复选框时，备份的阀值设置为0
        if (strategy.getDisable() == 1) {
            strategy.setFileBackUpLimit(0);
        }
        blueToothStrategyService.updateStrategy(strategy);
        return ResponseDTO.success(strategy);
    }

    /**
     * 删除蓝牙文件管控策略
     * @param ids
     */
    @Oplog(name = "pages.blueToothStrategy")
    @PostMapping(value = "delete")
    public void deleteVersionStrategy(String ids) {
        blueToothStrategyService.deleteStrategyById(ids);
    }

    @PostMapping("getByName")
    public BlueToothStrategy getByName(String name) {
        return blueToothStrategyService.getStrategyByName(name);
    }

    /**
     * 分页获取蓝牙文件管控策略
     * @param vo
     * @return
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<BlueToothStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return blueToothStrategyService.getStrategyPage(vo);
    }
}
