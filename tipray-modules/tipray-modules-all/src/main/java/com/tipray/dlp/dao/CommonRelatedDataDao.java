package com.tipray.dlp.dao;

import com.tipray.dlp.bean.BaseRelated;
import com.tipray.dlp.mybatis.annotation.WhereIn;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface CommonRelatedDataDao<T extends BaseRelated>{

    Long insert(@Param("table") String table, @Param("beans") Collection<? extends BaseRelated> relatedData);

    void updateById(@Param("table") String table, @Param("beans") Collection<? extends BaseRelated> relatedData);

    @WhereIn(@WhereIn.Param("ids"))
    void deleteByIds(@Param("table") String table, @Param("ids") String ids);
    @WhereIn(@WhereIn.Param("dataId"))
    void deleteByData(@Param("table") String table, @Param("dataId")String dataId);
    @WhereIn(@WhereIn.Param("bizId"))
    void deleteByBiz(@Param("table") String table, @Param("bizType")Integer bizType, @Param("bizId")String bizId);

    List<T> list(@Param("table") String table);

    long countByData(@Param("table") String table, @Param("dataId")Long dataId);
    List<T> listByData(@Param("table") String table, @Param("dataId")Long dataId);
    @WhereIn(@WhereIn.Param("dataIds"))
    List<T> listByDatas(@Param("table") String table, @Param("dataIds")String dataIds);

    List<T> listByBiz(@Param("table") String table, @Param("bizType")Integer bizType, @Param("bizId")Long bizId);

    List<Long> listDataByBiz(@Param("table") String table, @Param("bizType")Integer bizType, @Param("bizId")Long bizId);

    @WhereIn(@WhereIn.Param("dataId"))
    List<Long> listBizByData(@Param("table") String table, @Param("bizType")Integer bizType, @Param("dataId")String dataId);
}
