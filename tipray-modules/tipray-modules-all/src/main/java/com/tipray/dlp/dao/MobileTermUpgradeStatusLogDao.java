package com.tipray.dlp.dao;

import com.tipray.dlp.bean.MobileTermUpgradeStatusLog;
import com.tipray.dlp.bean.vo.LogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource
public interface MobileTermUpgradeStatusLogDao extends BaseDao<MobileTermUpgradeStatusLog>{

    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(LogVO vo);

    /**
     * 查询最新升级记录数量
     *
     * @return 数量
     */
    Long countNewStatusByVO(LogVO vo);

    /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<MobileTermUpgradeStatusLog> listByVO(LogVO vo);

    /**
     * 查询最新升级记录数据
     *
     * @return 对象列表
     */
    List<MobileTermUpgradeStatusLog> listNewStatusByVO(LogVO vo);

}
