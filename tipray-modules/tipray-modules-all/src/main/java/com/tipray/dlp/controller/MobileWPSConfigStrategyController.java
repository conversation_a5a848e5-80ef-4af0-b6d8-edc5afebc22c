package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.MobileWPSConfigStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.MobileWPSConfigStrategyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 移动终端WPS配置策略Controller
 * 移动终端WPS配置策略(MobileWPSConfigStrategy)表控制层
 */
@Oplog(MobileWPSConfigStrategy.class)
@RestController
@RequestMapping("/mobileWPSConfigStrategy")
public class MobileWPSConfigStrategyController {

    @Resource
    private MobileWPSConfigStrategyService service;

    /**
     * 新增移动终端WPS配置策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.mobileWPSConfigStrategy")
    @PostMapping(value = "add")
    public MobileWPSConfigStrategy[] insert(@RequestBody MobileWPSConfigStrategy[] bean){
        service.insert(bean);
        return bean;
    }

    /**
     * 修改移动终端WPS配置策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.mobileWPSConfigStrategy")
    @PostMapping(value = "update")
    public MobileWPSConfigStrategy[] update(@RequestBody MobileWPSConfigStrategy[] bean){
        service.update(bean);
        return bean;
    }

    /**
     * 删除移动终端WPS配置策略
     * @param ids
     */
    @Oplog(name = "pages.mobileWPSConfigStrategy")
    @PostMapping(value = "delete")
    public void deleteById(String ids){
        service.deleteById(ids);
    }

    @PostMapping("getByName")
    public MobileWPSConfigStrategy getByName(String name) {
        return service.getByName(name);
    }

    /** 分页查询*/
    @PostMapping(value = "getPage")
    public GridPageDTO<MobileWPSConfigStrategy> getPage(@RequestBody StrategyVO vo){
        return service.getStrategyPage(vo);
    }

}
