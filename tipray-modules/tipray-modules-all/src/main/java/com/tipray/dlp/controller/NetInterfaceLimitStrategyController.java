package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.NetInterfaceLimitStrategy;
import com.tipray.dlp.bean.UsbInterfaceLimitStrategy;
import com.tipray.dlp.bean.dict.MsgModuleDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.exception.ParamException;
import com.tipray.dlp.service.AssetInfoService;
import com.tipray.dlp.service.NetInterfaceLimitStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 网络接口管控策略Controller
 */
@Oplog(UsbInterfaceLimitStrategy.class)
@RestController
@RequestMapping("/netInterfaceLimit")
public class NetInterfaceLimitStrategyController {
    private static final String PROP_ID = "23101";

    @Resource
    private NetInterfaceLimitStrategyService netInterfaceLimitStrategyService;
    @Resource
    private AssetInfoService assetInfoService;

    /**
     * 添加接口限制策略
     * @param beans   策略内容
     * @return 修改后的策略内容
     */
    @Oplog(name = "pages.netInterface")
    @PostMapping(value = "add")
    public NetInterfaceLimitStrategy[] addStrategy(@RequestBody NetInterfaceLimitStrategy[] beans) {
        for (NetInterfaceLimitStrategy bean : beans){
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.NETINTERFACE.getCode());
        }
        netInterfaceLimitStrategyService.insertStrategy(beans);
        return beans;
    }

    /**
     * 修改接口限制策略
     * @param beans   策略内容
     * @return 修改后的策略内容
     */
    @Oplog(name = "pages.netInterface")
    @PostMapping(value = "update")
    public NetInterfaceLimitStrategy[] updateStrategy(@RequestBody NetInterfaceLimitStrategy[] beans) {
        for (NetInterfaceLimitStrategy bean : beans){
            if(bean.getId() == null){
                throw new ParamException("参数错误");
            }
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.NETINTERFACE.getCode());
        }
        netInterfaceLimitStrategyService.updateStrategy(beans);
        return beans;
    }

    /**
     * 删除接口限制策略
     * @param ids   策略主键
     */
    @Oplog(name = "pages.netInterface")
    @PostMapping(value = "delete")
    public void deleteStrategy(String ids) {
        netInterfaceLimitStrategyService.deleteStrategyById(ids);
    }

    /**
     * 根据ID获取策略
     * @param id   策略主键
     * @return 策略内容
     */
    @GetMapping("get/{id}")
    public NetInterfaceLimitStrategy getById(@PathVariable("id") Long id) {
        return netInterfaceLimitStrategyService.getStrategyById(id);
    }

    /**
     * 根据名称获取策略
     * @param name     策略名称
     * @return  策略内容
     */
    @PostMapping("getByName")
    public NetInterfaceLimitStrategy getByName(String name) {
        return netInterfaceLimitStrategyService.getStrategyByName(name);
    }

    /**
     * 获取接口限制策略
     * @param vo
     * @return
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<NetInterfaceLimitStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return netInterfaceLimitStrategyService.getStrategyPage(vo);
    }
}
