package com.tipray.dlp.dao;

import com.tipray.dlp.bean.ServiceAlarmMsg;
import com.tipray.dlp.bean.vo.ServiceAlarmLogVo;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import com.tipray.dlp.mybatis.annotation.WhereIn;
import com.tipray.dlp.mybatis.datasource.DataSourceFactory;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource(datasourceId = DataSourceFactory.SHARD_MASTER_DATA_SOURCE)
public interface ServiceAlarmMsgDao {
    @WhereIn({@WhereIn.Param("ids"), @WhereIn.Param("alarmTypes")})
    List<ServiceAlarmMsg> listByVo(ServiceAlarmLogVo alarmLogVo);

    @WhereIn(@WhereIn.Param("ids"))
    void updateMsgDealStatus(@Param("ids") String ids, @Param("dealStatus") Integer dealStatus);

    @WhereIn(@WhereIn.Param("ids"))
    void updateMsgAllDeal(@Param("ids") String ids);

    @WhereIn(@WhereIn.Param("msgs.id"))
    void updateByConsumerMsgs(List<ServiceAlarmMsg> msgs);

    @WhereIn(@WhereIn.Param("ids"))
    void deleteByIds(@Param("ids") String deleteIds);
}
