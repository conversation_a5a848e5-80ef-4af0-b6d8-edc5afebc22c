package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.HttpWhiteListDecLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.HttpWhiteListLogVo;
import com.tipray.dlp.service.HttpWhiteListLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 服务器白名单解密日志Controller
 */
@Oplog(HttpWhiteListDecLog.class)
@RestController
@RequestMapping("/log/httpWhiteListDec")
public class HttpWhiteListDecLogController {

    @Autowired
    private HttpWhiteListLogService service;

    @Oplog(value = HttpWhiteListLogVo.class,name = "route.httpWhiteListDecLog")
    @PostMapping(value = "getPage")
    public GridPageDTO<HttpWhiteListDecLog> getPage(@RequestBody HttpWhiteListLogVo vo) {
        return service.getPage(vo);
    }

    /**
     * 根据查询筛选条件导出Excel
     * @param vo
     * @return
     */
    @Oplog(value = HttpWhiteListLogVo.class, name = "route.httpWhiteListDecLog")
    @PostMapping(value = "export")
    public ResponseDTO exportExcelByLogVO(@RequestBody HttpWhiteListLogVo vo){
        return ResponseDTO.success(service.exportExcel(vo));
    }

    @Oplog(name = "route.httpWhiteListDecLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<HttpWhiteListDecLog> list) {
        service.deleteBatch(list);
        return ResponseDTO.success();
    }
}
