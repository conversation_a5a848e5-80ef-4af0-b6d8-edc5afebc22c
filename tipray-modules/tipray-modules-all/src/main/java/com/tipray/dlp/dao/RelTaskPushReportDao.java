package com.tipray.dlp.dao;

import com.tipray.dlp.bean.MsgPushTaskReportConfig;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RelTaskPushReportDao extends BaseDao<MsgPushTaskReportConfig>{

    /**
     * 根据模板id查询推送任务的报表配置
     * @param templateIds
     * @return
     */
    List<MsgPushTaskReportConfig> selectByTemplateIds(String templateIds);
}
