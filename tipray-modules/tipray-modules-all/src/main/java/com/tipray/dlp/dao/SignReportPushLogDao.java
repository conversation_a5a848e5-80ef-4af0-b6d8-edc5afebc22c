package com.tipray.dlp.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tipray.dlp.bean.SignReportPushLog;
import com.tipray.dlp.bean.SignReportPushLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import com.tipray.dlp.mybatis.bean.PageVO;
import com.tipray.dlp.mybatis.datasource.DataSourceFactory;
import com.tipray.dlp.util.StringUtil;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-01-31
 */
@Repository
@ShardingDataSource(datasourceId = DataSourceFactory.SHARD_MASTER_DATA_SOURCE)
public interface SignReportPushLogDao extends BaseDao<SignReportPushLog>{

    List<SignReportPushLog> listByVO(SignReportPushLogVO vo);

    PageVO<SignReportPushLog> selectPageByVO(SignReportPushLogVO vo);

    static QueryWrapper<SignReportPushLog> buildQueryWrapper(SignReportPushLogVO vo) {
        QueryWrapper<SignReportPushLog> qw = new QueryWrapper<>();
        if (vo.getCreateDate() != null) {
            qw.ge("create_time", vo.getCreateDate());
            qw.lt("create_time", new Date(vo.getCreateDate().getTime() + 24 * 60 * 60 * 1000L));
        } else {
            qw.ge("create_time", vo.getStartDate());
            qw.lt("create_time", new Date(vo.getEndDate().getTime() + 24 * 60 * 60 * 1000L));
        }
        if (StringUtil.isNotEmpty(vo.getKw())) {
            qw.like("sign_report_name", vo.getKw());
        }
        if (StringUtil.isNotEmpty(vo.getSortName())) {
            qw.orderBy(true, "asc".equals(vo.getSortOrder()), StringUtil.camelCaseToUnderLine(vo.getSortName()));
        }
        return qw;
    }
}
