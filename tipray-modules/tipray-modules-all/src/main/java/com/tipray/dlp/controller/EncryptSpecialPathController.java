package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.EncryptSpecialPath;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.EncryptSpecialPathService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collection;

/**
 * 加密模块特殊目录Controller
 */
@Oplog(EncryptSpecialPath.class)
@RestController
@RequestMapping("/encryptSpecialPath")
public class EncryptSpecialPathController {
    @Resource
    private EncryptSpecialPathService encryptSpecialPathService;

    /**
     * 添加加密特殊目录策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.encryptSpecialPath")
    @PostMapping(value = "add")
    public EncryptSpecialPath[] add(@RequestBody EncryptSpecialPath[] bean) {
        encryptSpecialPathService.insert(bean);
        return bean;
    }

    /**
     * 修改加密特殊目录策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.encryptSpecialPath")
    @PostMapping(value = "update")
    public EncryptSpecialPath[] update(@RequestBody EncryptSpecialPath[] bean) {
        encryptSpecialPathService.update(bean);
        return bean;
    }

    /**
     * 删除加密特殊目录策略
     * @param ids
     */
    @Oplog(name = "pages.encryptSpecialPath")
    @PostMapping(value = "delete")
    public void delete(String ids) {
        encryptSpecialPathService.deleteById(ids);
    }

    @PostMapping("getByName")
    public Collection<EncryptSpecialPath> getByName(String name) {
        return encryptSpecialPathService.listByName(name);
    }

    /** 查询加密特殊目录策略*/
    @PostMapping(value = "getPage")
    public GridPageDTO<EncryptSpecialPath> getPage(@RequestBody StrategyVO vo) {
        return encryptSpecialPathService.getPage(vo);
    }

}
