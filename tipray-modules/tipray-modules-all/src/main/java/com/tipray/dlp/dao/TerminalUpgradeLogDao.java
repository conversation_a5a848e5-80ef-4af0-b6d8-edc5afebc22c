package com.tipray.dlp.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tipray.dlp.bean.TerminalUpgradeLog;
import com.tipray.dlp.bean.vo.TerminalUpgradeLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import com.tipray.dlp.mybatis.annotation.WhereIn;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource
public interface TerminalUpgradeLogDao extends BaseMapper<TerminalUpgradeLog> {

    Long countByVO(TerminalUpgradeLogVO vo);

    List<TerminalUpgradeLog> listByVO(TerminalUpgradeLogVO vo);

    List<TerminalUpgradeLog> listByTaskGuid(String taskGuid);

    @WhereIn({@WhereIn.Param("termIdList"), @WhereIn.Param(value = "guidList", numerical = false)})
    List<TerminalUpgradeLog> listNewestStatusByTermId(@Param("termIdList") List<Long> termIdList, @Param("guidList") List<String> guidList);

}
