package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.AddTagLog;
import com.tipray.dlp.bean.DiskScanAddTagLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.DiskScanAddTagLogVO;
import com.tipray.dlp.service.DiskScanAddTagLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 全盘扫描加标签Controller
 */
@Oplog(DiskScanAddTagLog.class)
@RestController
@RequestMapping("/log/diskScanAddTagLog")
public class DiskScanAddTagLogController {
    @Autowired
    private DiskScanAddTagLogService service;

    /**
     * 删除全盘扫描加标签记录
     * @param list
     * @return
     */
    @Oplog(value = DiskScanAddTagLog.class, name = "pages.tagAddLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<DiskScanAddTagLog> list) {
        service.deleteBatch(list);
        return ResponseDTO.success();
    }

    /**
     * 查询全盘扫描加标签记录
     * @param vo
     * @return
     */
    @Oplog(value = DiskScanAddTagLogVO.class, name = "pages.tagAddLog")
    @PostMapping(value = "getPage")
    public GridPageDTO<DiskScanAddTagLog> getPage(@RequestBody DiskScanAddTagLogVO vo) {
        return service.getPage(vo);
    }
}
