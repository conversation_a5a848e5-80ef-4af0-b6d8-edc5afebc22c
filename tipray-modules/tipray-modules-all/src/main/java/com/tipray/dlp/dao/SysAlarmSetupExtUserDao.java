package com.tipray.dlp.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tipray.dlp.bean.SysAlarmSetupExtUser;
import com.tipray.dlp.util.CollectionUtil;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

/**
 * 系统预警设置：控制台弹窗告警管理员
 * <AUTHOR>
 * @date 2023/6/12
 */
@Repository
public interface SysAlarmSetupExtUserDao extends BaseDao<SysAlarmSetupExtUser> {
    default List<Long> listIdBySetupId(Long id){
        List<Object> userIds = id==null ? null :
                this.selectObjs(new LambdaQueryWrapper<SysAlarmSetupExtUser>().select(SysAlarmSetupExtUser::getId).eq(SysAlarmSetupExtUser::getSetupId, id));
        return CollectionUtil.toLongList(userIds);
    }

    default List<Long> listExtUserIdBySetupId(Long id){
        List<Object> userIds = id==null ? new LinkedList<>() :
                this.selectObjs(new LambdaQueryWrapper<SysAlarmSetupExtUser>().select(SysAlarmSetupExtUser::getUserId).eq(SysAlarmSetupExtUser::getSetupId, id));
        return CollectionUtil.toLongList(userIds);
    }

    default void deleteExtUser(Long id, Collection<Long> userIds){
        this.delete(new LambdaQueryWrapper<SysAlarmSetupExtUser>()
                .eq(SysAlarmSetupExtUser::getSetupId, id)
                .in(SysAlarmSetupExtUser::getUserId, userIds)
        );
    }
}
