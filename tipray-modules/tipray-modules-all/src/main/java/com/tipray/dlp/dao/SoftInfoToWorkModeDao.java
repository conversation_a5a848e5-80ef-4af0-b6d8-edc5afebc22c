package com.tipray.dlp.dao;

import com.tipray.dlp.bean.SoftInfoToWorkMode;
import com.tipray.dlp.bean.vo.AppInfoVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SoftInfoToWorkModeDao extends BaseDao<SoftInfoToWorkMode> {

    /**
     * 得到所有应用程序
     *
     * @return
     */
    List<SoftInfoToWorkMode> listByVO(AppInfoVO vo);

    Long countByVO(AppInfoVO vo);
}

