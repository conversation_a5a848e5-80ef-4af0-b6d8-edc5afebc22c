package com.tipray.dlp.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.SoftwareGroup;
import com.tipray.dlp.bean.SoftwareIdentifyConfig;
import com.tipray.dlp.bean.dict.OperationTypeDict;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.service.PropertyService;
import com.tipray.dlp.service.SoftwareDefService;
import com.tipray.dlp.util.JsonUtil;
import com.tipray.dlp.util.SoftwareSyncUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 同款软件识别Controller
 * <AUTHOR>
 */
@RestController
@Oplog(SoftwareGroup.class)
@RequestMapping("/softwareIdentify")
@Slf4j
public class SoftwareIdentifyController {

    private final String PROPERTY_CODE_RULE = "software.same.rule";

    @Resource
    private PropertyService propertyService;
    @Resource
    private SoftwareDefService softwareDefService;


    @Oplog(value = SoftwareIdentifyConfig.class, name = "pages.softwareIdentifyConfig", type = OperationTypeDict.UPDATE, defaultArg = true)
    @PostMapping("save")
    public ResponseDTO saveSoftwareIdentifyConfig(@RequestBody SoftwareIdentifyConfig softwareIdentifyConfig) throws JsonProcessingException {
        String value = JsonUtil.toJson(softwareIdentifyConfig);
        propertyService.updateProperty(PROPERTY_CODE_RULE, value);
        softwareDefService.deleteSoftwareDef();
        SoftwareSyncUtil.completeSync();
        return ResponseDTO.success();
    }

    @GetMapping("getConfig")
    public SoftwareIdentifyConfig getSoftwareIdentifyConfig() throws IOException {
        String value = propertyService.getPropertyValueByCode(PROPERTY_CODE_RULE);
        return JsonUtil.toBean(value, SoftwareIdentifyConfig.class);
    }

    @GetMapping("getSyncStatus")
    public Integer getSoftwareIdentifyStatus() {
        return SoftwareSyncUtil.TRANSFER_STATUS;
    }

    @GetMapping("getLastSyncTimestamp")
    public Long getLastSyncTimestamp() {
        return SoftwareSyncUtil.WEBSOCKET_REQUEST_ID;
    }

}
