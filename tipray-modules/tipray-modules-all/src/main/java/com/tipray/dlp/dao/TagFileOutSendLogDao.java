package com.tipray.dlp.dao;

import com.tipray.dlp.bean.TagFileOutSendLog;
import com.tipray.dlp.bean.vo.AddTagLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource
public interface TagFileOutSendLogDao extends BaseDao<TagFileOutSendLog> {
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(AddTagLogVO vo);

     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<TagFileOutSendLog> listByVO(AddTagLogVO vo);
}
