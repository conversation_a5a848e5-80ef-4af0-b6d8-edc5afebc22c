package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.FilePermissionControlStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.FilePermissionControlStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("filePermissionControlStrategy")
@Oplog(FilePermissionControlStrategy.class)
public class FilePermissionControlStrategyController {

    @Resource
    protected FilePermissionControlStrategyService strategyService;

    @Oplog(name = "route.filePermissionControl")
    @PostMapping(value = "add")
    public FilePermissionControlStrategy[] insert(@RequestBody FilePermissionControlStrategy[] bean) {
        strategyService.insert(bean);
        return bean;
    }

    @Oplog(name = "route.filePermissionControl")
    @PostMapping(value = "update")
    public FilePermissionControlStrategy[] update(@RequestBody FilePermissionControlStrategy[] bean) {
        strategyService.update(bean);
        return bean;
    }

    @Oplog(name = "route.filePermissionControl")
    @PostMapping(value = "delete")
    public void deleteById(String ids) {
        strategyService.deleteById(ids);
    }

    @GetMapping("getById")
    public FilePermissionControlStrategy getById(Long id) {
        return strategyService.getStrategyById(id);
    }

    @PostMapping("getByName")
    public FilePermissionControlStrategy getByName(String name) {
        return strategyService.getByName(name);
    }

    @PostMapping(value = "getPage")
    public GridPageDTO<FilePermissionControlStrategy> getPage(@RequestBody StrategyVO vo) {
        return strategyService.getStrategyPage(vo);
    }
}
