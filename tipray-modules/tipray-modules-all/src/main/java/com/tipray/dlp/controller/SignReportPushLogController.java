package com.tipray.dlp.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.BaseBean;
import com.tipray.dlp.bean.SignReportPushLog;
import com.tipray.dlp.bean.SignReportPushLogVO;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.dao.SignReportPushLogDao;
import com.tipray.dlp.export.ExportHelper;
import com.tipray.dlp.export.Exportable;
import com.tipray.dlp.export.call.SimpleExportCallable;
import com.tipray.dlp.export.format.CustomColumnHelper;
import com.tipray.dlp.formatter.exportFormatter.SignReportPushLogExportFormatter;
import com.tipray.dlp.mybatis.bean.PageVO;
import com.tipray.dlp.mybatis.export.ExportExecutor;
import com.tipray.dlp.util.LogUtil;
import com.tipray.dlp.util.StringUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.tipray.dlp.dao.SignReportPushLogDao.buildQueryWrapper;

/**
 * <AUTHOR>
 * @since 2024-02-01
 */
@RestController
@RequestMapping("/signReportPushLog")
public class SignReportPushLogController implements Exportable<SignReportPushLogVO> {

    @Resource
    private SignReportPushLogDao signReportPushLogDao;

    @Oplog(value= SignReportPushLogVO.class, name = "route.SignReportPushLog")
    @PostMapping("list")
    public GridPageDTO<SignReportPushLog> list(@RequestBody SignReportPushLogVO vo){
        if (vo.getCreateDate() == null && vo.getStartDate() == null && vo.getEndDate() == null) {
            return new GridPageDTO<>(new ArrayList<>(), 0);
        }
        if (vo.getPage() == null) {
            vo.setPage(1);
        }
        if (vo.getLimit() == null) {
            vo.setLimit(20);
        }
        if (StringUtil.isNotEmpty(vo.getId())) {
            List<SignReportPushLog> list = Arrays.asList(signReportPushLogDao.selectById(vo.getId()));
            return new GridPageDTO<>(list, list.size());
        }
        if (vo.getCreateDate() != null) {
            vo.setStartDate(vo.getCreateDate());
            vo.setEndDate(new Date(vo.getCreateDate().getTime() + 24 * 60 * 60 * 1000L));
        } else {
            vo.setEndDate(new Date(vo.getEndDate().getTime() + 24 * 60 * 60 * 1000L));
        }
        QueryWrapper<SignReportPushLog> qw = buildQueryWrapper(vo);
        vo.setSearchCount(true);
        PageVO<SignReportPushLog> page = signReportPushLogDao.selectPageByVO(vo);
        return new GridPageDTO<>(page.getRecords(), page.getTotal());
    }

    @Oplog(value= SignReportPushLogVO.class, name = "route.SignReportPushLog")
    @PostMapping("export")
    public ResponseDTO exportExcel(@RequestBody SignReportPushLogVO vo) {
        if (vo.getCreateDate() != null) {
            vo.setStartDate(vo.getCreateDate());
            vo.setEndDate(new Date(vo.getCreateDate().getTime() + 24 * 60 * 60 * 1000L));
        }else {
            vo.setEndDate(new Date(vo.getEndDate().getTime() + 24 * 60 * 60 * 1000L));
        }
        LogUtil.formatLogVO(vo);
        vo.removeLimit();
        SimpleExportCallable callable = export(vo);
        return ResponseDTO.success(ExportHelper.addTask(callable));
    }

    @Override
    public String exportMenu() {
        return "488";
    }

    // 征兆告警记录
    @Override
    public SimpleExportCallable export(SignReportPushLogVO vo) {
        int[] columnIndexes = CustomColumnHelper.getCustomColumnIndexes(vo);
        ExportExecutor executor = ExportExecutor.ofManaging(vo, signReportPushLogDao, "listByVO");
        SignReportPushLogExportFormatter formatter = new SignReportPushLogExportFormatter(columnIndexes);
        return SimpleExportCallable.single(executor, formatter);
    }

    @Oplog(value=SignReportPushLog.class, name = "route.SignReportPushLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<SignReportPushLog> list) {
        signReportPushLogDao.deleteBatchIds(list.stream().map(BaseBean::getId).collect(Collectors.toList()));
        return ResponseDTO.success();
    }
}
