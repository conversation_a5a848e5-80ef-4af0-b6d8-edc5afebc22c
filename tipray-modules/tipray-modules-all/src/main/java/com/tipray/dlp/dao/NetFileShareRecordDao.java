package com.tipray.dlp.dao;

import com.tipray.dlp.bean.NetFileShareRecord;
import com.tipray.dlp.bean.vo.NetFileShareLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * (NetFileShareRecord)表数据库访问层
 *
 *
 * <AUTHOR>
 * @since 2021-01-11 14:47:52
 */
@Repository
@ShardingDataSource
public interface NetFileShareRecordDao extends BaseDao<NetFileShareRecord>{


    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(NetFileShareLogVO vo);
     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<NetFileShareRecord> listByVO(NetFileShareLogVO vo);
}
