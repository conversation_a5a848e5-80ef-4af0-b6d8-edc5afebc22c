package com.tipray.dlp.controller.websocket;

import com.alibaba.fastjson.JSON;
import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.AuditLogCountResult;
import com.tipray.dlp.bean.AuditLogType;
import com.tipray.dlp.bean.Scene;
import com.tipray.dlp.bean.dict.RolePermissionTypeDict;
import com.tipray.dlp.bean.vo.AuditLogPolymerizationQueryVO;
import com.tipray.dlp.bean.vo.AuditLogSceneQueryVO;
import com.tipray.dlp.exception.ServiceException;
import com.tipray.dlp.mybatis.export.ParameterHelper;
import com.tipray.dlp.service.AuditLogQueryService;
import com.tipray.dlp.service.DepartmentService;
import com.tipray.dlp.service.RoleService;
import com.tipray.dlp.support.scene.SceneManager;
import com.tipray.dlp.util.*;
import com.tipray.dlp.websocket.AbstractWebSocketApi;
import com.tipray.dlp.websocket.WSocketContext;
import com.tipray.dlp.websocket.annotation.WSocketRequestId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.CancellationException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 审计日志统一查询websocket
 * <AUTHOR>
 */
@Slf4j
@RestController
public class AuditLogQueryWebSocketApi extends AbstractWebSocketApi {

    public static final String SOCKET_AUDIT_LOG_QUERY_URI = "/topic/auditLogQueryResult";

    private final AuditLogQueryService auditLogQueryService;
//    private final SceneService sceneService;
    private final DepartmentService departmentService;
    private final RoleService roleService;
    private final SceneManager sceneManager;



    // 存放用户查询任务的CompletableFuture，用于取消查询
    private static final ConcurrentHashMap<String, CompletableFuture<Void>> queryFutureMap = new ConcurrentHashMap<>();

    // 存放用户查询的取消标志，用于快速响应取消请求
    private static final ConcurrentHashMap<String, AtomicBoolean> cancelFlagMap = new ConcurrentHashMap<>();

    public AuditLogQueryWebSocketApi(AuditLogQueryService auditLogQueryService,
                                     DepartmentService departmentService,
                                     RoleService roleService,
                                     @Qualifier("cacheUserSceneManager") SceneManager sceneManager) {
        this.auditLogQueryService = auditLogQueryService;
        this.departmentService = departmentService;
        this.roleService = roleService;
        this.sceneManager = sceneManager;
    }

    @MessageMapping("/auditLogCount")
    @WSocketRequestId
    @Oplog(value= AuditLogPolymerizationQueryVO.class, name = "审计日志聚合搜索")
    public void auditLogCount(AuditLogPolymerizationQueryVO queryVO, @Headers Map<String, Object> headers) {
        // sql注入处理和基础验证
        String finalKeyword = ParameterHelper.escapeSqlInjection(queryVO.getKeyword());
        queryVO.setKeyword(finalKeyword);

        // 转化为 AuditLogSceneQueryVO
        AuditLogSceneQueryVO vo = new AuditLogSceneQueryVO();
        BeanUtil.copyProperties(queryVO, vo);

        try {
            simulateHttpRequest(headers);
            handleObjectIdsAndObjectType(vo);
            validate(vo);
        } catch (Exception e) {
            sendToClient(e.getLocalizedMessage());
            return;
        }

        String token = ThreadVariable.getToken();
        String username = ThreadVariable.getUsername();
        Long userId = ThreadVariable.getUserId();
        List<Long> roleIds = ThreadVariable.getRoleIds();

        // 获取前端传递的查询版本号
        Long currentQueryVersion = vo.getCurrentQueryVersion();
        if (currentQueryVersion == null) {
            sendToClient("查询版本号不能为空");
            return;
        }

        // 保存完整的线程变量上下文
        Map<Object, Object> threadContext = new HashMap<>(ThreadVariable.getMap());

        // 保存 WebSocket 会话 ID
        String wsSessionId = WSocketContext.getThreadSession();

        // 取消上一次查询并启动新查询
        startNewQuery(vo, token, username, userId, roleIds, threadContext, wsSessionId, currentQueryVersion);
    }

    /**
     * 终止搜索
     */
    @MessageMapping("/auditLogCountCease")
    @WSocketRequestId
    public void auditLogCountCease(@Headers Map<String, Object> headers) {
        simulateHttpRequest(headers);
        String token = ThreadVariable.getToken();
        String username = ThreadVariable.getUsername();

        // 取消查询并通知前端
        boolean cancelled = cancelQuery(token);
        log.info("用户 {} 请求停止查询，取消结果: {}", username, cancelled);

        // 立即通知前端停止（停止消息使用特殊版本号-1，前端无条件处理）
        AuditLogCountResult ceaseResult = AuditLogCountResult.cease();
        super.sender.convertAndSendToUser(token, SOCKET_AUDIT_LOG_QUERY_URI, JSON.toJSONString(ceaseResult));
    }

    /**
     * 处理权限信息， 参考 DataPermissionAspect
     * @param vo
     */
    private void handleObjectIdsAndObjectType(AuditLogSceneQueryVO vo) {
        if(PropertyUtil.containSuperRoleOrRootThreeUser(ThreadVariable.getRoleIds())){
            return;
        }
        if (vo != null) {
            if (vo.getObjectId() != null) {
                // 如果存在objectId字段，且值不为空，则不需要通过AOP给查询对象赋值，因为已经赋值
                return;
            }
            List<Long> rootDeptIds = departmentService.listRootDeptId();
            Set<Long> deptIds = new HashSet<>();
            for (Long roleId : ThreadVariable.getRoleIds()) {
                deptIds.addAll(roleService.listPermissionId(RolePermissionTypeDict.DATA_DEPT.getCode(), roleId));
                if(deptIds.containsAll(rootDeptIds)){
                    // 如果当前登录管理员的权限包含根部门，则不需要添加查询条件
                    return;
                }
            }
            List<Long> groupIds = PermissionUtil.listPermissionGroupId();
            Integer objectType = vo.getObjectType();
            if (objectType == null) {
                vo.setObjectType(PropertyUtil.STRATEGY_OBJECT_TYPE_TERMINALGROUP);
            }
            vo.setObjectIds(StringUtil.join(groupIds, ","));
        }
    }

    private void validate(AuditLogSceneQueryVO vo) {
        //
        if (StringUtils.isEmpty(vo.getKeyword())) {
            throw new ServiceException("请输入关键字");
        }
        if (StringUtils.isEmpty(vo.getSceneName())) {
            throw new ServiceException("请选择场景");
        }
        if (vo.getCreateDate() == null && vo.getStartDate() == null) {
            throw new ServiceException("请选择日期");
        }
    }

    /**
     * 启动新的查询任务
     */
    private void startNewQuery(AuditLogSceneQueryVO vo, String token, String username, Long userId,
                              List<Long> roleIds, Map<Object, Object> threadContext, String wsSessionId, Long queryVersion) {
        // 取消旧查询
        boolean oldCancelled = cancelQuery(token);
        if (oldCancelled) {
            log.info("用户 {} 的旧查询任务已被取消，准备启动新查询", username);
            // 给旧查询一点时间来清理资源
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // 创建新的取消标志
        AtomicBoolean cancelFlag = new AtomicBoolean(false);
        cancelFlagMap.put(token, cancelFlag);

        // 创建新查询任务
        CompletableFuture<Void> future = CompletableFuture
            .runAsync(() -> executeQuery(vo, token, username, userId, roleIds, threadContext, wsSessionId, queryVersion, cancelFlag))
            .whenComplete((result, throwable) -> {
                // 清理资源
                queryFutureMap.remove(token);
                cancelFlagMap.remove(token);

                if (throwable != null && !(throwable instanceof CancellationException)) {
                    log.error("用户 {} 查询任务执行异常", username, throwable);
                    // 只有在没有被取消的情况下才发送错误消息
                    if (!cancelFlag.get()) {
                        sendErrorToClient(throwable.getMessage(), token, wsSessionId);
                    }
                } else {
                    log.info("用户 {} 查询任务正常完成", username);
                }
            });

        queryFutureMap.put(token, future);
        log.info("用户 {} 新查询任务已启动，查询版本: {}", username, queryVersion);
    }

    /**
     * 取消查询任务
     */
    private boolean cancelQuery(String token) {
        boolean cancelled = false;

        // 设置取消标志
        AtomicBoolean cancelFlag = cancelFlagMap.get(token);
        if (cancelFlag != null) {
            cancelFlag.set(true);
            log.info("设置用户 {} 的取消标志为 true", ThreadVariable.getUsername());
        }

        // 取消Future任务
        CompletableFuture<Void> future = queryFutureMap.remove(token);
        if (future != null && !future.isDone()) {
            cancelled = future.cancel(true);
            log.info("强制取消用户 {} 的查询任务，结果: {}", ThreadVariable.getUsername(), cancelled);
        }

        // 清理取消标志
        cancelFlagMap.remove(token);

        return cancelled || cancelFlag != null;
    }

    /**
     * 执行查询任务的具体逻辑
     */
    private void executeQuery(AuditLogSceneQueryVO vo, String token, String username, Long userId,
                             List<Long> roleIds, Map<Object, Object> threadContext, String wsSessionId, Long queryVersion, AtomicBoolean cancelFlag) {
        // 恢复线程上下文
        ThreadVariable.setMap(threadContext);
        if (wsSessionId != null) {
            WSocketContext.setThreadSession(wsSessionId);
        }

        try {
            LogUtil.formatLogVO(vo);
            Scene scene = sceneManager.getScene(vo.getSceneName());
            if (scene == null) {
                throw new ServiceException("没有获取到场景，当前用户可能没有权限");
            }

            Integer total = scene.getAudits().size();
            Integer done = 0;
            Map<String, AuditLogType> map = init(scene);

            sendToClient(total, done, map, token, wsSessionId, queryVersion, cancelFlag);

            for (Scene.Audit audit : scene.getAudits()) {
                // 检查任务是否被取消
                if (cancelFlag.get() || Thread.currentThread().isInterrupted()) {
                    log.info("用户 {} 查询任务被取消", username);
                    return;
                }

                // 使用超时机制执行数据库查询，避免长时间阻塞
                Integer count = executeCountWithTimeout(audit, vo, cancelFlag, username);

                // 如果查询被取消，直接返回
                if (count == null) {
                    log.info("用户 {} 查询任务在数据查询过程中被取消", username);
                    return;
                }

                // 再次检查是否被取消
                if (cancelFlag.get() || Thread.currentThread().isInterrupted()) {
                    log.info("用户 {} 查询任务在数据查询后被取消", username);
                    return;
                }

                AuditLogType temp = map.get(audit.getName());
                temp.setCount(count);
                temp.setStatus(AuditLogType.STATUS_DONE);
                done++;

                sendToClient(total, done, map, token, wsSessionId, queryVersion, cancelFlag);
                Thread.sleep(10);
            }

        } catch (InterruptedException e) {
            log.info("用户 {} 查询任务被中断", username);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("用户 {} 查询任务执行异常", username, e);
            throw new RuntimeException(e);
        } finally {
            ThreadVariable.remove();
        }
    }

    /**
     * 带超时和取消检查的数据库查询执行
     */
    private Integer executeCountWithTimeout(Scene.Audit audit, AuditLogSceneQueryVO vo, AtomicBoolean cancelFlag, String username) {
        CompletableFuture<Integer> queryFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return auditLogQueryService.count(audit, vo);
            } catch (Exception e) {
                log.error("用户 {} 执行数据库查询异常: {}", username, e.getMessage());
                throw new RuntimeException(e);
            }
        });

        try {
            // 使用轮询方式检查取消状态，每100ms检查一次
            while (!queryFuture.isDone()) {
                if (cancelFlag.get() || Thread.currentThread().isInterrupted()) {
                    queryFuture.cancel(true);
                    return null;
                }
                Thread.sleep(100);
            }

            return queryFuture.get();
        } catch (InterruptedException e) {
            queryFuture.cancel(true);
            Thread.currentThread().interrupt();
            return null;
        } catch (Exception e) {
            queryFuture.cancel(true);
            log.error("用户 {} 查询执行异常", username, e);
            throw new RuntimeException(e);
        }
    }



    /**这里模拟http请求，将jwtToken获取用户信息，并设置到当前线程变量中**/
    private void simulateHttpRequest(Map<String, Object> headers) {
        LinkedMultiValueMap<String, Object> headerParams = (LinkedMultiValueMap<String, Object>)headers.get("nativeHeaders");

        // 获取JWT Token
        List<Object> list = headerParams.get("Authorization");
        String jwtToken = "";
        if (CollectionUtils.isNotEmpty(list)) {
            jwtToken = (String)list.get(0);
        }
        if (StringUtils.isEmpty(jwtToken)) {
            return;
        }
        if (!SessionUtil.verifyToken(jwtToken)) {
            SessionUtil.disableToken(jwtToken);
        }

        // 获取客户端IP地址
        String clientIp = getClientIpFromWebSocketHeaders(headers);

        String username = SessionUtil.getUsernameByToken(jwtToken);
        Long userId = SessionUtil.getUserIdByToken(jwtToken);
        List<Long> roleIds = SessionUtil.getRoleIdByToken(jwtToken);
        ThreadVariable.setUsername(username);
        ThreadVariable.setUserId(userId);
        ThreadVariable.setRoleIds(roleIds);
        ThreadVariable.setToken(jwtToken);
        ThreadVariable.setIp(clientIp);
    }

    /**
     * 从WebSocket消息头中获取客户端IP地址
     */
    private String getClientIpFromWebSocketHeaders(Map<String, Object> headers) {
        LinkedMultiValueMap<String, Object> headerParams = (LinkedMultiValueMap<String, Object>)headers.get("nativeHeaders");
        if (headerParams == null) {
            return getIpFromStompHeaders(headers);
        }

        String unknown = "unknown";
        String ip = null;

        // 尝试从各种可能的头部获取真实IP
        ip = getHeaderValue(headerParams, "x-forwarded-for");
        if (isValidIp(ip, unknown)) {
            return extractFirstIp(ip);
        }

        ip = getHeaderValue(headerParams, "X-Forwarded-For");
        if (isValidIp(ip, unknown)) {
            return extractFirstIp(ip);
        }

        ip = getHeaderValue(headerParams, "Proxy-Client-IP");
        if (isValidIp(ip, unknown)) {
            return ip;
        }

        ip = getHeaderValue(headerParams, "WL-Proxy-Client-IP");
        if (isValidIp(ip, unknown)) {
            return ip;
        }

        ip = getHeaderValue(headerParams, "HTTP_CLIENT_IP");
        if (isValidIp(ip, unknown)) {
            return ip;
        }

        ip = getHeaderValue(headerParams, "HTTP_X_FORWARDED_FOR");
        if (isValidIp(ip, unknown)) {
            return extractFirstIp(ip);
        }

        ip = getHeaderValue(headerParams, "X-Real-IP");
        if (isValidIp(ip, unknown)) {
            return ip;
        }

        // 如果都没有找到，尝试从STOMP头部获取
        return getIpFromStompHeaders(headers);
    }

    /**
     * 从STOMP消息头中获取IP（备用方案）
     */
    private String getIpFromStompHeaders(Map<String, Object> headers) {
        // 尝试从simpSessionAttributes中获取
        Object sessionAttributes = headers.get("simpSessionAttributes");
        if (sessionAttributes instanceof Map) {
            Map<String, Object> attributes = (Map<String, Object>) sessionAttributes;
            Object remoteAddress = attributes.get("remoteAddress");
            if (remoteAddress != null) {
                return remoteAddress.toString();
            }
        }

        // 返回默认值
        return IpUtil.getLocalIp();
    }

    /**
     * 从头部参数中获取值
     */
    private String getHeaderValue(LinkedMultiValueMap<String, Object> headerParams, String headerName) {
        List<Object> values = headerParams.get(headerName);
        if (CollectionUtils.isNotEmpty(values)) {
            Object value = values.get(0);
            return value != null ? value.toString() : null;
        }
        return null;
    }

    /**
     * 检查IP是否有效
     */
    private boolean isValidIp(String ip, String unknown) {
        return ip != null && !ip.isEmpty() && !unknown.equalsIgnoreCase(ip);
    }

    /**
     * 提取第一个IP（处理多个IP用逗号分隔的情况）
     */
    private String extractFirstIp(String ip) {
        if (ip != null && ip.contains(",")) {
            return ip.substring(0, ip.indexOf(",")).trim();
        }
        return ip;
    }

    public Map<String, AuditLogType> init(Scene scene) {
        Map<String, AuditLogType> map = new HashMap<>(scene.getAudits().size() * 2);
        scene.getAudits().forEach(detail -> {
            map.put(detail.getName(), new AuditLogType(detail.getName(), detail.getDescription(), 0, AuditLogType.STATUS_DOING));
        });
        return map;
    }

    public void sendToClient(Integer total, Integer done, Map<String, AuditLogType> map) {
        sendToClient(total, done, map, ThreadVariable.getToken(), WSocketContext.getThreadSession(), System.currentTimeMillis(), new AtomicBoolean(false));
    }

    private void sendToClient(Integer total, Integer done, Map<String, AuditLogType> map, String token, String wsSessionId, Long queryVersion, AtomicBoolean cancelFlag) {
        // 检查查询是否仍然有效
        if (cancelFlag.get() || Thread.currentThread().isInterrupted()) {
            return;
        }

        // 设置WebSocket会话上下文
        String originalWsSessionId = WSocketContext.getThreadSession();
        if (wsSessionId != null && !wsSessionId.equals(originalWsSessionId)) {
            WSocketContext.setThreadSession(wsSessionId);
        }

        try {
            List<AuditLogType> list = map.values().stream()
                    .sorted(Comparator.comparing(AuditLogType::getStatus)
                            .thenComparing(AuditLogType::getCount).reversed())
                    .collect(Collectors.toList());

            AuditLogCountResult result = AuditLogCountResult.ok(total, done, list);
            result.setCurrentQueryVersion(queryVersion);
            super.sender.convertAndSendToUser(token, SOCKET_AUDIT_LOG_QUERY_URI, JSON.toJSONString(result));

        } finally {
            // 恢复原始的 WebSocket 会话 ID
            if (wsSessionId != null && !wsSessionId.equals(originalWsSessionId)) {
                if (originalWsSessionId != null) {
                    WSocketContext.setThreadSession(originalWsSessionId);
                } else {
                    WSocketContext.setThreadSession(null);
                }
            }
        }
    }

    public void sendToClient(String errorMsg) {
        sendErrorToClient(errorMsg, ThreadVariable.getToken(), WSocketContext.getThreadSession());
    }

    private void sendErrorToClient(String errorMsg, String token, String wsSessionId) {
        // 检查任务是否仍然活跃（避免发送已取消任务的错误消息）
        AtomicBoolean cancelFlag = cancelFlagMap.get(token);
        if (Thread.currentThread().isInterrupted() || (cancelFlag != null && cancelFlag.get())) {
            return;
        }

        String originalWsSessionId = WSocketContext.getThreadSession();
        if (wsSessionId != null && !wsSessionId.equals(originalWsSessionId)) {
            WSocketContext.setThreadSession(wsSessionId);
        }

        try {
            AuditLogCountResult result = AuditLogCountResult.fail(errorMsg);
            super.sender.convertAndSendToUser(token, SOCKET_AUDIT_LOG_QUERY_URI, JSON.toJSONString(result));
        } finally {
            // 恢复原始的 WebSocket 会话 ID
            if (wsSessionId != null && !wsSessionId.equals(originalWsSessionId)) {
                if (originalWsSessionId != null) {
                    WSocketContext.setThreadSession(originalWsSessionId);
                } else {
                    WSocketContext.setThreadSession(null);
                }
            }
        }
    }


}
