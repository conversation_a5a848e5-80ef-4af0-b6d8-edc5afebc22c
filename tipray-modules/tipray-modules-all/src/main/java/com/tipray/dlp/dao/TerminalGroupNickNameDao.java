package com.tipray.dlp.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tipray.dlp.bean.TerminalGrpNickAcquisition;
import com.tipray.dlp.bean.dto.TerminalGrpNickNameDTO;
import com.tipray.dlp.bean.vo.TerminalGrpNickAcquisitionVO;
import com.tipray.dlp.mybatis.bean.PageVO;
import com.tipray.dlp.util.StringUtil;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface TerminalGroupNickNameDao extends BaseDao<TerminalGrpNickAcquisition> {
    default TerminalGrpNickAcquisition selectById(Long termId){
        return this.selectOne(new QueryWrapper<TerminalGrpNickAcquisition>().eq("term_id", termId));
    }
    default int deleteById(Long termId){
        return this.delete(new QueryWrapper<TerminalGrpNickAcquisition>().eq("term_id", termId));
    }
    @Override
    default void deleteByIds(String termIds){
        List<Long> termIdList = StringUtil.toLongList(termIds);
        if(!termIdList.isEmpty()){
            this.delete(new QueryWrapper<TerminalGrpNickAcquisition>().in("term_id", termIdList));
        }
    }
    /**
     * 自动处理逻辑删除和普通删除，对应LogicDeleteSqlInjector
     */
    @Override
    void deleteByIds(@Param("term_ids") String ids, @Param("value") String value, @Param("modifyTime") Date modifyTime);

    /**
     * 根据状态查询终端分组和昵称列表信息
     *
     */
//    List<TerminalGrpNickNameDTO> listByTerminalGrpNickAcquisitionVO(TerminalGrpNickAcquisitionVO vo);

    List<TerminalGrpNickAcquisition> terminalGrpNickAcquisitionListByVO(TerminalGrpNickAcquisitionVO vo);

    Long countByVO(TerminalGrpNickAcquisitionVO vo);

    void updateByTermId(TerminalGrpNickAcquisition tg);

    void deleteByTerminalDeleted();

    PageVO<TerminalGrpNickNameDTO> getTaskPage(TerminalGrpNickAcquisitionVO vo);
}
