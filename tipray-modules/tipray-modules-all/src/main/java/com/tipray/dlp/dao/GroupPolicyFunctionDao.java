package com.tipray.dlp.dao;

import com.tipray.dlp.bean.GroupPolicyFunction;
import com.tipray.dlp.bean.vo.GroupPolicyVO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface GroupPolicyFunctionDao extends BaseDao<GroupPolicyFunction> {
    List<GroupPolicyFunction> listByGroupId(Long groupId);

    String getNameById(Long id);

    List<GroupPolicyFunction> listByVO(GroupPolicyVO vo);
}
