package com.tipray.dlp.controller;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.trwfe.bean.ApprovalFlow;
import com.tipray.dlp.trwfe.bean.ApprovalFlowGroup;
import com.tipray.dlp.bean.SensitiveFileOutsendLog;
import com.tipray.dlp.trwfe.bean.dict.ApprovalCategoryDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.dto.TreeNodeDTO;
import com.tipray.dlp.bean.vo.SensitiveFileOutsendLogVO;
import com.tipray.dlp.service.SensitiveFileOutsendLogService;
import com.tipray.dlp.trwfe.util.TrwfeUtil;
import com.tipray.dlp.util.*;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 敏感文件外发日志(SensitiveFileOutsendLog)表控制层
 *
 * <AUTHOR>
 * @since 2021-09-02 10:01:11
 */
@Oplog(SensitiveFileOutsendLog.class)
@RestController
@RequestMapping("/log/sensitiveFileOutsend")
public class SensitiveFileOutsendLogController {
    /**
     * 服务对象
     */
    @Resource
    private SensitiveFileOutsendLogService sensitiveFileOutsendLogService;

    /**
     * 删除文件操作审计日志
     * @param list
     */
    @Oplog(name = "pages.fileOperationAuditLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<SensitiveFileOutsendLog> list) {
        sensitiveFileOutsendLogService.deleteBatch(list);
        return ResponseDTO.success();
    }

    /** 获取敏感外发流程树*/
    @GetMapping("/sensitiveOutsendTree")
    public ResponseDTO listTree() throws IOException {
        List<ApprovalFlowGroup> flowGroups = new ArrayList<>();
        //创建根节点  （敏感外发审批）
        ApprovalFlowGroup rootGroup = new ApprovalFlowGroup();
        rootGroup.setGroupId(0);
        rootGroup.setParentId(-1);
        rootGroup.setCategory(ApprovalCategoryDict.SENSITIVE_FILE_OUT_SEND.trwfeCategory);
        rootGroup.setGroupName(ApprovalCategoryDict.match(ApprovalCategoryDict.SENSITIVE_FILE_OUT_SEND.trwfeCategory));
        flowGroups.add(rootGroup);
        String approvalFlowGroup = "/trwfe/rest/ext/flow/groups?category=sensitiveFileOutSend";
        ResponseEntity<String> approvalFlowGroupResults = TrwfeUtil.exchange(approvalFlowGroup, null, HttpMethod.GET, String.class);
        if (approvalFlowGroupResults.getStatusCode().value() == HttpStatus.UNAUTHORIZED.value()) {
            return ResponseDTO.failure(new Throwable(), I18nUtils.get("pages.approvalConfigException7", "请求审批服务器的token异常，已重新生成token，请重新请求"));
        }
        if (approvalFlowGroupResults.getStatusCode().value() == HttpStatus.FORBIDDEN.value()) {
            return ResponseDTO.failure(new Throwable(), I18nUtils.get("pages.approvalConfigException8", "无法连接到审批服务器，请检查审批服务器地址跟端口号是否配置正确"));
        }
        if (approvalFlowGroupResults.getStatusCode().value() != 200) {
            return ResponseDTO.failure(new Throwable(), I18nUtils.get("pages.approvalConfigException9", "审批服务器异常"));
        }
        String approvalFlowGroupJson = approvalFlowGroupResults.getBody();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        JavaType flowGroupType = objectMapper.getTypeFactory().constructParametricType(ArrayList.class, ApprovalFlowGroup.class);
        List<ApprovalFlowGroup> childApprovalFlows = objectMapper.readValue(approvalFlowGroupJson, flowGroupType);
        //当前用户可查看的独立审批类别
        List<String> categories = flowGroups.stream().map(ApprovalFlowGroup::getCategory).collect(Collectors.toList());
        childApprovalFlows = childApprovalFlows.stream().filter(group -> categories.contains(group.getCategory())).collect(Collectors.toList());
        flowGroups.addAll(childApprovalFlows);
        String approvalFlow = "/trwfe/rest/repository/process-definitions?tenantId=ld&size=10000&suspended=false&latest=true&sort=category&category=sensitiveFileOutSend";
        ResponseEntity<String> approvalFlowResults = TrwfeUtil.exchange(approvalFlow, null, HttpMethod.GET, String.class);
        if (approvalFlowResults.getStatusCode().value() == HttpStatus.UNAUTHORIZED.value()) {
            return ResponseDTO.failure(new Throwable(), I18nUtils.get("pages.approvalConfigException7", "请求审批服务器的token异常，已重新生成token，请重新请求"));
        }
        if (approvalFlowResults.getStatusCode().value() == HttpStatus.FORBIDDEN.value()) {
            return ResponseDTO.failure(new Throwable(), I18nUtils.get("pages.approvalConfigException8", "无法连接到审批服务器，请检查审批服务器地址跟端口号是否配置正确"));
        }
        if (approvalFlowResults.getStatusCode().value() != 200) {
            return ResponseDTO.failure(new Throwable(), I18nUtils.get("pages.approvalConfigException9", "审批服务器异常"));
        }
        Map<String,Object> map = JsonUtil.toMap(approvalFlowResults.getBody());
        List<ApprovalFlow> approvalFlows = (List<ApprovalFlow>) map.get("data");
        //多转换一次approvalFlows的原因是  instanceof 后的到的是 linkHashMap类型 不是ApprovalFlow类型
        String approvalFlowJson = JsonUtil.toJson(approvalFlows);
        JavaType flowType = objectMapper.getTypeFactory().constructParametricType(ArrayList.class, ApprovalFlow.class);
        approvalFlows = objectMapper.readValue(approvalFlowJson,flowType);
        approvalFlows = approvalFlows.stream().filter(a -> categories.contains(a.getCategory())).map(a -> {if (a.getAuto()) {a.setName(I18nUtils.get("pages.approvalLog_Msg23",a.getName()));} return a;}).collect(Collectors.toList());
        List<TreeNodeDTO> treeNodes = TreeUtil.toTreeNodes(flowGroups,approvalFlows);
        return ResponseDTO.success(treeNodes);
    }

    /** 分页查询*/
    @Oplog(value = SensitiveFileOutsendLogVO.class, name = "pages.sensitiveFileOutSendLog")
    @PostMapping(value = "getPage")
    public GridPageDTO<SensitiveFileOutsendLog> getPage(@RequestBody SensitiveFileOutsendLogVO vo) {
        if (StringUtil.isNotEmpty(vo.getProcessDefinitionIds())) {
            List<String> processIds = Arrays.asList(vo.getProcessDefinitionIds().split(","));
            vo.setProcessIds(processIds);
        }
        return sensitiveFileOutsendLogService.getSensitiveFileOutsendLogPage(vo);
    }

    /**
     * 导出文件外发记录信息
     * 根据查询筛选条件导出Excel
     * @param vo
     * @return
     */
    @Oplog(value = SensitiveFileOutsendLogVO.class, name = "pages.sensitiveFileOutSendLog")
    @PostMapping(value = "export")
    public ResponseDTO exportExcelByLogVO(@RequestBody SensitiveFileOutsendLogVO vo){
        return ResponseDTO.success(sensitiveFileOutsendLogService.exportExcel(vo));
    }

}
