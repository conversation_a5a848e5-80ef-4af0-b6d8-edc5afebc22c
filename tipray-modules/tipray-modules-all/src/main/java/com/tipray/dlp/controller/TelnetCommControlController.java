package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.TelnetCommControlStrategy;
import com.tipray.dlp.bean.dict.MsgModuleDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.exception.ParamException;
import com.tipray.dlp.service.TelnetCommControlStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * telnet通讯管控Controller
 */
@Oplog(TelnetCommControlStrategy.class)
@RestController
@RequestMapping("/telnetCommControl")
public class TelnetCommControlController {
    /**
     * 服务对象
     */
    @Resource
    private TelnetCommControlStrategyService service;

    /**
     * 新增telnet通讯管控策略
     * @param beans
     * @return
     */
    @Oplog(name = "pages.telnetCommControlStrategy")
    @PostMapping(value = "add")
    public TelnetCommControlStrategy[] add(@RequestBody TelnetCommControlStrategy[] beans) {
        for (TelnetCommControlStrategy bean : beans) {
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.TELNET_COMM_CONTROL.getCode());
        }
        service.insert(beans);
        return beans;
    }

    /**
     * 修改telnet通讯管控策略
     * @param beans
     * @return
     */
    @Oplog(name = "pages.telnetCommControlStrategy")
    @PostMapping(value = "update")
    public TelnetCommControlStrategy[] update(@RequestBody TelnetCommControlStrategy[] beans) {
        for (TelnetCommControlStrategy bean : beans) {
            if (bean.getId() == null) {
                throw new ParamException("参数错误");
            }
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.TELNET_COMM_CONTROL.getCode());
        }

        service.update(beans);
        return beans;
    }

    /**
     * 删除telnet通讯管控策略
     * @param ids
     */
    @Oplog(name = "pages.telnetCommControlStrategy")
    @PostMapping(value = "delete")
    public void deleteById(String ids) {
        service.deleteById(ids);
    }

    /**
     * 通过主键查询单条数据
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("get/{id}")
    public TelnetCommControlStrategy getById(@PathVariable("id") Long id) {
        return service.getById(id);
    }

    /**
     * 通过名称查询单条数据
     */
    @PostMapping("getByName")
    public TelnetCommControlStrategy getByName(String name) {
        return service.getByName(name);
    }

    /**
     * 分页查询telnet通讯管控策略
     * @param vo
     * @return
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<TelnetCommControlStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return service.getStrategyPage(vo);
    }
}
