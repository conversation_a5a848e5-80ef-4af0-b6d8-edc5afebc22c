package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.OutgoingMachineCode;
import com.tipray.dlp.bean.OutgoingMachineCodeGroup;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.TreeNodeDTO;
import com.tipray.dlp.bean.vo.MachineCodeInfoVO;
import com.tipray.dlp.service.OutgoingMachineCodeService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-22
 * 机器码白名单控制层
 */
@Oplog(OutgoingMachineCode.class)
@RestController
@RequestMapping("outgoingMachineCode")
public class OutgoingMachineCodeController {

    @Resource
    private OutgoingMachineCodeService outgoingMachineCodeService;

    /**
     * 新增机器码白名单类别
     * @param bean
     * @return
     */
    @Oplog(value = OutgoingMachineCodeGroup.class, name = "table.machineCodeType")
    @PostMapping(value = "addGroup")
    public OutgoingMachineCodeGroup addGroup(@RequestBody OutgoingMachineCodeGroup bean) {
        outgoingMachineCodeService.insertGroup(bean);
        return bean;
    }

    /**
     * 删除机器码白名单类型
     * @param id
     */
    @Oplog(value = OutgoingMachineCodeGroup.class,name = "table.machineCodeType")
    @PostMapping(value = "deleteGroup")
    public void deleteGroup(Long id) {
        outgoingMachineCodeService.deleteGroupById(id);
    }

    /**
     * 更新机器码白名单类别
     * @param bean
     * @return
     */
    @Oplog(value = OutgoingMachineCodeGroup.class, name = "table.machineCodeType")
    @PostMapping(value = "updateGroup")
    public OutgoingMachineCodeGroup updateGroup(@RequestBody OutgoingMachineCodeGroup bean) {
        outgoingMachineCodeService.updateGroup(bean);
        return bean;
    }

    @PostMapping("getGroupByName")
    public OutgoingMachineCodeGroup getGroupByName(@RequestBody OutgoingMachineCodeGroup bean) {
        return outgoingMachineCodeService.getGroupByName(bean);
    }

    /**
     * 得到机器码白名单类别树
     * @return
     */
    @PostMapping(value = "getGroupTree")
    public List<TreeNodeDTO> getGroupTree() {
        OutgoingMachineCodeGroup group = new OutgoingMachineCodeGroup();
        return outgoingMachineCodeService.listGroupTreeNode(group);
    }

    @GetMapping(value = "countByGroupId")
    public Long countByGroupId(Long groupId) {
        return outgoingMachineCodeService.countAppByGroupId(groupId);
    }

    /**
     * 新增机器码白名单
     * @param bean
     * @return
     */
    @Oplog(name = "pages.machineCodeList")
    @PostMapping(value = "add")
    public OutgoingMachineCode insert(@RequestBody OutgoingMachineCode bean) {
        outgoingMachineCodeService.insertOutgoingMachineCode(bean);
        return bean;
    }

    @PostMapping("machineCodeWhiteListExist")
    public Boolean machineCodeWhiteListExist(@RequestBody OutgoingMachineCode bean) {
        return outgoingMachineCodeService.machineCodeWhiteListExist(bean);
    }

    /**
     * 修改机器码白名单
     * @param bean
     * @return
     */
    @Oplog(name = "pages.machineCodeList")
    @PostMapping(value = "update")
    public OutgoingMachineCode update(@RequestBody OutgoingMachineCode bean){
        outgoingMachineCodeService.updateOutgoingMachineCode(bean);
        return bean;
    }

    /**
     * 删除机器码白名单
     * @param ids
     */
    @Oplog(name = "pages.machineCodeList")
    @PostMapping(value = "delete")
    public void deleteById(String ids) {
        outgoingMachineCodeService.deleteOutgoingMachineCodeById(ids);
    }

    /** 分页查询白名单进程*/
    @PostMapping(value = "getPage")
    public GridPageDTO<OutgoingMachineCode> getPage(@RequestBody MachineCodeInfoVO vo) {
        return outgoingMachineCodeService.getPage(vo);
    }

    @PostMapping(value = "listMachineCode")
    public List<OutgoingMachineCode> listMachineCode(@RequestBody MachineCodeInfoVO vo) {
        return outgoingMachineCodeService.listMachineCode(vo);
    }
}
