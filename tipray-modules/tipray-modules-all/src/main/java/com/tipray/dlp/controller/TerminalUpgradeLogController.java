package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.TerminalUpgradeLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.TerminalUpgradeLogVO;
import com.tipray.dlp.service.TerminalUpgradeLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 终端升级记录Controller
 */
@Oplog(TerminalUpgradeLog.class)
@RestController
@RequestMapping("/log/terminalUpgradeLog")
public class TerminalUpgradeLogController {
    @Autowired
    private TerminalUpgradeLogService terminalUpgradeLogService;
    /**
     * 查询终端升级记录
     * @param vo
     * @return
     */
    @Oplog(value = TerminalUpgradeLogVO.class,name = "route.terminalUpgradeLog")
    @PostMapping(value = "getPage")
    public GridPageDTO<TerminalUpgradeLog> getPage(@RequestBody TerminalUpgradeLogVO vo) {
        return terminalUpgradeLogService.getTerminalUpgradeLogPage(vo);
    }

    /**
     * 导出终端升级记录
     * 根据查询筛选条件导出Excel
     * @param vo
     * @return
     */
    @Oplog(value = TerminalUpgradeLogVO.class,name = "route.terminalUpgradeLog")
    @PostMapping(value = "export")
    public ResponseDTO exportExcelByLogVO(@RequestBody TerminalUpgradeLogVO vo){
        return ResponseDTO.success(terminalUpgradeLogService.exportExcel(vo));
    }
    /**
     * 删除日志
     * @param list
     * @return
     */
    @Oplog(name = "route.terminalUpgradeLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<TerminalUpgradeLog> list) {
        terminalUpgradeLogService.deleteBatch(list);
        return ResponseDTO.success();
    }
}
