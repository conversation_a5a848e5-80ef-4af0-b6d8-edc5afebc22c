package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.SensitiveBackupConfig;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.SensitiveBackupConfigService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 敏感文件备份配置Controller
 */
@Oplog(SensitiveBackupConfig.class)
@RestController
@RequestMapping("/sensitiveBackupConfig")
public class SensitiveBackupConfigController {

    @Resource
    private SensitiveBackupConfigService sensitiveBackupConfigService;

    /**
     * 新增敏感内容文件备份配置
     * @param bean
     * @return
     */
    @Oplog(name = "pages.sensitiveBackupStg")
    @PostMapping(value = "add")
    public SensitiveBackupConfig insert(@RequestBody SensitiveBackupConfig bean){
        sensitiveBackupConfigService.insertStrategy(bean);
        return bean;
    }

    /**
     * 修改敏感内容文件备份配置
     * @param bean
     * @return
     */
    @Oplog(name = "pages.sensitiveBackupStg")
    @PostMapping(value = "update")
    public SensitiveBackupConfig update(@RequestBody SensitiveBackupConfig bean){
        sensitiveBackupConfigService.updateStrategy(bean);
        return bean;
    }

    /**
     * 删除敏感内容文件备份配置
     * @param ids
     */
    @Oplog(name = "pages.sensitiveBackupStg")
    @PostMapping(value = "delete")
    public void deleteById(String ids){
        sensitiveBackupConfigService.deleteStrategyById(ids);
    }
    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("get/{id}")
    public SensitiveBackupConfig getById(@PathVariable("id") Long id) {
        return sensitiveBackupConfigService.getStrategyById(id);
    }

    @PostMapping("getByName")
    public SensitiveBackupConfig getByName(String name) {
        return sensitiveBackupConfigService.getStrategyByName(name);
    }

    /** 分页查询*/
    @PostMapping(value = "getPage")
    public GridPageDTO<SensitiveBackupConfig> getPage(@RequestBody StrategyVO vo){
        return sensitiveBackupConfigService.getStrategyPage(vo);
    }
}
