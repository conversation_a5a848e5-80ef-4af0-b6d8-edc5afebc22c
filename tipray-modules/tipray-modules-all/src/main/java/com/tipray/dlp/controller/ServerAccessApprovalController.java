package com.tipray.dlp.controller;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.tipray.dlp.annotation.EncryptDecrypt;
import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.*;
import com.tipray.dlp.bean.dict.OperationTypeDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.*;
import com.tipray.dlp.service.DevServerAccessApproveService;
import com.tipray.dlp.service.DevServerAccessLogService;
import com.tipray.dlp.service.DevServerLoginLogService;
import com.tipray.dlp.service.ServerAccessApprovalService;
import com.tipray.dlp.util.IpUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务器接入审批Controller
 * <AUTHOR>
 * @date 2025-01-15 14:55
 */
@RestController
@RequestMapping("/serverAccessApproval")
public class ServerAccessApprovalController {

    @Resource
    private ServerAccessApprovalService serverAccessApprovalService;
    @Resource
    private DevServerAccessApproveService devServerAccessApproveService;
    @Resource
    private DevServerAccessLogService devServerAccessLogService;
    @Resource
    private DevServerLoginLogService devServerLoginLogService;


    /**
     * 获取服务器接入审批配置
     */
    @GetMapping("/getConfig")
    public ServerAccessApprovalConfigVO getConfig() {
        return serverAccessApprovalService.getConfig();
    }

    /**
     * 服务器接入审批设置
     * @param config
     * @return
     */
    @Oplog(name = "pages.serverAccessSetting", value = ServerAccessApprovalConfig.class, type = OperationTypeDict.UPDATE)
    @PostMapping("/saveConfig")
    public ResponseDTO saveConfig(@RequestBody ServerAccessApprovalConfig config) {
        serverAccessApprovalService.saveConfig(config);
        return ResponseDTO.success(config);
    }

    /**
     * 服务器接入审批分页查找
     * @param condition 条件选择器
     * @return
     */
    @PostMapping("/getPage")
    public GridPageDTO<DevServerAccessApprove> getPage(@RequestBody DevServerAccessApproveVO condition) {
        return devServerAccessApproveService.getPage(condition);
    }

    /**
     * 服务器接入审批
     * @param approveResult     审批结果
     * @return
     */
    @Oplog(name = "route.serverAccessApproval", value = DevServerApproveResult.class, type = OperationTypeDict.ADD)
    @PostMapping("/approve")
    public ResponseDTO approve(@RequestBody DevServerApproveResult approveResult) {
        return ResponseDTO.success(devServerAccessApproveService.approve(approveResult));
    }

    /**
     * 批量审批
     * @param approveResult     审批结果
     * @return
     */
    @Oplog(name = "服务器接入批量审批", value = DevServerApproveResult.class, type = OperationTypeDict.ADD)
    @PostMapping("/batchApprove")
    public ResponseDTO batchApprove(@RequestBody DevServerApproveResult approveResult) {
        devServerAccessApproveService.batchApprove(approveResult);
        return ResponseDTO.success(approveResult);
    }

    /**
     * 获取黑名单列表
     * @return
     */
    @PostMapping("/blackList")
    public ResponseDTO blacklist(@RequestBody DevServerAccessBlacklistVO vo) {
        return ResponseDTO.success(serverAccessApprovalService.listBlackList(vo));
    }

    /**
     * 将设备移除黑名单
     * @param list
     * @return
     */
    @Oplog(value = DevServerAccessBlacklist.class, name = "pages.blacklistInfo", type = OperationTypeDict.DELETE)
    @PostMapping("/removeBlackList")
    public ResponseDTO removeBlackList(@RequestBody DevServerAccessBlacklist list) {
        String ids = list.getIds();
        if (StringUtils.isNotEmpty(ids)) {
            String[] split = ids.split(",");
            List<Long> collect = ListUtil.toList(split).stream().map(Long::parseLong).collect(Collectors.toList());
            serverAccessApprovalService.removeBlackList(collect);
        }
        return ResponseDTO.success(ids);
    }

    /**
     * 服务器接入审批日志-分页查找
     * @param condition
     * @return
     */
    @PostMapping("/getLogPage")
    public GridPageDTO<DevServerAccessLog> getLogPage(@RequestBody DevServerAccessApproveVO condition) {
        return devServerAccessLogService.getPage(condition);
    }

    /**
     * 根据设备类型查询各设备信息（离线设备）
     * @param devType   服务器类型
     * @param onlineStatus  1:在线，0：离线, 2:全部
     * @param packageType 安装包类型
     * @return
     */
    @GetMapping("/listDevServer/{devType}/{onlineStatus}/{packageType}")
    public ResponseDTO listDevServer(@PathVariable Integer devType, @PathVariable Integer onlineStatus, @PathVariable Integer packageType) {
        return ResponseDTO.success(serverAccessApprovalService.listDevServer(devType, onlineStatus, packageType));
    }

    /**
     * 获取计算机IP地址
     * @return
     */
    @GetMapping("/getLocalIp")
    public ResponseDTO getLocalIp() {
        return ResponseDTO.success(IpUtil.getLocalMainIps());
    }

    /**
     * 获取可绑定的在线分部数据库设备信息
     *
     * @Param dbIds    同时查询此映射数据库绑定的分部数据库
     * @return
     */
    @GetMapping("/listCanBindBranchDbServer")
    @EncryptDecrypt
    public ResponseDTO listCanBindBranchDbServer(Long dbIds) {
        return ResponseDTO.success(serverAccessApprovalService.listCanBindBranchDbServer(dbIds));
    }

    /**
     * 服务器设备接入日志导出
     * @param condition
     * @return
     */
    @Oplog(name = "route.serverAccessLog", value = DevServerAccessApproveVO.class, type = OperationTypeDict.EXPORT)
    @PostMapping("/logExport")
    public ResponseDTO logExport(@RequestBody DevServerAccessApproveVO condition) {
        return ResponseDTO.success(devServerAccessLogService.export(condition));
    }

    /**
     * 服务器登录日志查询
     * @param condition
     * @return
     */
    @PostMapping("/getLoginLogPage")
    public ResponseDTO getLoginLogPage(@RequestBody DevServerLoginVO condition) {
        return ResponseDTO.success(devServerLoginLogService.getPage(condition));
    }

    /**
     * 更改各设备服务器的授权状态
     * @param devId             设备Id
     * @param devType           设备类型
     * @param authorizedStatus  授权状态  1-开启，0-关闭
     * @return
     */
    @Oplog(name = "pages.serverAuthStatus", value = DevServerAccessApproveVO.class, type = OperationTypeDict.UPDATE)
    @PostMapping("/updateAuthorizedStatus")
    public ResponseDTO updateAuthorizedStatus(Long devId, Integer devType, Integer authorizedStatus) {
        serverAccessApprovalService.updateAuthorizedStatus(devId, devType, authorizedStatus);
        return ResponseDTO.success();
    }

    /**
     * 服务器接入映射分页查找
     * @param condition 条件选择器
     * @return
     */
    @PostMapping("/getMappingPage")
    public GridPageDTO<DevServerAccessMapping> getMappingPage(@RequestBody DevServerAccessMappingVO condition) {
        return serverAccessApprovalService.getMappingPage(condition);
    }


    /**
     * 获取服务器接入审批设置中已配置的主数据库Id
     * @return
     */
    @GetMapping("/getConfigDbIds")
    public ResponseDTO getConfigDbIds() {
        return ResponseDTO.success(serverAccessApprovalService.getConfigDbIds());
    }


    /**
     * 判断是否拥有设置采集服务器和检测服务器的主数据库权限
     * @return
     */
    @GetMapping("/isSetAdminDb")
    public ResponseDTO isSetAdminDb() {
        return ResponseDTO.success(serverAccessApprovalService.isSetAdminDb());
    }

    /**
     * 根据设备编号获取网卡信息
     * @param devId
     * @return  getServerNetworkInfoList
     */
    @GetMapping("/getServerInfoByDevId/{devId}")
    public ResponseDTO getServerInfoByDevId(@PathVariable Long devId) {
        return ResponseDTO.success(serverAccessApprovalService.getServerInfoByDevId(devId));
    }

    /**
     * 根据数据库Id查询数据库映射关系中另外一个设备Id信息
     * @param devId
     * @return
     */
    @GetMapping("/getRelDbId/{devId}")
    public ResponseDTO getRelDbId(@PathVariable Integer devId) {
        return ResponseDTO.success(serverAccessApprovalService.getRelDbId(devId));
    }


    /**
     * 根据智能备份文件服务器查询绑定的智能备份服务器信息
     * @param fileDevId     智能备份文件服务器设备Id
     * @return
     */
    @GetMapping("/getSmartBackupByFileServerId/{fileDevId}")
    @EncryptDecrypt
    public ResponseDTO getSmartBackupByFileServerId(@PathVariable Long fileDevId) {
        return ResponseDTO.success(serverAccessApprovalService.getSmartBackupByFileServerId(fileDevId));
    }

}
