package com.tipray.dlp.dao;

import com.tipray.dlp.bean.SensitiveOpDetailLog;
import com.tipray.dlp.bean.vo.SensitiveOpDetailLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * (SensitiveOpDetailLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-05-06 19:19:07
 */
@Repository
@ShardingDataSource
public interface SensitiveOpDetailLogDao extends BaseDao<SensitiveOpDetailLog>{

    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(SensitiveOpDetailLogVO vo);

    /**
     * 通过条件查询统计数量
     * @see com.tipray.dlp.mybatis.interceptor.MyPaginationInnerInterceptor
     *      buildCountParams() 防止分页查询时报错
     *
     * @return 数量
     */
    Long countByEventGuid(SensitiveOpDetailLogVO vo);

     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<SensitiveOpDetailLog> listByEventGuid(SensitiveOpDetailLogVO vo);
}
