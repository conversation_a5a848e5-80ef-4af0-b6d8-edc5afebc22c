package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.BrowserDownloadLog;
import com.tipray.dlp.bean.BrowserUploadLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.BrowserLogVO;
import com.tipray.dlp.service.BrowserDownloadLogService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/1 14:04
 */
@Oplog(BrowserUploadLog.class)
@RestController
@RequestMapping("/log/browserDownload")
public class BrowserDownloadLogController {
    @Resource
    private BrowserDownloadLogService service;

    /**
     * 删除网页下载文件记录
     * @param list
     */
    @Oplog(name = "route.browserDownload")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<BrowserDownloadLog> list) {
        service.deleteBatch(list);
        return ResponseDTO.success();
    }

    /**
     * 分页查询网页下载文件记录
     * @param vo
     * @return
     */
    @Oplog(value = BrowserLogVO.class,name = "route.browserDownload")
    @PostMapping(value = "getPage")
    public GridPageDTO<BrowserDownloadLog> getPage(@RequestBody BrowserLogVO vo) {
        return service.getLogPage(vo);
    }

    /**
     * 导出网页下载文件记录
     * @param vo
     * @return
     */
    @Oplog(value = BrowserLogVO.class,name = "route.browserDownload")
    @PostMapping(value = "export")
    public ResponseDTO exportExcel(@RequestBody BrowserLogVO vo){
        return ResponseDTO.success(service.exportExcel(vo));
    }
}
