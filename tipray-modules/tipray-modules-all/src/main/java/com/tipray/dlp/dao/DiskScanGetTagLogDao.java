package com.tipray.dlp.dao;

import com.tipray.dlp.bean.DiskScanGetTagLog;
import com.tipray.dlp.bean.vo.DiskScanGetTagLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource
public interface DiskScanGetTagLogDao extends BaseDao<DiskScanGetTagLog>{
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(DiskScanGetTagLogVO vo);

     /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<DiskScanGetTagLog> listByVO(DiskScanGetTagLogVO vo);
}
