package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.EmailKeyWordBlock;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.EmailKeyWordBlockService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 邮件内容限制Controller
 */
@RestController
@Oplog(EmailKeyWordBlock.class)
@RequestMapping("/emailKeyWordBlock")
public class EmailKeyWordBlockController {
    @Resource
    private EmailKeyWordBlockService emailKeyWordBlockService;

    /**
     * 添加邮件内容限制策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.emailKeyWordBlock")
    @PostMapping(value = "add")
    public EmailKeyWordBlock add(@RequestBody EmailKeyWordBlock bean) {
        emailKeyWordBlockService.insert(bean);
        return bean;
    }

    /**
     * 修改邮件内容限制策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.emailKeyWordBlock")
    @PostMapping(value = "update")
    public EmailKeyWordBlock update(@RequestBody EmailKeyWordBlock bean) {
        emailKeyWordBlockService.update(bean);
        return bean;
    }

    /**
     * 删除邮件内容限制策略
     * @param ids
     */
    @Oplog(name = "pages.emailKeyWordBlock")
    @PostMapping(value = "delete")
    public void deleteById(String ids) {
        emailKeyWordBlockService.deleteById(ids);
    }

    @GetMapping("get/{id}")
    public EmailKeyWordBlock getById(@PathVariable("id") Long id) {
        return emailKeyWordBlockService.getStrategyById(id);
    }

    @PostMapping("getByName")
    public EmailKeyWordBlock getByName(String name) {
        return emailKeyWordBlockService.getByName(name);
    }

    /** 邮件内容限制策略分页列表*/
    @PostMapping(value = "getPage")
    public GridPageDTO<EmailKeyWordBlock> getPage(@RequestBody StrategyVO vo) {
        return emailKeyWordBlockService.getStrategyPage(vo);
    }
}
