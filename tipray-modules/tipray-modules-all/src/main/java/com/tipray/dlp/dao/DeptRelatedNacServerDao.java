package com.tipray.dlp.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.tipray.dlp.bean.DeptRelatedNac;
import com.tipray.dlp.bean.TerminalExtAssignServer;
import com.tipray.dlp.util.CollectionUtil;
import com.tipray.dlp.util.StringUtil;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;

@Repository
public interface DeptRelatedNacServerDao extends BaseDao<DeptRelatedNac>{
//    @Override
//    default int updateById(DeptRelatedNac bean){
//        UpdateWrapper wrapper = new UpdateWrapper<DeptRelatedNac>()
//                .set("deleted",0)
//                .eq("dept_id", bean.getGroupId());
//        return this.update(bean, wrapper);
//    }
    @Override
    default void deleteByIds(String ids){
        List<Long> idList = StringUtil.toLongList(ids);
        if(idList.isEmpty()){
            return;
        }
        this.delete(new LambdaQueryWrapper<DeptRelatedNac>().in(DeptRelatedNac::getGroupId, idList));
    }

    default List<Long> listDeptIdByServerId(Long serverId){
        List<Object> list = this.selectObjs(new LambdaQueryWrapper<DeptRelatedNac>()
                .select(DeptRelatedNac::getGroupId)
                .eq(DeptRelatedNac::getServerId, serverId));
        return CollectionUtil.toLongList(list);
    }
    default List<Long> listDeptIdByServerIds(String serverIds){
        List<Object> list = this.selectObjs(new LambdaQueryWrapper<DeptRelatedNac>()
                .select(DeptRelatedNac::getGroupId)
                .in(DeptRelatedNac::getServerId, StringUtil.toLongList(serverIds)));
        return CollectionUtil.toLongList(list);
    }
    default List<DeptRelatedNac> listByDeptIds(String deptIds){
        List<Long> idList = StringUtil.toLongList(deptIds);
        if(idList.isEmpty()){
            return new LinkedList<>();
        }
        return this.selectList(new LambdaQueryWrapper<DeptRelatedNac>().in(DeptRelatedNac::getGroupId, idList));
    }
    default List<DeptRelatedNac> listByServerId(Long serverId){
        return this.selectList(new LambdaQueryWrapper<DeptRelatedNac>()
                .eq(DeptRelatedNac::getServerId,serverId)
        );
    }
    List<DeptRelatedNac> selectDeptRelatedNacServerList();
}
