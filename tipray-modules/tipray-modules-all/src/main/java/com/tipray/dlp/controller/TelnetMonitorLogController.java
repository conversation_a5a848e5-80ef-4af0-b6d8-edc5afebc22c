package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.TelnetMonitorLog;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.TelnetLogVO;
import com.tipray.dlp.service.TelnetMonitorLogService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * telnet监控日志Controller
 * (TelnetMonitorLog)表控制层
 *
 * <AUTHOR>
 * @since 2020-06-02 16:43:56
 */
@Oplog(TelnetMonitorLog.class)
@RestController
@RequestMapping("/log/telnetMonitor")
public class TelnetMonitorLogController {
    /**
     * 服务对象
     */
    @Resource
    private TelnetMonitorLogService telnetMonitorLogService;

    /**
     * 删除telnet监控日志
     * @param list
     */
    @Oplog(name = "route.telnetMonitorLog")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<TelnetMonitorLog> list) {
        telnetMonitorLogService.deleteBatch(list);
        return ResponseDTO.success();
    }
    /**
     * 查询Telnet通讯记录
     * @param vo
     * @return
     */
    @Oplog(value = TelnetLogVO.class,name = "route.telnetMonitorLog")
    @PostMapping(value = "getPage")
    public GridPageDTO<TelnetMonitorLog> getPage(@ModelAttribute TelnetLogVO vo){
        return telnetMonitorLogService.getTelnetMonitorLogPage(vo);
    }

    /**
     * 导出Telnet通讯记录信息
     * 根据查询筛选条件导出Excel
     * @param vo
     * @return
     */
    @Oplog(value = TelnetLogVO.class,name = "route.telnetMonitorLog")
    @PostMapping(value = "export")
    public ResponseDTO exportExcelByLogVO(@RequestBody TelnetLogVO vo){
        return ResponseDTO.success(telnetMonitorLogService.exportExcel(vo));
    }

}
