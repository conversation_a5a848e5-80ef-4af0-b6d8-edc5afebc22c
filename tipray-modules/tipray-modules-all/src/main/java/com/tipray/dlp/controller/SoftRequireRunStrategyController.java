package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.SoftRequireRunStrategy;
import com.tipray.dlp.bean.dict.MsgModuleDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.exception.ParamException;
import com.tipray.dlp.service.SoftRequireRunStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * 必须运行软件设置Controller
 */
@RestController
@Oplog(SoftRequireRunStrategy.class)
@RequestMapping("/softRequireRunStrategy")
@Slf4j
public class SoftRequireRunStrategyController {
    @Resource
    private SoftRequireRunStrategyService softRequireRunStrategyService;

    /**
     * 新增软件限制策略
     * @param beans
     * @return
     */
    @Oplog(name = "pages.requireRun_Msg2")
    @PostMapping(value = "add")
    public SoftRequireRunStrategy[] add(@RequestBody SoftRequireRunStrategy[] beans) {
        for (SoftRequireRunStrategy bean : beans){
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.SOFT_REQUIRED_RUN_LIMIT.getCode());
        }
        softRequireRunStrategyService.insert(beans);
        return beans;
    }

    /**
     * 修改软件限制策略
     * @param beans
     * @return
     */
    @Oplog(name = "pages.requireRun_Msg2")
    @PostMapping(value = "update")
    public SoftRequireRunStrategy[] update(@RequestBody SoftRequireRunStrategy[] beans) {
        for (SoftRequireRunStrategy bean : beans){
            if(bean.getId() == null){
                throw new ParamException("参数错误");
            }
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.SOFT_REQUIRED_RUN_LIMIT.getCode());
        }

        softRequireRunStrategyService.update(beans);
        return beans;
    }

    /**
     * 删除软件限制策略
     * @param ids
     */
    @Oplog(name = "pages.requireRun_Msg2")
    @PostMapping(value = "delete")
    public void deleteAppBlock(String ids) {
        softRequireRunStrategyService.deleteById(ids);
    }

    @GetMapping("getById/{id}")
    public SoftRequireRunStrategy getById(@PathVariable("id") Long id) throws IOException {
        return softRequireRunStrategyService.getStrategyById(id);
    }

    @PostMapping("getByName")
    public List<SoftRequireRunStrategy> getByName(String name) {
        return softRequireRunStrategyService.listStrategyByName(name);
    }

    @PostMapping(value = "getPage")
    public GridPageDTO<SoftRequireRunStrategy> getStategyPage(@RequestBody StrategyVO vo) throws IOException {
        return softRequireRunStrategyService.getStategyPage(vo);
    }
}
