package com.tipray.dlp.controller;

import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.LogVO;
import com.tipray.dlp.bean.DecFileStatistics;
import com.tipray.dlp.service.DecFileStatisticsService;
import com.tipray.dlp.annotation.Oplog;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

/**
 * 解密文件统计Controller
 * (DecFileStatistics)表控制层
 *
 * <AUTHOR>
 * @since 2020-05-21 10:18:59
 */
@Oplog(DecFileStatistics.class)
@RestController
@RequestMapping("/decFileStatistics")
public class DecFileStatisticsController {
    /**
     * 服务对象
     */
    @Resource
    private DecFileStatisticsService decFileStatisticsService;

    /** 删除*/
    @PostMapping(value = "delete")
    public void deleteById(String ids){
        decFileStatisticsService.deleteDecFileStatisticsById(ids);
    }

    /** 分页查询用户*/
    @PostMapping(value = "getPage")
    public GridPageDTO<DecFileStatistics> getPage(@ModelAttribute LogVO vo){
        return decFileStatisticsService.getDecFileStatisticsPage(vo);
    }

}
