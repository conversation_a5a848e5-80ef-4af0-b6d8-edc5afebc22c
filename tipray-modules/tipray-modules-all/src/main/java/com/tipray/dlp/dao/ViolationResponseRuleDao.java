package com.tipray.dlp.dao;

import com.tipray.dlp.bean.ViolationResponseRule;
import com.tipray.dlp.bean.RuleOverview;
import com.tipray.dlp.bean.vo.RuleOverviewVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ViolationResponseRuleDao extends BaseDao<ViolationResponseRule> {

    /**
     * 获取违规响应规则总览
     * @param ruleOverviewVO 查询参数
     * @return 违规响应规则集合
     */
    List<RuleOverview> listByRuleOverviewVO(RuleOverviewVO ruleOverviewVO);

}

