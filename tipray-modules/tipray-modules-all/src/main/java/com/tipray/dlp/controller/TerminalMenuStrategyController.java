package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.TerminalMenuStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.TerminalMenuStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 终端菜单设置Controller
 */
@Oplog(TerminalMenuStrategy.class)
@RestController
@RequestMapping("/terminalMenuStrategy")
public class TerminalMenuStrategyController {
    /**
     * 服务对象
     */
    @Resource
    private TerminalMenuStrategyService terminalMenuStrategyService;

    /**
     * 新增终端菜单设置策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.terminalMenuStg")
    @PostMapping(value = "add")
    public TerminalMenuStrategy insert(@RequestBody TerminalMenuStrategy bean){
        terminalMenuStrategyService.insertStrategy(bean);
        return bean;
    }

    /**
     * 修改终端菜单设置策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.terminalMenuStg")
    @PostMapping(value = "update")
    public TerminalMenuStrategy update(@RequestBody TerminalMenuStrategy bean){
        terminalMenuStrategyService.updateStrategy(bean);
        return bean;
    }

    /**
     * 删除终端菜单设置策略
     * @param ids
     */
    @Oplog(name = "pages.terminalMenuStg")
    @PostMapping(value = "delete")
    public void deleteById(String ids){
        terminalMenuStrategyService.deleteStrategyById(ids);
    }
    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("get/{id}")
    public TerminalMenuStrategy getById(@PathVariable("id") Long id) {
        return terminalMenuStrategyService.getStrategyById(id);
    }

    @PostMapping("getByName")
    public TerminalMenuStrategy getByName(String name) {
        return terminalMenuStrategyService.getStrategyByName(name);
    }

    /** 分页查询*/
    @PostMapping(value = "getPage")
    public GridPageDTO<TerminalMenuStrategy> getPage(@RequestBody StrategyVO vo){
        return terminalMenuStrategyService.getStrategyPage(vo);
    }
}
