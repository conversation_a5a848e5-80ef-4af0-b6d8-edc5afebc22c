package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.OutgoingProcess;
import com.tipray.dlp.bean.OutgoingProcessGroup;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.dto.TreeNodeDTO;
import com.tipray.dlp.bean.vo.OutgoingProcessImportVo;
import com.tipray.dlp.bean.vo.OutgoingProcessVo;
import com.tipray.dlp.bean.vo.SoftwareInfoVO;
import com.tipray.dlp.mybatis.bean.PageVO;
import com.tipray.dlp.service.OutgoingProcessService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * 软件黑名单程序库(OutgoingProcess)表控制层
 */
@Oplog(OutgoingProcess.class)
@RestController
@RequestMapping("/softwareBlacklist")
public class SoftwareBlacklistController {
    /**
     * 服务对象
     */
    @Resource
    private OutgoingProcessService outgoingProcessService;

    /**
     * 添加软件黑名单程序类别
     * @param bean
     * @return
     */
    @Oplog(value = OutgoingProcessGroup.class, name = "table.appType")
    @PostMapping(value = "addGroup")
    public OutgoingProcessGroup addGroup(@RequestBody OutgoingProcessGroup bean) {
        if (bean.getGroupType() == null) {
            bean.setGroupType(1);
        }
        outgoingProcessService.insertGroup(bean);
        return bean;
    }

    /**
     * 删除软件黑名单程序类别
     */
    @Oplog(value = OutgoingProcessGroup.class, name = "table.appType")
    @PostMapping(value = "deleteGroup")
    public void deleteGroup(Long id) {
        outgoingProcessService.deleteGroupById(id);
    }

    /**
     * 更新软件黑名单程序类别
     * @param bean
     * @return
     */
    @Oplog(value = OutgoingProcessGroup.class, name = "table.appType")
    @PostMapping(value = "updateGroup")
    public OutgoingProcessGroup updateGroup(@RequestBody OutgoingProcessGroup bean) {
        if (bean.getGroupType() == null) {
            bean.setGroupType(1);
        }
        outgoingProcessService.updateGroup(bean);
        return bean;
    }

    @GetMapping(value = "countByGroupId/{groupId}")
    public Long countByGroupId(@PathVariable("groupId") Long groupId) {
        return outgoingProcessService.countAppByGroupId(groupId);
    }

    @PostMapping("getGroupByName")
    public OutgoingProcessGroup getGroupByName(OutgoingProcessGroup bean) {
        if (bean.getGroupType() == null) {
            bean.setGroupType(1);
        }
        return outgoingProcessService.getGroupByName(bean);
    }

    /**
     * 得到软件黑名单程序类别树
     * @return
     */
    @PostMapping(value = "getGroupTree")
    public List<TreeNodeDTO> getGroupTree() {
        OutgoingProcessGroup group = new OutgoingProcessGroup();
        group.setGroupType(1);
        return outgoingProcessService.listGroupTreeNode(group);
    }

    /**
     * 新增软件黑名单程序
     * @param bean
     * @return
     */
    @Oplog(name = "pages.softwareBlacklist")
    @PostMapping(value = "add")
    public OutgoingProcess insert(@RequestBody OutgoingProcess bean) {
        if (bean.getProcessType() == null) {
            bean.setProcessType(1);
        }
        outgoingProcessService.insertOutgoingProcess(bean);
        return bean;
    }

    /**
     * 修改软件黑名单程序
     * @param bean
     * @return
     * @throws Exception
     */
    @Oplog(name = "pages.softwareBlacklist")
    @PostMapping(value = "update")
    public OutgoingProcess update(@RequestBody OutgoingProcess bean) throws Exception {
        if (bean.getProcessType() == null) {
            bean.setProcessType(1);
        }
        outgoingProcessService.updateOutgoingProcess(bean);
        return bean;
    }

    /**
     * 删除软件黑名单程序
     * @param ids
     * @throws Exception
     */
    @Oplog(name = "pages.softwareBlacklist")
    @PostMapping(value = "delete")
    public void deleteById(String ids) throws Exception {
        outgoingProcessService.deleteOutgoingProcessById(ids, 1);
    }

    /**
     * 导入外发进程
     * @param vo
     * @param uploadFile
     * @return
     */
    @PostMapping(value = "importProcess")
    public ResponseDTO importProcess(OutgoingProcessImportVo vo, MultipartFile uploadFile) {
        try {
            if (vo.getProcessType() == null) {
                vo.setProcessType(1);
            }
            List<OutgoingProcess> list = outgoingProcessService.importProcess(vo, uploadFile);
            return ResponseDTO.success(list);
        } catch (Exception e) {
            return ResponseDTO.failure(e);
        }
    }

    /**
     * 导入外发进程(返回导入的进程，若存在与数据库重复，返回数据库中的进程数据）
     * @param vo vo
     * @param uploadFile uploadFile
     * @return 导入的进程
     */
    @PostMapping(value = "importProcessReturnList")
    public ResponseDTO importProcessReturnList(OutgoingProcessImportVo vo, MultipartFile uploadFile) {
        try {
            if (vo.getProcessType() == null) {
                vo.setProcessType(1);
            }
            List<OutgoingProcess> list = outgoingProcessService.importProcessReturnList(vo, uploadFile);
            return ResponseDTO.success(list);
        } catch (Exception e) {
            return ResponseDTO.failure(e);
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("get/{id}")
    public OutgoingProcess getById(@PathVariable("id") Long id) {
        return outgoingProcessService.getOutgoingProcessById(id);
    }

    /** 分页查询黑名单进程*/
    @PostMapping(value = "getPage")
    public GridPageDTO<OutgoingProcess> getPage(@ModelAttribute SoftwareInfoVO vo) {
        vo.setProcessType(1);
        return outgoingProcessService.getOutgoingProcessPage(vo);
    }

    /** 分页查询黑名单进程*/
    @PostMapping(value = "listProcess")
    public List<OutgoingProcess> listProcess(@ModelAttribute SoftwareInfoVO vo) {
        vo.setProcessType(1);
        return outgoingProcessService.listOutgoingProcess(vo);
    }

    /** 分页查询黑名单进程*/
    @PostMapping(value = "listProcessPage")
    public GridPageDTO<OutgoingProcess> listProcessPage(@ModelAttribute SoftwareInfoVO vo) {
        vo.setProcessType(1);
        return outgoingProcessService.getOutgoingProcessPage(vo);
    }

    /**
     * 从公共程序库导入外发进程
     * @param vo
     * @return
     */
    @PostMapping(value = "importFromLib")
    public List<OutgoingProcess> importFromLib(@RequestBody OutgoingProcessImportVo vo) {
        vo.setProcessType(1);
        return outgoingProcessService.importFormLib(vo);
    }

    /**
     * 从公共程序库导入外发进程
     * @param vo
     * @return
     */
    @Oplog(value = OutgoingProcessVo.class, name = "pages.softwareBlacklist")
    @PostMapping(value = "importFromExe")
    public List<OutgoingProcess> addFromExe(@RequestBody OutgoingProcessVo vo) {
        vo.setProcessType(1);
        return outgoingProcessService.importFromExe(vo);
    }

    /**
     * 根据id查询所关联的策略
     * @param ids
     * @return
     */
    @PostMapping(value = "getStrategyByOutgoingProcessIds")
    public ResponseDTO getStrategyByOutgoingProcessIds(String ids) {
        return ResponseDTO.success(outgoingProcessService.getSoftwareBlacklistStrategyByOutgoingProcessIds(ids));
    }

}
