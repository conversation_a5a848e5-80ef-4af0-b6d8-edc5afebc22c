package com.tipray.dlp.dao;

import com.tipray.dlp.bean.PatchLauguageInfo;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import com.tipray.dlp.mybatis.annotation.WhereIn;
import com.tipray.dlp.mybatis.bean.PageVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ShardingDataSource
public interface PatchLauguageInfoDao extends BaseDao<PatchLauguageInfo>{
    List<PatchLauguageInfo> getPatchLauguageInfoByGuid(@Param("guid") String guid);

    @WhereIn(@WhereIn.Param(value = "guidList", numerical = false))
    @Override
    PageVO<PatchLauguageInfo> selectPageByVO(PageVO vo);
}
