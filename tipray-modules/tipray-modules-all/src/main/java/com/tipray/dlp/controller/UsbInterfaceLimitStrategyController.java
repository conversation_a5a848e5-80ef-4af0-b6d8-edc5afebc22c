package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.UsbInterfaceLimitStrategy;
import com.tipray.dlp.bean.dict.MsgModuleDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.exception.ParamException;
import com.tipray.dlp.service.AssetInfoService;
import com.tipray.dlp.service.UsbInterfaceLimitStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * usb接口管控策略Controller
 */
@Oplog(UsbInterfaceLimitStrategy.class)
@RestController
@RequestMapping("/usbInterfaceLimit")
public class UsbInterfaceLimitStrategyController {
    private static final String PROP_ID = "23001";

    @Resource
    private UsbInterfaceLimitStrategyService usbInterfaceLimitStrategyService;
    @Resource
    private AssetInfoService assetInfoService;

    /**
     * 添加接口限制策略
     * @param beans   策略内容
     * @return 修改后的策略内容
     */
    @Oplog(name = "pages.usbInterface")
    @PostMapping(value = "add")
    public UsbInterfaceLimitStrategy[] addStrategy(@RequestBody UsbInterfaceLimitStrategy[] beans) {
        for (UsbInterfaceLimitStrategy bean : beans){
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.USBINTERFACE.getCode());
        }
        usbInterfaceLimitStrategyService.insertStrategy(beans);
        return beans;
    }

    /**
     * 修改接口限制策略
     * @param beans   策略内容
     * @return 修改后的策略内容
     */
    @Oplog(name = "pages.usbInterface")
    @PostMapping(value = "update")
    public UsbInterfaceLimitStrategy[] updateStrategy(@RequestBody UsbInterfaceLimitStrategy[] beans) {
        for (UsbInterfaceLimitStrategy bean : beans){
            if(bean.getId() == null){
                throw new ParamException("参数错误");
            }
            bean.setSstType(0);
            bean.setAlarmType(MsgModuleDict.USBINTERFACE.getCode());
        }
        usbInterfaceLimitStrategyService.updateStrategy(beans);
        return beans;
    }

    /**
     * 删除接口限制策略
     * @param ids   策略主键
     */
    @Oplog(name = "pages.usbInterface")
    @PostMapping(value = "delete")
    public void deleteStrategy(String ids) {
        usbInterfaceLimitStrategyService.deleteStrategyById(ids);
    }

    /**
     * 根据ID获取策略
     * @param id   策略主键
     * @return 策略内容
     */
    @GetMapping("get/{id}")
    public UsbInterfaceLimitStrategy getById(@PathVariable("id") Long id) {
        return usbInterfaceLimitStrategyService.getStrategyById(id);
    }

    /**
     * 根据名称获取策略
     * @param name     策略名称
     * @return  策略内容
     */
    @PostMapping("getByName")
    public UsbInterfaceLimitStrategy getByName(String name) {
        return usbInterfaceLimitStrategyService.getStrategyByName(name);
    }

    /**
     * 获取接口限制策略
     * @param vo
     * @return
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<UsbInterfaceLimitStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return usbInterfaceLimitStrategyService.getStrategyPage(vo);
    }
}
