package com.tipray.dlp.dao;

import com.tipray.dlp.mybatis.annotation.WhereIn;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Repository
public interface ServerReflectDataDao {
    List<Long> listServerIds(Long serverId);

    List<Map<String, Object>> findListByRelServerId(Long serverId);

    void deleteByRefServerId(Long serverId);
    @WhereIn(@WhereIn.Param("serverIds"))
    void deleteByRefServerIds(String serverIds);

    void deleteByServerId(Long serverId);
    @WhereIn(@WhereIn.Param("serverIds"))
    void deleteByServerIds(Collection<Long> serverIds);

    void batchInsert(@Param("refServerId") Long refServerId, @Param("serverIds") List<Long> serverIds);
    void batchInsertByMap(List<Map<String,Long>> data);

    void insert(Long serverId, Long refServerId, Integer syncType, Integer syncRate);

    /**
     * 获取所有总部服务器ID
     * @return
     */
    List<Long> listAllRefServerId();

    List<Long> listAllServerId();

    Long getRefServerIdByServerId(Long devId);

    List<Map<String, Long>> listMapByGroupId(Long groupId);

    List<Map<String, Long>> listAll();
}
