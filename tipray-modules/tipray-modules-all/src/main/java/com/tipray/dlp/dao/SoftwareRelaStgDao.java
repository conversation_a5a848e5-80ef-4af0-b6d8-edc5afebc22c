package com.tipray.dlp.dao;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SoftwareRelaStgDao {
    void insert(@Param("stgId") Long stgId, @Param("softwareName") String softwareName);

    void deleteByStgId(Long stgId);

    void deleteByStgIds(String stgIds);

    List<String> listSoftwareName(Long stgId);

    List<Long> listStgId(String softwareName);
}
