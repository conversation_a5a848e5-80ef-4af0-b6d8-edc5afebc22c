package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.SoftwareBlacklistStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.SoftwareBlacklistStrategyService;
import com.tipray.dlp.util.CollectionUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 外发进程黑名单策略Controller
 */
@RestController
@Oplog(SoftwareBlacklistStrategy.class)
@RequestMapping("/softwareBlacklistStrategy")
public class SoftwareBlacklistStrategyController {
    @Resource
    private SoftwareBlacklistStrategyService strategyService;

    /**
     * 新增软件黑名单策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.softwareBlacklistStrategy")
    @PostMapping(value = "add")
    public SoftwareBlacklistStrategy addStrategy(@RequestBody SoftwareBlacklistStrategy bean) {
        this.formatStrategy(bean);
        strategyService.insert(bean);
        return bean;
    }

    /**
     * 修改软件黑名单策略
     * @param bean
     * @return
     */
    @Oplog(name = "pages.softwareBlacklistStrategy")
    @PostMapping(value = "update")
    public SoftwareBlacklistStrategy updateStrategy(@RequestBody SoftwareBlacklistStrategy bean) {
        this.formatStrategy(bean);
        strategyService.update(bean);
        return bean;
    }

    private void formatStrategy(SoftwareBlacklistStrategy bean) {
        if (bean == null || CollectionUtil.isEmpty(bean.getSoftList())) {
            return;
        }
        // 将非必要的字段置为空，较少策略的长度
        bean.getSoftList().forEach(item -> {
            item.setCreateTime(null);
            item.setModifyTime(null);
        });
    }

    /**
     * 删除软件黑名单策略
     * @param ids
     */
    @Oplog(name = "pages.softwareBlacklistStrategy")
    @PostMapping(value = "delete")
    public void deleteStrategy(String ids) {
        strategyService.deleteById(ids);
    }

    @PostMapping("getByName")
    public SoftwareBlacklistStrategy getByName(String name) {
        return strategyService.getByName(name);
    }

    /** 获取外发进程黑名单策略*/
    @PostMapping(value = "getPage")
    public GridPageDTO<SoftwareBlacklistStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return strategyService.getStrategyPage(vo);
    }

}
