package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.SoftWareTaskStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.TreeNodeDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.SoftWareTaskStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 软件卸载Controller
 */
@RestController
@RequestMapping("/softWareTaskStrategy")
public class SoftWareTaskStrategyController {

    @Resource
    private SoftWareTaskStrategyService softWareTaskStrategyService;

    /** 分页查询*/
    @PostMapping(value = "getPage")
    public GridPageDTO<SoftWareTaskStrategy> getPage(@RequestBody StrategyVO vo){
        return softWareTaskStrategyService.getStrategyPage(vo);
    }

    /**
     * 删除卸载策略
     * @param ids
     */
    @Oplog(value = SoftWareTaskStrategy.class, name = "pages.uninstallationStrategy")
    @PostMapping(value = "delete")
    public void deleteById(String ids){
        softWareTaskStrategyService.deleteStrategyById(ids);
    }

    @GetMapping("listObjectTree/{objectId}/{objectType}")
    public List<TreeNodeDTO> listObjectTree(@PathVariable("objectId") Long objectId, @PathVariable("objectType")Integer objectType) {
        return softWareTaskStrategyService.listObjectTree(objectId, objectType);
    }

    /**
     * 保存卸载策略
     * @param bean
     */
    @Oplog(value = SoftWareTaskStrategy.class, name = "pages.uninstallationStrategy")
    @PostMapping(value = "save")
    public void saveStrategy(@RequestBody SoftWareTaskStrategy bean){
        softWareTaskStrategyService.saveStrategy(bean);
    }

    /**
     * 更新卸载策略
     * @param bean
     */
    @Oplog(value = SoftWareTaskStrategy.class, name = "pages.uninstallationStrategy")
    @PostMapping(value = "update")
    public void updateStrategy(@RequestBody SoftWareTaskStrategy bean){
        softWareTaskStrategyService.updateStrategy(bean);
    }

}
