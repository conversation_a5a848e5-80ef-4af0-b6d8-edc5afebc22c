package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.VersionInfo;
import com.tipray.dlp.bean.dto.ServerInstallPkgMakeDTO;
import com.tipray.dlp.service.ServerInstallPkgService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * 服务器安装包Controller
 * <AUTHOR>
 * @date 2025-07-03 11:39
 */
@RestController
@RequestMapping(value = "/serverInstallPkg")
public class ServerInstallPkgController {

    @Resource
    private ServerInstallPkgService service;

    /**
     * 服务器安装包制作
     * @param dto       /
     * @param response  /
     */
    @Oplog(value = ServerInstallPkgMakeDTO.class, name = "pages.makeInstallationPackage", defaultArg = true)
    @PostMapping("makeInstallPkg")
    public void makeInstallPkg(@RequestBody ServerInstallPkgMakeDTO dto, HttpServletResponse response) {
        service.makePackage(dto, response);
    }

}
