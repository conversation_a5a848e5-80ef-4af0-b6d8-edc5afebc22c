package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.BrowserFileDownloadStrategy;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.BrowserFileDownloadStrategyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/7/30 16:17
 */
@RestController
@Oplog(BrowserFileDownloadStrategy.class)
@RequestMapping("/browserFileDownloadStrategy")
public class BrowserFileDownloadStrategyController {
    @Autowired
    private BrowserFileDownloadStrategyService service;

    /**
     * 添加网络上传文件管控策略
     * @param bean
     * @return
     */
    @Oplog(name = "route.BrowserFileDownloadStrategy")
    @PostMapping(value = "add")
    public BrowserFileDownloadStrategy addStrategy(@RequestBody BrowserFileDownloadStrategy bean) {
        service.insertStrategy(bean);
        return bean;
    }

    /**
     * 修改网络上传文件管控策略
     * @param bean
     * @return
     */
    @Oplog(name = "route.BrowserFileDownloadStrategy")
    @PostMapping(value = "update")
    public BrowserFileDownloadStrategy updateStrategy(@RequestBody BrowserFileDownloadStrategy bean) {
        service.updateStrategy(bean);
        return bean;
    }

    /**
     * 删除网络上传文件管控策略
     * @param ids
     */
    @Oplog(name = "route.BrowserFileDownloadStrategy")
    @PostMapping(value = "delete")
    public void deleteStrategy(String ids) {
        service.deleteStrategyById(ids);
    }

    @GetMapping("get/{id}")
    public BrowserFileDownloadStrategy getById(@PathVariable("id") Long id) {
        return service.getStrategyById(id);
    }

    @PostMapping("getByName")
    public BrowserFileDownloadStrategy getByName(String name) {
        return service.getStrategyByName(name);
    }

    /**
     * 获取网页下载文件管控策略
     * @param vo
     * @return
     */
    @PostMapping(value = "getPage")
    public GridPageDTO<BrowserFileDownloadStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return service.getStrategyPage(vo);
    }
}
