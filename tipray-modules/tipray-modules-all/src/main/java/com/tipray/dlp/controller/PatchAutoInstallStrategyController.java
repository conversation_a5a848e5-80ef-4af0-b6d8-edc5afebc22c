package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.PatchAutoInstallStrategy;
import com.tipray.dlp.bean.dict.OperationTypeDict;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.StrategyVO;
import com.tipray.dlp.service.PatchAutoInstallStrategyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 补丁自动安装策略Controller
 */
@Oplog(PatchAutoInstallStrategy.class)
@RestController
@RequestMapping("/patchAutoInstallStrategy")
public class PatchAutoInstallStrategyController {
    @Resource
    private PatchAutoInstallStrategyService patchAutoInstallStrategyService;
    @Oplog(name = "route.autoInstallStrategy", type = OperationTypeDict.ADD, defaultArg = true)
    @PostMapping(value = "saveStrategy")
    public PatchAutoInstallStrategy insert(@RequestBody PatchAutoInstallStrategy bean){
        patchAutoInstallStrategyService.savePatchAutoInstallStrategy(bean);
        return bean;
    }

    @Oplog(name = "route.autoInstallStrategy", type = OperationTypeDict.UPDATE, defaultArg = true)
    @PostMapping(value = "updateStrategy")
    public PatchAutoInstallStrategy update(@RequestBody PatchAutoInstallStrategy bean){
        patchAutoInstallStrategyService.savePatchAutoInstallStrategy(bean);
        return bean;
    }
    /**
     * 删除自动补丁限制策略
     * @param ids
     */
    @Oplog(name="route.autoInstallStrategy")
    @PostMapping(value = "delete")
    public void deleteStrategy(String ids) {

        patchAutoInstallStrategyService.deleteStrategyById(ids);
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @PostMapping("getStrategy")
    public PatchAutoInstallStrategy getStrategy() {
        return patchAutoInstallStrategyService.getStrategyPage();
    }

    /**
     * 根据名称获取策略
     * @param name
     * @return
     */
    @PostMapping("getByName")
    public PatchAutoInstallStrategy getByName(String name) {
        return patchAutoInstallStrategyService.getStrategyByName(name);
    }

    @PostMapping(value="getPage")
    public GridPageDTO<PatchAutoInstallStrategy> getStrategyPage(@RequestBody StrategyVO vo) {
        return patchAutoInstallStrategyService.getListStrategyPage(vo);
    }
}
