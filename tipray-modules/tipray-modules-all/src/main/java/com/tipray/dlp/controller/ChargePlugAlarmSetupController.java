package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.ChargePlugAlarmSetup;
import com.tipray.dlp.service.ChargePlugAlarmSetupService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 插件授权预警设置Controller
 * <AUTHOR>
 * @date 2023/6/29
 */
@RestController
@Oplog(ChargePlugAlarmSetup.class)
@RequestMapping("/chargePlugAlarmSetup")
public class ChargePlugAlarmSetupController {
    @Resource
    private ChargePlugAlarmSetupService chargePlugAlarmSetupService;

    /**
     * 保存插件授权预警设置
     * @param bean
     * @return
     */
    @Oplog(name="pages.alarmSetting")
    @PostMapping(value = "updateAlarmSetup")
    public ChargePlugAlarmSetup updateAlarmSetup(@RequestBody ChargePlugAlarmSetup bean) {
        chargePlugAlarmSetupService.update(bean);
        return bean;
    }

    /**
     * 获取插件授权预警设置
     * @return
     */
    @GetMapping(value = "getAlarmSetup")
    public ChargePlugAlarmSetup getAlarmSetup() {
        return chargePlugAlarmSetupService.getChargePlugAlarmSetup();
    }
}
