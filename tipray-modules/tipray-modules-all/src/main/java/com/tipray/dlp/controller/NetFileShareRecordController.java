package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.NetFileShareRecord;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.dto.ResponseDTO;
import com.tipray.dlp.bean.vo.NetFileShareLogVO;
import com.tipray.dlp.service.NetFileShareRecordService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 本地文件共享记录Controller
 * (NetFileShareRecord)表控制层
 *
 * <AUTHOR>
 * @since 2021-01-11 14:47:52
 */
@Oplog(NetFileShareRecord.class)
@RestController
@RequestMapping("/log/netFileShare")
public class NetFileShareRecordController {
    /**
     * 服务对象
     */
    @Resource
    private NetFileShareRecordService netFileShareRecordService;

    /**
     * 查询网络共享记录
     * @param vo
     * @return
     */
    @Oplog(value = NetFileShareLogVO.class,name = "route.netFileBackupRecord")
    @PostMapping(value = "getPage")
    public GridPageDTO<NetFileShareRecord> getPage(@RequestBody NetFileShareLogVO vo){
        return netFileShareRecordService.getNetFileShareRecordPage(vo);
    }

    /**
     * 删除网络文件共享记录
     * @param list
     * @return
     */
    @Oplog(name = "route.netFileBackupRecord")
    @PostMapping(value = "delete")
    public ResponseDTO delete(@RequestBody List<NetFileShareRecord> list) {
        netFileShareRecordService.deleteBatch(list);
        return ResponseDTO.success();
    }

    /**
     * 导出网络共享记录信息
     * 根据查询筛选条件导出Excel
     * @param vo
     * @return
     */
    @Oplog(value = NetFileShareLogVO.class,name = "route.netFileBackupRecord")
    @PostMapping(value = "export")
    public ResponseDTO exportExcelByLogVO(@RequestBody NetFileShareLogVO vo){
        return ResponseDTO.success(netFileShareRecordService.exportExcel(vo));
    }
}
