package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.DefaultMsgParamSetup;
import com.tipray.dlp.service.DefaultMsgParamSetupService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 终端默认弹窗方式Controller
 */
@Oplog(DefaultMsgParamSetup.class)
@RestController
@RequestMapping("/defaultShowMsgType")
public class DefaultMsgParamSetupController {
    @Resource
    private DefaultMsgParamSetupService setupService;


    /**
     * 修改终端默认弹窗方式
     * @param bean
     */
    @Oplog(value = DefaultMsgParamSetup.class, name = "pages.defaultMsgParamSetup")
    @PostMapping(value = "update")
    public void update(@RequestBody DefaultMsgParamSetup bean) {
        List<DefaultMsgParamSetup> list = setupService.query();
        if(list.size() == 0){
            return;
        }
        bean.setId(list.get(0).getId());
        setupService.update(bean);
    }


    /** 得到终端默认弹窗方式信息*/
    @PostMapping(value = "get")
    public DefaultMsgParamSetup get() {
        List<DefaultMsgParamSetup> list = setupService.query();
        if(list.size() > 0){
            return list.get(0);
        }
        return new DefaultMsgParamSetup();
    }

}
