package com.tipray.dlp.controller;

import com.tipray.dlp.annotation.Oplog;
import com.tipray.dlp.bean.ProcessStgChild;
import com.tipray.dlp.bean.ProcessStgConfig;
import com.tipray.dlp.bean.ProcessStgNet;
import com.tipray.dlp.bean.dto.GridPageDTO;
import com.tipray.dlp.bean.vo.ProcessStgVO;
import com.tipray.dlp.service.ProcessStgConfigService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 受控程序策略库控制层
 * 受控程序高级配置Controller
 *
 * <AUTHOR>
 * @since 2020-04-24 13:40:33
 */
@RestController
@RequestMapping("/processStgConfig")
public class ProcessStgConfigController {
    @Resource
    private ProcessStgConfigService processStgConfigService;

    /**
     * 新增子进程策略
     * @param bean
     * @return
     */
    @Oplog(value = ProcessStgChild.class,name = "pages.processStgChild")
    @PostMapping(value = "addChild")
    public ProcessStgChild insert(@RequestBody ProcessStgChild bean){
        processStgConfigService.insert(bean);
        return bean;
    }

    /**
     * 修改子进程策略
     * @param bean
     * @return
     */
    @Oplog(value = ProcessStgChild.class,name = "pages.processStgChild")
    @PostMapping(value = "updateChild")
    public ProcessStgChild updateChild(@RequestBody ProcessStgChild bean){
        processStgConfigService.update(bean);
        return bean;
    }

    /**
     * 删除子进程策略
     * @param ids
     */
    @Oplog(value = ProcessStgChild.class,name = "pages.processStgChild")
    @PostMapping(value = "deleteChild")
    public void deleteChild(String ids){
        processStgConfigService.deleteChildById(ids);
    }

    /** 查询子进程策略*/
    @PostMapping("getChildByName")
    public ProcessStgChild getChildByName(String name) {
        return processStgConfigService.getByName(ProcessStgChild.class, name);
    }

    /** 分页查询子进程策略*/
    @PostMapping(value = "getChildPage")
    public GridPageDTO<ProcessStgChild> getChildPage(@RequestBody ProcessStgVO vo) {
        return processStgConfigService.getPage(ProcessStgChild.class, vo);
    }

    /**
     * 新增网络限制策略
     * @param bean
     * @return
     */
    @Oplog(value = ProcessStgNet.class,name = "pages.processStgNet")
    @PostMapping(value = "addNet")
    public ProcessStgNet insertNet(@RequestBody ProcessStgNet bean){
        processStgConfigService.insert(bean);
        return bean;
    }

    /**
     * 修改网络限制策略
     * @param bean
     * @return
     */
    @Oplog(value = ProcessStgNet.class,name = "pages.processStgNet")
    @PostMapping(value = "updateNet")
    public ProcessStgNet updateNet(@RequestBody ProcessStgNet bean){
        processStgConfigService.update(bean);
        return bean;
    }

    /**
     * 删除网络限制策略
     * @param ids
     */
    @Oplog(value = ProcessStgNet.class,name = "pages.processStgNet")
    @PostMapping(value = "deleteNet")
    public void deleteNet(String ids){
        processStgConfigService.deleteNetById(ids);
    }

    /** 获取网络限制策略*/
    @PostMapping("getNetByName")
    public ProcessStgNet getNetByName(String name) {
        return processStgConfigService.getByName(ProcessStgNet.class, name);
    }

    /** 分页查询网络限制策略*/
    @PostMapping(value = "getNetPage")
    public GridPageDTO<ProcessStgNet> getNetPage(@RequestBody ProcessStgVO vo) {
        return processStgConfigService.getPage(ProcessStgNet.class, vo);
    }

    /**
     * 新增高级策略
     * @param bean
     * @return
     */
    @Oplog(value = ProcessStgConfig.class,name = "pages.advancedStrategy")
    @PostMapping(value = "addConfig")
    public ProcessStgConfig insertConfig(@RequestBody ProcessStgConfig bean){
        processStgConfigService.insert(bean);
        return bean;
    }

    /**
     * 修改高级策略
     * @param bean
     * @return
     */
    @Oplog(value = ProcessStgConfig.class,name = "pages.advancedStrategy")
    @PostMapping(value = "updateConfig")
    public ProcessStgConfig updateConfig(@RequestBody ProcessStgConfig bean){
        processStgConfigService.update(bean);
        return bean;
    }

    /**
     * 删除高级策略
     * @param ids
     */
    @Oplog(value = ProcessStgConfig.class,name = "pages.advancedStrategy")
    @PostMapping(value = "deleteConfig")
    public void deleteConfig(String ids){
        processStgConfigService.deleteConfigById(ids);
    }

    /** 获取高级策略*/
    @PostMapping("getConfigByName")
    public ProcessStgConfig getConfigByName(String name) {
        return processStgConfigService.getByName(ProcessStgConfig.class, name);
    }

    /** 分页查询高级策略*/
    @PostMapping(value = "getConfigPage")
    public GridPageDTO<ProcessStgConfig> getConfigPage(@RequestBody ProcessStgVO vo) {
        return processStgConfigService.getPage(ProcessStgConfig.class, vo);
    }

    /** 根据processNames获取高级策略*/
    @PostMapping("getConfigByProcessNames")
    public List<ProcessStgConfig> getConfigByProcessNames(String processNames) {
        return (List<ProcessStgConfig>) processStgConfigService.getByProcessNames(ProcessStgConfig.class,processNames);
    }

}
