package com.tipray.dlp.dao;

import com.tipray.dlp.bean.PrintMonitorLog;
import com.tipray.dlp.bean.vo.PrinterFileNameLogVO;
import com.tipray.dlp.mybatis.annotation.ShardingDataSource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 打印操作日志(PrintMonitorLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2019-11-19 16:32:28
 */
@Repository
@ShardingDataSource
public interface PrintMonitorLogDao extends BaseDao<PrintMonitorLog> {
    /**
     * 通过条件查询统计数量
     *
     * @return 数量
     */
    Long countByVO(PrinterFileNameLogVO vo);

    /**
     * 通过条件查询数据
     *
     * @return 对象列表
     */
    List<PrintMonitorLog> listByVO(PrinterFileNameLogVO vo);
}
