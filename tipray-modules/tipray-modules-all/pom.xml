<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>tipray-modules</artifactId>
        <groupId>com.tipray</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <description>DLP主要业务代码，包括策略配置、审计日志查看等</description>

    <artifactId>tipray-modules-all</artifactId>
    <version>1.0</version>

    <build>
        <finalName>${project.artifactId}-${project.version}</finalName>
        <plugins>
            <plugin>
                <groupId>com.tipray</groupId>
                <artifactId>tipray-commons-plugin</artifactId>
                <version>1.0</version>
                <configuration>
                    <baseDir>${project.basedir}</baseDir>
                    <outputDir>${project.build.directory}</outputDir>
                    <targetDB>Oracle</targetDB>
                </configuration>
                <executions>
                    <execution>
                        <phase>test</phase>
                        <goals>
                            <!-- sqlite数据库文件处理：转utf-8-->
                            <goal>dbFile</goal>
                            <!-- 将mysql的mapper.xml文件转换成其它数据库的mapper.xml-->
                            <!--<goal>sqlMapperConvert</goal>-->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-communication</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-send</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-ftp</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-img</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-effectiveStg</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-modules-sync</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-modules-cloud</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-modules-base</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-modules-software</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-modules-backup</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-api-bwm</artifactId>
            <version>1.0</version>
        </dependency>
        <!--当需要给第三方提供接口时，加载此模块-->
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-modules-trwfe</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
        </dependency>
        <!-- Redis 缓存-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.lettuce</groupId>
                    <artifactId>lettuce-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-keyvalue</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-oxm</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- mail -->
        <dependency>
            <groupId>io.github.jaloon</groupId>
            <artifactId>javax-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.jaloon</groupId>
            <artifactId>eml-parser</artifactId>
            <version>1.6.7.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sun.mail</groupId>
                    <artifactId>jakarta.mail</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-yaml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.abel533</groupId>
            <artifactId>ECharts</artifactId>
        </dependency>

        <!-- easypoi excel导出工具 -->
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- websocket用于实现向前端推送消息 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
        </dependency>

        <dependency>
            <groupId>org.ini4j</groupId>
            <artifactId>ini4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.openpnp</groupId>
            <artifactId>opencv</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-imaging</artifactId>
        </dependency>
        <dependency>
            <groupId>net.dongliu</groupId>
            <artifactId>apk-parser</artifactId>
        </dependency>
        <!-- 访问共享盘符 -->
        <dependency>
            <groupId>org.codelibs</groupId>
            <artifactId>jcifs</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.draco1023</groupId>
            <artifactId>poi-tl-ext</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jfree</groupId>
            <artifactId>jfreechart</artifactId>
        </dependency>
        <dependency>
            <groupId>org.docx4j</groupId>
            <artifactId>docx4j-ImportXHTML</artifactId>
        </dependency>

    </dependencies>
</project>
