#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x000000005e0631dd, pid=31884, tid=0x0000000000007bbc
#
# JRE version: OpenJDK Runtime Environment (8.0_412-b08) (build 1.8.0_412-b08)
# Java VM: OpenJDK 64-Bit Server VM (25.412-b08 mixed mode windows-amd64 compressed oops)
# Problematic frame:
# V  [jvm.dll+0xa31dd]
#
# Failed to write core dump. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://github.com/adoptium/adoptium-support/issues
#

---------------  T H R E A D  ---------------

Current thread (0x000001623299a000):  JavaThread "ForkJoinPool.commonPool-worker-4" daemon [_thread_in_vm, id=31676, stack(0x000000560ca00000,0x000000560cb00000)]

siginfo: ExceptionCode=0xc0000005, reading address 0x000000000000009c

Registers:
RAX=0x0000ff450de4e7e9, RBX=0x00000007c18851e8, RCX=0x000000560cafb1e0, RDX=0x0000000000000000
RSP=0x000000560cafb040, RBP=0x0000000000000000, RSI=0x000001623299a000, RDI=0x0000000000000000
R8 =0x0000000000000000, R9 =0x0000000000000000, R10=0x00000162325d71f0, R11=0x0000016234f1fb68
R12=0x0000000000000001, R13=0x000000560cafb340, R14=0x000000560cafb280, R15=0x0000000000000000
RIP=0x000000005e0631dd, EFLAGS=0x0000000000010202

Top of Stack: (sp=0x000000560cafb040)
0x000000560cafb040:   000000560cafb360 000000560cafb360
0x000000560cafb050:   000001622e290228 000001623299a000
0x000000560cafb060:   00000007c18851e8 000000005e06358a
0x000000560cafb070:   00000007c0fa55f0 000000005dfcdb27
0x000000560cafb080:   000001622129e268 000001623299a000
0x000000560cafb090:   00000007c18851e8 0000ffff00000001
0x000000560cafb0a0:   000000560cafb0f0 000000005e1ce4eb
0x000000560cafb0b0:   000001622129e268 00000007c0fa55f0
0x000000560cafb0c0:   0000ff450de4f839 0000000000000000
0x000000560cafb0d0:   0000000000000001 000000005e1ce13a
0x000000560cafb0e0:   000000560cafb250 000001623299a000
0x000000560cafb0f0:   000001622129e268 000001623299a000
0x000000560cafb100:   000001622129e268 000001623299a000
0x000000560cafb110:   000001622129e268 000001623299a000
0x000000560cafb120:   000001623299a000 000000005e068381
0x000000560cafb130:   00000007c0fa55f0 000000005e099393 

Instructions: (pc=0x000000005e0631dd)
0x000000005e0631bd:   cc cc cc 40 53 56 57 48 81 ec 40 01 00 00 48 8b
0x000000005e0631cd:   05 26 57 6f 00 48 33 c4 48 89 84 24 10 01 00 00
0x000000005e0631dd:   8b 82 9c 00 00 00 48 8b da 48 8b b4 24 90 01 00
0x000000005e0631ed:   00 48 8b f9 c1 e8 09 48 89 74 24 38 a8 01 0f b6 


Register to memory mapping:

RAX=0x0000ff450de4e7e9 is an unknown value
RBX=0x00000007c18851e8 is pointing into metadata
RCX=0x000000560cafb1e0 is pointing into the stack for thread: 0x000001623299a000
RDX=0x0000000000000000 is an unknown value
RSP=0x000000560cafb040 is pointing into the stack for thread: 0x000001623299a000
RBP=0x0000000000000000 is an unknown value
RSI=0x000001623299a000 is a thread
RDI=0x0000000000000000 is an unknown value
R8 =0x0000000000000000 is an unknown value
R9 =0x0000000000000000 is an unknown value
R10=0x00000162325d71f0 is an unknown value
R11=0x0000016234f1fb68 is pointing into metadata
R12=0x0000000000000001 is an unknown value
R13=0x000000560cafb340 is pointing into the stack for thread: 0x000001623299a000
R14=0x000000560cafb280 is pointing into the stack for thread: 0x000001623299a000
R15=0x0000000000000000 is an unknown value


Stack: [0x000000560ca00000,0x000000560cb00000],  sp=0x000000560cafb040,  free space=1004k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  org.apache.shardingsphere.driver.executor.engine.DriverExecuteExecutor.execute(Lorg/apache/shardingsphere/infra/metadata/database/ShardingSphereDatabase;Lorg/apache/shardingsphere/infra/session/query/QueryContext;Lorg/apache/shardingsphere/infra/executor/sql/prepare/driver/DriverExecutionPrepareEngine;Lorg/apache/shardingsphere/driver/executor/callback/execute/StatementExecuteCallback;Lorg/apache/shardingsphere/driver/executor/callback/add/StatementAddCallback;Lorg/apache/shardingsphere/driver/executor/callback/replay/StatementReplayCallback;)Z+158
j  org.apache.shardingsphere.driver.executor.engine.facade.DriverExecutorFacade.execute(Lorg/apache/shardingsphere/infra/metadata/database/ShardingSphereDatabase;Lorg/apache/shardingsphere/infra/session/query/QueryContext;Lorg/apache/shardingsphere/driver/executor/callback/execute/StatementExecuteCallback;Lorg/apache/shardingsphere/driver/executor/callback/add/StatementAddCallback;Lorg/apache/shardingsphere/driver/executor/callback/replay/StatementReplayCallback;)Z+671
j  org.apache.shardingsphere.driver.jdbc.core.statement.ShardingSpherePreparedStatement.execute()Z+1360
j  sun.reflect.GeneratedMethodAccessor111.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+40
J 36446 C1 sun.reflect.DelegatingMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object; (10 bytes) @ 0x000001626b497044 [0x000001626b496fe0+0x64]
J 36445 C1 com.zeroturnaround.jrebelbase.facade.Forward.methodInvoke(Lcom/zeroturnaround/jrebelbase/h;Ljava/lang/Object;[Ljava/lang/Object;Ljava/lang/reflect/Method;)Ljava/lang/Object; (63 bytes) @ 0x000001626b4973fc [0x000001626b497360+0x9c]
J 36444 C1 java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object; (69 bytes) @ 0x000001626b4979ac [0x000001626b497820+0x18c]
j  org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(Ljava/lang/Object;Ljava/lang/reflect/Method;[Ljava/lang/Object;)Ljava/lang/Object;+132
j  com.sun.proxy.$Proxy1663.execute()Z+9
j  org.apache.ibatis.executor.statement.PreparedStatementHandler.query(Ljava/sql/Statement;Lorg/apache/ibatis/session/ResultHandler;)Ljava/util/List;+77
j  org.apache.ibatis.executor.statement.RoutingStatementHandler.query(Ljava/sql/Statement;Lorg/apache/ibatis/session/ResultHandler;)Ljava/util/List;+10
j  sun.reflect.GeneratedMethodAccessor107.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+48
J 36446 C1 sun.reflect.DelegatingMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object; (10 bytes) @ 0x000001626b497044 [0x000001626b496fe0+0x64]
J 36445 C1 com.zeroturnaround.jrebelbase.facade.Forward.methodInvoke(Lcom/zeroturnaround/jrebelbase/h;Ljava/lang/Object;[Ljava/lang/Object;Ljava/lang/reflect/Method;)Ljava/lang/Object; (63 bytes) @ 0x000001626b4973fc [0x000001626b497360+0x9c]
J 36444 C1 java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object; (69 bytes) @ 0x000001626b4979ac [0x000001626b497820+0x18c]
j  org.apache.ibatis.plugin.Invocation.proceed()Ljava/lang/Object;+16
j  com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(Lorg/apache/ibatis/plugin/Invocation;)Ljava/lang/Object;+168
j  org.apache.ibatis.plugin.Plugin.invoke(Ljava/lang/Object;Ljava/lang/reflect/Method;[Ljava/lang/Object;)Ljava/lang/Object;+60
j  com.sun.proxy.$Proxy1653.query(Ljava/sql/Statement;Lorg/apache/ibatis/session/ResultHandler;)Ljava/util/List;+20
j  org.apache.ibatis.executor.SimpleExecutor.doQuery(Lorg/apache/ibatis/mapping/MappedStatement;Ljava/lang/Object;Lorg/apache/ibatis/session/RowBounds;Lorg/apache/ibatis/session/ResultHandler;Lorg/apache/ibatis/mapping/BoundSql;)Ljava/util/List;+481
j  org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(Lorg/apache/ibatis/mapping/MappedStatement;Ljava/lang/Object;Lorg/apache/ibatis/session/RowBounds;Lorg/apache/ibatis/session/ResultHandler;Lorg/apache/ibatis/cache/CacheKey;Lorg/apache/ibatis/mapping/BoundSql;)Ljava/util/List;+20
j  org.apache.ibatis.executor.BaseExecutor.query(Lorg/apache/ibatis/mapping/MappedStatement;Ljava/lang/Object;Lorg/apache/ibatis/session/RowBounds;Lorg/apache/ibatis/session/ResultHandler;Lorg/apache/ibatis/cache/CacheKey;Lorg/apache/ibatis/mapping/BoundSql;)Ljava/util/List;+134
j  org.apache.ibatis.executor.CachingExecutor.query(Lorg/apache/ibatis/mapping/MappedStatement;Ljava/lang/Object;Lorg/apache/ibatis/session/RowBounds;Lorg/apache/ibatis/session/ResultHandler;Lorg/apache/ibatis/cache/CacheKey;Lorg/apache/ibatis/mapping/BoundSql;)Ljava/util/List;+114
j  com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.executeQuery(Lcom/tipray/dlp/mybatis/bean/BaseLogVo;Lorg/apache/ibatis/executor/Executor;Lorg/apache/ibatis/mapping/MappedStatement;Ljava/lang/Object;Lorg/apache/ibatis/mapping/BoundSql;Lorg/apache/ibatis/session/RowBounds;Lorg/apache/ibatis/session/ResultHandler;)Ljava/util/List;+62
j  com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.interceptQuery(Lorg/apache/ibatis/executor/Executor;[Ljava/lang/Object;Lorg/apache/ibatis/mapping/MappedStatement;Ljava/lang/Object;)Ljava/lang/Object;+214
j  com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(Lorg/apache/ibatis/plugin/Invocation;)Ljava/lang/Object;+82
j  org.apache.ibatis.plugin.Plugin.invoke(Ljava/lang/Object;Ljava/lang/reflect/Method;[Ljava/lang/Object;)Ljava/lang/Object;+60
j  com.sun.proxy.$Proxy1652.query(Lorg/apache/ibatis/mapping/MappedStatement;Ljava/lang/Object;Lorg/apache/ibatis/session/RowBounds;Lorg/apache/ibatis/session/ResultHandler;)Ljava/util/List;+29
j  org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(Ljava/lang/String;Ljava/lang/Object;Lorg/apache/ibatis/session/RowBounds;Lorg/apache/ibatis/session/ResultHandler;)Ljava/util/List;+38
J 32510 C1 org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(Ljava/lang/String;Ljava/lang/Object;Lorg/apache/ibatis/session/RowBounds;)Ljava/util/List; (15 bytes) @ 0x000001626aeb5bcc [0x000001626aeb5b60+0x6c]
j  org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(Ljava/lang/String;Ljava/lang/Object;)Ljava/util/List;+10
j  org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;+7
j  sun.reflect.GeneratedMethodAccessor162.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+48
J 36446 C1 sun.reflect.DelegatingMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object; (10 bytes) @ 0x000001626b497044 [0x000001626b496fe0+0x64]
J 36445 C1 com.zeroturnaround.jrebelbase.facade.Forward.methodInvoke(Lcom/zeroturnaround/jrebelbase/h;Ljava/lang/Object;[Ljava/lang/Object;Ljava/lang/reflect/Method;)Ljava/lang/Object; (63 bytes) @ 0x000001626b4973fc [0x000001626b497360+0x9c]
J 36444 C1 java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object; (69 bytes) @ 0x000001626b4979ac [0x000001626b497820+0x18c]
j  org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(Ljava/lang/Object;Ljava/lang/reflect/Method;[Ljava/lang/Object;)Ljava/lang/Object;+34
j  com.sun.proxy.$Proxy149.selectOne(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;+20
j  org.mybatis.spring.SqlSessionTemplate.selectOne(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;+10
j  com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(Lorg/apache/ibatis/session/SqlSession;[Ljava/lang/Object;)Ljava/lang/Object;+291
j  com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(Ljava/lang/Object;Ljava/lang/reflect/Method;[Ljava/lang/Object;Lorg/apache/ibatis/session/SqlSession;)Ljava/lang/Object;+11
j  com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(Ljava/lang/Object;Ljava/lang/reflect/Method;[Ljava/lang/Object;)Ljava/lang/Object;+35
j  com.sun.proxy.$Proxy894.count(Lcom/tipray/dlp/bean/vo/AuditLogSceneQueryVO;)Ljava/lang/Integer;+20
j  sun.reflect.GeneratedMethodAccessor677.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+40
J 36446 C1 sun.reflect.DelegatingMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object; (10 bytes) @ 0x000001626b497044 [0x000001626b496fe0+0x64]
J 36445 C1 com.zeroturnaround.jrebelbase.facade.Forward.methodInvoke(Lcom/zeroturnaround/jrebelbase/h;Ljava/lang/Object;[Ljava/lang/Object;Ljava/lang/reflect/Method;)Ljava/lang/Object; (63 bytes) @ 0x000001626b4973fc [0x000001626b497360+0x9c]
J 36444 C1 java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object; (69 bytes) @ 0x000001626b4979ac [0x000001626b497820+0x18c]
j  org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(Ljava/lang/Object;Ljava/lang/reflect/Method;[Ljava/lang/Object;)Ljava/lang/Object;+7
j  org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint()Ljava/lang/Object;+16
j  org.springframework.aop.framework.ReflectiveMethodInvocation.proceed()Ljava/lang/Object;+23
j  org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(Lorg/aopalliance/intercept/MethodInvocation;)Ljava/lang/Object;+5
j  org.springframework.aop.framework.ReflectiveMethodInvocation.proceed()Ljava/lang/Object;+133
j  org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(Lorg/aopalliance/intercept/MethodInvocation;)Ljava/lang/Object;+5
j  org.springframework.aop.framework.ReflectiveMethodInvocation.proceed()Ljava/lang/Object;+114
j  org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(Lorg/aopalliance/intercept/MethodInvocation;)Ljava/lang/Object;+5
j  org.springframework.aop.framework.ReflectiveMethodInvocation.proceed()Ljava/lang/Object;+114
j  org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(Lorg/aopalliance/intercept/MethodInvocation;)Ljava/lang/Object;+5
j  org.springframework.aop.framework.ReflectiveMethodInvocation.proceed()Ljava/lang/Object;+114
j  org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(Lorg/aopalliance/intercept/MethodInvocation;)Ljava/lang/Object;+32
j  org.springframework.aop.framework.ReflectiveMethodInvocation.proceed()Ljava/lang/Object;+114
j  org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(Lorg/aopalliance/intercept/MethodInvocation;)Ljava/lang/Object;+21
j  org.springframework.aop.framework.ReflectiveMethodInvocation.proceed()Ljava/lang/Object;+133
j  org.springframework.aop.framework.JdkDynamicAopProxy.invoke(Ljava/lang/Object;Ljava/lang/reflect/Method;[Ljava/lang/Object;)Ljava/lang/Object;+398
j  com.sun.proxy.$Proxy895.count(Lcom/tipray/dlp/bean/vo/AuditLogSceneQueryVO;)Ljava/lang/Integer;+20
j  com.tipray.dlp.service.impl.AuditLogQueryServiceImpl.count(Lcom/tipray/dlp/bean/vo/AuditLogSceneQueryVO;)Ljava/lang/Integer;+9
j  com.tipray.dlp.service.impl.AuditLogQueryServiceImpl.count(Lcom/tipray/dlp/bean/Scene$Audit;Lcom/tipray/dlp/bean/vo/AuditLogSceneQueryVO;)Ljava/lang/Integer;+13
j  com.tipray.dlp.controller.websocket.AuditLogQueryWebSocketApi.executeQuery(Lcom/tipray/dlp/bean/vo/AuditLogSceneQueryVO;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/util/List;Ljava/util/Map;Ljava/lang/String;Ljava/lang/Long;Ljava/util/concurrent/atomic/AtomicBoolean;)V+181
j  com.tipray.dlp.controller.websocket.AuditLogQueryWebSocketApi.lambda$startNewQuery$0(Lcom/tipray/dlp/bean/vo/AuditLogSceneQueryVO;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/util/List;Ljava/util/Map;Ljava/lang/String;Ljava/lang/Long;Ljava/util/concurrent/atomic/AtomicBoolean;)V+20
j  com.tipray.dlp.controller.websocket.AuditLogQueryWebSocketApi$$Lambda$2508.run()V+48
j  java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture()V+36
j  java.util.concurrent.CompletableFuture$AsyncRun.run()V+5
j  java.util.concurrent.CompletableFuture$AsyncRun.exec()Z+1
j  java.util.concurrent.ForkJoinTask.doExec$$$capture()I+10
j  java.util.concurrent.ForkJoinTask.doExec()I+5
j  java.util.concurrent.ForkJoinPool$WorkQueue.runTask(Ljava/util/concurrent/ForkJoinTask;)V+21
j  java.util.concurrent.ForkJoinPool.runWorker(Ljava/util/concurrent/ForkJoinPool$WorkQueue;)V+35
j  java.util.concurrent.ForkJoinWorkerThread.run()V+28
v  ~StubRoutines::call_stub

---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
  0x00000162329af000 JavaThread "clientOutboundChannel-56" [_thread_blocked, id=32996, stack(0x000000560cc00000,0x000000560cd00000)]
  0x00000162329a1000 JavaThread "clientInboundChannel-42" [_thread_blocked, id=31876, stack(0x000000560c900000,0x000000560ca00000)]
  0x00000162329a7800 JavaThread "clientInboundChannel-41" [_thread_blocked, id=35452, stack(0x000000560b000000,0x000000560b100000)]
  0x00000162329a2800 JavaThread "clientInboundChannel-40" [_thread_blocked, id=30872, stack(0x000000560af00000,0x000000560b000000)]
  0x00000162329a8000 JavaThread "clientInboundChannel-39" [_thread_blocked, id=31176, stack(0x0000005606f00000,0x0000005607000000)]
  0x000001623299c800 JavaThread "clientInboundChannel-38" [_thread_blocked, id=31580, stack(0x0000005606400000,0x0000005606500000)]
  0x00000162329a0000 JavaThread "clientInboundChannel-37" [_thread_blocked, id=24912, stack(0x0000005605d00000,0x0000005605e00000)]
  0x00000162329a2000 JavaThread "clientOutboundChannel-55" [_thread_blocked, id=25280, stack(0x0000005601300000,0x0000005601400000)]
  0x000001623299e000 JavaThread "clientOutboundChannel-54" [_thread_blocked, id=35224, stack(0x0000005605000000,0x0000005605100000)]
=>0x000001623299a000 JavaThread "ForkJoinPool.commonPool-worker-4" daemon [_thread_in_vm, id=31676, stack(0x000000560ca00000,0x000000560cb00000)]
  0x00000162329ab000 JavaThread "ShardingSphere-2" daemon [_thread_blocked, id=28360, stack(0x000000560cb00000,0x000000560cc00000)]
  0x00000162329c7800 JavaThread "MessageBroker-16" [_thread_blocked, id=16948, stack(0x0000005610100000,0x0000005610200000)]
  0x00000162329c6000 JavaThread "MessageBroker-15" [_thread_blocked, id=34756, stack(0x0000005610000000,0x0000005610100000)]
  0x00000162329c6800 JavaThread "MessageBroker-14" [_thread_blocked, id=34780, stack(0x000000560fe00000,0x000000560ff00000)]
  0x00000162329bf000 JavaThread "MessageBroker-13" [_thread_blocked, id=32656, stack(0x000000560fd00000,0x000000560fe00000)]
  0x00000162329bf800 JavaThread "MessageBroker-12" [_thread_blocked, id=23680, stack(0x000000560fc00000,0x000000560fd00000)]
  0x00000162329c2000 JavaThread "MessageBroker-11" [_thread_blocked, id=24232, stack(0x000000560fa00000,0x000000560fb00000)]
  0x00000162329b8800 JavaThread "MessageBroker-10" [_thread_blocked, id=32852, stack(0x000000560f200000,0x000000560f300000)]
  0x00000162329b5800 JavaThread "arthas-command-execute" daemon [_thread_blocked, id=30172, stack(0x000000560c600000,0x000000560c700000)]
  0x00000162329bb800 JavaThread "MessageBroker-9" [_thread_blocked, id=17292, stack(0x000000560ee00000,0x000000560ef00000)]
  0x00000162329b4000 JavaThread "server-log-pool-t-5" [_thread_blocked, id=34728, stack(0x000000560ea00000,0x000000560eb00000)]
  0x00000162329b7800 JavaThread "JNA Cleaner" daemon [_thread_blocked, id=31472, stack(0x000000560ed00000,0x000000560ee00000)]
  0x00000162329ac000 JavaThread "modules-pool-t-17" [_thread_blocked, id=24624, stack(0x000000560e000000,0x000000560e100000)]
  0x0000016232997000 JavaThread "MessageBroker-8" [_thread_blocked, id=35196, stack(0x000000560b500000,0x000000560b600000)]
  0x00000162329b9800 JavaThread "MessageBroker-7" [_thread_blocked, id=30920, stack(0x000000560ec00000,0x000000560ed00000)]
  0x0000016232996000 JavaThread "MessageBroker-6" [_thread_blocked, id=33608, stack(0x000000560d700000,0x000000560d800000)]
  0x000001622ce09800 JavaThread "WebSocket background processing" daemon [_thread_blocked, id=34848, stack(0x000000560c800000,0x000000560c900000)]
  0x000001622cdf8000 JavaThread "MessageBroker-5" [_thread_blocked, id=25948, stack(0x000000560c300000,0x000000560c400000)]
  0x000001622ce08000 JavaThread "pool-240-thread-1" [_thread_blocked, id=34980, stack(0x000000560d100000,0x000000560d200000)]
  0x000001622ce0a000 JavaThread "RMI Reaper" [_thread_blocked, id=34028, stack(0x000000560d000000,0x000000560d100000)]
  0x000001622ce07000 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=9400, stack(0x000000560cf00000,0x000000560d000000)]
  0x000001622ce06000 JavaThread "RMI TCP Accept-6666" daemon [_thread_in_native, id=7400, stack(0x000000560ce00000,0x000000560cf00000)]
  0x000001622cdf7800 JavaThread "MessageBroker-4" [_thread_blocked, id=14872, stack(0x000000560c700000,0x000000560c800000)]
  0x000001622cdfd000 JavaThread "GC Daemon" daemon [_thread_blocked, id=32920, stack(0x000000560c400000,0x000000560c500000)]
  0x000001622cdf9000 JavaThread "RMI RenewClean-[127.0.0.1:63320]" daemon [_thread_blocked, id=24324, stack(0x000000560c500000,0x000000560c600000)]
  0x000001622cdfd800 JavaThread "DestroyJavaVM" [_thread_blocked, id=31180, stack(0x0000005601800000,0x0000005601900000)]
  0x000001622cdfb000 JavaThread "scheduled-pool-t-10" [_thread_blocked, id=35280, stack(0x000000560c200000,0x000000560c300000)]
  0x000001622cdf5000 JavaThread "scheduled-pool-t-9" [_thread_blocked, id=35236, stack(0x000000560c100000,0x000000560c200000)]
  0x000001622cdf9800 JavaThread "scheduled-pool-t-8" [_thread_blocked, id=17300, stack(0x000000560c000000,0x000000560c100000)]
  0x000001622cdf5800 JavaThread "scheduled-pool-t-7" [_thread_blocked, id=15208, stack(0x000000560bf00000,0x000000560c000000)]
  0x000001622cdf6800 JavaThread "scheduled-pool-t-6" [_thread_blocked, id=32796, stack(0x000000560be00000,0x000000560bf00000)]
  0x000001622cdfc000 JavaThread "scheduled-pool-t-5" [_thread_blocked, id=26128, stack(0x000000560bd00000,0x000000560be00000)]
  0x000001622cdee800 JavaThread "scheduled-pool-t-4" [_thread_blocked, id=35124, stack(0x000000560bc00000,0x000000560bd00000)]
  0x000001622cded000 JavaThread "scheduled-pool-t-3" [_thread_blocked, id=20996, stack(0x000000560bb00000,0x000000560bc00000)]
  0x000001622cdec800 JavaThread "scheduled-pool-t-2" [_thread_blocked, id=32132, stack(0x000000560ba00000,0x000000560bb00000)]
  0x000001622cdee000 JavaThread "scheduled-pool-t-1" [_thread_blocked, id=8748, stack(0x000000560b900000,0x000000560ba00000)]
  0x000001622cdf2800 JavaThread "modules-pool-t-16" [_thread_blocked, id=34544, stack(0x000000560b800000,0x000000560b900000)]
  0x000001622cdf2000 JavaThread "MessageBroker-2" [_thread_blocked, id=25660, stack(0x000000560b700000,0x000000560b800000)]
  0x000001622cdef800 JavaThread "MessageBroker-3" [_thread_blocked, id=33444, stack(0x000000560b600000,0x000000560b700000)]
  0x000001622cdeb800 JavaThread "server-log-pool-t-4" [_thread_blocked, id=33020, stack(0x000000560b100000,0x000000560b200000)]
  0x000001622cde7800 JavaThread "modules-pool-t-15" [_thread_blocked, id=25536, stack(0x000000560b400000,0x000000560b500000)]
  0x000001622cde3800 JavaThread "soft-pool-t-2" [_thread_blocked, id=32884, stack(0x000000560b300000,0x000000560b400000)]
  0x000001622cde6000 JavaThread "pool-198-thread-1" [_thread_blocked, id=34472, stack(0x000000560b200000,0x000000560b300000)]
  0x000001622cdd8000 JavaThread "ThreadPoolTaskScheduler-4" [_thread_blocked, id=5044, stack(0x000000560ae00000,0x000000560af00000)]
  0x000001622cdcf800 JavaThread "arthas-TermServer-1-9" daemon [_thread_in_native, id=34904, stack(0x000000560ad00000,0x000000560ae00000)]
  0x000001622cdd6800 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-11" daemon [_thread_in_native, id=30916, stack(0x000000560ac00000,0x000000560ad00000)]
  0x000001622cdd5000 JavaThread "ThreadPoolTaskScheduler-3" [_thread_blocked, id=32000, stack(0x000000560ab00000,0x000000560ac00000)]
  0x000001622cdd4000 JavaThread "modules-pool-t-14" [_thread_blocked, id=35520, stack(0x000000560aa00000,0x000000560ab00000)]
  0x000001622cdd3000 JavaThread "ThreadPoolTaskScheduler-2" [_thread_blocked, id=33420, stack(0x000000560a900000,0x000000560aa00000)]
  0x000001622cdd5800 JavaThread "ThreadPoolTaskScheduler-1" [_thread_blocked, id=34772, stack(0x000000560a800000,0x000000560a900000)]
  0x000001622cdd1800 JavaThread "arthas-TermServer-1-8" daemon [_thread_in_native, id=32276, stack(0x000000560a700000,0x000000560a800000)]
  0x000001622cdd1000 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-10" daemon [_thread_in_native, id=33840, stack(0x000000560a600000,0x000000560a700000)]
  0x000001622cdd2800 JavaThread "arthas-TermServer-1-7" daemon [_thread_in_native, id=32596, stack(0x000000560a500000,0x000000560a600000)]
  0x000001622cdd0000 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-9" daemon [_thread_in_native, id=34216, stack(0x000000560a400000,0x000000560a500000)]
  0x000001622ad63000 JavaThread "arthas-TermServer-1-4" daemon [_thread_in_native, id=34076, stack(0x000000560a300000,0x000000560a400000)]
  0x000001622ad5b000 JavaThread "arthas-TermServer-1-6" daemon [_thread_in_native, id=31008, stack(0x000000560a200000,0x000000560a300000)]
  0x000001622ad45000 JavaThread "arthas-TermServer-1-5" daemon [_thread_in_native, id=14216, stack(0x000000560a100000,0x000000560a200000)]
  0x000001622aed4000 JavaThread "arthas-TermServer-1-3" daemon [_thread_in_native, id=6888, stack(0x000000560a000000,0x000000560a100000)]
  0x000001622aeda800 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-8" daemon [_thread_in_native, id=22196, stack(0x0000005609f00000,0x000000560a000000)]
  0x000001622aed9800 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-7" daemon [_thread_in_native, id=14320, stack(0x0000005609e00000,0x0000005609f00000)]
  0x000001622aed7800 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-6" daemon [_thread_in_native, id=34124, stack(0x0000005609d00000,0x0000005609e00000)]
  0x000001622aed5000 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-5" daemon [_thread_in_native, id=31804, stack(0x0000005606e00000,0x0000005606f00000)]
  0x000001622aed9000 JavaThread "arthas-TermServer-1-2" daemon [_thread_in_native, id=30452, stack(0x0000005609c00000,0x0000005609d00000)]
  0x000001622aeca800 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-4" daemon [_thread_in_native, id=32032, stack(0x0000005609b00000,0x0000005609c00000)]
  0x000001622aeca000 JavaThread "arthas-TermServer-1-1" daemon [_thread_in_native, id=25396, stack(0x0000005609a00000,0x0000005609b00000)]
  0x000001622aecf800 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-3" daemon [_thread_in_native, id=33212, stack(0x0000005609900000,0x0000005609a00000)]
  0x000001622aed1000 JavaThread "modules-pool-t-13" [_thread_blocked, id=35068, stack(0x0000005609800000,0x0000005609900000)]
  0x000001622aed0000 JavaThread "modules-pool-t-12" [_thread_blocked, id=31000, stack(0x0000005609700000,0x0000005609800000)]
  0x000001622aece800 JavaThread "modules-pool-t-11" [_thread_blocked, id=30792, stack(0x0000005609600000,0x0000005609700000)]
  0x000001622aece000 JavaThread "modules-pool-t-10" [_thread_blocked, id=33640, stack(0x0000005609500000,0x0000005609600000)]
  0x000001622aec8800 JavaThread "modules-pool-t-9" [_thread_blocked, id=34960, stack(0x0000005609400000,0x0000005609500000)]
  0x000001622aecd000 JavaThread "modules-pool-t-8" [_thread_blocked, id=28684, stack(0x0000005609300000,0x0000005609400000)]
  0x000001622aebe000 JavaThread "modules-pool-t-7" [_thread_blocked, id=35272, stack(0x0000005609200000,0x0000005609300000)]
  0x000001622aec3000 JavaThread "modules-pool-t-6" [_thread_blocked, id=35460, stack(0x0000005609100000,0x0000005609200000)]
  0x000001622aec2000 JavaThread "modules-pool-t-5" [_thread_blocked, id=34888, stack(0x0000005609000000,0x0000005609100000)]
  0x000001622aebc800 JavaThread "modules-pool-t-4" [_thread_blocked, id=34080, stack(0x0000005608f00000,0x0000005609000000)]
  0x000001622aec1800 JavaThread "modules-pool-t-3" [_thread_blocked, id=1776, stack(0x0000005608e00000,0x0000005608f00000)]
  0x000001622aec0800 JavaThread "Thread-397" daemon [_thread_in_native, id=22012, stack(0x0000005608d00000,0x0000005608e00000)]
  0x000001622aebf800 JavaThread "server-log-pool-t-3" [_thread_blocked, id=33332, stack(0x0000005608c00000,0x0000005608d00000)]
  0x000001622aebc000 JavaThread "server-log-pool-t-2" [_thread_blocked, id=33472, stack(0x0000005608b00000,0x0000005608c00000)]
  0x000001622aebf000 JavaThread "server-log-pool-t-1" [_thread_blocked, id=33172, stack(0x0000005608a00000,0x0000005608b00000)]
  0x000001622ad61800 JavaThread "Thread-396" daemon [_thread_blocked, id=35052, stack(0x0000005608900000,0x0000005608a00000)]
  0x000001622ad5c800 JavaThread "modules-pool-t-2" [_thread_blocked, id=31488, stack(0x0000005608800000,0x0000005608900000)]
  0x000001622ad60800 JavaThread "modules-pool-t-1" [_thread_blocked, id=23948, stack(0x0000005608700000,0x0000005608800000)]
  0x000001622ad5f800 JavaThread "Thread-393" daemon [_thread_in_native, id=15564, stack(0x0000005608600000,0x0000005608700000)]
  0x000001622ad5f000 JavaThread "sync-scheduler-pool-t-1" [_thread_blocked, id=35264, stack(0x0000005608500000,0x0000005608600000)]
  0x000001622ad5d800 JavaThread "http-nio-28080-Acceptor" daemon [_thread_in_native, id=33376, stack(0x0000005608400000,0x0000005608500000)]
  0x000001622ad5e000 JavaThread "http-nio-28080-Poller" daemon [_thread_in_native, id=17304, stack(0x0000005608300000,0x0000005608400000)]
  0x000001622ad55800 JavaThread "http-nio-28080-exec-10" daemon [_thread_blocked, id=34500, stack(0x0000005608200000,0x0000005608300000)]
  0x000001622ad5a000 JavaThread "http-nio-28080-exec-9" daemon [_thread_blocked, id=35836, stack(0x0000005608100000,0x0000005608200000)]
  0x000001622ad52800 JavaThread "http-nio-28080-exec-8" daemon [_thread_blocked, id=33276, stack(0x0000005608000000,0x0000005608100000)]
  0x000001622ad54800 JavaThread "http-nio-28080-exec-7" daemon [_thread_blocked, id=28156, stack(0x0000005607f00000,0x0000005608000000)]
  0x000001622ad59800 JavaThread "http-nio-28080-exec-6" daemon [_thread_blocked, id=33928, stack(0x0000005607e00000,0x0000005607f00000)]
  0x000001622ad54000 JavaThread "http-nio-28080-exec-5" daemon [_thread_blocked, id=2540, stack(0x0000005607d00000,0x0000005607e00000)]
  0x000001622ad51800 JavaThread "http-nio-28080-exec-4" daemon [_thread_blocked, id=29396, stack(0x0000005607c00000,0x0000005607d00000)]
  0x000001622ad53000 JavaThread "http-nio-28080-exec-3" daemon [_thread_blocked, id=30728, stack(0x0000005607b00000,0x0000005607c00000)]
  0x000001622ad58800 JavaThread "http-nio-28080-exec-2" daemon [_thread_blocked, id=27552, stack(0x0000005607a00000,0x0000005607b00000)]
  0x000001622ad58000 JavaThread "http-nio-28080-exec-1" daemon [_thread_blocked, id=34316, stack(0x0000005607900000,0x0000005607a00000)]
  0x000001622ad49000 JavaThread "MessageBroker-1" [_thread_blocked, id=32692, stack(0x0000005607800000,0x0000005607900000)]
  0x000001622ad50000 JavaThread "arthas-session-manager" daemon [_thread_blocked, id=23888, stack(0x0000005607700000,0x0000005607800000)]
  0x000001622ad4a800 JavaThread "arthas-shell-server" daemon [_thread_blocked, id=33384, stack(0x0000005607600000,0x0000005607700000)]
  0x000001622ad4e800 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-2" daemon [_thread_in_native, id=35092, stack(0x0000005607500000,0x0000005607600000)]
  0x000001622ad48000 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-1" daemon [_thread_in_native, id=27672, stack(0x0000005607400000,0x0000005607500000)]
  0x000001622ad46800 JavaThread "arthas-NettyHttpTelnetBootstrap-3-1" daemon [_thread_in_native, id=22132, stack(0x0000005607200000,0x0000005607300000)]
  0x00000162215fe800 JavaThread "arthas-timer" daemon [_thread_blocked, id=7316, stack(0x0000005605e00000,0x0000005605f00000)]
  0x0000016221604800 JavaThread "idle-connection-evictor-1" daemon [_thread_blocked, id=32712, stack(0x0000005605c00000,0x0000005605d00000)]
  0x00000162215f6000 JavaThread "idle-connection-evictor-1" daemon [_thread_blocked, id=34464, stack(0x0000005603d00000,0x0000005603e00000)]
  0x00000162202b8000 JavaThread "idle-connection-evictor-1" daemon [_thread_blocked, id=11856, stack(0x0000005607300000,0x0000005607400000)]
  0x00000162202b7000 JavaThread "idle-connection-evictor-1" daemon [_thread_blocked, id=34632, stack(0x0000005604f00000,0x0000005605000000)]
  0x000001621c44e800 JavaThread "HikariPool-JdbcPersistRepo housekeeper" daemon [_thread_blocked, id=22560, stack(0x0000005607100000,0x0000005607200000)]
  0x000001621c44b800 JavaThread "pool-5-thread-1" [_thread_blocked, id=34424, stack(0x0000005601400000,0x0000005601500000)]
  0x000001621c446800 JavaThread "HikariPool-ds_9004 housekeeper" daemon [_thread_blocked, id=34392, stack(0x0000005601200000,0x0000005601300000)]
  0x000001621c446000 JavaThread "HikariPool-ds_9001 housekeeper" daemon [_thread_blocked, id=31732, stack(0x0000005606d00000,0x0000005606e00000)]
  0x000001621be53000 JavaThread "websocket-pool-t-4" [_thread_blocked, id=35072, stack(0x0000005607000000,0x0000005607100000)]
  0x000001621a630000 JavaThread "HikariPool-master housekeeper" daemon [_thread_blocked, id=34444, stack(0x0000005606c00000,0x0000005606d00000)]
  0x000001621a628800 JavaThread "mysql-cj-abandoned-connection-cleanup" daemon [_thread_blocked, id=32696, stack(0x0000005606b00000,0x0000005606c00000)]
  0x000001621a627800 JavaThread "Pure-Timer-1" [_thread_blocked, id=33896, stack(0x0000005606a00000,0x0000005606b00000)]
  0x000001621a028800 JavaThread "mybatis-pool-t-1" [_thread_in_native, id=35428, stack(0x0000005606900000,0x0000005606a00000)]
  0x00000162180ec800 JavaThread "websocket-pool-t-3" [_thread_blocked, id=18252, stack(0x0000005606800000,0x0000005606900000)]
  0x000001621a027000 JavaThread "socket-pool-t-3" [_thread_in_native, id=18312, stack(0x0000005606700000,0x0000005606800000)]
  0x000001621a027800 JavaThread "socket-pool-t-2" [_thread_blocked, id=31456, stack(0x0000005606600000,0x0000005606700000)]
  0x000001621a01f000 JavaThread "socket-pool-t-1" [_thread_blocked, id=2552, stack(0x0000005606500000,0x0000005606600000)]
  0x000001621a025800 JavaThread "container-0" [_thread_blocked, id=35416, stack(0x0000005606300000,0x0000005606400000)]
  0x000001621a020800 JavaThread "Catalina-utility-2" [_thread_blocked, id=24320, stack(0x0000005606200000,0x0000005606300000)]
  0x000001621a01e800 JavaThread "Catalina-utility-1" [_thread_blocked, id=33632, stack(0x0000005606100000,0x0000005606200000)]
  0x000001621a024000 JavaThread "websocket-pool-t-2" [_thread_blocked, id=29620, stack(0x0000005606000000,0x0000005606100000)]
  0x000001621a023000 JavaThread "websocket-pool-t-1" [_thread_blocked, id=32756, stack(0x0000005605f00000,0x0000005606000000)]
  0x0000016212cb9800 JavaThread "AsyncAppender-Worker-ASYNC_DOWNLOAD_FILE" daemon [_thread_blocked, id=10988, stack(0x0000005605b00000,0x0000005605c00000)]
  0x0000016212cb8800 JavaThread "AsyncAppender-Worker-ASYNC_FTP_FILE" daemon [_thread_blocked, id=32328, stack(0x0000005605a00000,0x0000005605b00000)]
  0x0000016212cb6800 JavaThread "AsyncAppender-Worker-ASYNC_DB_EDIT_FILE" daemon [_thread_blocked, id=33756, stack(0x0000005605900000,0x0000005605a00000)]
  0x0000016212cb5800 JavaThread "AsyncAppender-Worker-ASYNC_DB_FILE" daemon [_thread_blocked, id=33008, stack(0x0000005605800000,0x0000005605900000)]
  0x0000016212cb8000 JavaThread "AsyncAppender-Worker-ASYNC_VNC_FILE" daemon [_thread_blocked, id=27588, stack(0x0000005605700000,0x0000005605800000)]
  0x0000016212cb0000 JavaThread "AsyncAppender-Worker-ASYNC_WEBSOCKET_FILE" daemon [_thread_blocked, id=28708, stack(0x0000005605500000,0x0000005605600000)]
  0x0000016212cb4000 JavaThread "AsyncAppender-Worker-ASYNC_SOCKET_FILE" daemon [_thread_blocked, id=31964, stack(0x0000005605400000,0x0000005605500000)]
  0x0000016212cb2800 JavaThread "AsyncAppender-Worker-ASYNC_HTTP_FILE" daemon [_thread_blocked, id=33068, stack(0x0000005605300000,0x0000005605400000)]
  0x0000016212cb1000 JavaThread "AsyncAppender-Worker-ASYNC_ERROR_FILE" daemon [_thread_blocked, id=24508, stack(0x0000005605200000,0x0000005605300000)]
  0x0000016212cb7000 JavaThread "AsyncAppender-Worker-ASYNC_INFO_FILE" daemon [_thread_blocked, id=35744, stack(0x0000005605100000,0x0000005605200000)]
  0x0000016212cae800 JavaThread "AsyncAppender-Worker-ASYNC_stdout" daemon [_thread_blocked, id=33544, stack(0x0000005604e00000,0x0000005604f00000)]
  0x0000016211496800 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=23092, stack(0x0000005605600000,0x0000005605700000)]
  0x0000016211493000 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=35800, stack(0x0000005604c00000,0x0000005604d00000)]
  0x000001627e89e800 JavaThread "Service Thread" daemon [_thread_blocked, id=34760, stack(0x0000005604b00000,0x0000005604c00000)]
  0x000001627e89f800 JavaThread "C1 CompilerThread11" daemon [_thread_blocked, id=33684, stack(0x0000005604a00000,0x0000005604b00000)]
  0x000001627e89e000 JavaThread "C1 CompilerThread10" daemon [_thread_blocked, id=11068, stack(0x0000005604900000,0x0000005604a00000)]
  0x000001627e89d000 JavaThread "C1 CompilerThread9" daemon [_thread_blocked, id=35232, stack(0x0000005604800000,0x0000005604900000)]
  0x000001627e8a2800 JavaThread "C1 CompilerThread8" daemon [_thread_blocked, id=35540, stack(0x0000005604700000,0x0000005604800000)]
  0x000001627e8a1000 JavaThread "C2 CompilerThread7" daemon [_thread_blocked, id=30756, stack(0x0000005604600000,0x0000005604700000)]
  0x000001627e8a0000 JavaThread "C2 CompilerThread6" daemon [_thread_blocked, id=32004, stack(0x0000005604500000,0x0000005604600000)]
  0x000001627e895000 JavaThread "C2 CompilerThread5" daemon [_thread_blocked, id=31932, stack(0x0000005604400000,0x0000005604500000)]
  0x000001627e89c800 JavaThread "C2 CompilerThread4" daemon [_thread_blocked, id=35832, stack(0x0000005604300000,0x0000005604400000)]
  0x000001627e898800 JavaThread "C2 CompilerThread3" daemon [_thread_blocked, id=32868, stack(0x0000005604200000,0x0000005604300000)]
  0x000001627e894800 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=34884, stack(0x0000005604100000,0x0000005604200000)]
  0x000001627e89b800 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=35820, stack(0x0000005604000000,0x0000005604100000)]
  0x000001627e896000 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=34420, stack(0x0000005603f00000,0x0000005604000000)]
  0x000001627e897000 JavaThread "rebel-messaging-executor-61" daemon [_thread_blocked, id=35432, stack(0x0000005603e00000,0x0000005603f00000)]
  0x000001627e893800 JavaThread "rebel-build-info" daemon [_thread_in_native, id=34836, stack(0x0000005603700000,0x0000005603800000)]
  0x000001627e89a800 JavaThread "rebel-change-detector-thread" daemon [_thread_blocked, id=35792, stack(0x0000005603600000,0x0000005603700000)]
  0x000001627e89a000 JavaThread "rebel-debugger-thread" daemon [_thread_blocked, id=34560, stack(0x0000005603500000,0x0000005603600000)]
  0x000001627e899000 JavaThread "rebel-debugger-attach-notifier" daemon [_thread_blocked, id=35372, stack(0x0000005603400000,0x0000005603500000)]
  0x000001627e5a2000 JavaThread "rebel-heartbeat-thread" daemon [_thread_blocked, id=24524, stack(0x0000005603300000,0x0000005603400000)]
  0x000001627e5a1800 JavaThread "rebel-redeploy-thread" daemon [_thread_blocked, id=34320, stack(0x0000005603200000,0x0000005603300000)]
  0x000001627e5a7000 JavaThread "rebel-leaseManager-1" daemon [_thread_blocked, id=33904, stack(0x0000005603100000,0x0000005603200000)]
  0x000001627e5a4800 JavaThread "rebel-IDENotificationsImpl-PostCycle" daemon [_thread_blocked, id=6824, stack(0x0000005602f00000,0x0000005603000000)]
  0x000001627e5a3000 JavaThread "rebel-weak-reaper" daemon [_thread_blocked, id=21816, stack(0x0000005602e00000,0x0000005602f00000)]
  0x000001627e5a6000 JavaThread "rebel-fsnotify-OutputReader" daemon [_thread_in_native, id=23372, stack(0x0000005603c00000,0x0000005603d00000)]
  0x000001627e5a5800 JavaThread "rebel-fsnotify-OutputReader" daemon [_thread_in_native, id=35668, stack(0x0000005603b00000,0x0000005603c00000)]
  0x000001627e5a4000 JavaThread "rebel-fsnotify-ShutdownOnTermination" daemon [_thread_in_native, id=35564, stack(0x0000005603800000,0x0000005603900000)]
  0x000001627e5a0800 JavaThread "rebel-CacheKeepAlive" daemon [_thread_blocked, id=35692, stack(0x0000005603a00000,0x0000005603b00000)]
  0x000001627e5a0000 JavaThread "rebel-logger" daemon [_thread_blocked, id=34300, stack(0x0000005603900000,0x0000005603a00000)]
  0x000001627bbc2800 JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=29452, stack(0x0000005602d00000,0x0000005602e00000)]
  0x000001627bbbd800 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=18072, stack(0x0000005602c00000,0x0000005602d00000)]
  0x000001627bbba800 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=31924, stack(0x0000005602b00000,0x0000005602c00000)]
  0x000001627bb9b800 JavaThread "Attach Listener" daemon [_thread_blocked, id=29608, stack(0x0000005602a00000,0x0000005602b00000)]
  0x000001627bb73800 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=23444, stack(0x0000005602900000,0x0000005602a00000)]
  0x0000016267718000 JavaThread "Finalizer" daemon [_thread_blocked, id=30424, stack(0x0000005602800000,0x0000005602900000)]
  0x0000016267711000 JavaThread "Reference Handler" daemon [_thread_blocked, id=33596, stack(0x0000005602700000,0x0000005602800000)]

Other Threads:
  0x000001627bb13800 VMThread [stack: 0x0000005602600000,0x0000005602700000] [id=2172]
  0x0000016211521800 WatcherThread [stack: 0x0000005604d00000,0x0000005604e00000] [id=35104]

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

heap address: 0x00000005c4000000, size: 8128 MB, Compressed Oops mode: Zero based, Oop shift amount: 3
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x00000007c0000000

Heap:
 PSYoungGen      total 2266624K, used 775764K [0x0000000716b00000, 0x00000007bdc00000, 0x00000007c0000000)
  eden space 1810432K, 39% used [0x0000000716b00000,0x0000000741d01530,0x0000000785300000)
  from space 456192K, 15% used [0x00000007a1e80000,0x00000007a6213b80,0x00000007bdc00000)
  to   space 463360K, 0% used [0x0000000785300000,0x0000000785300000,0x00000007a1780000)
 ParOldGen       total 1536000K, used 719638K [0x00000005c4000000, 0x0000000621c00000, 0x0000000716b00000)
  object space 1536000K, 46% used [0x00000005c4000000,0x00000005efec59c8,0x0000000621c00000)
 Metaspace       used 308675K, capacity 322288K, committed 322456K, reserved 1343488K
  class space    used 25679K, capacity 27795K, committed 27824K, reserved 1048576K

Card table byte_map: [0x0000016276af0000,0x0000016277ae0000] byte_map_base: 0x0000016273cd0000

Marking Bits: (ParMarkBitMap*) 0x000000005e7a0830
 Begin Bits: [0x0000016200000000, 0x0000016207f00000)
 End Bits:   [0x0000016207f00000, 0x000001620fe00000)

Polling page: 0x0000016263bf0000

CodeCache: size=245760Kb used=63089Kb max_used=63089Kb free=182670Kb
 bounds [0x0000016267730000, 0x000001626b4e0000, 0x0000016276730000]
 total_blobs=36871 nmethods=35850 adapters=934
 compilation: enabled

Compilation events (10 events):
Event: 973.174 Thread 0x000001627e89d000 36550   !   1       org.springframework.aop.aspectj.AspectJExpressionPointcut::_jr$ldcProxyMethodInvocation4532d53309o192 (15 bytes)
Event: 973.174 Thread 0x000001627e89d000 nmethod 36550 0x000001626b4cbc90 code [0x000001626b4cbde0, 0x000001626b4cbef8]
Event: 973.174 Thread 0x000001627e89d000 36547       1       com.alibaba.ttl.TransmittableThreadLocal$1::childValue (13 bytes)
Event: 973.175 Thread 0x000001627e8a2800 36551       1       com.zaxxer.hikari.pool.PoolBase::_jr$ig$isUseJdbc4Validation (8 bytes)
Event: 973.175 Thread 0x000001627e8a2800 nmethod 36551 0x000001626b4cbf50 code [0x000001626b4cc0a0, 0x000001626b4cc1d8]
Event: 973.177 Thread 0x000001627e89f800 nmethod 36543 0x000001626b4cc290 code [0x000001626b4cc520, 0x000001626b4ccfb0]
Event: 973.177 Thread 0x000001627e89d000 nmethod 36547 0x000001626b4cdc90 code [0x000001626b4cde40, 0x000001626b4ce0b0]
Event: 973.179 Thread 0x000001627e89e000 nmethod 36548 0x000001626b4ce250 code [0x000001626b4ce640, 0x000001626b4cfdd0]
Event: 973.247 Thread 0x000001627e8a2800 36552       1       java.util.Calendar::_jr$ip$sharedZone (9 bytes)
Event: 973.247 Thread 0x000001627e8a2800 nmethod 36552 0x000001626b4d1990 code [0x000001626b4d1ae0, 0x000001626b4d1c18]

GC Heap History (10 events):
Event: 189.201 GC heap before
{Heap before GC invocations=56 (full 6):
 PSYoungGen      total 2202624K, used 417876K [0x0000000716b00000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 1779712K, 0% used [0x0000000716b00000,0x0000000716b00000,0x0000000783500000)
  from space 422912K, 98% used [0x00000007a6300000,0x00000007bfb15250,0x00000007c0000000)
  to   space 497152K, 0% used [0x0000000783500000,0x0000000783500000,0x00000007a1a80000)
 ParOldGen       total 1039872K, used 336236K [0x00000005c4000000, 0x0000000603780000, 0x0000000716b00000)
  object space 1039872K, 32% used [0x00000005c4000000,0x00000005d885b120,0x0000000603780000)
 Metaspace       used 265294K, capacity 273859K, committed 273896K, reserved 1300480K
  class space    used 20813K, capacity 22283K, committed 22320K, reserved 1048576K
Event: 189.916 GC heap after
Heap after GC invocations=56 (full 6):
 PSYoungGen      total 2202624K, used 0K [0x0000000716b00000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 1779712K, 0% used [0x0000000716b00000,0x0000000716b00000,0x0000000783500000)
  from space 422912K, 0% used [0x00000007a6300000,0x00000007a6300000,0x00000007c0000000)
  to   space 497152K, 0% used [0x0000000783500000,0x0000000783500000,0x00000007a1a80000)
 ParOldGen       total 1536000K, used 719582K [0x00000005c4000000, 0x0000000621c00000, 0x0000000716b00000)
  object space 1536000K, 46% used [0x00000005c4000000,0x00000005efeb79c8,0x0000000621c00000)
 Metaspace       used 265294K, capacity 273859K, committed 273896K, reserved 1300480K
  class space    used 20813K, capacity 22283K, committed 22320K, reserved 1048576K
}
Event: 194.783 GC heap before
{Heap before GC invocations=57 (full 6):
 PSYoungGen      total 2202624K, used 1779712K [0x0000000716b00000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 1779712K, 100% used [0x0000000716b00000,0x0000000783500000,0x0000000783500000)
  from space 422912K, 0% used [0x00000007a6300000,0x00000007a6300000,0x00000007c0000000)
  to   space 497152K, 0% used [0x0000000783500000,0x0000000783500000,0x00000007a1a80000)
 ParOldGen       total 1536000K, used 719582K [0x00000005c4000000, 0x0000000621c00000, 0x0000000716b00000)
  object space 1536000K, 46% used [0x00000005c4000000,0x00000005efeb79c8,0x0000000621c00000)
 Metaspace       used 274671K, capacity 284237K, committed 284568K, reserved 1310720K
  class space    used 21791K, capacity 23368K, committed 23472K, reserved 1048576K
Event: 194.810 GC heap after
Heap after GC invocations=57 (full 6):
 PSYoungGen      total 2276864K, used 58545K [0x0000000716b00000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 1779712K, 0% used [0x0000000716b00000,0x0000000716b00000,0x0000000783500000)
  from space 497152K, 11% used [0x0000000783500000,0x0000000786e2c7d8,0x00000007a1a80000)
  to   space 495104K, 0% used [0x00000007a1c80000,0x00000007a1c80000,0x00000007c0000000)
 ParOldGen       total 1536000K, used 719590K [0x00000005c4000000, 0x0000000621c00000, 0x0000000716b00000)
  object space 1536000K, 46% used [0x00000005c4000000,0x00000005efeb99c8,0x0000000621c00000)
 Metaspace       used 274671K, capacity 284237K, committed 284568K, reserved 1310720K
  class space    used 21791K, capacity 23368K, committed 23472K, reserved 1048576K
}
Event: 200.465 GC heap before
{Heap before GC invocations=58 (full 6):
 PSYoungGen      total 2276864K, used 1838257K [0x0000000716b00000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 1779712K, 100% used [0x0000000716b00000,0x0000000783500000,0x0000000783500000)
  from space 497152K, 11% used [0x0000000783500000,0x0000000786e2c7d8,0x00000007a1a80000)
  to   space 495104K, 0% used [0x00000007a1c80000,0x00000007a1c80000,0x00000007c0000000)
 ParOldGen       total 1536000K, used 719590K [0x00000005c4000000, 0x0000000621c00000, 0x0000000716b00000)
  object space 1536000K, 46% used [0x00000005c4000000,0x00000005efeb99c8,0x0000000621c00000)
 Metaspace       used 291564K, capacity 302500K, committed 302616K, reserved 1327104K
  class space    used 23530K, capacity 25281K, committed 25392K, reserved 1048576K
Event: 200.498 GC heap after
Heap after GC invocations=58 (full 6):
 PSYoungGen      total 2261504K, used 65776K [0x0000000716b00000, 0x00000007bf100000, 0x00000007c0000000)
  eden space 1781760K, 0% used [0x0000000716b00000,0x0000000716b00000,0x0000000783700000)
  from space 479744K, 13% used [0x00000007a1c80000,0x00000007a5cbc1a8,0x00000007bf100000)
  to   space 488448K, 0% used [0x0000000783700000,0x0000000783700000,0x00000007a1400000)
 ParOldGen       total 1536000K, used 719606K [0x00000005c4000000, 0x0000000621c00000, 0x0000000716b00000)
  object space 1536000K, 46% used [0x00000005c4000000,0x00000005efebd9c8,0x0000000621c00000)
 Metaspace       used 291564K, capacity 302500K, committed 302616K, reserved 1327104K
  class space    used 23530K, capacity 25281K, committed 25392K, reserved 1048576K
}
Event: 207.013 GC heap before
{Heap before GC invocations=59 (full 6):
 PSYoungGen      total 2261504K, used 1847536K [0x0000000716b00000, 0x00000007bf100000, 0x00000007c0000000)
  eden space 1781760K, 100% used [0x0000000716b00000,0x0000000783700000,0x0000000783700000)
  from space 479744K, 13% used [0x00000007a1c80000,0x00000007a5cbc1a8,0x00000007bf100000)
  to   space 488448K, 0% used [0x0000000783700000,0x0000000783700000,0x00000007a1400000)
 ParOldGen       total 1536000K, used 719606K [0x00000005c4000000, 0x0000000621c00000, 0x0000000716b00000)
  object space 1536000K, 46% used [0x00000005c4000000,0x00000005efebd9c8,0x0000000621c00000)
 Metaspace       used 302691K, capacity 314932K, committed 315160K, reserved 1337344K
  class space    used 24978K, capacity 26905K, committed 26928K, reserved 1048576K
Event: 207.056 GC heap after
Heap after GC invocations=59 (full 6):
 PSYoungGen      total 2270208K, used 87342K [0x0000000716b00000, 0x00000007bee80000, 0x00000007c0000000)
  eden space 1781760K, 0% used [0x0000000716b00000,0x0000000716b00000,0x0000000783700000)
  from space 488448K, 17% used [0x0000000783700000,0x0000000788c4b928,0x00000007a1400000)
  to   space 475136K, 0% used [0x00000007a1e80000,0x00000007a1e80000,0x00000007bee80000)
 ParOldGen       total 1536000K, used 719622K [0x00000005c4000000, 0x0000000621c00000, 0x0000000716b00000)
  object space 1536000K, 46% used [0x00000005c4000000,0x00000005efec19c8,0x0000000621c00000)
 Metaspace       used 302691K, capacity 314932K, committed 315160K, reserved 1337344K
  class space    used 24978K, capacity 26905K, committed 26928K, reserved 1048576K
}
Event: 218.862 GC heap before
{Heap before GC invocations=60 (full 6):
 PSYoungGen      total 2270208K, used 1869102K [0x0000000716b00000, 0x00000007bee80000, 0x00000007c0000000)
  eden space 1781760K, 100% used [0x0000000716b00000,0x0000000783700000,0x0000000783700000)
  from space 488448K, 17% used [0x0000000783700000,0x0000000788c4b928,0x00000007a1400000)
  to   space 475136K, 0% used [0x00000007a1e80000,0x00000007a1e80000,0x00000007bee80000)
 ParOldGen       total 1536000K, used 719622K [0x00000005c4000000, 0x0000000621c00000, 0x0000000716b00000)
  object space 1536000K, 46% used [0x00000005c4000000,0x00000005efec19c8,0x0000000621c00000)
 Metaspace       used 306948K, capacity 320022K, committed 320152K, reserved 1341440K
  class space    used 25453K, capacity 27484K, committed 27568K, reserved 1048576K
Event: 218.893 GC heap after
Heap after GC invocations=60 (full 6):
 PSYoungGen      total 2266624K, used 69198K [0x0000000716b00000, 0x00000007bdc00000, 0x00000007c0000000)
  eden space 1810432K, 0% used [0x0000000716b00000,0x0000000716b00000,0x0000000785300000)
  from space 456192K, 15% used [0x00000007a1e80000,0x00000007a6213b80,0x00000007bdc00000)
  to   space 463360K, 0% used [0x0000000785300000,0x0000000785300000,0x00000007a1780000)
 ParOldGen       total 1536000K, used 719638K [0x00000005c4000000, 0x0000000621c00000, 0x0000000716b00000)
  object space 1536000K, 46% used [0x00000005c4000000,0x00000005efec59c8,0x0000000621c00000)
 Metaspace       used 306948K, capacity 320022K, committed 320152K, reserved 1341440K
  class space    used 25453K, capacity 27484K, committed 27568K, reserved 1048576K
}

Deoptimization events (0 events):
No events

Classes redefined (10 events):
Event: 1.484 Thread 0x000001627bb13800 redefined class name=com.zeroturnaround.jrebelbase.facade.ac, count=1
Event: 3.863 Thread 0x000001627bb13800 redefined class name=java.lang.Throwable, count=2
Event: 219.005 Thread 0x000001627bb13800 redefined class name=org.apache.ibatis.executor.SimpleExecutor, count=1
Event: 219.075 Thread 0x000001627bb13800 redefined class name=org.apache.ibatis.executor.BaseExecutor, count=1
Event: 237.855 Thread 0x000001627bb13800 redefined class name=org.apache.ibatis.executor.SimpleExecutor, count=3
Event: 237.920 Thread 0x000001627bb13800 redefined class name=org.apache.ibatis.executor.BaseExecutor, count=2
Event: 427.330 Thread 0x000001627bb13800 redefined class name=org.apache.ibatis.executor.statement.PreparedStatementHandler, count=1
Event: 513.031 Thread 0x000001627bb13800 redefined class name=org.apache.shardingsphere.driver.jdbc.core.statement.ShardingSpherePreparedStatement, count=1
Event: 614.688 Thread 0x000001627bb13800 redefined class name=org.apache.shardingsphere.driver.executor.engine.facade.DriverExecutorFacade, count=1
Event: 966.131 Thread 0x000001627bb13800 redefined class name=org.apache.shardingsphere.driver.executor.engine.DriverExecuteExecutor, count=1

Internal exceptions (10 events):
Event: 923.189 Thread 0x000001622aebf800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000071e21fb20) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 923.189 Thread 0x000001621a028800 Exception <a 'java/net/ConnectException': Connection timed out: connect> (0x000000073dfc7e78) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 739]
Event: 923.191 Thread 0x000001622aebf800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000071e2206c8) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 926.414 Thread 0x000001622aebf800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000071e221290) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 933.941 Thread 0x000001622aebf800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000071e221e58) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 943.927 Thread 0x000001622aebf800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000071e222a20) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 948.792 Thread 0x000001622ad5f000 Exception <a 'sun/nio/fs/WindowsException'> (0x000000073a278a28) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 948.792 Thread 0x000001622ad5f000 Exception <a 'sun/nio/fs/WindowsException'> (0x000000073a278ee8) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 953.935 Thread 0x000001622aebf800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000071e2235e8) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 963.930 Thread 0x000001622aebf800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000071e2241b0) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]

Events (10 events):
Event: 973.245 Thread 0x000001623299a000 DEOPT PACKING pc=0x0000016268a40d14 sp=0x000000560cafcbf0
Event: 973.245 Thread 0x000001623299a000 DEOPT UNPACKING pc=0x00000162677775d8 sp=0x000000560cafc9b8 mode 1
Event: 973.245 Thread 0x000001623299a000 DEOPT PACKING pc=0x0000016268a40d14 sp=0x000000560cafcbf0
Event: 973.245 Thread 0x000001623299a000 DEOPT UNPACKING pc=0x00000162677775d8 sp=0x000000560cafc9b8 mode 1
Event: 973.245 Thread 0x000001623299a000 DEOPT PACKING pc=0x0000016268a40d14 sp=0x000000560cafcbf0
Event: 973.245 Thread 0x000001623299a000 DEOPT UNPACKING pc=0x00000162677775d8 sp=0x000000560cafc9b8 mode 1
Event: 973.245 Thread 0x000001623299a000 DEOPT PACKING pc=0x0000016268a40d14 sp=0x000000560cafcbf0
Event: 973.245 Thread 0x000001623299a000 DEOPT UNPACKING pc=0x00000162677775d8 sp=0x000000560cafc9b8 mode 1
Event: 973.245 Thread 0x000001623299a000 DEOPT PACKING pc=0x0000016268a40d14 sp=0x000000560cafcbf0
Event: 973.245 Thread 0x000001623299a000 DEOPT UNPACKING pc=0x00000162677775d8 sp=0x000000560cafc9b8 mode 1


Dynamic libraries:
0x00007ff7fa670000 - 0x00007ff7fa6c1000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\bin\java.exe
0x00007ffa9e0f0000 - 0x00007ffa9e307000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa9d640000 - 0x00007ffa9d704000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa9b350000 - 0x00007ffa9b723000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa84ff0000 - 0x00007ffa85007000 	C:\DLP\Application\ghijt64.dll
0x00007ffa9db40000 - 0x00007ffa9dbf1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa9d7c0000 - 0x00007ffa9d867000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa9d1e0000 - 0x00007ffa9d288000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa9b200000 - 0x00007ffa9b228000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffa9dc00000 - 0x00007ffa9dd14000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa9d3a0000 - 0x00007ffa9d551000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa9bd60000 - 0x00007ffa9bd86000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa9d2f0000 - 0x00007ffa9d319000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa9b230000 - 0x00007ffa9b34b000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa9ba80000 - 0x00007ffa9bb1a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa9b730000 - 0x00007ffa9b841000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa83590000 - 0x00007ffa83828000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5124_none_270e8f4f7386d69d\COMCTL32.dll
0x00007ffa838a0000 - 0x00007ffa838eb000 	C:\DLP\Application\LdInjectDLL64.dll
0x00007ffa96510000 - 0x00007ffa9651a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa9de20000 - 0x00007ffa9de51000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa83560000 - 0x00007ffa83588000 	C:\DLP\Application\HookDataInteractionx64.dll
0x00007ffa834f0000 - 0x00007ffa83516000 	C:\DLP\Application\TrClientProtect64.dll
0x00007ffa36c60000 - 0x00007ffa36dd7000 	C:\DLP\Application\LdGetPrintContent64.dll
0x00007ffa9d870000 - 0x00007ffa9da11000 	C:\WINDOWS\System32\ole32.dll
0x00007ffa9c110000 - 0x00007ffa9c4a2000 	C:\WINDOWS\System32\combase.dll
0x00007ffa75270000 - 0x00007ffa75318000 	C:\WINDOWS\SYSTEM32\WINSPOOL.DRV
0x00007ffa9be10000 - 0x00007ffa9bf1a000 	C:\WINDOWS\System32\shcore.dll
0x00007ffa9aa10000 - 0x00007ffa9aa1c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.DLL
0x00007ffa9c4b0000 - 0x00007ffa9cd46000 	C:\WINDOWS\System32\Shell32.dll
0x00007ffa9bba0000 - 0x00007ffa9bcdf000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffa36be0000 - 0x00007ffa36c54000 	C:\DLP\Application\LdSmartEnc64.dll
0x00007ffa70270000 - 0x00007ffa7027b000 	C:\WINDOWS\SYSTEM32\FLTLIB.DLL
0x00007ffa36fc0000 - 0x00007ffa373ac000 	C:\DLP\Application\LdWaterMarkHook64.dll
0x00007ffa9e090000 - 0x00007ffa9e098000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffa657a0000 - 0x00007ffa6595a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.22621.5124_none_57f98234ce19506b\gdiplus.dll
0x00007ffa87d00000 - 0x00007ffa87d15000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\vcruntime140.dll
0x00007ffa36de0000 - 0x00007ffa36fb2000 	C:\DLP\Application\LdPrintMonitor64.dll
0x00007ffa9bd90000 - 0x00007ffa9be01000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa82310000 - 0x00007ffa823ab000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\msvcp140.dll
0x000000005dfc0000 - 0x000000005e819000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\server\jvm.dll
0x00007ffa96200000 - 0x00007ffa96209000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffa8f970000 - 0x00007ffa8f9a4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa36a70000 - 0x00007ffa36bdd000 	C:\DLP\Application\EncAppCtrl64.dll
0x00007ffa9de60000 - 0x00007ffa9dec3000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffa9d560000 - 0x00007ffa9d637000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffa63b70000 - 0x00007ffa63ba1000 	C:\WINDOWS\SYSTEM32\Mapi32.dll
0x00007ffa9bf20000 - 0x00007ffa9bf3f000 	C:\WINDOWS\System32\imagehlp.dll
0x00007ffa9a230000 - 0x00007ffa9a248000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa88130000 - 0x00007ffa88140000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\verify.dll
0x00007ffa78f30000 - 0x00007ffa78f4f000 	C:\DLP\Application\HookCreateProcessInternal64.dll
0x00007ffa873e0000 - 0x00007ffa8740b000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\java.dll
0x00007ffa369f0000 - 0x00007ffa36a66000 	C:\DLP\Application\MonFileOp64.dll
0x00007ffa820c0000 - 0x00007ffa820de000 	C:\WINDOWS\SYSTEM32\MPR.dll
0x00007ffa369b0000 - 0x00007ffa369e1000 	C:\DLP\Application\LdSensitiveTaskCenter64.dll
0x00007ffa87d70000 - 0x00007ffa87da6000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\jdwp.dll
0x00007ffa882a0000 - 0x00007ffa882a9000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\npt.dll
0x00007ffa7f2e0000 - 0x00007ffa7f32e000 	C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2024.3\plugins\jr-ide-idea\jrebel6\lib\jrebel64.dll
0x00007ffa7b920000 - 0x00007ffa7b959000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\instrument.dll
0x00007ffa82410000 - 0x00007ffa82428000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\zip.dll
0x00007ffa99120000 - 0x00007ffa99a3a000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa9b130000 - 0x00007ffa9b15b000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffa87f90000 - 0x00007ffa87f9a000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\dt_socket.dll
0x00007ffa9a7a0000 - 0x00007ffa9a80a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa823f0000 - 0x00007ffa8240c000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\net.dll
0x00007ffa80a50000 - 0x00007ffa80a63000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\nio.dll
0x00007ffa822e0000 - 0x00007ffa82304000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\sunec.dll
0x00007ffa87070000 - 0x00007ffa8707d000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\management.dll
0x00007ffa87d40000 - 0x00007ffa87d4e000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\sunmscapi.dll
0x00007ffa9b850000 - 0x00007ffa9b9b7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffa9abb0000 - 0x00007ffa9abdd000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffa9ab70000 - 0x00007ffa9aba7000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffa9a9f0000 - 0x00007ffa9aa0b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa9a190000 - 0x00007ffa9a1c7000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa9a890000 - 0x00007ffa9a8b8000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa9bce0000 - 0x00007ffa9bd5b000 	C:\WINDOWS\System32\bcryptprimitives.dll
0x00007ffa99ca0000 - 0x00007ffa99ccd000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa9d1d0000 - 0x00007ffa9d1d9000 	C:\WINDOWS\System32\NSI.dll
0x00007ffa96520000 - 0x00007ffa96539000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffa95b50000 - 0x00007ffa95b6f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffa99d10000 - 0x00007ffa99e12000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffa933e0000 - 0x00007ffa933ea000 	C:\Windows\System32\rasadhlp.dll
0x00007ffa941d0000 - 0x00007ffa94253000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffa63a30000 - 0x00007ffa63a47000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffa63a10000 - 0x00007ffa63a2b000 	C:\WINDOWS\system32\pnrpnsp.dll
0x00007ffa639f0000 - 0x00007ffa63a01000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffa94a90000 - 0x00007ffa94aa5000 	C:\WINDOWS\system32\wshbth.dll
0x00007ffa639c0000 - 0x00007ffa639e7000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ffa7f2c0000 - 0x00007ffa7f2db000 	C:\Users\<USER>\.jrebel\jrebel-temp\ver-7c19eea5\IdeaWin64.dll
0x00007ff9e7f80000 - 0x00007ff9eafc6000 	C:\Users\<USER>\AppData\Local\Temp\opencv_openpnp9116473437589520133\nu\pattern\opencv\windows\x86_64\opencv_java451.dll
0x00007ffa723d0000 - 0x00007ffa7259e000 	C:\WINDOWS\SYSTEM32\MFPlat.DLL
0x00007ffa60480000 - 0x00007ffa6050a000 	C:\WINDOWS\SYSTEM32\MF.dll
0x00007ffa89090000 - 0x00007ffa891b1000 	C:\WINDOWS\SYSTEM32\MFReadWrite.dll
0x00007ffa9aec0000 - 0x00007ffa9af0e000 	C:\WINDOWS\SYSTEM32\cfgmgr32.dll
0x00007ffa16340000 - 0x00007ffa166db000 	C:\WINDOWS\SYSTEM32\MFCORE.DLL
0x00007ffa88a90000 - 0x00007ffa88ac3000 	C:\WINDOWS\SYSTEM32\RTWorkQ.DLL
0x00007ffa7b5b0000 - 0x00007ffa7b5f5000 	C:\Users\<USER>\AppData\Local\Temp\jna-115883602\jna1160534571473282194.dll
0x00007ffa7b520000 - 0x00007ffa7b57f000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\bin\Ldwebdf.dll
0x00007ffa70fb0000 - 0x00007ffa71019000 	C:\WINDOWS\SYSTEM32\OLEACC.dll
0x0000000180000000 - 0x00000001800b3000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\bin\lddf_64.dll
0x00007ffa7b140000 - 0x00007ffa7b1cc000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\bin\mtoken_gm3000_64.dll
0x00007ffa9cd50000 - 0x00007ffa9d1c4000 	C:\WINDOWS\System32\SETUPAPI.dll
0x00007ffa99a40000 - 0x00007ffa99a4e000 	C:\WINDOWS\SYSTEM32\HID.DLL
0x00007ffa85c20000 - 0x00007ffa85e52000 	C:\WINDOWS\SYSTEM32\dbghelp.dll

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:51988,suspend=y,server=n -Drebel.base=C:\Users\<USER>\.jrebel -Drebel.env.ide.plugin.build=2d7357bde6d770c5760a889e2d4bf4d26276ce12 -Drebel.env.ide.plugin.version=2025.2.0 -Drebel.env.ide.version=2024.3.5 -Drebel.env.ide.product=IU -Drebel.env.ide=intellij -Drebel.notification.url=http://localhost:63500 -agentpath:C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2024.3\plugins\jr-ide-idea\jrebel6\lib\jrebel64.dll -Dspring.profiles.active=dev -Dcool.request.port=63319 -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2024.3\plugins\bean-invoker-intellij-plugin\lib\bean-invoker-agent-1.0.3-with-dependencies.jar -Dspring.invokePort=6060 -Drebel.plugins=C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2024.3\plugins\jr-mp-ide-idea\lib\jr-mybatisplus-1.0.7.jar -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2024.3\captureAgent\debugger-agent.jar=file:/C:/Users/<USER>/AppData/Local/Temp/capture2.props -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 
java_command: com.tipray.dlp.app.APP
java_class_path (initial): C:\Users\<USER>\AppData\Local\Temp\classpath2057323679.jar;C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2024.3\plugins\bean-invoker-intellij-plugin\lib\bean-invoker-agent-1.0.3-with-dependencies.jar;C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2024.3\captureAgent\debugger-agent.jar
Launcher Type: SUN_STANDARD

Environment Variables:
PATH=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;D:\VMware\VMware Workstation\bin\;C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\TortoiseSVN\bin;C:\Program Files\Git\cmd;C:\Program Files\MySQL\MySQL Server 5.7\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\gradle-8.14\bin;;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Pandoc\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\cursor\resources\app\bin
USERNAME=zhuhs
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 191 Stepping 2, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 11 , 64 bit Build 22621 (10.0.22621.5124)

CPU:total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 191 stepping 2, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx

Memory: 4k page, physical 33289648k(6776516k free), swap 57406896k(21288992k free)

vm_info: OpenJDK 64-Bit Server VM (25.412-b08) for windows-amd64 JRE (1.8.0_412-b08), built on Apr 17 2024 02:10:30 by "jenkins" with MS VC++ 15.9 (VS2017)

time: Mon Aug  4 17:00:24 2025
timezone: Intel64 Family 6 Model 191 Stepping 2, GenuineIntel
elapsed time: 973.269865 seconds (0d 0h 16m 13s)

