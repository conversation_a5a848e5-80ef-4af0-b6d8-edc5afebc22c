package com.tipray.dlp.mybatis.progress.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 分库分表查询进度DTO
 * 用于前端展示查询进度信息
 * 
 * <AUTHOR>
 */
@Data
public class ShardingQueryProgressDTO {
    
    /** 查询任务ID */
    private String taskId;
    
    /** 总库数 */
    private int totalDatabases;
    
    /** 总表数 */
    private int totalTables;
    
    /** 已完成的库数 */
    private int completedDatabases;
    
    /** 已完成的表数 */
    private int completedTables;
    
    /** 当前正在查询的库名 */
    private String currentDatabase;
    
    /** 当前正在查询的表名 */
    private String currentTable;
    
    /** 当前库的时间描述（如：2025年07月） */
    private String currentDatabaseTime;
    
    /** 当前表的时间描述（如：2025年08月03日） */
    private String currentTableTime;
    
    /** 库查询进度百分比 */
    private double databaseProgress;
    
    /** 表查询进度百分比 */
    private double tableProgress;
    
    /** 总体进度百分比 */
    private double overallProgress;
    
    /** 查询开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    /** 查询结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    /** 查询耗时（毫秒） */
    private long elapsedTime;
    
    /** 查询耗时描述 */
    private String elapsedTimeDesc;
    
    /** 是否已完成 */
    private boolean completed;
    
    /** 是否已取消 */
    private boolean cancelled;
    
    /** 总查询记录数 */
    private long totalRecords;
    
    /** 错误信息 */
    private String errorMessage;
    
    /** 当前状态描述 */
    private String currentStatus;
    
    /** 进度摘要信息 */
    private String progressSummary;
    
    /** 查询状态：0-进行中，1-已完成，2-已取消，3-异常 */
    private int status;
    
    /** 预计剩余时间（毫秒） */
    private long estimatedRemainingTime;
    
    /** 预计剩余时间描述 */
    private String estimatedRemainingTimeDesc;
    
    /** 平均每表查询时间（毫秒） */
    private long avgTimePerTable;
    
    /**
     * 格式化时间描述
     */
    public void formatTimeDescriptions() {
        this.elapsedTimeDesc = formatDuration(this.elapsedTime);
        this.estimatedRemainingTimeDesc = formatDuration(this.estimatedRemainingTime);
    }
    
    /**
     * 格式化持续时间
     */
    private String formatDuration(long milliseconds) {
        if (milliseconds < 0) {
            return "未知";
        }
        
        long seconds = milliseconds / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        
        if (hours > 0) {
            return String.format("%d小时%d分钟%d秒", hours, minutes % 60, seconds % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }
    
    /**
     * 计算查询状态
     */
    public void calculateStatus() {
        if (cancelled) {
            this.status = 2;
        } else if (errorMessage != null) {
            this.status = 3;
        } else if (completed) {
            this.status = 1;
        } else {
            this.status = 0;
        }
    }
    
    /**
     * 计算预计剩余时间
     */
    public void calculateEstimatedTime() {
        if (completedTables > 0 && !completed && !cancelled && errorMessage == null) {
            this.avgTimePerTable = elapsedTime / completedTables;
            int remainingTables = totalTables - completedTables;
            this.estimatedRemainingTime = avgTimePerTable * remainingTables;
        } else {
            this.estimatedRemainingTime = -1;
        }
    }
}
