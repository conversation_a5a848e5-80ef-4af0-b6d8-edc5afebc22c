package com.tipray.dlp.mybatis.progress.example;

import com.tipray.dlp.mybatis.progress.ShardingQueryProgress;
import com.tipray.dlp.mybatis.progress.ShardingQueryProgressManager;
import com.tipray.dlp.mybatis.progress.dto.ShardingQueryProgressDTO;
import com.tipray.dlp.mybatis.progress.util.ShardingQueryProgressUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 分库分表查询进度使用示例
 * 展示如何在实际查询中使用进度追踪组件
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ShardingQueryWithProgressExample {
    
    @Autowired
    private ShardingQueryProgressManager progressManager;
    
    /**
     * 示例：带进度追踪的分库分表查询
     */
    public CompletableFuture<List<Object>> queryWithProgress(String userId, String keyword, 
                                                           String startDate, String endDate) {
        
        // 1. 生成任务ID
        String taskId = ShardingQueryProgressUtil.generateTaskId("audit_query", Long.valueOf(userId));
        
        // 2. 计算需要查询的库表数量
        List<String> databases = calculateDatabases(startDate, endDate);
        List<String> tables = calculateTables(startDate, endDate);
        int totalTables = databases.size() * tables.size();
        
        // 3. 创建进度追踪器
        ShardingQueryProgress progress = progressManager.createProgress(taskId, databases.size(), totalTables);
        
        // 4. 异步执行查询
        return CompletableFuture.supplyAsync(() -> {
            try {
                return executeShardingQuery(progress, databases, tables, keyword);
            } catch (Exception e) {
                progress.setError(e.getMessage());
                throw new RuntimeException("查询异常", e);
            }
        });
    }
    
    /**
     * 执行分库分表查询
     */
    private List<Object> executeShardingQuery(ShardingQueryProgress progress, 
                                            List<String> databases, 
                                            List<String> tables, 
                                            String keyword) {
        List<Object> allResults = new ArrayList<>();
        
        try {
            for (String database : databases) {
                for (String table : tables) {
                    // 检查是否被取消
                    if (progress.isCancelled()) {
                        log.info("查询任务 [{}] 被取消", progress.getTaskId());
                        return allResults;
                    }
                    
                    // 开始查询当前表
                    progress.startQueryTable(database, table);
                    
                    // 模拟查询数据库
                    List<Object> tableResults = queryTable(database, table, keyword);
                    allResults.addAll(tableResults);
                    
                    // 完成当前表查询
                    progress.completeTable(database, table, tableResults.size());
                    
                    // 模拟查询耗时
                    Thread.sleep(100);
                }
                
                // 完成当前库查询
                progress.completeDatabase(database);
            }
            
            // 标记查询完成
            progress.markCompleted();
            
        } catch (InterruptedException e) {
            progress.markCancelled();
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            progress.setError(e.getMessage());
            throw e;
        }
        
        return allResults;
    }
    
    /**
     * 模拟查询单个表
     */
    private List<Object> queryTable(String database, String table, String keyword) {
        // 这里是实际的数据库查询逻辑
        // 模拟返回一些数据
        List<Object> results = new ArrayList<>();
        
        // 模拟查询结果数量
        int recordCount = (int)(Math.random() * 1000);
        for (int i = 0; i < recordCount; i++) {
            results.add(new Object()); // 实际应该是查询结果对象
        }
        
        return results;
    }
    
    /**
     * 计算需要查询的数据库列表
     * 根据时间范围计算，例如：dlp_audit_2507, dlp_audit_2508
     */
    private List<String> calculateDatabases(String startDate, String endDate) {
        List<String> databases = new ArrayList<>();
        
        // 这里应该根据实际的分库规则来计算
        // 示例：假设按月分库
        databases.add("dlp_audit_2507"); // 2025年07月
        databases.add("dlp_audit_2508"); // 2025年08月
        databases.add("dlp_audit_2509"); // 2025年09月
        
        return databases;
    }
    
    /**
     * 计算需要查询的表列表
     * 根据时间范围计算，例如：audit_log_20250801, audit_log_20250802
     */
    private List<String> calculateTables(String startDate, String endDate) {
        List<String> tables = new ArrayList<>();
        
        // 这里应该根据实际的分表规则来计算
        // 示例：假设按天分表
        for (int day = 1; day <= 31; day++) {
            tables.add(String.format("audit_log_202508%02d", day));
        }
        
        return tables;
    }
    
    /**
     * 获取查询进度
     */
    public ShardingQueryProgressDTO getQueryProgress(String taskId) {
        ShardingQueryProgress progress = progressManager.getProgress(taskId);
        return ShardingQueryProgressUtil.toDTO(progress);
    }
    
    /**
     * 取消查询
     */
    public boolean cancelQuery(String taskId) {
        ShardingQueryProgress progress = progressManager.getProgress(taskId);
        if (progress != null && !progress.isCompleted()) {
            progress.markCancelled();
            return true;
        }
        return false;
    }
    
    /**
     * 清理查询进度
     */
    public void cleanupProgress(String taskId) {
        progressManager.removeProgress(taskId);
    }
}
