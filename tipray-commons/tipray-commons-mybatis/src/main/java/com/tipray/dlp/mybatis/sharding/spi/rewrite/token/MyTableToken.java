package com.tipray.dlp.mybatis.sharding.spi.rewrite.token;

import com.tipray.dlp.mybatis.MybatisContext;
import com.tipray.dlp.mybatis.datasource.DataSourceFactory;
import com.tipray.dlp.mybatis.sharding.MyShardingSphereDataSource;
import org.apache.shardingsphere.infra.binder.context.statement.SQLStatementContext;
import org.apache.shardingsphere.infra.binder.context.statement.dml.SelectStatementContext;
import org.apache.shardingsphere.infra.binder.context.type.TableAvailable;
import org.apache.shardingsphere.infra.database.core.metadata.database.enums.QuoteCharacter;
import org.apache.shardingsphere.infra.exception.kernel.metadata.TableNotFoundException;
import org.apache.shardingsphere.infra.rewrite.sql.token.common.pojo.RouteUnitAware;
import org.apache.shardingsphere.infra.rewrite.sql.token.common.pojo.SQLToken;
import org.apache.shardingsphere.infra.rewrite.sql.token.common.pojo.Substitutable;
import org.apache.shardingsphere.infra.route.context.RouteMapper;
import org.apache.shardingsphere.infra.route.context.RouteUnit;
import org.apache.shardingsphere.sharding.rewrite.token.pojo.ShardingTableToken;
import org.apache.shardingsphere.sharding.rule.ShardingRule;
import org.apache.shardingsphere.sql.parser.statement.core.segment.generic.table.SimpleTableSegment;

import java.util.*;

/**
 * 将逻辑表名改写成路由到的实际表名，并处理实际表与逻辑表相比缺失部分字段的问题
 *
 * @see ShardingTableToken
 */
public class MyTableToken extends SQLToken implements Substitutable, RouteUnitAware {
    private final int stopIndex;
    private final String logicTable;
    private final String tableAlias;
    private final QuoteCharacter quoteCharacter;
    private final SQLStatementContext sqlStatementContext;
    private final ShardingRule shardingRule;

    public MyTableToken(SimpleTableSegment tableSegment, SQLStatementContext sqlStatementContext, ShardingRule shardingRule) {
        super(tableSegment.getStartIndex());
        this.stopIndex = tableSegment.getTableName().getStopIndex(); // 表存在别名时，不重写表的别名
        this.logicTable = tableSegment.getTableName().getIdentifier().getValue();
        this.tableAlias = tableSegment.getAliasName().orElse(null);
        this.quoteCharacter = tableSegment.getTableName().getIdentifier().getQuoteCharacter();
        this.sqlStatementContext = sqlStatementContext;
        this.shardingRule = shardingRule;
    }

    public String getLogicTable() {
        return this.logicTable;
    }

    @Override
    public int getStopIndex() {
        return this.stopIndex;
    }

    @Override
    public String toString(RouteUnit routeUnit) {
        String dsName = routeUnit.getDataSourceMapper().getLogicName();
        String actualTable = getActualTable(dsName, routeUnit);
        // 1、不需改写的情况：非查询sql语句不需填充字段
        if (!(this.sqlStatementContext instanceof SelectStatementContext)) {
            return getQuoteWrappedTableName(actualTable);
        }
        // 2、不需填充缺失字段的情况（需添加路由字段）：（1）配置文件配置了不填充；（2）单数据源且查询模板表
        int index = actualTable.indexOf('.');
        boolean isNotFillLackColumn = !Boolean.TRUE.equals(MybatisContext.IS_FILL_COLUMN) ||
                (this.shardingRule.getDataSourceNames().size() == 1 && (actualTable.equals(this.logicTable) || index < 0));
        if (isNotFillLackColumn) {
            return rewriteTableWithLackedColumns(this.logicTable, actualTable, this.tableAlias == null, Collections.emptySet(), dsName);
        }
        // 3、需要检查并填充缺失字段的情况：配置了填充且查询日志分表
        String dbName, tblName;
        if (index < 0) {
            dbName = MybatisContext.SHARD_MODULE_DB;
            tblName = actualTable;
        } else {
            dbName = actualTable.substring(0, index);
            tblName = actualTable.substring(index + 1);
        }
        MyShardingSphereDataSource dataSource = DataSourceFactory.getShardingDataSource();
        if (!dataSource.existsActualTable(dsName, dbName, tblName)) {
            // 路由表不存在（正常情况下不会走到这）
            List<String> logicColumns = dataSource.getLogicTableColumns(this.logicTable);
            if (logicColumns.isEmpty()) {
                throw new TableNotFoundException(this.logicTable);
            }
            StringBuilder sb = new StringBuilder("(SELECT t0.*");
            for (String column : logicColumns) {
                sb.append(", NULL AS ").append(column);
            }
            sb.append(", NULL AS data_source_name, NULL AS logic_table_name, NULL AS actual_table_name");
            sb.append(" FROM (SELECT 0 AS f0) t0 WHERE t0.f0 = 1)");
            if (this.tableAlias == null) {
                sb.append(" t1");
            }
            return sb.toString();
        }
//        Set<String> lackColumns = dataSource.getLackColumns(dsName, dbName, tblName, this.logicTable);
        Set<String> lackColumns = new HashSet<>(0);
        return rewriteTableWithLackedColumns(this.logicTable, actualTable, this.tableAlias == null, lackColumns, dsName);
    }

    /**
     * 重写缺失列的表
     *
     * @param logicTableName  逻辑表名称
     * @param actualTableName 真实表名称
     * @param noTableAlias    表没有别名
     * @param lackColumns     缺失的列
     * @param routeDatasource 路由数据源
     * @return 重写后的子查询语句
     */
    public static String rewriteTableWithLackedColumns(String logicTableName, String actualTableName,
                                                       boolean noTableAlias, Set<String> lackColumns, String routeDatasource) {
        StringBuilder sb = new StringBuilder("(SELECT t0f0.*");
        for (String column : lackColumns) {
            sb.append(", ").append(DataSourceFactory.getShardingDataSource().replaceLackColumn(column)).append(" AS ").append(column);
        }
        if (null != routeDatasource) {
            sb.append(", '").append(routeDatasource).append("' AS data_source_name");
            sb.append(", '").append(logicTableName).append("' AS logic_table_name");
            sb.append(", '").append(actualTableName).append("' AS actual_table_name");
        }
        sb.append(" FROM ").append(actualTableName).append(" t0f0)");
        // 如果表没有指定别名，那么需要额外给组装的表加一个别名
        if (noTableAlias) {
            sb.append(" t1f0");
        }
        return sb.toString();
    }

    private String getActualTable(String dsName, RouteUnit routeUnit) {
        String thisLogicTableLower = this.logicTable.toLowerCase();
        Collection<String> tableNames = null;
        String logicTable, actualTable;
        Map<String, String> bindingTableMap;
        Collection<RouteMapper> tableMappers = routeUnit.getTableMappers();
        for (RouteMapper tableMapper : tableMappers) {
            logicTable = tableMapper.getLogicName();
            actualTable = tableMapper.getActualName();
            if (logicTable.equalsIgnoreCase(thisLogicTableLower)) {
                return actualTable;
            }
            if (tableNames == null) {
                tableNames = ((TableAvailable) this.sqlStatementContext).getTablesContext().getTableNames();
            }
            bindingTableMap = this.shardingRule.getLogicAndActualTablesFromBindingTable(dsName, logicTable, actualTable, tableNames);
            actualTable = bindingTableMap.get(thisLogicTableLower);
            if (actualTable != null) {
                return actualTable;
            }
        }
        return thisLogicTableLower;
    }

    private String getQuoteWrappedTableName(String tableName) {
        if (QuoteCharacter.NONE.equals(this.quoteCharacter)) {
            return tableName;
        }
        StringBuilder sb = new StringBuilder(tableName.length() + 4);
        int start = 0;
        for (int i = 0; i < tableName.length(); i++) {
            if (tableName.charAt(i) == '.') {
                sb.append(this.quoteCharacter.getStartDelimiter());
                sb.append(tableName, start, i);
                sb.append(this.quoteCharacter.getEndDelimiter());
                sb.append('.');
                start = i + 1;
            }
        }
        sb.append(this.quoteCharacter.getStartDelimiter());
        sb.append(tableName.substring(start));
        sb.append(this.quoteCharacter.getEndDelimiter());
        return sb.toString();
    }

}
