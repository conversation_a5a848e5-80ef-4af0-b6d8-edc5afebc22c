package com.tipray.dlp.mybatis.progress;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 分库分表查询进度追踪器
 * 用于追踪分库分表查询的进度信息
 * 
 * <AUTHOR>
 */
@Slf4j
@Data
public class ShardingQueryProgress {
    
    /** 查询任务ID */
    private String taskId;
    
    /** 总库数 */
    private int totalDatabases;
    
    /** 总表数 */
    private int totalTables;
    
    /** 已完成的库数 */
    private final AtomicInteger completedDatabases = new AtomicInteger(0);
    
    /** 已完成的表数 */
    private final AtomicInteger completedTables = new AtomicInteger(0);
    
    /** 当前正在查询的库名 */
    private volatile String currentDatabase;
    
    /** 当前正在查询的表名 */
    private volatile String currentTable;
    
    /** 查询开始时间 */
    private LocalDateTime startTime;
    
    /** 查询结束时间 */
    private LocalDateTime endTime;
    
    /** 是否已完成 */
    private volatile boolean completed = false;
    
    /** 是否已取消 */
    private volatile boolean cancelled = false;
    
    /** 总查询记录数 */
    private final AtomicLong totalRecords = new AtomicLong(0);
    
    /** 错误信息 */
    private volatile String errorMessage;
    
    public ShardingQueryProgress(String taskId, int totalDatabases, int totalTables) {
        this.taskId = taskId;
        this.totalDatabases = totalDatabases;
        this.totalTables = totalTables;
        this.startTime = LocalDateTime.now();
    }
    
    /**
     * 开始查询某个库表
     */
    public void startQueryTable(String database, String table) {
        this.currentDatabase = database;
        this.currentTable = table;
        log.debug("开始查询 [{}] 库表: {}.{}", taskId, database, table);
    }
    
    /**
     * 完成某个表的查询
     */
    public void completeTable(String database, String table, long recordCount) {
        completedTables.incrementAndGet();
        totalRecords.addAndGet(recordCount);
        log.debug("完成查询 [{}] 库表: {}.{}, 记录数: {}", taskId, database, table, recordCount);
    }
    
    /**
     * 完成某个库的查询
     */
    public void completeDatabase(String database) {
        completedDatabases.incrementAndGet();
        log.debug("完成查询 [{}] 库: {}", taskId, database);
    }
    
    /**
     * 标记查询完成
     */
    public void markCompleted() {
        this.completed = true;
        this.endTime = LocalDateTime.now();
        log.info("查询任务 [{}] 完成，总记录数: {}", taskId, totalRecords.get());
    }
    
    /**
     * 标记查询取消
     */
    public void markCancelled() {
        this.cancelled = true;
        this.endTime = LocalDateTime.now();
        log.info("查询任务 [{}] 已取消", taskId);
    }
    
    /**
     * 设置错误信息
     */
    public void setError(String errorMessage) {
        this.errorMessage = errorMessage;
        this.endTime = LocalDateTime.now();
        log.error("查询任务 [{}] 发生错误: {}", taskId, errorMessage);
    }
    
    /**
     * 获取库查询进度百分比
     */
    public double getDatabaseProgress() {
        if (totalDatabases == 0) return 0.0;
        return (double) completedDatabases.get() / totalDatabases * 100;
    }
    
    /**
     * 获取表查询进度百分比
     */
    public double getTableProgress() {
        if (totalTables == 0) return 0.0;
        return (double) completedTables.get() / totalTables * 100;
    }
    
    /**
     * 获取总体进度百分比
     */
    public double getOverallProgress() {
        return getTableProgress();
    }
    
    /**
     * 获取查询耗时（毫秒）
     */
    public long getElapsedTime() {
        LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();
        return java.time.Duration.between(startTime, end).toMillis();
    }
    
    /**
     * 解析库名中的时间信息
     * 例如: dlp_audit_2507 -> 2025年07月
     */
    public String parseDatabaseTime(String database) {
        try {
            if (database.contains("_")) {
                String[] parts = database.split("_");
                String timePart = parts[parts.length - 1];
                if (timePart.length() == 4) {
                    String year = "20" + timePart.substring(0, 2);
                    String month = timePart.substring(2, 4);
                    return year + "年" + month + "月";
                }
            }
        } catch (Exception e) {
            log.warn("解析库名时间失败: {}", database);
        }
        return database;
    }
    
    /**
     * 解析表名中的时间信息
     * 例如: audit_log_20250803 -> 2025年08月03日
     */
    public String parseTableTime(String table) {
        try {
            if (table.contains("_")) {
                String[] parts = table.split("_");
                String timePart = parts[parts.length - 1];
                if (timePart.length() == 8) {
                    String year = timePart.substring(0, 4);
                    String month = timePart.substring(4, 6);
                    String day = timePart.substring(6, 8);
                    return year + "年" + month + "月" + day + "日";
                }
            }
        } catch (Exception e) {
            log.warn("解析表名时间失败: {}", table);
        }
        return table;
    }
    
    /**
     * 获取当前查询状态描述
     */
    public String getCurrentStatus() {
        if (cancelled) {
            return "已取消";
        }
        if (completed) {
            return "已完成";
        }
        if (errorMessage != null) {
            return "查询异常: " + errorMessage;
        }
        if (currentDatabase != null && currentTable != null) {
            return String.format("正在查询 %s.%s (%s.%s)", 
                currentDatabase, currentTable,
                parseDatabaseTime(currentDatabase), 
                parseTableTime(currentTable));
        }
        return "准备中";
    }
    
    /**
     * 获取进度摘要信息
     */
    public String getProgressSummary() {
        return String.format("库进度: %d/%d (%.1f%%), 表进度: %d/%d (%.1f%%), 总记录: %d, 耗时: %dms",
            completedDatabases.get(), totalDatabases, getDatabaseProgress(),
            completedTables.get(), totalTables, getTableProgress(),
            totalRecords.get(), getElapsedTime());
    }
}
