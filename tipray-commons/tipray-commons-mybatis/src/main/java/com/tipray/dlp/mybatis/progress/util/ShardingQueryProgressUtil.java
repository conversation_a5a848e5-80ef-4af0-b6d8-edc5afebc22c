package com.tipray.dlp.mybatis.progress.util;

import com.tipray.dlp.mybatis.progress.ShardingQueryProgress;
import com.tipray.dlp.mybatis.progress.dto.ShardingQueryProgressDTO;
import org.springframework.beans.BeanUtils;

/**
 * 分库分表查询进度工具类
 * 
 * <AUTHOR>
 */
public class ShardingQueryProgressUtil {
    
    /**
     * 将进度对象转换为DTO
     */
    public static ShardingQueryProgressDTO toDTO(ShardingQueryProgress progress) {
        if (progress == null) {
            return null;
        }
        
        ShardingQueryProgressDTO dto = new ShardingQueryProgressDTO();
        
        // 复制基础属性
        dto.setTaskId(progress.getTaskId());
        dto.setTotalDatabases(progress.getTotalDatabases());
        dto.setTotalTables(progress.getTotalTables());
        dto.setCompletedDatabases(progress.getCompletedDatabases().get());
        dto.setCompletedTables(progress.getCompletedTables().get());
        dto.setCurrentDatabase(progress.getCurrentDatabase());
        dto.setCurrentTable(progress.getCurrentTable());
        dto.setStartTime(progress.getStartTime());
        dto.setEndTime(progress.getEndTime());
        dto.setCompleted(progress.isCompleted());
        dto.setCancelled(progress.isCancelled());
        dto.setTotalRecords(progress.getTotalRecords().get());
        dto.setErrorMessage(progress.getErrorMessage());
        
        // 计算进度百分比
        dto.setDatabaseProgress(progress.getDatabaseProgress());
        dto.setTableProgress(progress.getTableProgress());
        dto.setOverallProgress(progress.getOverallProgress());
        
        // 计算时间相关信息
        dto.setElapsedTime(progress.getElapsedTime());
        
        // 解析时间描述
        if (progress.getCurrentDatabase() != null) {
            dto.setCurrentDatabaseTime(progress.parseDatabaseTime(progress.getCurrentDatabase()));
        }
        if (progress.getCurrentTable() != null) {
            dto.setCurrentTableTime(progress.parseTableTime(progress.getCurrentTable()));
        }
        
        // 设置状态描述
        dto.setCurrentStatus(progress.getCurrentStatus());
        dto.setProgressSummary(progress.getProgressSummary());
        
        // 计算状态和预计时间
        dto.calculateStatus();
        dto.calculateEstimatedTime();
        dto.formatTimeDescriptions();
        
        return dto;
    }
    
    /**
     * 生成任务ID
     * 格式：用户ID_时间戳_随机数
     */
    public static String generateTaskId(String prefix) {
        return String.format("%s_%d_%d", 
            prefix != null ? prefix : "task",
            System.currentTimeMillis(),
            (int)(Math.random() * 1000));
    }
    
    /**
     * 生成任务ID（带用户信息）
     */
    public static String generateTaskId(String prefix, Long userId) {
        return String.format("%s_%s_%d_%d", 
            prefix != null ? prefix : "task",
            userId != null ? userId : "unknown",
            System.currentTimeMillis(),
            (int)(Math.random() * 1000));
    }
    
    /**
     * 解析库名获取年月
     * 例如: dlp_audit_2507 -> [2025, 07]
     */
    public static String[] parseDatabaseYearMonth(String database) {
        try {
            if (database != null && database.contains("_")) {
                String[] parts = database.split("_");
                String timePart = parts[parts.length - 1];
                if (timePart.length() == 4) {
                    String year = "20" + timePart.substring(0, 2);
                    String month = timePart.substring(2, 4);
                    return new String[]{year, month};
                }
            }
        } catch (Exception e) {
            // 忽略解析错误
        }
        return null;
    }
    
    /**
     * 解析表名获取年月日
     * 例如: audit_log_20250803 -> [2025, 08, 03]
     */
    public static String[] parseTableYearMonthDay(String table) {
        try {
            if (table != null && table.contains("_")) {
                String[] parts = table.split("_");
                String timePart = parts[parts.length - 1];
                if (timePart.length() == 8) {
                    String year = timePart.substring(0, 4);
                    String month = timePart.substring(4, 6);
                    String day = timePart.substring(6, 8);
                    return new String[]{year, month, day};
                }
            }
        } catch (Exception e) {
            // 忽略解析错误
        }
        return null;
    }
    
    /**
     * 计算查询速度（记录数/秒）
     */
    public static double calculateQuerySpeed(ShardingQueryProgress progress) {
        long elapsedSeconds = progress.getElapsedTime() / 1000;
        if (elapsedSeconds > 0) {
            return (double) progress.getTotalRecords().get() / elapsedSeconds;
        }
        return 0.0;
    }
    
    /**
     * 格式化查询速度描述
     */
    public static String formatQuerySpeed(double recordsPerSecond) {
        if (recordsPerSecond >= 1000) {
            return String.format("%.1fK条/秒", recordsPerSecond / 1000);
        } else {
            return String.format("%.0f条/秒", recordsPerSecond);
        }
    }
    
    /**
     * 检查任务是否超时
     */
    public static boolean isTaskTimeout(ShardingQueryProgress progress, long timeoutMillis) {
        return progress.getElapsedTime() > timeoutMillis && 
               !progress.isCompleted() && 
               !progress.isCancelled() && 
               progress.getErrorMessage() == null;
    }
}
