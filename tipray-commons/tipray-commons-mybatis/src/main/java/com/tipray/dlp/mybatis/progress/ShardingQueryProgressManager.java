package com.tipray.dlp.mybatis.progress;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 分库分表查询进度管理器
 * 管理多个查询任务的进度追踪
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ShardingQueryProgressManager {
    
    /** 存储所有查询任务的进度 */
    private final ConcurrentHashMap<String, ShardingQueryProgress> progressMap = new ConcurrentHashMap<>();
    
    /** 定时清理已完成任务的线程池 */
    private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "progress-cleanup");
        t.setDaemon(true);
        return t;
    });
    
    public ShardingQueryProgressManager() {
        // 每5分钟清理一次已完成超过30分钟的任务
        cleanupExecutor.scheduleAtFixedRate(this::cleanupCompletedTasks, 5, 5, TimeUnit.MINUTES);
    }
    
    /**
     * 创建新的查询进度追踪器
     */
    public ShardingQueryProgress createProgress(String taskId, int totalDatabases, int totalTables) {
        ShardingQueryProgress progress = new ShardingQueryProgress(taskId, totalDatabases, totalTables);
        progressMap.put(taskId, progress);
        log.info("创建查询进度追踪器 [{}]: {}库 {}表", taskId, totalDatabases, totalTables);
        return progress;
    }
    
    /**
     * 获取查询进度
     */
    public ShardingQueryProgress getProgress(String taskId) {
        return progressMap.get(taskId);
    }
    
    /**
     * 移除查询进度
     */
    public void removeProgress(String taskId) {
        ShardingQueryProgress removed = progressMap.remove(taskId);
        if (removed != null) {
            log.info("移除查询进度追踪器 [{}]", taskId);
        }
    }
    
    /**
     * 获取所有活跃的查询任务数量
     */
    public int getActiveTaskCount() {
        return (int) progressMap.values().stream()
            .filter(p -> !p.isCompleted() && !p.isCancelled() && p.getErrorMessage() == null)
            .count();
    }
    
    /**
     * 清理已完成的任务
     */
    private void cleanupCompletedTasks() {
        long thirtyMinutesAgo = System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(30);
        
        progressMap.entrySet().removeIf(entry -> {
            ShardingQueryProgress progress = entry.getValue();
            boolean shouldRemove = (progress.isCompleted() || progress.isCancelled() || progress.getErrorMessage() != null)
                && progress.getStartTime().isBefore(java.time.LocalDateTime.now().minusMinutes(30));
            
            if (shouldRemove) {
                log.debug("清理已完成的查询进度追踪器 [{}]", entry.getKey());
            }
            return shouldRemove;
        });
    }
    
    /**
     * 获取所有进度信息的摘要
     */
    public String getAllProgressSummary() {
        if (progressMap.isEmpty()) {
            return "当前无查询任务";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("查询任务摘要:\n");
        
        progressMap.forEach((taskId, progress) -> {
            summary.append(String.format("任务[%s]: %s - %s\n", 
                taskId, progress.getCurrentStatus(), progress.getProgressSummary()));
        });
        
        return summary.toString();
    }
    
    /**
     * 关闭管理器
     */
    public void shutdown() {
        cleanupExecutor.shutdown();
        try {
            if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanupExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            cleanupExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
