<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.tipray</groupId>
    <artifactId>dlp</artifactId>
    <packaging>pom</packaging>
    <version>3.0</version>

    <organization>
        <name>tipray</name>
        <url>https://www.tipray.com</url>
    </organization>

    <!-- 父项目spring boot -->
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
    </parent>

    <modules>
        <module>tipray-commons</module>
        <module>tipray-dlp-core</module>
        <module>tipray-modules</module>
        <module>tipray-trwfe-dlp</module>
        <module>tipray-api</module>
    </modules>

    <!-- 项目属性配置 -->
    <properties>
        <!--指定时间格式-->
        <maven.build.timestamp.format>yyyy-MM-dd HH:mm:ss</maven.build.timestamp.format>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <jna.version>5.13.0</jna.version>
        <poi.version>4.1.2</poi.version>
        <zxing.version>3.2.1</zxing.version>
        <slf4j.version>2.0.17</slf4j.version>
        <logback.version>1.3.15</logback.version>
        <tomcat.version>9.0.107</tomcat.version>
        <netty.version>4.1.119.Final</netty.version>
        <httpclient5.version>5.4.3</httpclient5.version>
        <httpcore5.version>5.3.4</httpcore5.version>
        <okhttp.version>4.12.0</okhttp.version>
        <castor.version>1.4.1</castor.version>
        <lombok.version>1.18.36</lombok.version>
        <ffmpeg.version>4.4-1.5.6</ffmpeg.version>
        <bouncycastle.version>1.80</bouncycastle.version>
        <spring-framework.version>5.3.39</spring-framework.version>
        <spring-security.version>5.7.14</spring-security.version>
        <jackson.version>2.15.4</jackson.version>
        <release.url>http://192.168.10.136:8083/repository/maven-releases/</release.url>
        <snapshots.url>http://192.168.10.136:8083/repository/maven-snapshots/</snapshots.url>
        <public.url>http://192.168.10.136:8083/repository/maven-public/</public.url>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <!--<configuration>
                    <archive>
                        &lt;!&ndash; MANIFEST.MF 文件额外输出数据&ndash;&gt;
                        <manifestEntries>
                            <build-time>${maven.build.timestamp} (UTC)</build-time>
                        </manifestEntries>
                    </archive>
                </configuration>-->
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <!--<dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-reload4j</artifactId>
        </dependency>-->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jul-to-slf4j</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.jaloon</groupId>
            <artifactId>spring-webmvc5</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>jakarta.activation</groupId>
                    <artifactId>jakarta.activation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <!-- 项目依赖配置 -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>springloaded</artifactId>
                <version>1.2.8.RELEASE</version>
            </dependency>

            <!-- 替换 org.springframework:spring-webmvc:5.3.39 版本 -->
            <dependency>
                <groupId>io.github.jaloon</groupId>
                <artifactId>spring-webmvc5</artifactId>
                <version>5.3.39.1</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>2.7.18</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-webmvc</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>3.8.1</version>
            </dependency>

            <dependency>
                <groupId>io.github.jaloon</groupId>
                <artifactId>javax-mail</artifactId>
                <version>1.6.7</version>
            </dependency>
            <dependency>
                <groupId>io.github.jaloon</groupId>
                <artifactId>eml-parser</artifactId>
                <version>1.6.7.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sun.mail</groupId>
                        <artifactId>jakarta.mail</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sun.activation</groupId>
                <artifactId>jakarta.activation</artifactId>
                <version>1.2.2</version>
            </dependency>
            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>2.0.2</version>
            </dependency>

            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>4.6</version>
            </dependency>
            <dependency>
                <groupId>io.github.jaloon</groupId>
                <artifactId>jsqlparser-context</artifactId>
                <version>4.6</version>
            </dependency>
            <!-- mybatis 相关组件版本由 mybatis-plus 指定-->
            <!--<dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>3.5.13</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>2.1.1</version>
            </dependency>-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.5.4</version>
            </dependency>
            <dependency>
                <groupId>io.github.jaloon</groupId>
                <artifactId>shardingsphere-jdbc</artifactId>
                <version>5.5.2.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.shardingsphere</groupId>
                        <artifactId>shardingsphere-test-util</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- JDBC Drivers -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>8.4.0</version>
            </dependency>
            <!-- mysql-connector-j 8.4.0 版本依赖的 protobuf 3.25.1 版本有漏洞 CVE-2024-7254-->
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>3.25.7</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc6</artifactId>
                <version>11.2.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ucp</artifactId>
                <version>21.3.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>8.1.2.141</version>
            </dependency>
            <dependency>
                <groupId>org.xerial</groupId>
                <artifactId>sqlite-jdbc</artifactId>
                <version>3.47.2.0</version>
            </dependency>
            <!-- A Pure Java JDBC Driver for Access -->
            <dependency>
                <groupId>net.sf.ucanaccess</groupId>
                <artifactId>ucanaccess</artifactId>
                <version>5.0.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>


            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna</artifactId>
                <version>${jna.version}</version>
            </dependency>
            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna-platform</artifactId>
                <version>${jna.version}</version>
            </dependency>

            <dependency>
                <groupId>net.ifok.image</groupId>
                <artifactId>image4j</artifactId>
                <version>0.7.2</version>
            </dependency>
            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>2.1.4</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>2.2</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>32.1.3-jre</version>
            </dependency>
            <!-- 加解密、证书处理 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk18on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpg-jdk18on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcutil-jdk18on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>
            <!-- Java Objects size estimation. -->
            <dependency>
                <groupId>com.carrotsearch</groupId>
                <artifactId>java-sizeof</artifactId>
                <version>0.0.5</version>
            </dependency>
            <!--
                Resilience4j 是一个为Java设计的轻量级容错库，它受到Netflix Hystrix的启发，但专为函数式编程而设计。
                Resilience4j提供了一系列高阶函数（装饰器），可以增强任何函数接口、lambda表达式或方法引用，
                以实现断路器（Circuit Breaker）、速率限制器（Rate Limiter）、重试（Retry）或隔板（Bulkhead）等容错机制。

                NOTE: Resilience4j 2 requires Java 17.
            -->
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-spring-boot2</artifactId>
                <version>1.7.1</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>2.0.51</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.37</version>
            </dependency>

            <!-- OSHI is a free JNA-based (native) Operating System and Hardware Information library for Java -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>6.4.6</version>
            </dependency>

            <!-- apache commons tools  -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.18.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.12.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.27.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-imaging</artifactId>
                <version>1.0.0-alpha5</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.11.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.17.0</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>3.11.1</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.6.0</version>
            </dependency>

            <!-- 日志输出框架 -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <!-- SLF4J LOG4J-12 relocated to slf4j-reload4j: SLF4J Reload4j Provider -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-reload4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <!-- Log4j2 implemented over SLF4J -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <!-- Apache commons logging 1.2 implemented over SLF4J -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <!-- Jul-to-slf4j includes a java.util.logging (jul) handler, namely SLF4JBridgeHandler, which routes all incoming jul records to the SLF4j API. -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jul-to-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dingtalk</artifactId>
                <version>2.1.99</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibaba-dingtalk-service-sdk</artifactId>
                <version>2.0.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>2.14.5</version>
            </dependency>
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>3.30.2-GA</version>
            </dependency>

            <dependency>
                <groupId>org.jetbrains</groupId>
                <artifactId>annotations</artifactId>
                <version>24.1.0</version>
            </dependency>
            <!--查找子类或接口的实现类-->
            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>0.10.2</version>
            </dependency>

            <!-- 自动生成api接口文档 -->
            <dependency>
                <groupId>com.github.shalousun</groupId>
                <artifactId>smart-doc</artifactId>
                <version>2.7.7</version>
            </dependency>
            <dependency>
                <groupId>io.github.yedaxia</groupId>
                <artifactId>japidocs</artifactId>
                <version>1.4.4</version>
            </dependency>

            <!-- 访问共享盘符 -->
            <dependency>
                <groupId>org.codelibs</groupId>
                <artifactId>jcifs</artifactId>
                <version>2.1.39</version>
            </dependency>

            <!-- Excel电子表格读写处理 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>net.sourceforge.jexcelapi</groupId>
                <artifactId>jxl</artifactId>
                <version>2.6.12</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-spring-boot-starter</artifactId>
                <version>4.1.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.validation</groupId>
                        <artifactId>validation-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-webmvc</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- poi-tl版本需与poi版本匹配-->
            <dependency>
                <groupId>com.deepoove</groupId>
                <artifactId>poi-tl</artifactId>
                <version>1.10.6</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.github.draco1023</groupId>
                <artifactId>poi-tl-ext</artifactId>
                <version>0.4.23</version>
            </dependency>

            <dependency>
                <groupId>com.opencsv</groupId>
                <artifactId>opencsv</artifactId>
                <version>3.9</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-csv</artifactId>
                <version>1.10.0</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-core</artifactId>
                <version>${spring-security.version}</version>
            </dependency>
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itextpdf</artifactId>
                <version>5.5.13.4</version>
            </dependency>
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itext-asian</artifactId>
                <version>5.2.0</version>
            </dependency>
            <dependency>
                <groupId>org.jfree</groupId>
                <artifactId>jfreechart</artifactId>
                <version>1.0.19</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-crypto</artifactId>
                <version>${spring-security.version}</version>
            </dependency>
            <dependency>
                <groupId>org.docx4j</groupId>
                <artifactId>docx4j-ImportXHTML</artifactId>
                <version>8.3.8</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>jakarta.activation</groupId>
                        <artifactId>jakarta.activation-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>xerces</groupId>
                <artifactId>xercesImpl</artifactId>
                <version>2.12.2</version>
            </dependency>

            <dependency>
                <groupId>org.ini4j</groupId>
                <artifactId>ini4j</artifactId>
                <version>0.5.4</version>
            </dependency>

            <!-- xml bean 对象装换 -->
            <dependency>
                <groupId>org.codehaus.castor</groupId>
                <artifactId>castor-core</artifactId>
                <version>${castor.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.codehaus.castor</groupId>
                <artifactId>castor-xml</artifactId>
                <version>${castor.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 字符编码格式检测 -->
            <dependency>
                <groupId>com.github.albfernandez</groupId>
                <artifactId>juniversalchardet</artifactId>
                <version>2.5.0</version>
            </dependency>
            <!-- 图形验证码 -->
            <dependency>
                <groupId>com.github.penggle</groupId>
                <artifactId>kaptcha</artifactId>
                <version>2.3.2</version>
            </dependency>
            <!-- 条形码/二维码等编解码处理-->
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>${zxing.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>${zxing.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.abel533</groupId>
                <artifactId>ECharts</artifactId>
                <version>3.0.0.6</version>
            </dependency>
            <dependency>
                <groupId>net.dongliu</groupId>
                <artifactId>apk-parser</artifactId>
                <version>2.6.10</version>
            </dependency>

            <dependency>
                <groupId>org.openpnp</groupId>
                <artifactId>opencv</artifactId>
                <version>4.5.1-2</version>
            </dependency>
            <!--FFmpeg的jar包 -->
            <dependency>
                <groupId>org.bytedeco</groupId>
                <artifactId>javacpp</artifactId>
                <version>1.5.6</version>
            </dependency>
            <dependency>
                <groupId>org.bytedeco</groupId>
                <artifactId>javacv</artifactId>
                <version>1.5.6</version>
            </dependency>
            <dependency>
                <groupId>org.bytedeco</groupId>
                <artifactId>ffmpeg</artifactId>
                <version>${ffmpeg.version}</version>
                <classifier>windows-x86_64</classifier>
            </dependency>
            <dependency>
                <groupId>org.bytedeco</groupId>
                <artifactId>ffmpeg</artifactId>
                <version>${ffmpeg.version}</version>
                <classifier>linux-x86_64</classifier>
            </dependency>
            <dependency>
                <groupId>org.bytedeco</groupId>
                <artifactId>ffmpeg</artifactId>
                <version>${ffmpeg.version}</version>
                <classifier>linux-arm64</classifier>
            </dependency>

            <!--LZMA压缩算法-->
            <dependency>
                <groupId>com.github.jponge</groupId>
                <artifactId>lzma-java</artifactId>
                <version>1.3</version>
            </dependency>

            <dependency>
                <groupId>org.antlr</groupId>
                <artifactId>antlr-runtime</artifactId>
                <version>3.5.3</version>
            </dependency>

            <dependency>
                <groupId>org.tmatesoft.svnkit</groupId>
                <artifactId>svnkit</artifactId>
                <version>1.9.3</version>
            </dependency>

            <!-- Jackson Core -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <!-- Jackson Databind -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <!-- Jackson Annotations -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <!-- deploy 配置 -->
    <!--<distributionManagement>
        <repository>
            &lt;!&ndash; nexus server id &ndash;&gt;
            <id>maven-release</id>
            <name>tipray release repo</name>
            <url>${release.url}</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>tipray snapshots repo</name>
            <url>${snapshots.url}</url>
        </snapshotRepository>
    </distributionManagement>-->

    <!-- 仓库配置 -->
    <!--<repositories>
        <repository>
            <id>public</id>
            <name>tipray nexus</name>
            <url>${public.url}</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>tipray nexus</name>
            <url>${public.url}</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>-->

</project>
