#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x0000000000000000, pid=30456, tid=0x0000000000007858
#
# JRE version: OpenJDK Runtime Environment (8.0_412-b08) (build 1.8.0_412-b08)
# Java VM: OpenJDK 64-Bit Server VM (25.412-b08 mixed mode windows-amd64 compressed oops)
# Problematic frame:
# C  0x0000000000000000
#
# Failed to write core dump. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://github.com/adoptium/adoptium-support/issues
#

---------------  T H R E A D  ---------------

Current thread (0x0000027afa721000):  JavaThread "scheduled-pool-t-3" [_thread_in_Java, id=30808, stack(0x000000836a600000,0x000000836a700000)]

siginfo: ExceptionCode=0xc0000005, ExceptionInformation=0x0000000000000008 0x0000000000000000

Registers:
RAX=0x00000005de08e3c8, RBX=0x0000027aeb6d1650, RCX=0x0000000000000040, RDX=0x00000005cca6ab60
RSP=0x000000836a6fc138, RBP=0x000000836a6fc5b8, RSI=0x00000005de08e3f0, RDI=0x00000007c000d810
R8 =0x00000005de08e3f0, R9 =0x0000000000000000, R10=0x0000000054de5070, R11=0x00000005da40e2d8
R12=0x0000000000000000, R13=0x00000005c4031ba8, R14=0x00000005c40769e8, R15=0x0000027afa721000
RIP=0x0000000000000000, EFLAGS=0x0000000000010206

Top of Stack: (sp=0x000000836a6fc138)
0x000000836a6fc138:   0000027abbcad274 00000005def56418
0x000000836a6fc148:   00000005c4031e50 00000005c411a920
0x000000836a6fc158:   00000007195547f8 00000005c4031ba8
0x000000836a6fc168:   00000005c4b4f4b0 000000836a6fc5b8
0x000000836a6fc178:   0000027aba75d0b4 00000005c4b4f4b0
0x000000836a6fc188:   00000005cca6ab60 00000005de08e3c8
0x000000836a6fc198:   00000005de08e3f0 00000005c4031e90
0x000000836a6fc1a8:   00000007195547b8 000000836a6fc5b8
0x000000836a6fc1b8:   0000027abbcad714 0000000700000000
0x000000836a6fc1c8:   00000007195547f8 00000005c4031ba8
0x000000836a6fc1d8:   00000005c4031ba8 00000005de08e3c8
0x000000836a6fc1e8:   00000005cca6ab60 00000007195547f8
0x000000836a6fc1f8:   0000027abcc4a984 000000836a6fc5b8
0x000000836a6fc208:   0000027abc22bc8c 00000005da40e2d8
0x000000836a6fc218:   00000005c4031ba8 00000005c41dcdd8
0x000000836a6fc228:   0000000000000000 00000005c40362b0 

Instructions: (pc=0x0000000000000000)
0xffffffffffffffe0:   


Register to memory mapping:

RAX=0x00000005de08e3c8 is an oop
java.lang.invoke.DirectMethodHandle$Special 
 - klass: 'java/lang/invoke/DirectMethodHandle$Special'
RBX=0x0000027aeb6d1650 is pointing into metadata
RCX=0x0000000000000040 is an unknown value
RDX=0x00000005cca6ab60 is an oop
com.sun.proxy.$Proxy222 
 - klass: 'com/sun/proxy/$Proxy222'
RSP=0x000000836a6fc138 is pointing into the stack for thread: 0x0000027afa721000
RBP=0x000000836a6fc5b8 is pointing into the stack for thread: 0x0000027afa721000
RSI=0x00000005de08e3f0 is an oop
java.lang.invoke.MemberName 
 - klass: 'java/lang/invoke/MemberName'
RDI=0x00000007c000d810 is pointing into metadata
R8 =0x00000005de08e3f0 is an oop
java.lang.invoke.MemberName 
 - klass: 'java/lang/invoke/MemberName'
R9 =0x0000000000000000 is an unknown value
R10=0x0000000054de5070 is an unknown value
R11=0x00000005da40e2d8 is an oop
java.lang.invoke.LambdaForm 
 - klass: 'java/lang/invoke/LambdaForm'
R12=0x0000000000000000 is an unknown value
R13=0x00000005c4031ba8 is an oop
java.lang.invoke.MethodType 
 - klass: 'java/lang/invoke/MethodType'
R14=0x00000005c40769e8 is an oop
java.lang.Class 
 - klass: 'java/lang/Class'
R15=0x0000027afa721000 is a thread


Stack: [0x000000836a600000,0x000000836a700000],  sp=0x000000836a6fc138,  free space=1008k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)


---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
  0x0000027a808cf800 JavaThread "clientOutboundChannel-146" [_thread_blocked, id=3920, stack(0x000000836c800000,0x000000836c900000)]
  0x0000027a808d2000 JavaThread "ThreadPoolTaskScheduler-5" [_thread_blocked, id=28472, stack(0x000000836c300000,0x000000836c400000)]
  0x0000027a808bb000 JavaThread "WebSocket background processing" daemon [_thread_blocked, id=29288, stack(0x0000008367f00000,0x0000008368000000)]
  0x0000027afd388800 JavaThread "ftp-exec-1" [_thread_blocked, id=30056, stack(0x0000008370e00000,0x0000008370f00000)]
  0x0000027a808d0800 JavaThread "rebel-intermediate-collector" daemon [_thread_blocked, id=27500, stack(0x0000008367300000,0x0000008367400000)]
  0x0000027a8089a800 JavaThread "http-nio-28080-exec-11" daemon [_thread_blocked, id=28176, stack(0x0000008366400000,0x0000008366500000)]
  0x0000027afd395800 JavaThread "MessageBroker-16" [_thread_blocked, id=6040, stack(0x0000008366500000,0x0000008366600000)]
  0x0000027afd395000 JavaThread "MessageBroker-15" [_thread_blocked, id=30672, stack(0x000000836cf00000,0x000000836d000000)]
  0x0000027afd386000 JavaThread "MessageBroker-14" [_thread_blocked, id=12512, stack(0x0000008366600000,0x0000008366700000)]
  0x0000027afd394000 JavaThread "MessageBroker-13" [_thread_blocked, id=19432, stack(0x000000836ce00000,0x000000836cf00000)]
  0x0000027afd38e000 JavaThread "MessageBroker-12" [_thread_blocked, id=27172, stack(0x000000836cd00000,0x000000836ce00000)]
  0x0000027afd393800 JavaThread "MessageBroker-11" [_thread_blocked, id=30280, stack(0x000000836cc00000,0x000000836cd00000)]
  0x0000027afd392800 JavaThread "MessageBroker-10" [_thread_blocked, id=32496, stack(0x000000836cb00000,0x000000836cc00000)]
  0x0000027afd38d000 JavaThread "MessageBroker-9" [_thread_blocked, id=31224, stack(0x000000836ca00000,0x000000836cb00000)]
  0x0000027afd38c800 JavaThread "MessageBroker-8" [_thread_blocked, id=32704, stack(0x000000836c900000,0x000000836ca00000)]
  0x0000027afd385000 JavaThread "MessageBroker-7" [_thread_blocked, id=6416, stack(0x0000008366800000,0x0000008366900000)]
  0x0000027afd38a000 JavaThread "MessageBroker-6" [_thread_blocked, id=32968, stack(0x000000836c500000,0x000000836c600000)]
  0x0000027afd37f800 JavaThread "AWT-Windows" daemon [_thread_in_native, id=22668, stack(0x0000008366900000,0x0000008366a00000)]
  0x0000027afd377000 JavaThread "MessageBroker-5" [_thread_blocked, id=26776, stack(0x0000008366300000,0x0000008366400000)]
  0x0000027afd37e000 JavaThread "Java2D Disposer" daemon [_thread_blocked, id=30368, stack(0x0000008365700000,0x0000008365800000)]
  0x0000027afd37d800 JavaThread "MessageBroker-3" [_thread_blocked, id=26216, stack(0x0000008364300000,0x0000008364400000)]
  0x0000027afd365000 JavaThread "MessageBroker-4" [_thread_blocked, id=30200, stack(0x0000008361900000,0x0000008361a00000)]
  0x0000027afd37c000 JavaThread "MessageBroker-2" [_thread_blocked, id=29836, stack(0x0000008366200000,0x0000008366300000)]
  0x0000027afbd8e000 JavaThread "HikariPool-JdbcPersistRepo housekeeper" daemon [_thread_blocked, id=24736, stack(0x000000836c400000,0x000000836c500000)]
  0x0000027afbd88800 JavaThread "HikariPool-ds_9001 housekeeper" daemon [_thread_blocked, id=27092, stack(0x000000836c200000,0x000000836c300000)]
  0x0000027afbd87800 JavaThread "server-log-pool-t-5" [_thread_blocked, id=30204, stack(0x000000836c100000,0x000000836c200000)]
  0x0000027afbd87000 JavaThread "server-log-pool-t-4" [_thread_blocked, id=31628, stack(0x000000836b100000,0x000000836b200000)]
  0x0000027afbd86000 JavaThread "pool-255-thread-1" [_thread_blocked, id=25640, stack(0x000000836bf00000,0x000000836c000000)]
  0x0000027afbd8a000 JavaThread "modules-pool-t-19" [_thread_blocked, id=28056, stack(0x000000836be00000,0x000000836bf00000)]
  0x0000027afbd84000 JavaThread "modules-pool-t-20" [_thread_blocked, id=32060, stack(0x000000836bd00000,0x000000836be00000)]
  0x0000027afbd8b000 JavaThread "modules-pool-t-18" [_thread_blocked, id=11252, stack(0x000000836bc00000,0x000000836bd00000)]
  0x0000027afbd85800 JavaThread "modules-pool-t-17" [_thread_blocked, id=28588, stack(0x000000836bb00000,0x000000836bc00000)]
  0x0000027afbd82000 JavaThread "RMI Reaper" [_thread_blocked, id=26400, stack(0x000000836ba00000,0x000000836bb00000)]
  0x0000027afbd7c000 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=27704, stack(0x000000836b900000,0x000000836ba00000)]
  0x0000027afbd7d800 JavaThread "RMI TCP Accept-6666" daemon [_thread_in_native, id=30228, stack(0x000000836b800000,0x000000836b900000)]
  0x0000027afbd81800 JavaThread "JNA Cleaner" daemon [_thread_blocked, id=30592, stack(0x000000836b500000,0x000000836b600000)]
  0x0000027afbd73800 JavaThread "modules-pool-t-16" [_thread_blocked, id=26544, stack(0x000000836b400000,0x000000836b500000)]
  0x0000027afbd7a800 JavaThread "GC Daemon" daemon [_thread_blocked, id=33784, stack(0x000000836b200000,0x000000836b300000)]
  0x0000027afbd79000 JavaThread "RMI RenewClean-[127.0.0.1:49190]" daemon [_thread_blocked, id=29788, stack(0x000000836b300000,0x000000836b400000)]
  0x0000027afbd78000 JavaThread "DestroyJavaVM" [_thread_blocked, id=1988, stack(0x0000008361e00000,0x0000008361f00000)]
  0x0000027afbd72800 JavaThread "offline-strategy-extend-task-3" [_thread_blocked, id=25988, stack(0x000000836b000000,0x000000836b100000)]
  0x0000027afbd79800 JavaThread "offline-strategy-extend-task-2" [_thread_blocked, id=30728, stack(0x000000836ad00000,0x000000836ae00000)]
  0x0000027afbd76800 JavaThread "offline-strategy-extend-task-1" [_thread_blocked, id=22052, stack(0x000000836af00000,0x000000836b000000)]
  0x0000027afbd74000 JavaThread "scheduled-pool-t-10" [_thread_blocked, id=32092, stack(0x000000836ae00000,0x000000836af00000)]
  0x0000027aee539000 JavaThread "scheduled-pool-t-9" [_thread_blocked, id=27900, stack(0x000000836ac00000,0x000000836ad00000)]
  0x0000027afa722800 JavaThread "scheduled-pool-t-8" [_thread_blocked, id=27808, stack(0x000000836ab00000,0x000000836ac00000)]
  0x0000027afa71d000 JavaThread "scheduled-pool-t-7" [_thread_blocked, id=21572, stack(0x000000836aa00000,0x000000836ab00000)]
  0x0000027afa71b000 JavaThread "scheduled-pool-t-6" [_thread_blocked, id=21372, stack(0x000000836a900000,0x000000836aa00000)]
  0x0000027afa722000 JavaThread "scheduled-pool-t-5" [_thread_blocked, id=21816, stack(0x000000836a800000,0x000000836a900000)]
  0x0000027afa71a000 JavaThread "scheduled-pool-t-4" [_thread_blocked, id=28140, stack(0x000000836a700000,0x000000836a800000)]
=>0x0000027afa721000 JavaThread "scheduled-pool-t-3" [_thread_in_Java, id=30808, stack(0x000000836a600000,0x000000836a700000)]
  0x0000027afa720800 JavaThread "scheduled-pool-t-2" [_thread_blocked, id=30244, stack(0x000000836a500000,0x000000836a600000)]
  0x0000027afa71f000 JavaThread "scheduled-pool-t-1" [_thread_blocked, id=30760, stack(0x000000836a400000,0x000000836a500000)]
  0x0000027afa71b800 JavaThread "modules-pool-t-15" [_thread_blocked, id=25708, stack(0x000000836a300000,0x000000836a400000)]
  0x0000027afa711800 JavaThread "ThreadPoolTaskScheduler-4" [_thread_blocked, id=31940, stack(0x000000836a200000,0x000000836a300000)]
  0x0000027afa717000 JavaThread "pool-192-thread-1" [_thread_in_native, id=8632, stack(0x000000836a100000,0x000000836a200000)]
  0x0000027afa716000 JavaThread "ThreadPoolTaskScheduler-3" [_thread_blocked, id=32144, stack(0x000000836a000000,0x000000836a100000)]
  0x0000027afa715800 JavaThread "modules-pool-t-14" [_thread_blocked, id=15824, stack(0x0000008369f00000,0x000000836a000000)]
  0x0000027afa714800 JavaThread "ThreadPoolTaskScheduler-2" [_thread_blocked, id=6100, stack(0x0000008369e00000,0x0000008369f00000)]
  0x0000027afa713000 JavaThread "ThreadPoolTaskScheduler-1" [_thread_blocked, id=27160, stack(0x0000008369d00000,0x0000008369e00000)]
  0x0000027afa707000 JavaThread "modules-pool-t-13" [_thread_blocked, id=24432, stack(0x0000008369c00000,0x0000008369d00000)]
  0x0000027afa70d800 JavaThread "modules-pool-t-12" [_thread_blocked, id=32812, stack(0x0000008369b00000,0x0000008369c00000)]
  0x0000027afa70c800 JavaThread "modules-pool-t-11" [_thread_blocked, id=23416, stack(0x0000008369a00000,0x0000008369b00000)]
  0x0000027afa709800 JavaThread "modules-pool-t-10" [_thread_blocked, id=25268, stack(0x0000008369900000,0x0000008369a00000)]
  0x0000027afa705000 JavaThread "modules-pool-t-9" [_thread_blocked, id=31280, stack(0x0000008369800000,0x0000008369900000)]
  0x0000027afa70c000 JavaThread "modules-pool-t-8" [_thread_blocked, id=26580, stack(0x0000008369700000,0x0000008369800000)]
  0x0000027afa709000 JavaThread "modules-pool-t-7" [_thread_blocked, id=29520, stack(0x0000008369600000,0x0000008369700000)]
  0x0000027afa708000 JavaThread "modules-pool-t-6" [_thread_blocked, id=17012, stack(0x0000008369500000,0x0000008369600000)]
  0x0000027afa429800 JavaThread "modules-pool-t-5" [_thread_blocked, id=1448, stack(0x0000008369400000,0x0000008369500000)]
  0x0000027afa430800 JavaThread "modules-pool-t-4" [_thread_blocked, id=30620, stack(0x0000008369300000,0x0000008369400000)]
  0x0000027afa429000 JavaThread "modules-pool-t-3" [_thread_blocked, id=29896, stack(0x0000008369200000,0x0000008369300000)]
  0x0000027afa42f000 JavaThread "Thread-545" daemon [_thread_in_native, id=31396, stack(0x0000008369100000,0x0000008369200000)]
  0x0000027afa42d800 JavaThread "server-log-pool-t-3" [_thread_blocked, id=31972, stack(0x0000008369000000,0x0000008369100000)]
  0x0000027afa42c000 JavaThread "server-log-pool-t-2" [_thread_blocked, id=8060, stack(0x0000008368f00000,0x0000008369000000)]
  0x0000027afa42e800 JavaThread "server-log-pool-t-1" [_thread_blocked, id=26848, stack(0x0000008368d00000,0x0000008368e00000)]
  0x0000027afa422000 JavaThread "Thread-544" daemon [_thread_blocked, id=29052, stack(0x0000008368e00000,0x0000008368f00000)]
  0x0000027afa426800 JavaThread "modules-pool-t-2" [_thread_blocked, id=25956, stack(0x0000008368b00000,0x0000008368c00000)]
  0x0000027afa425800 JavaThread "modules-pool-t-1" [_thread_blocked, id=30952, stack(0x0000008368c00000,0x0000008368d00000)]
  0x0000027af7756000 JavaThread "Thread-543" daemon [_thread_in_native, id=22180, stack(0x0000008368a00000,0x0000008368b00000)]
  0x0000027af7756800 JavaThread "sync-scheduler-pool-t-1" [_thread_blocked, id=29324, stack(0x0000008368900000,0x0000008368a00000)]
  0x0000027af7758800 JavaThread "http-nio-28080-Acceptor" daemon [_thread_in_native, id=24932, stack(0x0000008368800000,0x0000008368900000)]
  0x0000027af7751000 JavaThread "http-nio-28080-Poller" daemon [_thread_in_native, id=30488, stack(0x0000008368700000,0x0000008368800000)]
  0x0000027af7753000 JavaThread "http-nio-28080-exec-10" daemon [_thread_blocked, id=25468, stack(0x0000008368600000,0x0000008368700000)]
  0x0000027af7757800 JavaThread "http-nio-28080-exec-9" daemon [_thread_blocked, id=29904, stack(0x0000008368500000,0x0000008368600000)]
  0x0000027af7753800 JavaThread "http-nio-28080-exec-8" daemon [_thread_blocked, id=30976, stack(0x0000008368400000,0x0000008368500000)]
  0x0000027af7749800 JavaThread "http-nio-28080-exec-7" daemon [_thread_blocked, id=31332, stack(0x0000008368300000,0x0000008368400000)]
  0x0000027af7750800 JavaThread "http-nio-28080-exec-6" daemon [_thread_blocked, id=31236, stack(0x0000008368200000,0x0000008368300000)]
  0x0000027af774f800 JavaThread "http-nio-28080-exec-5" daemon [_thread_blocked, id=6204, stack(0x0000008368100000,0x0000008368200000)]
  0x0000027af7748000 JavaThread "http-nio-28080-exec-4" daemon [_thread_blocked, id=30836, stack(0x0000008368000000,0x0000008368100000)]
  0x0000027af7746000 JavaThread "http-nio-28080-exec-2" daemon [_thread_blocked, id=31492, stack(0x0000008367e00000,0x0000008367f00000)]
  0x0000027af7748800 JavaThread "http-nio-28080-exec-1" daemon [_thread_blocked, id=29020, stack(0x0000008367d00000,0x0000008367e00000)]
  0x0000027af773d000 JavaThread "MessageBroker-1" [_thread_blocked, id=27524, stack(0x0000008367c00000,0x0000008367d00000)]
  0x0000027af7741800 JavaThread "idle-connection-evictor-1" daemon [_thread_blocked, id=23988, stack(0x0000008366a00000,0x0000008366b00000)]
  0x0000027aee53d800 JavaThread "idle-connection-evictor-1" daemon [_thread_blocked, id=21268, stack(0x0000008366700000,0x0000008366800000)]
  0x0000027aee535800 JavaThread "idle-connection-evictor-1" daemon [_thread_blocked, id=29360, stack(0x0000008365500000,0x0000008365600000)]
  0x0000027aee532800 JavaThread "idle-connection-evictor-1" daemon [_thread_blocked, id=22328, stack(0x0000008361a00000,0x0000008361b00000)]
  0x0000027aee522000 JavaThread "pool-5-thread-1" [_thread_blocked, id=2156, stack(0x0000008361800000,0x0000008361900000)]
  0x0000027aecadc800 JavaThread "websocket-pool-t-4" [_thread_blocked, id=26588, stack(0x0000008367a00000,0x0000008367b00000)]
  0x0000027aecadb000 JavaThread "HikariPool-master housekeeper" daemon [_thread_blocked, id=32164, stack(0x0000008367800000,0x0000008367900000)]
  0x0000027ae25aa800 JavaThread "mysql-cj-abandoned-connection-cleanup" daemon [_thread_blocked, id=20120, stack(0x0000008367700000,0x0000008367800000)]
  0x0000027ae9148000 JavaThread "Pure-Timer-1" [_thread_blocked, id=31480, stack(0x0000008367600000,0x0000008367700000)]
  0x0000027ae25a6000 JavaThread "mybatis-pool-t-1" [_thread_in_native, id=32784, stack(0x0000008367500000,0x0000008367600000)]
  0x0000027ae25a7800 JavaThread "websocket-pool-t-3" [_thread_blocked, id=7588, stack(0x0000008367400000,0x0000008367500000)]
  0x0000027ae259c800 JavaThread "socket-pool-t-3" [_thread_in_native, id=23536, stack(0x0000008367200000,0x0000008367300000)]
  0x0000027ae25a0800 JavaThread "socket-pool-t-2" [_thread_blocked, id=27460, stack(0x0000008367100000,0x0000008367200000)]
  0x0000027ae25a3800 JavaThread "socket-pool-t-1" [_thread_blocked, id=25492, stack(0x0000008367000000,0x0000008367100000)]
  0x0000027ae259f800 JavaThread "container-0" [_thread_blocked, id=6696, stack(0x0000008366f00000,0x0000008367000000)]
  0x0000027ae25a3000 JavaThread "Catalina-utility-2" [_thread_blocked, id=28136, stack(0x0000008366e00000,0x0000008366f00000)]
  0x0000027ae25a2000 JavaThread "Catalina-utility-1" [_thread_blocked, id=21272, stack(0x0000008366d00000,0x0000008366e00000)]
  0x0000027aeae35800 JavaThread "websocket-pool-t-2" [_thread_blocked, id=32268, stack(0x0000008366c00000,0x0000008366d00000)]
  0x0000027aeae33000 JavaThread "websocket-pool-t-1" [_thread_blocked, id=31560, stack(0x0000008366b00000,0x0000008366c00000)]
  0x0000027ae3965000 JavaThread "AsyncAppender-Worker-ASYNC_DOWNLOAD_FILE" daemon [_thread_blocked, id=27620, stack(0x0000008366100000,0x0000008366200000)]
  0x0000027ae395b000 JavaThread "AsyncAppender-Worker-ASYNC_FTP_FILE" daemon [_thread_blocked, id=21172, stack(0x0000008366000000,0x0000008366100000)]
  0x0000027ae395d000 JavaThread "AsyncAppender-Worker-ASYNC_DB_EDIT_FILE" daemon [_thread_blocked, id=6104, stack(0x0000008365f00000,0x0000008366000000)]
  0x0000027ae2158000 JavaThread "AsyncAppender-Worker-ASYNC_DB_FILE" daemon [_thread_blocked, id=28872, stack(0x0000008365e00000,0x0000008365f00000)]
  0x0000027ae2157800 JavaThread "AsyncAppender-Worker-ASYNC_VNC_FILE" daemon [_thread_blocked, id=29972, stack(0x0000008365d00000,0x0000008365e00000)]
  0x0000027ae2159000 JavaThread "AsyncAppender-Worker-ASYNC_WEBSOCKET_FILE" daemon [_thread_blocked, id=27752, stack(0x0000008365b00000,0x0000008365c00000)]
  0x0000027ae2156800 JavaThread "AsyncAppender-Worker-ASYNC_SOCKET_FILE" daemon [_thread_blocked, id=33320, stack(0x0000008365600000,0x0000008365700000)]
  0x0000027ae2156000 JavaThread "AsyncAppender-Worker-ASYNC_HTTP_FILE" daemon [_thread_blocked, id=23600, stack(0x0000008365a00000,0x0000008365b00000)]
  0x0000027ae2155000 JavaThread "AsyncAppender-Worker-ASYNC_ERROR_FILE" daemon [_thread_blocked, id=3080, stack(0x0000008365900000,0x0000008365a00000)]
  0x0000027ae215b800 JavaThread "AsyncAppender-Worker-ASYNC_INFO_FILE" daemon [_thread_blocked, id=31436, stack(0x0000008365800000,0x0000008365900000)]
  0x0000027ae215a000 JavaThread "AsyncAppender-Worker-ASYNC_stdout" daemon [_thread_blocked, id=33580, stack(0x0000008365400000,0x0000008365500000)]
  0x0000027ae395f800 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=30972, stack(0x0000008365c00000,0x0000008365d00000)]
  0x0000027ae2154800 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=29096, stack(0x0000008365200000,0x0000008365300000)]
  0x0000027ae2152000 JavaThread "Service Thread" daemon [_thread_blocked, id=21844, stack(0x0000008365100000,0x0000008365200000)]
  0x0000027ae214f000 JavaThread "C1 CompilerThread11" daemon [_thread_blocked, id=6152, stack(0x0000008365000000,0x0000008365100000)]
  0x0000027ae2286800 JavaThread "C1 CompilerThread10" daemon [_thread_blocked, id=26768, stack(0x0000008364f00000,0x0000008365000000)]
  0x0000027ae2281800 JavaThread "C1 CompilerThread9" daemon [_thread_blocked, id=31848, stack(0x0000008364e00000,0x0000008364f00000)]
  0x0000027ae2280800 JavaThread "C1 CompilerThread8" daemon [_thread_blocked, id=4460, stack(0x0000008364d00000,0x0000008364e00000)]
  0x0000027ae221b000 JavaThread "C2 CompilerThread7" daemon [_thread_blocked, id=31708, stack(0x0000008364c00000,0x0000008364d00000)]
  0x0000027ae221a800 JavaThread "C2 CompilerThread6" daemon [_thread_blocked, id=17872, stack(0x0000008364b00000,0x0000008364c00000)]
  0x0000027ae2277800 JavaThread "C2 CompilerThread5" daemon [_thread_blocked, id=28316, stack(0x0000008364a00000,0x0000008364b00000)]
  0x0000027ae2276800 JavaThread "C2 CompilerThread4" daemon [_thread_blocked, id=22124, stack(0x0000008364900000,0x0000008364a00000)]
  0x0000027ae2276000 JavaThread "C2 CompilerThread3" daemon [_thread_blocked, id=20772, stack(0x0000008364800000,0x0000008364900000)]
  0x0000027ae2275000 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=29816, stack(0x0000008364700000,0x0000008364800000)]
  0x0000027ae2274800 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=26268, stack(0x0000008364600000,0x0000008364700000)]
  0x0000027ae2280000 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=31732, stack(0x0000008364500000,0x0000008364600000)]
  0x0000027ae214e000 JavaThread "rebel-messaging-executor-61" daemon [_thread_blocked, id=27296, stack(0x0000008364400000,0x0000008364500000)]
  0x0000027ae214f800 JavaThread "rebel-build-info" daemon [_thread_in_native, id=26556, stack(0x0000008363d00000,0x0000008363e00000)]
  0x0000027ae2152800 JavaThread "rebel-change-detector-thread" daemon [_thread_blocked, id=18424, stack(0x0000008363c00000,0x0000008363d00000)]
  0x0000027ae2150800 JavaThread "rebel-debugger-thread" daemon [_thread_blocked, id=25088, stack(0x0000008363b00000,0x0000008363c00000)]
  0x0000027ae2153800 JavaThread "rebel-debugger-attach-notifier" daemon [_thread_blocked, id=21576, stack(0x0000008363a00000,0x0000008363b00000)]
  0x0000027ae047d000 JavaThread "rebel-heartbeat-thread" daemon [_thread_blocked, id=30020, stack(0x0000008363900000,0x0000008363a00000)]
  0x0000027ae047a800 JavaThread "rebel-redeploy-thread" daemon [_thread_blocked, id=21616, stack(0x0000008363800000,0x0000008363900000)]
  0x0000027ae0478000 JavaThread "rebel-leaseManager-1" daemon [_thread_blocked, id=32820, stack(0x0000008363700000,0x0000008363800000)]
  0x0000027ae047a000 JavaThread "rebel-IDENotificationsImpl-PostCycle" daemon [_thread_blocked, id=30784, stack(0x0000008363500000,0x0000008363600000)]
  0x0000027ae0476800 JavaThread "rebel-weak-reaper" daemon [_thread_blocked, id=27236, stack(0x0000008363400000,0x0000008363500000)]
  0x0000027ae0477800 JavaThread "rebel-fsnotify-OutputReader" daemon [_thread_in_native, id=17740, stack(0x0000008364200000,0x0000008364300000)]
  0x0000027ae047c000 JavaThread "rebel-fsnotify-OutputReader" daemon [_thread_in_native, id=25572, stack(0x0000008364100000,0x0000008364200000)]
  0x0000027ae0479000 JavaThread "rebel-fsnotify-ShutdownOnTermination" daemon [_thread_in_native, id=29064, stack(0x0000008363e00000,0x0000008363f00000)]
  0x0000027ae0476000 JavaThread "rebel-CacheKeepAlive" daemon [_thread_blocked, id=25164, stack(0x0000008364000000,0x0000008364100000)]
  0x0000027ae047b800 JavaThread "rebel-logger" daemon [_thread_blocked, id=30768, stack(0x0000008363f00000,0x0000008364000000)]
  0x0000027addc43800 JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=31020, stack(0x0000008363300000,0x0000008363400000)]
  0x0000027addc3e800 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=21244, stack(0x0000008363200000,0x0000008363300000)]
  0x0000027addc2d800 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=29000, stack(0x0000008363100000,0x0000008363200000)]
  0x0000027addbf5800 JavaThread "Attach Listener" daemon [_thread_blocked, id=30324, stack(0x0000008363000000,0x0000008363100000)]
  0x0000027addbf4800 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=31256, stack(0x0000008362f00000,0x0000008363000000)]
  0x0000027adbf4a800 JavaThread "Finalizer" daemon [_thread_blocked, id=32036, stack(0x0000008362e00000,0x0000008362f00000)]
  0x0000027adbf48000 JavaThread "Reference Handler" daemon [_thread_blocked, id=27244, stack(0x0000008362d00000,0x0000008362e00000)]

Other Threads:
  0x0000027addb82800 VMThread [stack: 0x0000008362c00000,0x0000008362d00000] [id=336]
  0x0000027ae37ab800 WatcherThread [stack: 0x0000008365300000,0x0000008365400000] [id=20504]

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

heap address: 0x00000005c4000000, size: 8128 MB, Compressed Oops mode: Zero based, Oop shift amount: 3
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x00000007c0000000

Heap:
 PSYoungGen      total 2037248K, used 43448K [0x0000000716b00000, 0x00000007a5480000, 0x00000007c0000000)
  eden space 1741312K, 2% used [0x0000000716b00000,0x000000071956e108,0x0000000780f80000)
  from space 295936K, 0% used [0x0000000780f80000,0x0000000780f80000,0x0000000793080000)
  to   space 297472K, 0% used [0x0000000793200000,0x0000000793200000,0x00000007a5480000)
 ParOldGen       total 1115648K, used 669656K [0x00000005c4000000, 0x0000000608180000, 0x0000000716b00000)
  object space 1115648K, 60% used [0x00000005c4000000,0x00000005ecdf6000,0x0000000608180000)
 Metaspace       used 319094K, capacity 334761K, committed 336624K, reserved 1357824K
  class space    used 25926K, capacity 28277K, committed 28696K, reserved 1048576K

Card table byte_map: [0x0000027ac8c40000,0x0000027ac9c30000] byte_map_base: 0x0000027ac5e20000

Marking Bits: (ParMarkBitMap*) 0x00000000551a0830
 Begin Bits: [0x0000027aca6d0000, 0x0000027ad25d0000)
 End Bits:   [0x0000027ad25d0000, 0x0000027ada4d0000)

Polling page: 0x0000027ab5de0000

CodeCache: size=245760Kb used=67450Kb max_used=69540Kb free=178309Kb
 bounds [0x0000027ab9880000, 0x0000027abdc70000, 0x0000027ac8880000]
 total_blobs=39392 nmethods=38294 adapters=1011
 compilation: enabled

Compilation events (10 events):
Event: 3923.777 Thread 0x0000027ae2281800 nmethod 52281 0x0000027abd62b150 code [0x0000027abd62b340, 0x0000027abd62b880]
Event: 3923.777 Thread 0x0000027ae214f000 nmethod 52275 0x0000027abbded710 code [0x0000027abbdedb20, 0x0000027abbdeea90]
Event: 3926.307 Thread 0x0000027ae2286800 52282       1       com.mysql.cj.jdbc.AbandonedConnectionCleanupThread$ConnectionFinalizerPhantomReference::_jr$ig$networkResources (8 bytes)
Event: 3926.308 Thread 0x0000027ae2286800 nmethod 52282 0x0000027abd62ae10 code [0x0000027abd62af60, 0x0000027abd62b098]
Event: 3926.324 Thread 0x0000027ae2281800 52283       1       java.util.zip.Inflater::finalize (17 bytes)
Event: 3926.324 Thread 0x0000027ae2281800 nmethod 52283 0x0000027abd62aa50 code [0x0000027abd62abc0, 0x0000027abd62ad30]
Event: 3926.325 Thread 0x0000027ae214f000 52284       1       java.util.concurrent.ConcurrentHashMap$KeySetView::remove (21 bytes)
Event: 3926.326 Thread 0x0000027ae214f000 nmethod 52284 0x0000027abd62a690 code [0x0000027abd62a800, 0x0000027abd62a990]
Event: 3956.391 Thread 0x0000027ae2286800 52285       1       com.tipray.dlp.mybatis.datasource.MybatisShardingDataSourceBuilder::_jr$$sg$toShardDbMap (4 bytes)
Event: 3956.392 Thread 0x0000027ae2286800 nmethod 52285 0x0000027abd62a3d0 code [0x0000027abd62a520, 0x0000027abd62a638]

GC Heap History (10 events):
Event: 2813.247 GC heap before
{Heap before GC invocations=60 (full 6):
 PSYoungGen      total 2172416K, used 2171985K [0x0000000716b00000, 0x00000007b1980000, 0x00000007c0000000)
  eden space 1977856K, 100% used [0x0000000716b00000,0x000000078f680000,0x000000078f680000)
  from space 194560K, 99% used [0x0000000794b80000,0x00000007a0914518,0x00000007a0980000)
  to   space 278528K, 0% used [0x00000007a0980000,0x00000007a0980000,0x00000007b1980000)
 ParOldGen       total 1115648K, used 653725K [0x00000005c4000000, 0x0000000608180000, 0x0000000716b00000)
  object space 1115648K, 58% used [0x00000005c4000000,0x00000005ebe67670,0x0000000608180000)
 Metaspace       used 318839K, capacity 334691K, committed 334832K, reserved 1355776K
  class space    used 25943K, capacity 28358K, committed 28440K, reserved 1048576K
Event: 2813.354 GC heap after
Heap after GC invocations=60 (full 6):
 PSYoungGen      total 2104832K, used 191971K [0x0000000716b00000, 0x00000007ad680000, 0x00000007c0000000)
  eden space 1894912K, 0% used [0x0000000716b00000,0x0000000716b00000,0x000000078a580000)
  from space 209920K, 91% used [0x00000007a0980000,0x00000007ac4f8f40,0x00000007ad680000)
  to   space 287232K, 0% used [0x000000078a580000,0x000000078a580000,0x000000079be00000)
 ParOldGen       total 1115648K, used 653741K [0x00000005c4000000, 0x0000000608180000, 0x0000000716b00000)
  object space 1115648K, 58% used [0x00000005c4000000,0x00000005ebe6b670,0x0000000608180000)
 Metaspace       used 318839K, capacity 334691K, committed 334832K, reserved 1355776K
  class space    used 25943K, capacity 28358K, committed 28440K, reserved 1048576K
}
Event: 3419.815 GC heap before
{Heap before GC invocations=61 (full 6):
 PSYoungGen      total 2104832K, used 2086883K [0x0000000716b00000, 0x00000007ad680000, 0x00000007c0000000)
  eden space 1894912K, 100% used [0x0000000716b00000,0x000000078a580000,0x000000078a580000)
  from space 209920K, 91% used [0x00000007a0980000,0x00000007ac4f8f40,0x00000007ad680000)
  to   space 287232K, 0% used [0x000000078a580000,0x000000078a580000,0x000000079be00000)
 ParOldGen       total 1115648K, used 653741K [0x00000005c4000000, 0x0000000608180000, 0x0000000716b00000)
  object space 1115648K, 58% used [0x00000005c4000000,0x00000005ebe6b670,0x0000000608180000)
 Metaspace       used 318940K, capacity 334915K, committed 335088K, reserved 1355776K
  class space    used 25966K, capacity 28396K, committed 28440K, reserved 1048576K
Event: 3419.958 GC heap after
Heap after GC invocations=61 (full 6):
 PSYoungGen      total 2009088K, used 192770K [0x0000000716b00000, 0x00000007a9380000, 0x00000007c0000000)
  eden space 1816064K, 0% used [0x0000000716b00000,0x0000000716b00000,0x0000000785880000)
  from space 193024K, 99% used [0x000000078a580000,0x00000007961c09b0,0x0000000796200000)
  to   space 292352K, 0% used [0x0000000797600000,0x0000000797600000,0x00000007a9380000)
 ParOldGen       total 1115648K, used 653757K [0x00000005c4000000, 0x0000000608180000, 0x0000000716b00000)
  object space 1115648K, 58% used [0x00000005c4000000,0x00000005ebe6f670,0x0000000608180000)
 Metaspace       used 318940K, capacity 334915K, committed 335088K, reserved 1355776K
  class space    used 25966K, capacity 28396K, committed 28440K, reserved 1048576K
}
Event: 3829.047 GC heap before
{Heap before GC invocations=62 (full 6):
 PSYoungGen      total 2009088K, used 2008834K [0x0000000716b00000, 0x00000007a9380000, 0x00000007c0000000)
  eden space 1816064K, 100% used [0x0000000716b00000,0x0000000785880000,0x0000000785880000)
  from space 193024K, 99% used [0x000000078a580000,0x00000007961c09b0,0x0000000796200000)
  to   space 292352K, 0% used [0x0000000797600000,0x0000000797600000,0x00000007a9380000)
 ParOldGen       total 1115648K, used 653757K [0x00000005c4000000, 0x0000000608180000, 0x0000000716b00000)
  object space 1115648K, 58% used [0x00000005c4000000,0x00000005ebe6f670,0x0000000608180000)
 Metaspace       used 320014K, capacity 336445K, committed 336624K, reserved 1357824K
  class space    used 26103K, capacity 28571K, committed 28696K, reserved 1048576K
Event: 3829.132 GC heap after
Heap after GC invocations=62 (full 6):
 PSYoungGen      total 1966080K, used 194643K [0x0000000716b00000, 0x00000007a5180000, 0x00000007c0000000)
  eden space 1741312K, 0% used [0x0000000716b00000,0x0000000716b00000,0x0000000780f80000)
  from space 224768K, 86% used [0x0000000797600000,0x00000007a3414d30,0x00000007a5180000)
  to   space 295936K, 0% used [0x0000000780f80000,0x0000000780f80000,0x0000000793080000)
 ParOldGen       total 1115648K, used 653765K [0x00000005c4000000, 0x0000000608180000, 0x0000000716b00000)
  object space 1115648K, 58% used [0x00000005c4000000,0x00000005ebe71670,0x0000000608180000)
 Metaspace       used 320014K, capacity 336445K, committed 336624K, reserved 1357824K
  class space    used 26103K, capacity 28571K, committed 28696K, reserved 1048576K
}
Event: 3924.674 GC heap before
{Heap before GC invocations=63 (full 6):
 PSYoungGen      total 1966080K, used 356003K [0x0000000716b00000, 0x00000007a5180000, 0x00000007c0000000)
  eden space 1741312K, 9% used [0x0000000716b00000,0x0000000720894078,0x0000000780f80000)
  from space 224768K, 86% used [0x0000000797600000,0x00000007a3414d30,0x00000007a5180000)
  to   space 295936K, 0% used [0x0000000780f80000,0x0000000780f80000,0x0000000793080000)
 ParOldGen       total 1115648K, used 653765K [0x00000005c4000000, 0x0000000608180000, 0x0000000716b00000)
  object space 1115648K, 58% used [0x00000005c4000000,0x00000005ebe71670,0x0000000608180000)
 Metaspace       used 320035K, capacity 336479K, committed 336624K, reserved 1357824K
  class space    used 26106K, capacity 28576K, committed 28696K, reserved 1048576K
Event: 3924.746 GC heap after
Heap after GC invocations=63 (full 6):
 PSYoungGen      total 2037248K, used 192034K [0x0000000716b00000, 0x00000007a5480000, 0x00000007c0000000)
  eden space 1741312K, 0% used [0x0000000716b00000,0x0000000716b00000,0x0000000780f80000)
  from space 295936K, 64% used [0x0000000780f80000,0x000000078cb08bb0,0x0000000793080000)
  to   space 297472K, 0% used [0x0000000793200000,0x0000000793200000,0x00000007a5480000)
 ParOldGen       total 1115648K, used 653773K [0x00000005c4000000, 0x0000000608180000, 0x0000000716b00000)
  object space 1115648K, 58% used [0x00000005c4000000,0x00000005ebe73670,0x0000000608180000)
 Metaspace       used 320035K, capacity 336479K, committed 336624K, reserved 1357824K
  class space    used 26106K, capacity 28576K, committed 28696K, reserved 1048576K
}
Event: 3924.746 GC heap before
{Heap before GC invocations=64 (full 7):
 PSYoungGen      total 2037248K, used 192034K [0x0000000716b00000, 0x00000007a5480000, 0x00000007c0000000)
  eden space 1741312K, 0% used [0x0000000716b00000,0x0000000716b00000,0x0000000780f80000)
  from space 295936K, 64% used [0x0000000780f80000,0x000000078cb08bb0,0x0000000793080000)
  to   space 297472K, 0% used [0x0000000793200000,0x0000000793200000,0x00000007a5480000)
 ParOldGen       total 1115648K, used 653773K [0x00000005c4000000, 0x0000000608180000, 0x0000000716b00000)
  object space 1115648K, 58% used [0x00000005c4000000,0x00000005ebe73670,0x0000000608180000)
 Metaspace       used 320035K, capacity 336479K, committed 336624K, reserved 1357824K
  class space    used 26106K, capacity 28576K, committed 28696K, reserved 1048576K
Event: 3926.296 GC heap after
Heap after GC invocations=64 (full 7):
 PSYoungGen      total 2037248K, used 0K [0x0000000716b00000, 0x00000007a5480000, 0x00000007c0000000)
  eden space 1741312K, 0% used [0x0000000716b00000,0x0000000716b00000,0x0000000780f80000)
  from space 295936K, 0% used [0x0000000780f80000,0x0000000780f80000,0x0000000793080000)
  to   space 297472K, 0% used [0x0000000793200000,0x0000000793200000,0x00000007a5480000)
 ParOldGen       total 1115648K, used 669656K [0x00000005c4000000, 0x0000000608180000, 0x0000000716b00000)
  object space 1115648K, 60% used [0x00000005c4000000,0x00000005ecdf6000,0x0000000608180000)
 Metaspace       used 319094K, capacity 334761K, committed 336624K, reserved 1357824K
  class space    used 25926K, capacity 28277K, committed 28696K, reserved 1048576K
}

Deoptimization events (0 events):
No events

Classes redefined (10 events):
Event: 1325.329 Thread 0x0000027addb82800 redefined class name=com.tipray.dlp.app.config.MybatisConfig$$EnhancerBySpringCGLIB$$1$$FastClassBySpringCGLIB$$1, count=3
Event: 1325.373 Thread 0x0000027addb82800 redefined class name=org.springframework.context.ApplicationContextAware, count=1
Event: 1325.418 Thread 0x0000027addb82800 redefined class name=com.tipray.dlp.cloud.outfile.service.MachineCodeService, count=1
Event: 1325.466 Thread 0x0000027addb82800 redefined class name=java.lang.reflect.Proxy, count=2
Event: 1325.509 Thread 0x0000027addb82800 redefined class name=com.tipray.dlp.init.CleanInitRunner, count=3
Event: 1325.551 Thread 0x0000027addb82800 redefined class name=com.tipray.dlp.service.job.SysAlarmJob, count=2
Event: 1325.599 Thread 0x0000027addb82800 redefined class name=com.sun.proxy.$Proxy547, count=3
Event: 1325.642 Thread 0x0000027addb82800 redefined class name=com.tipray.dlp.bean.vo.LogVO, count=4
Event: 1325.683 Thread 0x0000027addb82800 redefined class name=com.tipray.dlp.util.UsbTermUtil, count=2
Event: 1328.142 Thread 0x0000027addb82800 redefined class name=com.tipray.dlp.service.StgObjectObserver, count=1

Internal exceptions (10 events):
Event: 3903.243 Thread 0x0000027afbd87800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000071744a190) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 3908.704 Thread 0x0000027af7756800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000071e1fdbb8) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 3908.704 Thread 0x0000027af7756800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000071e1fe078) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 3913.247 Thread 0x0000027afbd87800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000071744ad58) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 3923.236 Thread 0x0000027afbd87800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000071744b920) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 3926.297 Thread 0x0000027ae25a6000 Exception <a 'java/net/ConnectException': Connection timed out: connect> (0x0000000716c4fa90) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 739]
Event: 3933.249 Thread 0x0000027afbd87800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000071799f908) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 3943.237 Thread 0x0000027afbd87800 Exception <a 'sun/nio/fs/WindowsException'> (0x00000007179a04d0) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 3953.240 Thread 0x0000027afa42c000 Exception <a 'sun/nio/fs/WindowsException'> (0x00000007186018a8) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 3963.237 Thread 0x0000027afa42c000 Exception <a 'sun/nio/fs/WindowsException'> (0x0000000718c73c80) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]

Events (10 events):
Event: 3964.892 Thread 0x0000027a808d3000 DEOPT UNPACKING pc=0x0000027ab98c75d8 sp=0x00000083679fd5b0 mode 1
Event: 3964.892 Thread 0x0000027a808d3000 DEOPT PACKING pc=0x0000027abbd9bac4 sp=0x00000083679fd930
Event: 3964.892 Thread 0x0000027a808d3000 DEOPT UNPACKING pc=0x0000027ab98c75d8 sp=0x00000083679fd5f0 mode 1
Event: 3964.892 Thread 0x0000027a808d3000 DEOPT PACKING pc=0x0000027abbe112a4 sp=0x00000083679fd9d0
Event: 3964.892 Thread 0x0000027a808d3000 DEOPT UNPACKING pc=0x0000027ab98c75d8 sp=0x00000083679fd678 mode 1
Event: 3964.892 Thread 0x0000027a808d3000 DEOPT PACKING pc=0x0000027abc4439e4 sp=0x00000083679fda50
Event: 3964.892 Thread 0x0000027a808d3000 DEOPT UNPACKING pc=0x0000027ab98c75d8 sp=0x00000083679fd740 mode 1
Event: 3964.893 Executing VM operation: ForceSafepoint
Event: 3964.893 Executing VM operation: ForceSafepoint done
Event: 3964.893 Thread 0x0000027a808d3000 Thread exited: 0x0000027a808d3000


Dynamic libraries:
0x00007ff7c1070000 - 0x00007ff7c10c1000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\bin\java.exe
0x00007ffe0a830000 - 0x00007ffe0aa47000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffe09710000 - 0x00007ffe097d4000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffe07ae0000 - 0x00007ffe07eb3000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffdf4060000 - 0x00007ffdf4077000 	C:\DLP\Application\ghijt64.dll
0x00007ffe097e0000 - 0x00007ffe09891000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffe0a4f0000 - 0x00007ffe0a597000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffe084f0000 - 0x00007ffe08598000 	C:\WINDOWS\System32\sechost.dll
0x00007ffe07ec0000 - 0x00007ffe07ee8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffe08d70000 - 0x00007ffe08e84000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffdf5060000 - 0x00007ffdf5076000 	C:\LeaderDisk\LdFbs\LdFbsClient\fbsijt64.DLL
0x00007ffe08bb0000 - 0x00007ffe08d61000 	C:\WINDOWS\System32\USER32.dll
0x00007ffe084a0000 - 0x00007ffe084c6000 	C:\WINDOWS\System32\win32u.dll
0x00007ffe08ea0000 - 0x00007ffe08ec9000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffe079c0000 - 0x00007ffe07adb000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffe08150000 - 0x00007ffe081ea000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe08030000 - 0x00007ffe08141000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffdf1230000 - 0x00007ffdf14c8000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5124_none_270e8f4f7386d69d\COMCTL32.dll
0x00007ffdf3fa0000 - 0x00007ffdf3feb000 	C:\DLP\Application\LdInjectDLL64.dll
0x00007ffe02270000 - 0x00007ffe0227a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffe09040000 - 0x00007ffe09071000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffdf3d70000 - 0x00007ffdf3d98000 	C:\DLP\Application\HookDataInteractionx64.dll
0x00007ffdf3ae0000 - 0x00007ffdf3b06000 	C:\DLP\Application\TrClientProtect64.dll
0x00007ffda5ad0000 - 0x00007ffda5c47000 	C:\DLP\Application\LdGetPrintContent64.dll
0x00007ffe09550000 - 0x00007ffe096f1000 	C:\WINDOWS\System32\ole32.dll
0x00007ffe098a0000 - 0x00007ffe09c32000 	C:\WINDOWS\System32\combase.dll
0x00007ffdd9c40000 - 0x00007ffdd9ce8000 	C:\WINDOWS\SYSTEM32\WINSPOOL.DRV
0x00007ffe092c0000 - 0x00007ffe093ca000 	C:\WINDOWS\System32\shcore.dll
0x00007ffe07130000 - 0x00007ffe0713c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.DLL
0x00007ffe09c40000 - 0x00007ffe0a4d6000 	C:\WINDOWS\System32\Shell32.dll
0x00007ffe07ef0000 - 0x00007ffe0802f000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffda5a50000 - 0x00007ffda5ac4000 	C:\DLP\Application\LdSmartEnc64.dll
0x00007ffddc970000 - 0x00007ffddc97b000 	C:\WINDOWS\SYSTEM32\FLTLIB.DLL
0x00007ffdf5040000 - 0x00007ffdf505c000 	C:\LeaderDisk\LdFbs\LdFbsClient\LdProtHook64.DLL
0x00007ffdf11c0000 - 0x00007ffdf11e5000 	C:\LeaderDisk\LdFbs\LdFbsClient\ghhlp64.dll
0x00007ffde3200000 - 0x00007ffde3215000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\vcruntime140.dll
0x00007ffda5e30000 - 0x00007ffda621c000 	C:\DLP\Application\LdWaterMarkHook64.dll
0x00007ffe08e90000 - 0x00007ffe08e98000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffdd10d0000 - 0x00007ffdd128a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.22621.5124_none_57f98234ce19506b\gdiplus.dll
0x00007ffdd0970000 - 0x00007ffdd0a0b000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\msvcp140.dll
0x00007ffda5c50000 - 0x00007ffda5e22000 	C:\DLP\Application\LdPrintMonitor64.dll
0x00007ffe09240000 - 0x00007ffe092b1000 	C:\WINDOWS\System32\WS2_32.dll
0x00000000549c0000 - 0x0000000055219000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\server\jvm.dll
0x00007ffdf56a0000 - 0x00007ffdf56a9000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffdfc070000 - 0x00007ffdfc0a4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffda4fe0000 - 0x00007ffda514d000 	C:\DLP\Application\EncAppCtrl64.dll
0x00007ffe086c0000 - 0x00007ffe08723000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffe0a600000 - 0x00007ffe0a6d7000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffda5900000 - 0x00007ffda5931000 	C:\WINDOWS\SYSTEM32\Mapi32.dll
0x00007ffe084d0000 - 0x00007ffe084ef000 	C:\WINDOWS\System32\imagehlp.dll
0x00007ffe069f0000 - 0x00007ffe06a08000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffdf40a0000 - 0x00007ffdf40b0000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\verify.dll
0x00007ffdd86a0000 - 0x00007ffdd86bf000 	C:\DLP\Application\HookCreateProcessInternal64.dll
0x00007ffdd0af0000 - 0x00007ffdd0b1b000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\java.dll
0x00007ffda4f00000 - 0x00007ffda4f76000 	C:\DLP\Application\MonFileOp64.dll
0x00007ffdef270000 - 0x00007ffdef28e000 	C:\WINDOWS\SYSTEM32\MPR.dll
0x00007ffda59b0000 - 0x00007ffda59e1000 	C:\DLP\Application\LdSensitiveTaskCenter64.dll
0x00007ffdd0a90000 - 0x00007ffdd0ac6000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\jdwp.dll
0x00007ffdef5c0000 - 0x00007ffdef5c9000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\npt.dll
0x00007ffdd0630000 - 0x00007ffdd067e000 	C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2024.3\plugins\jr-ide-idea\jrebel6\lib\jrebel64.dll
0x00007ffdd0a50000 - 0x00007ffdd0a89000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\instrument.dll
0x00007ffdde2d0000 - 0x00007ffdde2e8000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\zip.dll
0x00007ffe05860000 - 0x00007ffe0617a000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffe07870000 - 0x00007ffe0789b000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffdedb00000 - 0x00007ffdedb0a000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\dt_socket.dll
0x00007ffe06ee0000 - 0x00007ffe06f4a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffdde190000 - 0x00007ffdde1ac000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\net.dll
0x00007ffdde070000 - 0x00007ffdde083000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\nio.dll
0x00007ffdd0940000 - 0x00007ffdd0964000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\sunec.dll
0x00007ffddec40000 - 0x00007ffddec4d000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\management.dll
0x00007ffdddc50000 - 0x00007ffdddc5e000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\sunmscapi.dll
0x00007ffe082b0000 - 0x00007ffe08417000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffe072f0000 - 0x00007ffe0731d000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffe072b0000 - 0x00007ffe072e7000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffe07140000 - 0x00007ffe0715b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffe06940000 - 0x00007ffe06977000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffe06fd0000 - 0x00007ffe06ff8000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffe07940000 - 0x00007ffe079bb000 	C:\WINDOWS\System32\bcryptprimitives.dll
0x00007ffe063e0000 - 0x00007ffe0640d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffe08ed0000 - 0x00007ffe08ed9000 	C:\WINDOWS\System32\NSI.dll
0x00007ffe02280000 - 0x00007ffe02299000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffe01500000 - 0x00007ffe0151f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffe06450000 - 0x00007ffe06552000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffdffb00000 - 0x00007ffdffb0a000 	C:\Windows\System32\rasadhlp.dll
0x00007ffe008c0000 - 0x00007ffe00943000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffdcc660000 - 0x00007ffdcc677000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffdcc640000 - 0x00007ffdcc65b000 	C:\WINDOWS\system32\pnrpnsp.dll
0x00007ffdcc620000 - 0x00007ffdcc631000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffe00f00000 - 0x00007ffe00f15000 	C:\WINDOWS\system32\wshbth.dll
0x00007ffdcc5f0000 - 0x00007ffdcc617000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ffdd1800000 - 0x00007ffdd181b000 	C:\Users\<USER>\.jrebel\jrebel-temp\ver-7c19eea5\IdeaWin64.dll
0x00007ffd38090000 - 0x00007ffd3b0d6000 	C:\Users\<USER>\AppData\Local\Temp\opencv_openpnp8661121673245233581\nu\pattern\opencv\windows\x86_64\opencv_java451.dll
0x00007ffddea50000 - 0x00007ffddec1e000 	C:\WINDOWS\SYSTEM32\MFPlat.DLL
0x00007ffda8110000 - 0x00007ffda819a000 	C:\WINDOWS\SYSTEM32\MF.dll
0x00007ffdd0fa0000 - 0x00007ffdd10c1000 	C:\WINDOWS\SYSTEM32\MFReadWrite.dll
0x00007ffe07630000 - 0x00007ffe0767e000 	C:\WINDOWS\SYSTEM32\cfgmgr32.dll
0x00007ffd6d940000 - 0x00007ffd6dcdb000 	C:\WINDOWS\SYSTEM32\MFCORE.DLL
0x00007ffdf5850000 - 0x00007ffdf5883000 	C:\WINDOWS\SYSTEM32\RTWorkQ.DLL
0x00007ffdeb950000 - 0x00007ffdeb995000 	C:\Users\<USER>\AppData\Local\Temp\jna-115883602\jna7426801253649681672.dll
0x00007ffd84750000 - 0x00007ffd847af000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\bin\Ldwebdf.dll
0x00007ffddd940000 - 0x00007ffddd9a9000 	C:\WINDOWS\SYSTEM32\OLEACC.dll
0x0000000180000000 - 0x00000001800b3000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\bin\lddf_64.dll
0x00007ffd84560000 - 0x00007ffd845ec000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\bin\mtoken_gm3000_64.dll
0x00007ffe08730000 - 0x00007ffe08ba4000 	C:\WINDOWS\System32\SETUPAPI.dll
0x00007ffe06180000 - 0x00007ffe0618e000 	C:\WINDOWS\SYSTEM32\HID.DLL
0x00007ffd68ce0000 - 0x00007ffd68e74000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\awt.dll
0x00007ffe036b0000 - 0x00007ffe03747000 	C:\WINDOWS\SYSTEM32\apphelp.dll
0x00007ffd684e0000 - 0x00007ffd6862d000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\freetype.dll
0x00007ffd692b0000 - 0x00007ffd69381000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\fontmanager.dll
0x00007ffe051e0000 - 0x00007ffe0520e000 	C:\WINDOWS\system32\DWMAPI.DLL
0x00007ffe04eb0000 - 0x00007ffe04f63000 	C:\WINDOWS\system32\uxtheme.dll
0x00007ffdd0a10000 - 0x00007ffdd0a4f000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\lcms.dll
0x00007ffda16a0000 - 0x00007ffda16c1000 	C:\DLP\Application\msghijt64.dll
0x00007ffe08ee0000 - 0x00007ffe09040000 	C:\WINDOWS\System32\MSCTF.dll
0x00007ffdf3550000 - 0x00007ffdf3782000 	C:\WINDOWS\SYSTEM32\dbghelp.dll

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:50710,suspend=y,server=n -Drebel.base=C:\Users\<USER>\.jrebel -Drebel.env.ide.plugin.build=2d7357bde6d770c5760a889e2d4bf4d26276ce12 -Drebel.env.ide.plugin.version=2025.2.0 -Drebel.env.ide.version=2024.3.5 -Drebel.env.ide.product=IU -Drebel.env.ide=intellij -Drebel.notification.url=http://localhost:49429 -agentpath:C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2024.3\plugins\jr-ide-idea\jrebel6\lib\jrebel64.dll -Dspring.profiles.active=dev -Dcool.request.port=49430 -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2024.3\plugins\bean-invoker-intellij-plugin\lib\bean-invoker-agent-1.0.3-with-dependencies.jar -Dspring.invokePort=6060 -Drebel.plugins=C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2024.3\plugins\jr-mp-ide-idea\lib\jr-mybatisplus-1.0.7.jar -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2024.3\captureAgent\debugger-agent.jar=file:/C:/Users/<USER>/AppData/Local/Temp/capture.props -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 
java_command: com.tipray.dlp.app.APP
java_class_path (initial): C:\Users\<USER>\AppData\Local\Temp\classpath255596959.jar;C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2024.3\plugins\bean-invoker-intellij-plugin\lib\bean-invoker-agent-1.0.3-with-dependencies.jar;C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2024.3\captureAgent\debugger-agent.jar
Launcher Type: SUN_STANDARD

Environment Variables:
PATH=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;D:\VMware\VMware Workstation\bin\;C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\TortoiseSVN\bin;C:\Program Files\Git\cmd;C:\Program Files\MySQL\MySQL Server 5.7\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\gradle-8.14\bin;;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Pandoc\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\cursor\resources\app\bin
USERNAME=zhuhs
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 191 Stepping 2, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 11 , 64 bit Build 22621 (10.0.22621.5124)

CPU:total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 191 stepping 2, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx

Memory: 4k page, physical 33289648k(10020748k free), swap 56358320k(20516884k free)

vm_info: OpenJDK 64-Bit Server VM (25.412-b08) for windows-amd64 JRE (1.8.0_412-b08), built on Apr 17 2024 02:10:30 by "jenkins" with MS VC++ 15.9 (VS2017)

time: Mon Jul 28 16:36:56 2025
timezone: Intel64 Family 6 Model 191 Stepping 2, GenuineIntel
elapsed time: 3964.939216 seconds (0d 1h 6m 4s)

