#
# A fatal error has been detected by the Java Runtime Environment:
#
#  Internal Error (javaCalls.cpp:51), pid=32004, tid=0x0000000000008678
#  guarantee(thread->is_Java_thread()) failed: crucial check - the VM thread cannot and must not escape to Java code
#
# JRE version: OpenJDK Runtime Environment (8.0_412-b08) (build 1.8.0_412-b08)
# Java VM: OpenJDK 64-Bit Server VM (25.412-b08 mixed mode windows-amd64 compressed oops)
# Failed to write core dump. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://github.com/adoptium/adoptium-support/issues
#

---------------  T H R E A D  ---------------

Current thread (0x0000019d9ef94000):  VMThread [stack: 0x0000008439200000,0x0000008439300000] [id=34424]

Stack: [0x0000008439200000,0x0000008439300000]
[error occurred during error reporting (printing stack bounds), id 0xc0000005]

Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)

VM_Operation (0x00000084397ff0e0): GetOrSetLocal, mode: safepoint, requested by thread 0x0000019d9f033800


---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
  0x0000019dc12ee800 JavaThread "clientOutboundChannel-127" [_thread_blocked, id=34956, stack(0x0000008440c00000,0x0000008440d00000)]
  0x0000019dc12e4800 JavaThread "clientOutboundChannel-126" [_thread_blocked, id=22664, stack(0x0000008440b00000,0x0000008440c00000)]
  0x0000019dc12ec800 JavaThread "HikariPool-ds_9004 connection adder" daemon [_thread_in_native, id=33044, stack(0x0000008444300000,0x0000008444400000)]
  0x0000019dc12e1800 JavaThread "clientOutboundChannel-125" [_thread_blocked, id=30008, stack(0x000000843dd00000,0x000000843de00000)]
  0x0000019dc12d9800 JavaThread "clientOutboundChannel-124" [_thread_blocked, id=33808, stack(0x000000843bd00000,0x000000843be00000)]
  0x0000019dc12f2000 JavaThread "clientOutboundChannel-123" [_thread_blocked, id=5320, stack(0x0000008444100000,0x0000008444200000)]
  0x0000019dc12f6800 JavaThread "ForkJoinPool.commonPool-worker-15" daemon [_thread_blocked, id=34884, stack(0x0000008444200000,0x0000008444300000)]
  0x0000019dc12db000 JavaThread "ShardingSphere-3" daemon [_thread_blocked, id=21856, stack(0x0000008440900000,0x0000008440a00000)]
  0x0000019dc12ee000 JavaThread "ForkJoinPool.commonPool-worker-6" daemon [_thread_blocked, id=35372, stack(0x0000008444a00000,0x0000008444b00000)]
  0x0000019dc12c2000 JavaThread "rebel-intermediate-collector" daemon [_thread_blocked, id=33892, stack(0x000000843bb00000,0x000000843bc00000)]
  0x0000019dc12dd800 JavaThread "arthas-command-execute" daemon [_thread_blocked, id=35320, stack(0x0000008444000000,0x0000008444100000)]
  0x0000019dc12d1800 JavaThread "arthas-TermServer-1-9" daemon [_thread_in_native, id=24992, stack(0x0000008443f00000,0x0000008444000000)]
  0x0000019dc12d8800 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-11" daemon [_thread_in_native, id=34840, stack(0x0000008443e00000,0x0000008443f00000)]
  0x0000019dc12d7000 JavaThread "arthas-TermServer-1-8" daemon [_thread_in_native, id=23756, stack(0x0000008443d00000,0x0000008443e00000)]
  0x0000019dc12d4000 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-10" daemon [_thread_in_native, id=33692, stack(0x0000008443c00000,0x0000008443d00000)]
  0x0000019dc12ca000 JavaThread "arthas-TermServer-1-7" daemon [_thread_in_native, id=31640, stack(0x0000008443b00000,0x0000008443c00000)]
  0x0000019dc12cd800 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-9" daemon [_thread_in_native, id=28240, stack(0x0000008443a00000,0x0000008443b00000)]
  0x0000019dc12c9000 JavaThread "arthas-TermServer-1-6" daemon [_thread_in_native, id=33880, stack(0x0000008443900000,0x0000008443a00000)]
  0x0000019dc12cd000 JavaThread "arthas-TermServer-1-3" daemon [_thread_in_native, id=35288, stack(0x0000008443800000,0x0000008443900000)]
  0x0000019dc12cb800 JavaThread "arthas-TermServer-1-5" daemon [_thread_in_native, id=34592, stack(0x0000008443700000,0x0000008443800000)]
  0x0000019dc12cf800 JavaThread "arthas-TermServer-1-4" daemon [_thread_in_native, id=34580, stack(0x0000008443500000,0x0000008443600000)]
  0x0000019dc12c4800 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-8" daemon [_thread_in_native, id=25172, stack(0x0000008443400000,0x0000008443500000)]
  0x0000019dc12c8000 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-7" daemon [_thread_in_native, id=33128, stack(0x0000008443300000,0x0000008443400000)]
  0x0000019dc12c5000 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-6" daemon [_thread_in_native, id=33584, stack(0x0000008443200000,0x0000008443300000)]
  0x0000019dc12c6800 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-5" daemon [_thread_in_native, id=31700, stack(0x0000008443000000,0x0000008443100000)]
  0x0000019dc12bc800 JavaThread "arthas-TermServer-1-2" daemon [_thread_in_native, id=33464, stack(0x0000008442f00000,0x0000008443000000)]
  0x0000019dc12c3800 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-4" daemon [_thread_in_native, id=33288, stack(0x0000008442e00000,0x0000008442f00000)]
  0x0000019dc12bb800 JavaThread "arthas-TermServer-1-1" daemon [_thread_in_native, id=33744, stack(0x0000008442d00000,0x0000008442e00000)]
  0x0000019dc12bf000 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-3" daemon [_thread_in_native, id=25772, stack(0x0000008442800000,0x0000008442900000)]
  0x0000019dc12d6800 JavaThread "MessageBroker-16" [_thread_blocked, id=2520, stack(0x0000008445a00000,0x0000008445b00000)]
  0x0000019dc12ce800 JavaThread "MessageBroker-15" [_thread_blocked, id=35732, stack(0x0000008445900000,0x0000008445a00000)]
  0x0000019dc12d3000 JavaThread "MessageBroker-14" [_thread_blocked, id=35772, stack(0x0000008445800000,0x0000008445900000)]
  0x0000019dc12d5800 JavaThread "MessageBroker-13" [_thread_blocked, id=35668, stack(0x0000008445700000,0x0000008445800000)]
  0x0000019dc12d2800 JavaThread "MessageBroker-12" [_thread_blocked, id=31392, stack(0x0000008445600000,0x0000008445700000)]
  0x0000019dc12d5000 JavaThread "MessageBroker-11" [_thread_blocked, id=34456, stack(0x0000008445500000,0x0000008445600000)]
  0x0000019dc12d0000 JavaThread "MessageBroker-10" [_thread_blocked, id=33192, stack(0x0000008445200000,0x0000008445300000)]
  0x0000019dc12cc000 JavaThread "MessageBroker-9" [_thread_blocked, id=34636, stack(0x000000843cb00000,0x000000843cc00000)]
  0x0000019dc12c1000 JavaThread "MessageBroker-8" [_thread_blocked, id=31284, stack(0x0000008444600000,0x0000008444700000)]
  0x0000019dc12c7800 JavaThread "MessageBroker-7" [_thread_blocked, id=26380, stack(0x0000008444500000,0x0000008444600000)]
  0x0000019dbd1ac800 JavaThread "MessageBroker-6" [_thread_blocked, id=35352, stack(0x0000008443600000,0x0000008443700000)]
  0x0000019dbd1a7000 JavaThread "WebSocket background processing" daemon [_thread_blocked, id=25272, stack(0x0000008443100000,0x0000008443200000)]
  0x0000019dbd1a5800 JavaThread "MessageBroker-5" [_thread_blocked, id=28860, stack(0x0000008442c00000,0x0000008442d00000)]
  0x0000019dbd19c800 JavaThread "MessageBroker-4" [_thread_blocked, id=30460, stack(0x0000008442b00000,0x0000008442c00000)]
  0x0000019dbd19e800 JavaThread "MessageBroker-2" [_thread_blocked, id=35056, stack(0x0000008442a00000,0x0000008442b00000)]
  0x0000019dbd19c000 JavaThread "MessageBroker-3" [_thread_blocked, id=34572, stack(0x0000008442900000,0x0000008442a00000)]
  0x0000019dbd199000 JavaThread "JNA Cleaner" daemon [_thread_blocked, id=32832, stack(0x0000008442500000,0x0000008442600000)]
  0x0000019dbd198000 JavaThread "modules-pool-t-18" [_thread_blocked, id=34752, stack(0x0000008442300000,0x0000008442400000)]
  0x0000019dbd18d000 JavaThread "server-log-pool-t-5" [_thread_blocked, id=34484, stack(0x0000008441f00000,0x0000008442000000)]
  0x0000019dbd18e800 JavaThread "pool-236-thread-1" [_thread_blocked, id=30080, stack(0x0000008442200000,0x0000008442300000)]
  0x0000019dbd19a800 JavaThread "RMI Reaper" [_thread_blocked, id=33436, stack(0x0000008442400000,0x0000008442500000)]
  0x0000019dbd197000 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=32864, stack(0x0000008442100000,0x0000008442200000)]
  0x0000019dbd195800 JavaThread "RMI TCP Accept-6666" daemon [_thread_in_native, id=396, stack(0x0000008442000000,0x0000008442100000)]
  0x0000019dbd191800 JavaThread "GC Daemon" daemon [_thread_blocked, id=820, stack(0x0000008441e00000,0x0000008441f00000)]
  0x0000019dbd190000 JavaThread "RMI RenewClean-[127.0.0.1:63320]" daemon [_thread_blocked, id=35500, stack(0x0000008441d00000,0x0000008441e00000)]
  0x0000019dbd191000 JavaThread "DestroyJavaVM" [_thread_blocked, id=18312, stack(0x0000008438400000,0x0000008438500000)]
  0x0000019dbd18b800 JavaThread "scheduled-pool-t-10" [_thread_blocked, id=35644, stack(0x0000008441b00000,0x0000008441c00000)]
  0x0000019dbd18a800 JavaThread "scheduled-pool-t-9" [_thread_blocked, id=32928, stack(0x0000008441a00000,0x0000008441b00000)]
  0x0000019dbd183000 JavaThread "scheduled-pool-t-8" [_thread_blocked, id=34240, stack(0x0000008441900000,0x0000008441a00000)]
  0x0000019dbd187800 JavaThread "scheduled-pool-t-7" [_thread_blocked, id=33556, stack(0x0000008441800000,0x0000008441900000)]
  0x0000019dbd18a000 JavaThread "scheduled-pool-t-6" [_thread_blocked, id=1556, stack(0x0000008441700000,0x0000008441800000)]
  0x0000019dbd186800 JavaThread "scheduled-pool-t-5" [_thread_blocked, id=1896, stack(0x0000008441600000,0x0000008441700000)]
  0x0000019dbd183800 JavaThread "scheduled-pool-t-4" [_thread_blocked, id=31124, stack(0x0000008441500000,0x0000008441600000)]
  0x0000019dbd189000 JavaThread "scheduled-pool-t-3" [_thread_blocked, id=6264, stack(0x0000008441400000,0x0000008441500000)]
  0x0000019dbd188800 JavaThread "scheduled-pool-t-2" [_thread_blocked, id=25560, stack(0x0000008441300000,0x0000008441400000)]
  0x0000019dbd181800 JavaThread "scheduled-pool-t-1" [_thread_blocked, id=26700, stack(0x0000008441200000,0x0000008441300000)]
  0x0000019dbd186000 JavaThread "modules-pool-t-17" [_thread_blocked, id=35376, stack(0x0000008441100000,0x0000008441200000)]
  0x0000019dbd17c000 JavaThread "pool-202-thread-1" [_thread_blocked, id=23516, stack(0x0000008440f00000,0x0000008441000000)]
  0x0000019dbd180800 JavaThread "soft-pool-t-2" [_thread_blocked, id=33220, stack(0x0000008441000000,0x0000008441100000)]
  0x0000019dbd17f800 JavaThread "modules-pool-t-16" [_thread_blocked, id=32596, stack(0x0000008440e00000,0x0000008440f00000)]
  0x0000019dbd179800 JavaThread "server-log-pool-t-4" [_thread_blocked, id=33860, stack(0x0000008440d00000,0x0000008440e00000)]
  0x0000019dc036b000 JavaThread "modules-pool-t-15" [_thread_blocked, id=33668, stack(0x0000008440a00000,0x0000008440b00000)]
  0x0000019dc0369800 JavaThread "ThreadPoolTaskScheduler-4" [_thread_blocked, id=33064, stack(0x000000843d800000,0x000000843d900000)]
  0x0000019dc0370800 JavaThread "ThreadPoolTaskScheduler-3" [_thread_blocked, id=35788, stack(0x0000008440800000,0x0000008440900000)]
  0x0000019dc036f800 JavaThread "modules-pool-t-14" [_thread_blocked, id=35572, stack(0x0000008440700000,0x0000008440800000)]
  0x0000019dc0367000 JavaThread "ThreadPoolTaskScheduler-2" [_thread_blocked, id=33200, stack(0x0000008440600000,0x0000008440700000)]
  0x0000019dc036e000 JavaThread "ThreadPoolTaskScheduler-1" [_thread_blocked, id=35744, stack(0x0000008440500000,0x0000008440600000)]
  0x0000019dc035f800 JavaThread "modules-pool-t-13" [_thread_blocked, id=3176, stack(0x0000008440400000,0x0000008440500000)]
  0x0000019dc0366800 JavaThread "modules-pool-t-12" [_thread_blocked, id=9324, stack(0x0000008440300000,0x0000008440400000)]
  0x0000019dc0365800 JavaThread "modules-pool-t-11" [_thread_blocked, id=25692, stack(0x0000008440200000,0x0000008440300000)]
  0x0000019dc0364800 JavaThread "modules-pool-t-10" [_thread_blocked, id=32660, stack(0x0000008440100000,0x0000008440200000)]
  0x0000019dc035d800 JavaThread "modules-pool-t-9" [_thread_blocked, id=35440, stack(0x0000008440000000,0x0000008440100000)]
  0x0000019dc0364000 JavaThread "modules-pool-t-8" [_thread_blocked, id=35116, stack(0x000000843ff00000,0x0000008440000000)]
  0x0000019dc0363000 JavaThread "modules-pool-t-7" [_thread_blocked, id=30876, stack(0x000000843fe00000,0x000000843ff00000)]
  0x0000019dc0361000 JavaThread "modules-pool-t-6" [_thread_blocked, id=34296, stack(0x000000843fd00000,0x000000843fe00000)]
  0x0000019dc0359000 JavaThread "modules-pool-t-5" [_thread_blocked, id=35776, stack(0x000000843fc00000,0x000000843fd00000)]
  0x0000019dc035d000 JavaThread "modules-pool-t-4" [_thread_blocked, id=35520, stack(0x000000843fb00000,0x000000843fc00000)]
  0x0000019dc035b800 JavaThread "modules-pool-t-3" [_thread_blocked, id=34512, stack(0x000000843fa00000,0x000000843fb00000)]
  0x0000019da66f9800 JavaThread "Thread-465" daemon [_thread_in_native, id=23616, stack(0x000000843f900000,0x000000843fa00000)]
  0x0000019dbf503800 JavaThread "server-log-pool-t-3" [_thread_blocked, id=22004, stack(0x000000843f800000,0x000000843f900000)]
  0x0000019dbf503000 JavaThread "server-log-pool-t-2" [_thread_blocked, id=34108, stack(0x000000843f700000,0x000000843f800000)]
  0x0000019dbf501000 JavaThread "server-log-pool-t-1" [_thread_blocked, id=25668, stack(0x000000843f600000,0x000000843f700000)]
  0x0000019dbf500800 JavaThread "Thread-463" daemon [_thread_blocked, id=28420, stack(0x000000843f500000,0x000000843f600000)]
  0x0000019dbf4fe000 JavaThread "modules-pool-t-2" [_thread_blocked, id=7112, stack(0x000000843f400000,0x000000843f500000)]
  0x0000019dbf4ff800 JavaThread "modules-pool-t-1" [_thread_blocked, id=32220, stack(0x000000843f300000,0x000000843f400000)]
  0x0000019dbf4fd800 JavaThread "Thread-461" daemon [_thread_in_native, id=25240, stack(0x000000843f200000,0x000000843f300000)]
  0x0000019dbf4fc800 JavaThread "sync-scheduler-pool-t-1" [_thread_blocked, id=25280, stack(0x000000843f100000,0x000000843f200000)]
  0x0000019dbf4fb800 JavaThread "http-nio-28080-Acceptor" daemon [_thread_in_native, id=21080, stack(0x000000843f000000,0x000000843f100000)]
  0x0000019dbf4f1800 JavaThread "http-nio-28080-Poller" daemon [_thread_in_native, id=21032, stack(0x000000843ef00000,0x000000843f000000)]
  0x0000019dbf4f8800 JavaThread "http-nio-28080-exec-10" daemon [_thread_blocked, id=32048, stack(0x000000843ee00000,0x000000843ef00000)]
  0x0000019dbf4f0800 JavaThread "http-nio-28080-exec-9" daemon [_thread_blocked, id=32708, stack(0x000000843ed00000,0x000000843ee00000)]
  0x0000019dbf4f8000 JavaThread "http-nio-28080-exec-8" daemon [_thread_blocked, id=35824, stack(0x000000843ec00000,0x000000843ed00000)]
  0x0000019dbf4f7000 JavaThread "http-nio-28080-exec-7" daemon [_thread_blocked, id=8032, stack(0x000000843eb00000,0x000000843ec00000)]
  0x0000019dbf4ef000 JavaThread "http-nio-28080-exec-6" daemon [_thread_blocked, id=35544, stack(0x000000843ea00000,0x000000843eb00000)]
  0x0000019dbf4f3000 JavaThread "http-nio-28080-exec-5" daemon [_thread_blocked, id=32104, stack(0x000000843e900000,0x000000843ea00000)]
  0x0000019dbf4f2800 JavaThread "http-nio-28080-exec-4" daemon [_thread_blocked, id=26948, stack(0x000000843e800000,0x000000843e900000)]
  0x0000019dbf4f0000 JavaThread "http-nio-28080-exec-3" daemon [_thread_blocked, id=34184, stack(0x000000843e700000,0x000000843e800000)]
  0x0000019dbf4f4800 JavaThread "http-nio-28080-exec-2" daemon [_thread_blocked, id=33812, stack(0x000000843e600000,0x000000843e700000)]
  0x0000019dbf4e7800 JavaThread "http-nio-28080-exec-1" daemon [_thread_blocked, id=33136, stack(0x000000843e500000,0x000000843e600000)]
  0x0000019dbf4e6800 JavaThread "MessageBroker-1" [_thread_blocked, id=13932, stack(0x000000843e400000,0x000000843e500000)]
  0x0000019dbf4ed800 JavaThread "arthas-session-manager" daemon [_thread_blocked, id=33372, stack(0x000000843e300000,0x000000843e400000)]
  0x0000019dbf4ea800 JavaThread "arthas-shell-server" daemon [_thread_blocked, id=26000, stack(0x000000843e200000,0x000000843e300000)]
  0x0000019dbf4e5800 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-2" daemon [_thread_in_native, id=31556, stack(0x000000843df00000,0x000000843e000000)]
  0x0000019dbf4e8000 JavaThread "arthas-NettyWebsocketTtyBootstrap-4-1" daemon [_thread_in_native, id=31768, stack(0x000000843dc00000,0x000000843dd00000)]
  0x0000019dbf4e9800 JavaThread "arthas-NettyHttpTelnetBootstrap-3-1" daemon [_thread_in_native, id=25104, stack(0x000000843da00000,0x000000843db00000)]
  0x0000019dbcf2e800 JavaThread "arthas-timer" daemon [_thread_blocked, id=25716, stack(0x000000843ca00000,0x000000843cb00000)]
  0x0000019daf8e5800 JavaThread "idle-connection-evictor-1" daemon [_thread_blocked, id=32632, stack(0x000000843c900000,0x000000843ca00000)]
  0x0000019db00bd000 JavaThread "idle-connection-evictor-1" daemon [_thread_blocked, id=7372, stack(0x000000843c800000,0x000000843c900000)]
  0x0000019db00b6000 JavaThread "idle-connection-evictor-1" daemon [_thread_blocked, id=34224, stack(0x000000843e100000,0x000000843e200000)]
  0x0000019db00b7800 JavaThread "idle-connection-evictor-1" daemon [_thread_blocked, id=25696, stack(0x000000843e000000,0x000000843e100000)]
  0x0000019daf8dc000 JavaThread "HikariPool-JdbcPersistRepo housekeeper" daemon [_thread_blocked, id=4184, stack(0x000000843de00000,0x000000843df00000)]
  0x0000019daf8de000 JavaThread "pool-5-thread-1" [_thread_blocked, id=12372, stack(0x0000008437f00000,0x0000008438000000)]
  0x0000019daf8e3000 JavaThread "HikariPool-ds_9004 housekeeper" daemon [_thread_blocked, id=2720, stack(0x0000008438000000,0x0000008438100000)]
  0x0000019daf8e1800 JavaThread "HikariPool-ds_9001 housekeeper" daemon [_thread_blocked, id=32884, stack(0x0000008437e00000,0x0000008437f00000)]
  0x0000019dae1bf800 JavaThread "websocket-pool-t-4" [_thread_blocked, id=33784, stack(0x000000843db00000,0x000000843dc00000)]
  0x0000019dae1b6800 JavaThread "HikariPool-master housekeeper" daemon [_thread_blocked, id=34724, stack(0x000000843d900000,0x000000843da00000)]
  0x0000019dad7f7800 JavaThread "mysql-cj-abandoned-connection-cleanup" daemon [_thread_blocked, id=33368, stack(0x000000843d700000,0x000000843d800000)]
  0x0000019dad7f6800 JavaThread "Pure-Timer-1" [_thread_blocked, id=25740, stack(0x000000843d600000,0x000000843d700000)]
  0x0000019dad7ee800 JavaThread "websocket-pool-t-3" [_thread_blocked, id=34092, stack(0x000000843d500000,0x000000843d600000)]
  0x0000019dad7f3800 JavaThread "mybatis-pool-t-1" [_thread_blocked, id=30372, stack(0x000000843d400000,0x000000843d500000)]
  0x0000019dad7f1000 JavaThread "socket-pool-t-3" [_thread_in_native, id=30976, stack(0x000000843d300000,0x000000843d400000)]
  0x0000019dad7ec800 JavaThread "socket-pool-t-2" [_thread_blocked, id=34264, stack(0x000000843d200000,0x000000843d300000)]
  0x0000019dac1fd000 JavaThread "socket-pool-t-1" [_thread_blocked, id=34396, stack(0x000000843d100000,0x000000843d200000)]
  0x0000019da7e4c000 JavaThread "container-0" [_thread_blocked, id=35760, stack(0x000000843d000000,0x000000843d100000)]
  0x0000019daa2e7000 JavaThread "Catalina-utility-2" [_thread_blocked, id=34144, stack(0x000000843cf00000,0x000000843d000000)]
  0x0000019dac206000 JavaThread "Catalina-utility-1" [_thread_blocked, id=27508, stack(0x000000843ce00000,0x000000843cf00000)]
  0x0000019dac204000 JavaThread "websocket-pool-t-2" [_thread_blocked, id=34748, stack(0x000000843cd00000,0x000000843ce00000)]
  0x0000019dac204800 JavaThread "websocket-pool-t-1" [_thread_blocked, id=27644, stack(0x000000843cc00000,0x000000843cd00000)]
  0x0000019da358b800 JavaThread "AsyncAppender-Worker-ASYNC_DOWNLOAD_FILE" daemon [_thread_blocked, id=33240, stack(0x000000843c700000,0x000000843c800000)]
  0x0000019da358b000 JavaThread "AsyncAppender-Worker-ASYNC_FTP_FILE" daemon [_thread_blocked, id=31540, stack(0x000000843c600000,0x000000843c700000)]
  0x0000019da3587800 JavaThread "AsyncAppender-Worker-ASYNC_DB_EDIT_FILE" daemon [_thread_blocked, id=6248, stack(0x000000843c500000,0x000000843c600000)]
  0x0000019da3587000 JavaThread "AsyncAppender-Worker-ASYNC_DB_FILE" daemon [_thread_blocked, id=33152, stack(0x000000843c400000,0x000000843c500000)]
  0x0000019da3586000 JavaThread "AsyncAppender-Worker-ASYNC_VNC_FILE" daemon [_thread_blocked, id=33016, stack(0x000000843c300000,0x000000843c400000)]
  0x0000019da357f000 JavaThread "AsyncAppender-Worker-ASYNC_WEBSOCKET_FILE" daemon [_thread_blocked, id=33948, stack(0x000000843c100000,0x000000843c200000)]
  0x0000019da3585800 JavaThread "AsyncAppender-Worker-ASYNC_SOCKET_FILE" daemon [_thread_blocked, id=34016, stack(0x000000843bc00000,0x000000843bd00000)]
  0x0000019da4ddf000 JavaThread "AsyncAppender-Worker-ASYNC_HTTP_FILE" daemon [_thread_blocked, id=28084, stack(0x000000843c000000,0x000000843c100000)]
  0x0000019da4dde000 JavaThread "AsyncAppender-Worker-ASYNC_ERROR_FILE" daemon [_thread_blocked, id=33680, stack(0x000000843bf00000,0x000000843c000000)]
  0x0000019da4dd4000 JavaThread "AsyncAppender-Worker-ASYNC_INFO_FILE" daemon [_thread_blocked, id=32272, stack(0x000000843be00000,0x000000843bf00000)]
  0x0000019da4dda000 JavaThread "AsyncAppender-Worker-ASYNC_stdout" daemon [_thread_blocked, id=24472, stack(0x000000843ba00000,0x000000843bb00000)]
  0x0000019da4dd7000 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=32152, stack(0x000000843c200000,0x000000843c300000)]
  0x0000019da3580800 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=31424, stack(0x000000843b800000,0x000000843b900000)]
  0x0000019da357e000 JavaThread "Service Thread" daemon [_thread_blocked, id=8228, stack(0x000000843b700000,0x000000843b800000)]
  0x0000019da3680800 JavaThread "C1 CompilerThread11" daemon [_thread_blocked, id=35404, stack(0x000000843b600000,0x000000843b700000)]
  0x0000019da3680000 JavaThread "C1 CompilerThread10" daemon [_thread_blocked, id=25468, stack(0x000000843b500000,0x000000843b600000)]
  0x0000019da367e000 JavaThread "C1 CompilerThread9" daemon [_thread_blocked, id=30928, stack(0x000000843b400000,0x000000843b500000)]
  0x0000019da361d000 JavaThread "C1 CompilerThread8" daemon [_thread_blocked, id=34128, stack(0x000000843b300000,0x000000843b400000)]
  0x0000019da361b000 JavaThread "C2 CompilerThread7" daemon [_thread_blocked, id=35604, stack(0x000000843b200000,0x000000843b300000)]
  0x0000019da3618800 JavaThread "C2 CompilerThread6" daemon [_thread_blocked, id=18592, stack(0x000000843b100000,0x000000843b200000)]
  0x0000019da3617000 JavaThread "C2 CompilerThread5" daemon [_thread_blocked, id=29504, stack(0x000000843b000000,0x000000843b100000)]
  0x0000019da3613800 JavaThread "C2 CompilerThread4" daemon [_thread_blocked, id=33960, stack(0x000000843af00000,0x000000843b000000)]
  0x0000019da3612000 JavaThread "C2 CompilerThread3" daemon [_thread_blocked, id=33864, stack(0x000000843ae00000,0x000000843af00000)]
  0x0000019da3611000 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=33980, stack(0x000000843ad00000,0x000000843ae00000)]
  0x0000019da3610800 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=32876, stack(0x000000843ac00000,0x000000843ad00000)]
  0x0000019da367d800 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=35624, stack(0x000000843ab00000,0x000000843ac00000)]
  0x0000019da357d800 JavaThread "rebel-messaging-executor-61" daemon [_thread_blocked, id=28724, stack(0x000000843aa00000,0x000000843ab00000)]
  0x0000019da3580000 JavaThread "rebel-build-info" daemon [_thread_in_native, id=9668, stack(0x000000843a300000,0x000000843a400000)]
  0x0000019da357c800 JavaThread "rebel-change-detector-thread" daemon [_thread_blocked, id=30920, stack(0x000000843a200000,0x000000843a300000)]
  0x0000019da3583800 JavaThread "rebel-debugger-thread" daemon [_thread_blocked, id=33532, stack(0x000000843a100000,0x000000843a200000)]
  0x0000019da3583000 JavaThread "rebel-debugger-attach-notifier" daemon [_thread_blocked, id=34060, stack(0x000000843a000000,0x000000843a100000)]
  0x0000019da19cd000 JavaThread "rebel-heartbeat-thread" daemon [_thread_blocked, id=23352, stack(0x0000008439f00000,0x000000843a000000)]
  0x0000019da19cc800 JavaThread "rebel-redeploy-thread" daemon [_thread_blocked, id=34500, stack(0x0000008439e00000,0x0000008439f00000)]
  0x0000019da19d2000 JavaThread "rebel-leaseManager-1" daemon [_thread_blocked, id=7380, stack(0x0000008439d00000,0x0000008439e00000)]
  0x0000019da19d0800 JavaThread "rebel-IDENotificationsImpl-PostCycle" daemon [_thread_blocked, id=33364, stack(0x0000008439b00000,0x0000008439c00000)]
  0x0000019da19cb800 JavaThread "rebel-weak-reaper" daemon [_thread_blocked, id=33456, stack(0x0000008439a00000,0x0000008439b00000)]
  0x0000019da19d1000 JavaThread "rebel-fsnotify-OutputReader" daemon [_thread_in_native, id=35236, stack(0x000000843a800000,0x000000843a900000)]
  0x0000019da19cf000 JavaThread "rebel-fsnotify-OutputReader" daemon [_thread_blocked, id=17300, stack(0x000000843a700000,0x000000843a800000)]
  0x0000019da19cf800 JavaThread "rebel-fsnotify-ShutdownOnTermination" daemon [_thread_in_native, id=15208, stack(0x000000843a400000,0x000000843a500000)]
  0x0000019da19ce000 JavaThread "rebel-CacheKeepAlive" daemon [_thread_blocked, id=35804, stack(0x000000843a600000,0x000000843a700000)]
  0x0000019da19d2800 JavaThread "rebel-logger" daemon [_thread_blocked, id=34772, stack(0x000000843a500000,0x000000843a600000)]
  0x0000019d9f03c800 JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=32692, stack(0x0000008439900000,0x0000008439a00000)]
  0x0000019d9f037800 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=35068, stack(0x0000008439800000,0x0000008439900000)]
  0x0000019d9f033800 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=34464, stack(0x0000008439700000,0x0000008439800000)]
  0x0000019d9f011800 JavaThread "Attach Listener" daemon [_thread_blocked, id=7316, stack(0x0000008439600000,0x0000008439700000)]
  0x0000019d9efea800 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=11856, stack(0x0000008439500000,0x0000008439600000)]
  0x0000019dfe5ad800 JavaThread "Finalizer" daemon [_thread_blocked, id=30988, stack(0x0000008439400000,0x0000008439500000)]
  0x0000019dfe5ad000 JavaThread "Reference Handler" daemon [_thread_blocked, id=22560, stack(0x0000008439300000,0x0000008439400000)]

Other Threads:
=>0x0000019d9ef94000 VMThread [stack: 0x0000008439200000,0x0000008439300000] [id=34424]
  0x0000019da4ae9000 WatcherThread [stack: 0x000000843b900000,0x000000843ba00000] [id=2268]

VM state:at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000019dfabe3ed0] Threads_lock - owner thread: 0x0000019d9ef94000

heap address: 0x00000005c4000000, size: 8128 MB, Compressed Oops mode: Zero based, Oop shift amount: 3
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x00000007c0000000

Heap:
 PSYoungGen      total 2355200K, used 858319K [0x0000000716b00000, 0x00000007b8080000, 0x00000007c0000000)
  eden space 2253312K, 33% used [0x0000000716b00000,0x0000000744e06328,0x00000007a0380000)
  from space 101888K, 99% used [0x00000007a6200000,0x00000007ac52dc38,0x00000007ac580000)
  to   space 191488K, 0% used [0x00000007ac580000,0x00000007ac580000,0x00000007b8080000)
 ParOldGen       total 1299456K, used 733544K [0x00000005c4000000, 0x0000000613500000, 0x0000000716b00000)
  object space 1299456K, 56% used [0x00000005c4000000,0x00000005f0c5a388,0x0000000613500000)
 Metaspace       used 312651K, capacity 326881K, committed 327144K, reserved 1347584K
  class space    used 26073K, capacity 28407K, committed 28464K, reserved 1048576K

Card table byte_map: [0x0000019dfb0a0000,0x0000019dfc090000] byte_map_base: 0x0000019df8280000

Marking Bits: (ParMarkBitMap*) 0x000000005e7a0830
 Begin Bits: [0x0000019d8f000000, 0x0000019d96f00000)
 End Bits:   [0x0000019d96f00000, 0x0000019d9ee00000)

Polling page: 0x0000019df9bd0000

CodeCache: size=245760Kb used=52337Kb max_used=67789Kb free=193422Kb
 bounds [0x0000019d80000000, 0x0000019d84270000, 0x0000019d8f000000]
 total_blobs=33342 nmethods=32317 adapters=938
 compilation: enabled

Compilation events (10 events):
Event: 2101.544 Thread 0x0000019da367e000 46448       1       org.apache.catalina.webresources.StandardRoot::_jr$ig$cache (8 bytes)
Event: 2101.545 Thread 0x0000019da367e000 nmethod 46448 0x0000019d828b7650 code [0x0000019d828b77a0, 0x0000019d828b78d8]
Event: 2101.940 Thread 0x0000019da3680000 46449       1       java.nio.file.Files::newDirectoryStream (12 bytes)
Event: 2101.940 Thread 0x0000019da3680000 nmethod 46449 0x0000019d828b7290 code [0x0000019d828b7400, 0x0000019d828b7590]
Event: 2102.834 Thread 0x0000019da3680800 46450       1       org.apache.shardingsphere.infra.rewrite.sql.impl.AbstractSQLBuilder::_jr$ig$sql (16 bytes)
Event: 2102.834 Thread 0x0000019da367e000 46452   !   1       org.apache.shardingsphere.infra.rewrite.sql.impl.AbstractSQLBuilder::_jr$ldcSQLToken70db274e33o192 (14 bytes)
Event: 2102.834 Thread 0x0000019da361d000 46451       1       org.apache.shardingsphere.infra.rewrite.sql.impl.AbstractSQLBuilder::_jr$ig$sqlTokens (16 bytes)
Event: 2102.835 Thread 0x0000019da367e000 nmethod 46452 0x0000019d828b6f10 code [0x0000019d828b7060, 0x0000019d828b71d0]
Event: 2102.838 Thread 0x0000019da361d000 nmethod 46451 0x0000019d828b6a10 code [0x0000019d828b6b80, 0x0000019d828b6d80]
Event: 2102.838 Thread 0x0000019da3680800 nmethod 46450 0x0000019d828b6510 code [0x0000019d828b6680, 0x0000019d828b6880]

GC Heap History (10 events):
Event: 227.049 GC heap before
{Heap before GC invocations=55 (full 6):
 PSYoungGen      total 2579456K, used 185332K [0x0000000716b00000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 2394112K, 0% used [0x0000000716b00000,0x0000000716b00000,0x00000007a8d00000)
  from space 185344K, 99% used [0x00000007a8d00000,0x00000007b41fd158,0x00000007b4200000)
  to   space 194560K, 0% used [0x00000007b4200000,0x00000007b4200000,0x00000007c0000000)
 ParOldGen       total 782848K, used 579193K [0x00000005c4000000, 0x00000005f3c80000, 0x0000000716b00000)
  object space 782848K, 73% used [0x00000005c4000000,0x00000005e759e410,0x00000005f3c80000)
 Metaspace       used 265307K, capacity 273856K, committed 273896K, reserved 1300480K
  class space    used 20811K, capacity 22283K, committed 22320K, reserved 1048576K
Event: 227.995 GC heap after
Heap after GC invocations=55 (full 6):
 PSYoungGen      total 2579456K, used 0K [0x0000000716b00000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 2394112K, 0% used [0x0000000716b00000,0x0000000716b00000,0x00000007a8d00000)
  from space 185344K, 0% used [0x00000007a8d00000,0x00000007a8d00000,0x00000007b4200000)
  to   space 194560K, 0% used [0x00000007b4200000,0x00000007b4200000,0x00000007c0000000)
 ParOldGen       total 1299456K, used 733472K [0x00000005c4000000, 0x0000000613500000, 0x0000000716b00000)
  object space 1299456K, 56% used [0x00000005c4000000,0x00000005f0c48388,0x0000000613500000)
 Metaspace       used 265307K, capacity 273856K, committed 273896K, reserved 1300480K
  class space    used 20811K, capacity 22283K, committed 22320K, reserved 1048576K
}
Event: 234.259 GC heap before
{Heap before GC invocations=56 (full 6):
 PSYoungGen      total 2579456K, used 2394112K [0x0000000716b00000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 2394112K, 100% used [0x0000000716b00000,0x00000007a8d00000,0x00000007a8d00000)
  from space 185344K, 0% used [0x00000007a8d00000,0x00000007a8d00000,0x00000007b4200000)
  to   space 194560K, 0% used [0x00000007b4200000,0x00000007b4200000,0x00000007c0000000)
 ParOldGen       total 1299456K, used 733472K [0x00000005c4000000, 0x0000000613500000, 0x0000000716b00000)
  object space 1299456K, 56% used [0x00000005c4000000,0x00000005f0c48388,0x0000000613500000)
 Metaspace       used 279180K, capacity 289200K, committed 289256K, reserved 1314816K
  class space    used 22211K, capacity 23853K, committed 23856K, reserved 1048576K
Event: 234.297 GC heap after
Heap after GC invocations=56 (full 6):
 PSYoungGen      total 2539520K, used 71080K [0x0000000716b00000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 2344960K, 0% used [0x0000000716b00000,0x0000000716b00000,0x00000007a5d00000)
  from space 194560K, 36% used [0x00000007b4200000,0x00000007b876a0d8,0x00000007c0000000)
  to   space 214528K, 0% used [0x00000007a5d00000,0x00000007a5d00000,0x00000007b2e80000)
 ParOldGen       total 1299456K, used 733480K [0x00000005c4000000, 0x0000000613500000, 0x0000000716b00000)
  object space 1299456K, 56% used [0x00000005c4000000,0x00000005f0c4a388,0x0000000613500000)
 Metaspace       used 279180K, capacity 289200K, committed 289256K, reserved 1314816K
  class space    used 22211K, capacity 23853K, committed 23856K, reserved 1048576K
}
Event: 251.127 GC heap before
{Heap before GC invocations=57 (full 6):
 PSYoungGen      total 2539520K, used 2415585K [0x0000000716b00000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 2344960K, 99% used [0x0000000716b00000,0x00000007a5c8e438,0x00000007a5d00000)
  from space 194560K, 36% used [0x00000007b4200000,0x00000007b876a0d8,0x00000007c0000000)
  to   space 214528K, 0% used [0x00000007a5d00000,0x00000007a5d00000,0x00000007b2e80000)
 ParOldGen       total 1299456K, used 733480K [0x00000005c4000000, 0x0000000613500000, 0x0000000716b00000)
  object space 1299456K, 56% used [0x00000005c4000000,0x00000005f0c4a388,0x0000000613500000)
 Metaspace       used 293132K, capacity 304775K, committed 305000K, reserved 1329152K
  class space    used 23857K, capacity 25698K, committed 25776K, reserved 1048576K
Event: 251.159 GC heap after
Heap after GC invocations=57 (full 6):
 PSYoungGen      total 2559488K, used 67893K [0x0000000716b00000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 2344960K, 0% used [0x0000000716b00000,0x0000000716b00000,0x00000007a5d00000)
  from space 214528K, 31% used [0x00000007a5d00000,0x00000007a9f4d400,0x00000007b2e80000)
  to   space 209408K, 0% used [0x00000007b3380000,0x00000007b3380000,0x00000007c0000000)
 ParOldGen       total 1299456K, used 733496K [0x00000005c4000000, 0x0000000613500000, 0x0000000716b00000)
  object space 1299456K, 56% used [0x00000005c4000000,0x00000005f0c4e388,0x0000000613500000)
 Metaspace       used 293132K, capacity 304775K, committed 305000K, reserved 1329152K
  class space    used 23857K, capacity 25698K, committed 25776K, reserved 1048576K
}
Event: 265.228 GC heap before
{Heap before GC invocations=58 (full 6):
 PSYoungGen      total 2559488K, used 2412853K [0x0000000716b00000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 2344960K, 100% used [0x0000000716b00000,0x00000007a5d00000,0x00000007a5d00000)
  from space 214528K, 31% used [0x00000007a5d00000,0x00000007a9f4d400,0x00000007b2e80000)
  to   space 209408K, 0% used [0x00000007b3380000,0x00000007b3380000,0x00000007c0000000)
 ParOldGen       total 1299456K, used 733496K [0x00000005c4000000, 0x0000000613500000, 0x0000000716b00000)
  object space 1299456K, 56% used [0x00000005c4000000,0x00000005f0c4e388,0x0000000613500000)
 Metaspace       used 303173K, capacity 316203K, committed 316264K, reserved 1339392K
  class space    used 25023K, capacity 27042K, committed 27056K, reserved 1048576K
Event: 265.265 GC heap after
Heap after GC invocations=58 (full 6):
 PSYoungGen      total 2543104K, used 69799K [0x0000000716b00000, 0x00000007bf000000, 0x00000007c0000000)
  eden space 2350080K, 0% used [0x0000000716b00000,0x0000000716b00000,0x00000007a6200000)
  from space 193024K, 36% used [0x00000007b3380000,0x00000007b77a9cf0,0x00000007bf000000)
  to   space 203776K, 0% used [0x00000007a6200000,0x00000007a6200000,0x00000007b2900000)
 ParOldGen       total 1299456K, used 733520K [0x00000005c4000000, 0x0000000613500000, 0x0000000716b00000)
  object space 1299456K, 56% used [0x00000005c4000000,0x00000005f0c54388,0x0000000613500000)
 Metaspace       used 303173K, capacity 316203K, committed 316264K, reserved 1339392K
  class space    used 25023K, capacity 27042K, committed 27056K, reserved 1048576K
}
Event: 1953.940 GC heap before
{Heap before GC invocations=59 (full 6):
 PSYoungGen      total 2543104K, used 2419879K [0x0000000716b00000, 0x00000007bf000000, 0x00000007c0000000)
  eden space 2350080K, 100% used [0x0000000716b00000,0x00000007a6200000,0x00000007a6200000)
  from space 193024K, 36% used [0x00000007b3380000,0x00000007b77a9cf0,0x00000007bf000000)
  to   space 203776K, 0% used [0x00000007a6200000,0x00000007a6200000,0x00000007b2900000)
 ParOldGen       total 1299456K, used 733520K [0x00000005c4000000, 0x0000000613500000, 0x0000000716b00000)
  object space 1299456K, 56% used [0x00000005c4000000,0x00000005f0c54388,0x0000000613500000)
 Metaspace       used 311283K, capacity 325267K, committed 325608K, reserved 1347584K
  class space    used 25918K, capacity 28110K, committed 28208K, reserved 1048576K
Event: 1953.990 GC heap after
Heap after GC invocations=59 (full 6):
 PSYoungGen      total 2355200K, used 101559K [0x0000000716b00000, 0x00000007b8080000, 0x00000007c0000000)
  eden space 2253312K, 0% used [0x0000000716b00000,0x0000000716b00000,0x00000007a0380000)
  from space 101888K, 99% used [0x00000007a6200000,0x00000007ac52dc38,0x00000007ac580000)
  to   space 191488K, 0% used [0x00000007ac580000,0x00000007ac580000,0x00000007b8080000)
 ParOldGen       total 1299456K, used 733544K [0x00000005c4000000, 0x0000000613500000, 0x0000000716b00000)
  object space 1299456K, 56% used [0x00000005c4000000,0x00000005f0c5a388,0x0000000613500000)
 Metaspace       used 311283K, capacity 325267K, committed 325608K, reserved 1347584K
  class space    used 25918K, capacity 28110K, committed 28208K, reserved 1048576K
}

Deoptimization events (0 events):
No events

Classes redefined (10 events):
Event: 1952.020 Thread 0x0000019d9ef94000 redefined class name=org.apache.ibatis.transaction.TransactionFactory, count=1
Event: 1952.065 Thread 0x0000019d9ef94000 redefined class name=org.springframework.cglib.reflect.FastClass, count=2
Event: 2029.366 Thread 0x0000019d9ef94000 redefined class name=org.apache.shardingsphere.infra.rewrite.sql.impl.RouteSQLBuilder, count=2
Event: 2029.429 Thread 0x0000019d9ef94000 redefined class name=org.apache.shardingsphere.infra.rewrite.sql.impl.AbstractSQLBuilder, count=2
Event: 2041.714 Thread 0x0000019d9ef94000 redefined class name=org.apache.shardingsphere.infra.rewrite.sql.impl.RouteSQLBuilder, count=4
Event: 2041.770 Thread 0x0000019d9ef94000 redefined class name=org.apache.shardingsphere.infra.rewrite.sql.impl.AbstractSQLBuilder, count=3
Event: 2056.072 Thread 0x0000019d9ef94000 redefined class name=org.apache.shardingsphere.infra.rewrite.sql.impl.RouteSQLBuilder, count=6
Event: 2056.128 Thread 0x0000019d9ef94000 redefined class name=org.apache.shardingsphere.infra.rewrite.sql.impl.AbstractSQLBuilder, count=4
Event: 2085.688 Thread 0x0000019d9ef94000 redefined class name=org.apache.shardingsphere.infra.rewrite.sql.impl.RouteSQLBuilder, count=8
Event: 2085.756 Thread 0x0000019d9ef94000 redefined class name=org.apache.shardingsphere.infra.rewrite.sql.impl.AbstractSQLBuilder, count=5

Internal exceptions (10 events):
Event: 2071.941 Thread 0x0000019dbd179800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000071a432a90) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 2073.189 Thread 0x0000019dc12e4800 Exception <a 'java/net/UnknownHostException'> (0x000000074188e200) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 739]
Event: 2074.165 Thread 0x0000019dad7f3800 Exception <a 'java/net/ConnectException': Connection timed out: connect> (0x000000073b2d60e8) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 739]
Event: 2081.947 Thread 0x0000019dbd179800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000071a433658) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 2083.870 Thread 0x0000019dc12ec800 Exception <a 'java/net/UnknownHostException'> (0x00000007424eb6a8) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 739]
Event: 2088.834 Thread 0x0000019dc12ec800 Exception <a 'java/net/UnknownHostException'> (0x0000000742ad6050) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 739]
Event: 2091.951 Thread 0x0000019dbd18d000 Exception <a 'sun/nio/fs/WindowsException'> (0x000000071a464a70) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]
Event: 2096.515 Thread 0x0000019dc12ec800 Exception <a 'java/net/UnknownHostException'> (0x0000000743f950a8) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 739]
Event: 2101.482 Thread 0x0000019dc12ec800 Exception <a 'java/net/UnknownHostException'> (0x0000000744252720) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 739]
Event: 2101.939 Thread 0x0000019dbd18d000 Exception <a 'sun/nio/fs/WindowsException'> (0x000000071a465638) thrown at [C:\workspace\openjdk-build\workspace\build\src\hotspot\src\share\vm\prims\jni.cpp, line 712]

Events (10 events):
Event: 2101.939 Thread 0x0000019dbd18d000 DEOPT PACKING pc=0x0000019d80ecb7cc sp=0x0000008441ffecf0
Event: 2101.939 Thread 0x0000019dbd18d000 DEOPT UNPACKING pc=0x0000019d800475d8 sp=0x0000008441ffeac0 mode 1
Event: 2102.097 Executing VM operation: ForceSafepoint
Event: 2102.098 Executing VM operation: ForceSafepoint done
Event: 2102.098 Thread 0x0000019dc12ef800 Thread exited: 0x0000019dc12ef800
Event: 2103.021 Executing VM operation: ChangeBreakpoints
Event: 2103.030 Executing VM operation: ChangeBreakpoints done
Event: 2103.208 Executing VM operation: ForceSafepoint
Event: 2103.209 Executing VM operation: ForceSafepoint done
Event: 2103.214 Executing VM operation: GetOrSetLocal


Dynamic libraries:
0x00007ff7fa670000 - 0x00007ff7fa6c1000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\bin\java.exe
0x00007ffa9e0f0000 - 0x00007ffa9e307000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa9d640000 - 0x00007ffa9d704000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa9b350000 - 0x00007ffa9b723000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa84ff0000 - 0x00007ffa85007000 	C:\DLP\Application\ghijt64.dll
0x00007ffa9db40000 - 0x00007ffa9dbf1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa9d7c0000 - 0x00007ffa9d867000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa9d1e0000 - 0x00007ffa9d288000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa9b200000 - 0x00007ffa9b228000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffa9dc00000 - 0x00007ffa9dd14000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa9d3a0000 - 0x00007ffa9d551000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa9bd60000 - 0x00007ffa9bd86000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa9d2f0000 - 0x00007ffa9d319000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa9b230000 - 0x00007ffa9b34b000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa9ba80000 - 0x00007ffa9bb1a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa9b730000 - 0x00007ffa9b841000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa83590000 - 0x00007ffa83828000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5124_none_270e8f4f7386d69d\COMCTL32.dll
0x00007ffa838a0000 - 0x00007ffa838eb000 	C:\DLP\Application\LdInjectDLL64.dll
0x00007ffa96510000 - 0x00007ffa9651a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa9de20000 - 0x00007ffa9de51000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa83560000 - 0x00007ffa83588000 	C:\DLP\Application\HookDataInteractionx64.dll
0x00007ffa834f0000 - 0x00007ffa83516000 	C:\DLP\Application\TrClientProtect64.dll
0x00007ffa36c60000 - 0x00007ffa36dd7000 	C:\DLP\Application\LdGetPrintContent64.dll
0x00007ffa9d870000 - 0x00007ffa9da11000 	C:\WINDOWS\System32\ole32.dll
0x00007ffa9c110000 - 0x00007ffa9c4a2000 	C:\WINDOWS\System32\combase.dll
0x00007ffa75270000 - 0x00007ffa75318000 	C:\WINDOWS\SYSTEM32\WINSPOOL.DRV
0x00007ffa9be10000 - 0x00007ffa9bf1a000 	C:\WINDOWS\System32\shcore.dll
0x00007ffa9aa10000 - 0x00007ffa9aa1c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.DLL
0x00007ffa9c4b0000 - 0x00007ffa9cd46000 	C:\WINDOWS\System32\Shell32.dll
0x00007ffa9bba0000 - 0x00007ffa9bcdf000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffa36be0000 - 0x00007ffa36c54000 	C:\DLP\Application\LdSmartEnc64.dll
0x00007ffa70270000 - 0x00007ffa7027b000 	C:\WINDOWS\SYSTEM32\FLTLIB.DLL
0x00007ffa36fc0000 - 0x00007ffa373ac000 	C:\DLP\Application\LdWaterMarkHook64.dll
0x00007ffa9e090000 - 0x00007ffa9e098000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffa657a0000 - 0x00007ffa6595a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.22621.5124_none_57f98234ce19506b\gdiplus.dll
0x00007ffa87d00000 - 0x00007ffa87d15000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\vcruntime140.dll
0x00007ffa36de0000 - 0x00007ffa36fb2000 	C:\DLP\Application\LdPrintMonitor64.dll
0x00007ffa9bd90000 - 0x00007ffa9be01000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa82310000 - 0x00007ffa823ab000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\msvcp140.dll
0x00007ffa36a70000 - 0x00007ffa36bdd000 	C:\DLP\Application\EncAppCtrl64.dll
0x00007ffa9de60000 - 0x00007ffa9dec3000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffa9d560000 - 0x00007ffa9d637000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffa63b70000 - 0x00007ffa63ba1000 	C:\WINDOWS\SYSTEM32\Mapi32.dll
0x00007ffa9bf20000 - 0x00007ffa9bf3f000 	C:\WINDOWS\System32\imagehlp.dll
0x00007ffa9a230000 - 0x00007ffa9a248000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x000000005dfc0000 - 0x000000005e819000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\server\jvm.dll
0x00007ffa96200000 - 0x00007ffa96209000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffa8f970000 - 0x00007ffa8f9a4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa78f30000 - 0x00007ffa78f4f000 	C:\DLP\Application\HookCreateProcessInternal64.dll
0x00007ffa88130000 - 0x00007ffa88140000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\verify.dll
0x00007ffa369f0000 - 0x00007ffa36a66000 	C:\DLP\Application\MonFileOp64.dll
0x00007ffa820c0000 - 0x00007ffa820de000 	C:\WINDOWS\SYSTEM32\MPR.dll
0x00007ffa369b0000 - 0x00007ffa369e1000 	C:\DLP\Application\LdSensitiveTaskCenter64.dll
0x00007ffa873e0000 - 0x00007ffa8740b000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\java.dll
0x00007ffa7f2f0000 - 0x00007ffa7f326000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\jdwp.dll
0x00007ffa882a0000 - 0x00007ffa882a9000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\npt.dll
0x00007ffa7b5b0000 - 0x00007ffa7b5fe000 	C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2024.3\plugins\jr-ide-idea\jrebel6\lib\jrebel64.dll
0x00007ffa7b920000 - 0x00007ffa7b959000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\instrument.dll
0x00007ffa82410000 - 0x00007ffa82428000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\zip.dll
0x00007ffa99120000 - 0x00007ffa99a3a000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa9b130000 - 0x00007ffa9b15b000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffa87f90000 - 0x00007ffa87f9a000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\dt_socket.dll
0x00007ffa9a7a0000 - 0x00007ffa9a80a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa823f0000 - 0x00007ffa8240c000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\net.dll
0x00007ffa80a50000 - 0x00007ffa80a63000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\nio.dll
0x00007ffa822e0000 - 0x00007ffa82304000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\sunec.dll
0x00007ffa87070000 - 0x00007ffa8707d000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\management.dll
0x00007ffa87d40000 - 0x00007ffa87d4e000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\jre\bin\sunmscapi.dll
0x00007ffa9b850000 - 0x00007ffa9b9b7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffa9abb0000 - 0x00007ffa9abdd000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffa9ab70000 - 0x00007ffa9aba7000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffa9a9f0000 - 0x00007ffa9aa0b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa9a190000 - 0x00007ffa9a1c7000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa9a890000 - 0x00007ffa9a8b8000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa9bce0000 - 0x00007ffa9bd5b000 	C:\WINDOWS\System32\bcryptprimitives.dll
0x00007ffa99ca0000 - 0x00007ffa99ccd000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa9d1d0000 - 0x00007ffa9d1d9000 	C:\WINDOWS\System32\NSI.dll
0x00007ffa96520000 - 0x00007ffa96539000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffa95b50000 - 0x00007ffa95b6f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffa99d10000 - 0x00007ffa99e12000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffa933e0000 - 0x00007ffa933ea000 	C:\Windows\System32\rasadhlp.dll
0x00007ffa941d0000 - 0x00007ffa94253000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffa63a30000 - 0x00007ffa63a47000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffa63a10000 - 0x00007ffa63a2b000 	C:\WINDOWS\system32\pnrpnsp.dll
0x00007ffa639f0000 - 0x00007ffa63a01000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffa94a90000 - 0x00007ffa94aa5000 	C:\WINDOWS\system32\wshbth.dll
0x00007ffa639c0000 - 0x00007ffa639e7000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ffa7f2d0000 - 0x00007ffa7f2eb000 	C:\Users\<USER>\.jrebel\jrebel-temp\ver-7c19eea5\IdeaWin64.dll
0x00007ff9e7f80000 - 0x00007ff9eafc6000 	C:\Users\<USER>\AppData\Local\Temp\opencv_openpnp621775542491275604\nu\pattern\opencv\windows\x86_64\opencv_java451.dll
0x00007ffa723d0000 - 0x00007ffa7259e000 	C:\WINDOWS\SYSTEM32\MFPlat.DLL
0x00007ffa60480000 - 0x00007ffa6050a000 	C:\WINDOWS\SYSTEM32\MF.dll
0x00007ffa89090000 - 0x00007ffa891b1000 	C:\WINDOWS\SYSTEM32\MFReadWrite.dll
0x00007ffa9aec0000 - 0x00007ffa9af0e000 	C:\WINDOWS\SYSTEM32\cfgmgr32.dll
0x00007ffa16340000 - 0x00007ffa166db000 	C:\WINDOWS\SYSTEM32\MFCORE.DLL
0x00007ffa88a90000 - 0x00007ffa88ac3000 	C:\WINDOWS\SYSTEM32\RTWorkQ.DLL
0x00007ffa648b0000 - 0x00007ffa648f5000 	C:\Users\<USER>\AppData\Local\Temp\jna-115883602\jna6407017904500334277.dll
0x00007ffa646f0000 - 0x00007ffa6474f000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\bin\Ldwebdf.dll
0x00007ffa70fb0000 - 0x00007ffa71019000 	C:\WINDOWS\SYSTEM32\OLEACC.dll
0x0000000180000000 - 0x00000001800b3000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\bin\lddf_64.dll
0x00007ffa64660000 - 0x00007ffa646ec000 	C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\bin\mtoken_gm3000_64.dll
0x00007ffa9cd50000 - 0x00007ffa9d1c4000 	C:\WINDOWS\System32\SETUPAPI.dll
0x00007ffa99a40000 - 0x00007ffa99a4e000 	C:\WINDOWS\SYSTEM32\HID.DLL

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:53476,suspend=y,server=n -Drebel.base=C:\Users\<USER>\.jrebel -Drebel.env.ide.plugin.build=2d7357bde6d770c5760a889e2d4bf4d26276ce12 -Drebel.env.ide.plugin.version=2025.2.0 -Drebel.env.ide.version=2024.3.5 -Drebel.env.ide.product=IU -Drebel.env.ide=intellij -Drebel.notification.url=http://localhost:63500 -agentpath:C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2024.3\plugins\jr-ide-idea\jrebel6\lib\jrebel64.dll -Dspring.profiles.active=dev -Dcool.request.port=63319 -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2024.3\plugins\bean-invoker-intellij-plugin\lib\bean-invoker-agent-1.0.3-with-dependencies.jar -Dspring.invokePort=6060 -Drebel.plugins=C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2024.3\plugins\jr-mp-ide-idea\lib\jr-mybatisplus-1.0.7.jar -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2024.3\captureAgent\debugger-agent.jar=file:/C:/Users/<USER>/AppData/Local/Temp/capture2.props -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 
java_command: com.tipray.dlp.app.APP
java_class_path (initial): C:\Users\<USER>\AppData\Local\Temp\classpath1000736427.jar;C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2024.3\plugins\bean-invoker-intellij-plugin\lib\bean-invoker-agent-1.0.3-with-dependencies.jar;C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2024.3\captureAgent\debugger-agent.jar
Launcher Type: SUN_STANDARD

Environment Variables:
PATH=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;D:\VMware\VMware Workstation\bin\;C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\TortoiseSVN\bin;C:\Program Files\Git\cmd;C:\Program Files\MySQL\MySQL Server 5.7\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\gradle-8.14\bin;;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Pandoc\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\cursor\resources\app\bin
USERNAME=zhuhs
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 191 Stepping 2, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 11 , 64 bit Build 22621 (10.0.22621.5124)

CPU:total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 191 stepping 2, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx

Memory: 4k page, physical 33289648k(7044556k free), swap 57406896k(21239852k free)

vm_info: OpenJDK 64-Bit Server VM (25.412-b08) for windows-amd64 JRE (1.8.0_412-b08), built on Apr 17 2024 02:10:30 by "jenkins" with MS VC++ 15.9 (VS2017)

time: Mon Aug  4 17:35:38 2025
timezone: Intel64 Family 6 Model 191 Stepping 2, GenuineIntel
elapsed time: 2103.224450 seconds (0d 0h 35m 3s)

