0805 08:52:29.436 - <PERSON><PERSON><PERSON> 32328 [sync-scheduler-pool-t-1] c.t.d.s.SocketFactory     : daemon scheduled perhaps is not working, going to stop
0805 08:52:29.437 - <PERSON><PERSON><PERSON> 32328 [sync-scheduler-pool-t-1] c.t.d.s.SocketFactory     : new daemon scheduled
0805 08:52:29.439 - <PERSON>R<PERSON> 32328 [sync-scheduler-pool-t-1] c.t.d.s.SocketFactory     : heartbeat scheduled perhaps is not working, going to stop
0805 08:52:29.439 - WAR<PERSON> 32328 [sync-scheduler-pool-t-1] c.t.d.s.SocketFactory     : new heartbeat scheduled
0805 08:52:30.850 -ERROR 32328 [socket-pool-t-1] c.t.d.s.SelectorThread    : close socket selector
0805 08:52:30.853 - WARN 32328 [socket-pool-t-1] c.t.d.s.AbstractClient    : close socket client[9003_23682527453900]
0805 08:52:30.855 - WAR<PERSON> 32328 [socket-pool-t-1] c.t.d.s.SocketFactory     : restart new socket client[9003_23682527453900]!
0805 08:52:30.857 - WAR<PERSON> 32328 [socket-pool-t-3] c.t.d.s.AbstractClient    : close socket client[9003_23682527453900]
0805 08:52:30.892 -ERROR 32328 [socket-pool-t-3] c.t.d.s.u.LogUtil         : socket client[9003_23682527453900] exception 
java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at com.tipray.dlp.socket.SelectorThread.run(SelectorThread.java:57)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
0805 08:52:35.440 - WARN 32328 [socket-pool-t-1] c.t.d.s.SocketFactory     : restart new socket client[9003_23682527453900]!
0805 08:52:35.441 - WARN 32328 [socket-pool-t-3] c.t.d.s.AbstractClient    : close socket client[9003_23682527453900]
0805 08:52:35.442 -ERROR 32328 [socket-pool-t-3] c.t.d.s.u.LogUtil         : socket client[9003_23682527453900] exception 
java.net.SocketException: Network is unreachable: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at com.tipray.dlp.socket.SelectorThread.run(SelectorThread.java:57)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
0805 08:52:40.446 - WARN 32328 [socket-pool-t-2] c.t.d.s.SocketFactory     : restart new socket client[9003_23682527453900]!
0805 08:52:40.449 - WARN 32328 [socket-pool-t-3] c.t.d.s.AbstractClient    : close socket client[9003_23682527453900]
0805 08:52:40.450 -ERROR 32328 [socket-pool-t-3] c.t.d.s.u.LogUtil         : socket client[9003_23682527453900] exception 
java.net.SocketException: Network is unreachable: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at com.tipray.dlp.socket.SelectorThread.run(SelectorThread.java:57)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
0805 08:52:45.448 - WARN 32328 [socket-pool-t-2] c.t.d.s.SocketFactory     : restart new socket client[9003_23682527453900]!
0805 08:52:56.518 - WARN 32328 [socket-pool-t-3] c.t.d.s.AbstractClient    : close socket client[9003_23682527453900]
0805 08:52:56.518 -ERROR 32328 [socket-pool-t-3] c.t.d.s.u.LogUtil         : socket client[9003_23682527453900] exception 
java.net.SocketException: Software caused connection abort: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at com.tipray.dlp.socket.SelectorThread.run(SelectorThread.java:57)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
0805 08:53:00.440 - WARN 32328 [socket-pool-t-1] c.t.d.s.SocketFactory     : restart new socket client[9003_23682527453900]!
0805 08:53:19.988 - WARN 32328 [socket-pool-t-3] c.t.d.s.AbstractClient    : close socket client[9003_23682527453900]
0805 08:53:19.989 -ERROR 32328 [socket-pool-t-3] c.t.d.s.u.LogUtil         : socket client[9003_23682527453900] exception 
java.net.SocketException: Software caused connection abort: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at com.tipray.dlp.socket.SelectorThread.run(SelectorThread.java:57)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
0805 08:53:20.440 - WARN 32328 [socket-pool-t-1] c.t.d.s.SocketFactory     : restart new socket client[9003_23682527453900]!
0805 08:53:20.441 - WARN 32328 [socket-pool-t-3] c.t.d.s.AbstractClient    : socket client[9003_23682527453900] connect success
0805 08:53:30.448 - WARN 32328 [socket-pool-t-3] c.t.d.s.l.LoginPackageInterceptor : socket client[9003_23682527453900] login success
0805 09:02:29.231 - INFO 32328 [socket-pool-t-3] c.t.d.s.l.LoggerPackageInterceptor : send pkg: AppHead(hdrLen=0, ver=0, msgProp=0, msgBodyLen=0, srcId=9003, dstId=1, msgSn=6, subPack=null, msgCode=0x050107)
0805 09:02:29.234 - INFO 32328 [socket-pool-t-3] c.t.d.s.l.LoggerPackageInterceptor : recv pkg: AppHead(hdrLen=21, ver=16, msgProp=0, msgBodyLen=845, srcId=1, dstId=9003, msgSn=6, subPack=null, msgCode=0x050187)
0805 09:21:29.397 - WARN 5396 [           main] c.t.d.s.SocketFactory     : ====================================socket log start===========================================
0805 09:21:29.401 - WARN 5396 [           main] c.t.d.s.SocketFactory     : new daemon scheduled
0805 09:21:29.408 - WARN 5396 [           main] c.t.d.s.SocketFactory     : new heartbeat scheduled
0805 09:21:29.436 - WARN 5396 [socket-pool-t-3] c.t.d.s.AbstractClient    : socket client[9003_503237467700] connect success
0805 09:21:30.571 - WARN 5396 [socket-pool-t-3] c.t.d.s.l.LoginPackageInterceptor : socket client[9003_503237467700] login success
