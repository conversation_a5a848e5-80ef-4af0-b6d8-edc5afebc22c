0805 08:52:28.500 - <PERSON><PERSON><PERSON> 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 08:52:28.688 - WAR<PERSON> 32328 [HikariPool-ds_9004 housekeeper] c.z.h.p.HikariPool$HouseKeeper : HikariPool-ds_9004 - Thread starvation or clock leap detected (housekeeper delta=14h48m57s164ms87µs800ns).
0805 08:52:29.694 - WARN 32328 [ol-Jdbc<PERSON>ersistRepo housekeeper] c.z.h.p.HikariPool$HouseKeeper : HikariPool-JdbcPersistRepo - Thread starvation or clock leap detected (housekeeper delta=14h48m57s167ms453µs).
0805 08:52:33.515 - WAR<PERSON> 32328 [mybatis-pool-t-1] c.z.h.p.PoolBase          : HikariPool-ds_9004 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@34ddcccd (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
0805 08:52:33.654 - WARN 32328 [pool-198-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 08:52:53.692 - WARN 32328 [HikariPool-ds_9001 housekeeper] c.z.h.p.HikariPool$HouseKeeper : HikariPool-ds_9001 - Thread starvation or clock leap detected (housekeeper delta=14h48m57s156ms218µs600ns).
0805 08:52:56.751 - WARN 32328 [HikariPool-master housekeeper] c.z.h.p.HikariPool$HouseKeeper : HikariPool-master - Thread starvation or clock leap detected (housekeeper delta=14h48m57s165ms635µs200ns).
0805 08:52:58.503 -ERROR 32328 [mybatis-pool-t-1] c.t.d.m.s.s.p.MyHikariDataSource : [MyHikariDataSource]数据源连接检查失败: HikariPool-ds_9004 - Connection is not available, request timed out after 30001ms.
java.sql.SQLTransientConnectionException: HikariPool-ds_9004 - Connection is not available, request timed out after 30001ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at com.tipray.dlp.mybatis.sharding.spi.pool.MyHikariDataSource.checkConnected(MyHikariDataSource.java:88)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at com.tipray.dlp.mybatis.sharding.MyShardingSphereDataSource.checkConnected(MyShardingSphereDataSource.java:141)
	at com.tipray.dlp.mybatis.datasource.MybatisShardingDataSourceBuilder.lambda$observe$0(MybatisShardingDataSourceBuilder.java:191)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:165)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:55)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:861)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:234)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:180)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	... 3 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.GeneratedConstructorAccessor255.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:52)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:95)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:140)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:156)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:79)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:139)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:980)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:851)
	... 14 common frames omitted
Caused by: java.net.SocketException: Software caused connection abort: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:144)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:53)
	... 17 common frames omitted
0805 08:53:01.039 -ERROR 32328 [sync-scheduler-pool-t-1] c.t.d.m.s.MyShardingSphereDataSource : check table exists error:
java.sql.SQLTransientConnectionException: HikariPool-ds_9004 - Connection is not available, request timed out after 30014ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at com.tipray.dlp.mybatis.sharding.spi.pool.MyHikariDataSource.getConnection(MyHikariDataSource.java:134)
	at com.tipray.dlp.mybatis.sharding.MyShardingSphereDataSource.existsTable(MyShardingSphereDataSource.java:332)
	at com.tipray.dlp.mybatis.sharding.MyShardingSphereDataSource.existsActualTable(MyShardingSphereDataSource.java:304)
	at com.tipray.dlp.mybatis.sharding.strategy.algorithm.HintTableShardingAlgorithm.lambda$doSharding$0(HintTableShardingAlgorithm.java:61)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:174)
	at java.util.Iterator.forEachRemaining(Iterator.java:116)
	at java.util.Spliterators$IteratorSpliterator.forEachRemaining(Spliterators.java:1801)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at com.tipray.dlp.mybatis.sharding.strategy.algorithm.HintTableShardingAlgorithm.doSharding(HintTableShardingAlgorithm.java:63)
	at org.apache.shardingsphere.sharding.route.strategy.type.hint.HintShardingStrategy.doSharding(HintShardingStrategy.java:55)
	at org.apache.shardingsphere.sharding.route.engine.type.standard.ShardingStandardRouteEngine.routeTables(ShardingStandardRouteEngine.java:275)
	at org.apache.shardingsphere.sharding.route.engine.type.standard.ShardingStandardRouteEngine.route0(ShardingStandardRouteEngine.java:254)
	at org.apache.shardingsphere.sharding.route.engine.type.standard.ShardingStandardRouteEngine.routeByHint(ShardingStandardRouteEngine.java:125)
	at org.apache.shardingsphere.sharding.route.engine.type.standard.ShardingStandardRouteEngine.getDataNodes(ShardingStandardRouteEngine.java:101)
	at org.apache.shardingsphere.sharding.route.engine.type.standard.ShardingStandardRouteEngine.route(ShardingStandardRouteEngine.java:86)
	at org.apache.shardingsphere.sharding.route.engine.ShardingSQLRouter.createRouteContext0(ShardingSQLRouter.java:74)
	at org.apache.shardingsphere.sharding.route.engine.ShardingSQLRouter.createRouteContext(ShardingSQLRouter.java:60)
	at org.apache.shardingsphere.sharding.route.engine.ShardingSQLRouter.createRouteContext(ShardingSQLRouter.java:47)
	at org.apache.shardingsphere.infra.route.engine.SQLRouteEngine.route(SQLRouteEngine.java:113)
	at org.apache.shardingsphere.infra.route.engine.SQLRouteEngine.route(SQLRouteEngine.java:97)
	at org.apache.shardingsphere.infra.connection.kernel.KernelProcessor.route(KernelProcessor.java:68)
	at org.apache.shardingsphere.infra.connection.kernel.KernelProcessor.generateExecutionContext(KernelProcessor.java:51)
	at org.apache.shardingsphere.driver.executor.engine.DriverExecuteExecutor.execute(DriverExecuteExecutor.java:113)
	at org.apache.shardingsphere.driver.executor.engine.facade.DriverExecutorFacade.execute(DriverExecutorFacade.java:141)
	at org.apache.shardingsphere.driver.jdbc.core.statement.ShardingSpherePreparedStatement.execute(ShardingSpherePreparedStatement.java:247)
	at sun.reflect.GeneratedMethodAccessor112.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy1663.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at sun.reflect.GeneratedMethodAccessor329.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:84)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1653.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.GeneratedMethodAccessor327.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.interceptUpdate(TRMybatisPlusInterceptor.java:158)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:75)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1652.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.delete(DefaultSqlSession.java:212)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	at com.sun.proxy.$Proxy149.delete(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.delete(SqlSessionTemplate.java:304)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:69)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy407.deleteByVo(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy409.deleteByVo(Unknown Source)
	at com.tipray.dlp.service.impl.DbBackupNoticeMsgServiceImpl.deleteOverTimeBackNote(DbBackupNoticeMsgServiceImpl.java:83)
	at com.tipray.dlp.service.impl.DbBackupNoticeMsgServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.service.impl.DbBackupNoticeMsgServiceImpl$$EnhancerBySpringCGLIB$$1.deleteOverTimeBackNote(<generated>)
	at com.tipray.dlp.service.job.DbBackupNotificationJob.execute(DbBackupNotificationJob.java:32)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:165)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:55)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:861)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:234)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:180)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	... 3 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.GeneratedConstructorAccessor255.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:52)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:95)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:140)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:156)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:79)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:139)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:980)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:851)
	... 14 common frames omitted
Caused by: java.net.SocketException: Software caused connection abort: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:144)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:53)
	... 17 common frames omitted
0805 08:53:01.234 -ERROR 32328 [sync-scheduler-pool-t-1] o.s.s.s.TaskUtils$LoggingErrorHandler : Unexpected error occurred in scheduled task
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
### The error may exist in com/tipray/dlp/dao/StgIssueDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT data_type, max(id) AS id, stg_type FROM stg_issue WHERE (data_type NOT IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND data_type NOT IN (?, ?)) GROUP BY data_type
### Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy149.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy368.selectList(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor243.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy371.selectList(Unknown Source)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.listToShowStgIssue(StgIssueServiceImpl.java:122)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.getNoticeSize(StgIssueServiceImpl.java:379)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.notice(StgIssueServiceImpl.java:365)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$EnhancerBySpringCGLIB$$1.notice(<generated>)
	at com.tipray.dlp.service.job.DbBackupNotificationJob.execute(DbBackupNotificationJob.java:33)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:113)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:938)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:359)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor112.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy1663.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at sun.reflect.GeneratedMethodAccessor107.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:84)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1653.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.executeQuery(TRMybatisPlusInterceptor.java:181)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.interceptQuery(TRMybatisPlusInterceptor.java:141)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:72)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1652.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.GeneratedMethodAccessor110.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 54 common frames omitted
0805 08:53:07.100 - WARN 32328 [http-nio-28080-exec-4] c.t.d.c.UsbTermController : 获取U盘插件安装包版本，插件安装包不存在
0805 08:53:07.113 - WARN 32328 [http-nio-28080-exec-3] c.t.d.c.UsbTermController : 获取U盘插件安装包版本，插件安装包不存在
0805 08:53:50.353 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 08:54:41.680 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 08:55:33.680 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 08:56:25.670 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 08:57:17.689 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 08:57:34.764 - WARN 32328 [pool-198-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 08:58:09.682 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 08:59:01.680 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 08:59:53.675 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:00:45.682 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:01:37.693 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:02:26.396 - WARN 32328 [http-nio-28080-exec-10] c.t.d.c.UsbTermController : 获取U盘插件安装包版本，插件安装包不存在
0805 09:02:26.411 - WARN 32328 [http-nio-28080-exec-3] c.t.d.c.UsbTermController : 获取U盘插件安装包版本，插件安装包不存在
0805 09:02:29.669 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:02:34.342 -ERROR 32328 [http-nio-28080-exec-1] c.t.d.a.SyslogAspect      : admin invoke com.tipray.dlp.controller.StgIssueController.getNoticeSize[throw Exception]：
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
### The error may exist in com/tipray/dlp/dao/StgIssueDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT data_type, max(id) AS id, stg_type FROM stg_issue WHERE (data_type NOT IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND data_type NOT IN (?, ?)) GROUP BY data_type
### Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy149.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy368.selectList(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor243.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy371.selectList(Unknown Source)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.listToShowStgIssue(StgIssueServiceImpl.java:122)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.getNoticeSize(StgIssueServiceImpl.java:379)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at com.tipray.dlp.controller.StgIssueController.getNoticeSize(StgIssueController.java:70)
	at com.tipray.dlp.controller.StgIssueController$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.controller.StgIssueController$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.tipray.dlp.websocket.WSocketServletRequestFilter.doFilterInternal(WSocketServletRequestFilter.java:76)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.ExpiredSessionFilter.doFilterInternal(ExpiredSessionFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.JWTAuthenticationFilter.doFilterInternal(JWTAuthenticationFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:113)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:938)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:359)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor112.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy1663.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at sun.reflect.GeneratedMethodAccessor107.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:84)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1653.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.executeQuery(TRMybatisPlusInterceptor.java:181)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.interceptQuery(TRMybatisPlusInterceptor.java:141)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:72)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1652.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.GeneratedMethodAccessor110.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 166 common frames omitted
0805 09:02:34.344 -ERROR 32328 [http-nio-28080-exec-1] c.t.d.a.ControllerResponseAdvice : 
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
### The error may exist in com/tipray/dlp/dao/StgIssueDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT data_type, max(id) AS id, stg_type FROM stg_issue WHERE (data_type NOT IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND data_type NOT IN (?, ?)) GROUP BY data_type
### Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy149.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy368.selectList(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor243.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy371.selectList(Unknown Source)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.listToShowStgIssue(StgIssueServiceImpl.java:122)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.getNoticeSize(StgIssueServiceImpl.java:379)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at com.tipray.dlp.controller.StgIssueController.getNoticeSize(StgIssueController.java:70)
	at com.tipray.dlp.controller.StgIssueController$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.controller.StgIssueController$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.tipray.dlp.websocket.WSocketServletRequestFilter.doFilterInternal(WSocketServletRequestFilter.java:76)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.ExpiredSessionFilter.doFilterInternal(ExpiredSessionFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.JWTAuthenticationFilter.doFilterInternal(JWTAuthenticationFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:113)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:938)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:359)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor112.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy1663.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at sun.reflect.GeneratedMethodAccessor107.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:84)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1653.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.executeQuery(TRMybatisPlusInterceptor.java:181)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.interceptQuery(TRMybatisPlusInterceptor.java:141)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:72)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1652.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.GeneratedMethodAccessor110.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 166 common frames omitted
0805 09:02:35.146 - WARN 32328 [pool-198-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 09:02:41.921 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.a.ShardingAlgorithmHelper : [路由表计算] 未找到匹配的分片表，使用模板表: usb_device_opera_log
0805 09:02:42.352 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.a.ShardingAlgorithmHelper : [路由表计算] Schema检查耗时过长: 430ms
0805 09:02:42.759 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.a.ShardingAlgorithmHelper : [路由表计算] Schema存在性检查耗时过长 - 数据源: ds_9004, 数据库: dlp_data_202408, 耗时: 406ms
0805 09:02:43.158 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.a.ShardingAlgorithmHelper : [路由表计算] Schema存在性检查耗时过长 - 数据源: ds_9004, 数据库: dlp_data_202409, 耗时: 395ms
0805 09:02:43.159 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.a.ShardingAlgorithmHelper : [路由表计算] 分片表数量过多: 32，可能严重影响性能
0805 09:02:43.530 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 370ms - 表: dlp_data_202408.usb_device_opera_log_20240801
0805 09:02:43.531 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 371ms - 逻辑表: usb_device_opera_log
0805 09:02:43.531 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 371ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:43.898 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 366ms - 表: dlp_data_202408.usb_device_opera_log_20240802
0805 09:02:43.899 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 367ms - 逻辑表: usb_device_opera_log
0805 09:02:43.900 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 367ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:44.286 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 386ms - 表: dlp_data_202408.usb_device_opera_log_20240803
0805 09:02:44.287 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 387ms - 逻辑表: usb_device_opera_log
0805 09:02:44.287 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 387ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:44.691 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 403ms - 表: dlp_data_202408.usb_device_opera_log_20240804
0805 09:02:44.691 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 403ms - 逻辑表: usb_device_opera_log
0805 09:02:44.691 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 403ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:45.092 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 400ms - 表: dlp_data_202408.usb_device_opera_log_20240805
0805 09:02:45.093 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 401ms - 逻辑表: usb_device_opera_log
0805 09:02:45.093 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 401ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:45.479 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 385ms - 表: dlp_data_202408.usb_device_opera_log_20240806
0805 09:02:45.480 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 386ms - 逻辑表: usb_device_opera_log
0805 09:02:45.480 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 386ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:45.853 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 373ms - 表: dlp_data_202408.usb_device_opera_log_20240807
0805 09:02:45.854 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 374ms - 逻辑表: usb_device_opera_log
0805 09:02:45.855 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 374ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:46.243 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 388ms - 表: dlp_data_202408.usb_device_opera_log_20240808
0805 09:02:46.244 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 389ms - 逻辑表: usb_device_opera_log
0805 09:02:46.244 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 389ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:46.629 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 385ms - 表: dlp_data_202408.usb_device_opera_log_20240809
0805 09:02:46.629 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 385ms - 逻辑表: usb_device_opera_log
0805 09:02:46.630 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 385ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:47.000 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 370ms - 表: dlp_data_202408.usb_device_opera_log_20240810
0805 09:02:47.000 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 370ms - 逻辑表: usb_device_opera_log
0805 09:02:47.001 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 370ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:47.387 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 386ms - 表: dlp_data_202408.usb_device_opera_log_20240811
0805 09:02:47.388 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 387ms - 逻辑表: usb_device_opera_log
0805 09:02:47.388 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 387ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:47.766 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 378ms - 表: dlp_data_202408.usb_device_opera_log_20240812
0805 09:02:47.766 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 378ms - 逻辑表: usb_device_opera_log
0805 09:02:47.766 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 378ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:48.140 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 374ms - 表: dlp_data_202408.usb_device_opera_log_20240813
0805 09:02:48.140 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 374ms - 逻辑表: usb_device_opera_log
0805 09:02:48.140 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 374ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:48.568 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 428ms - 表: dlp_data_202408.usb_device_opera_log_20240814
0805 09:02:48.568 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 428ms - 逻辑表: usb_device_opera_log
0805 09:02:48.568 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 428ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:48.944 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 375ms - 表: dlp_data_202408.usb_device_opera_log_20240815
0805 09:02:48.944 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 375ms - 逻辑表: usb_device_opera_log
0805 09:02:48.945 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 375ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:49.370 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 425ms - 表: dlp_data_202408.usb_device_opera_log_20240816
0805 09:02:49.371 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 426ms - 逻辑表: usb_device_opera_log
0805 09:02:49.371 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 426ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:49.751 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 380ms - 表: dlp_data_202408.usb_device_opera_log_20240817
0805 09:02:49.753 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 382ms - 逻辑表: usb_device_opera_log
0805 09:02:49.754 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 382ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:50.130 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 375ms - 表: dlp_data_202408.usb_device_opera_log_20240818
0805 09:02:50.133 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 378ms - 逻辑表: usb_device_opera_log
0805 09:02:50.134 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 378ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:50.562 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 427ms - 表: dlp_data_202408.usb_device_opera_log_20240819
0805 09:02:50.562 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 428ms - 逻辑表: usb_device_opera_log
0805 09:02:50.562 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 428ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:50.986 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 423ms - 表: dlp_data_202408.usb_device_opera_log_20240820
0805 09:02:50.987 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 424ms - 逻辑表: usb_device_opera_log
0805 09:02:50.987 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 424ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:51.385 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 397ms - 表: dlp_data_202408.usb_device_opera_log_20240821
0805 09:02:51.386 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 398ms - 逻辑表: usb_device_opera_log
0805 09:02:51.387 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 398ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:51.804 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 417ms - 表: dlp_data_202408.usb_device_opera_log_20240822
0805 09:02:51.805 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 418ms - 逻辑表: usb_device_opera_log
0805 09:02:51.805 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 418ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:52.184 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 379ms - 表: dlp_data_202408.usb_device_opera_log_20240823
0805 09:02:52.184 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 379ms - 逻辑表: usb_device_opera_log
0805 09:02:52.184 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 379ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:52.593 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 408ms - 表: dlp_data_202408.usb_device_opera_log_20240824
0805 09:02:52.594 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 409ms - 逻辑表: usb_device_opera_log
0805 09:02:52.595 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 409ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:52.996 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 401ms - 表: dlp_data_202408.usb_device_opera_log_20240825
0805 09:02:52.996 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 401ms - 逻辑表: usb_device_opera_log
0805 09:02:52.997 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 401ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:53.397 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 400ms - 表: dlp_data_202408.usb_device_opera_log_20240826
0805 09:02:53.397 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 400ms - 逻辑表: usb_device_opera_log
0805 09:02:53.397 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 400ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:53.821 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 424ms - 表: dlp_data_202408.usb_device_opera_log_20240827
0805 09:02:53.822 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 425ms - 逻辑表: usb_device_opera_log
0805 09:02:53.822 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 425ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:54.247 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 424ms - 表: dlp_data_202408.usb_device_opera_log_20240828
0805 09:02:54.251 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 429ms - 逻辑表: usb_device_opera_log
0805 09:02:54.252 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 429ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:54.668 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 415ms - 表: dlp_data_202408.usb_device_opera_log_20240829
0805 09:02:54.669 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 416ms - 逻辑表: usb_device_opera_log
0805 09:02:54.669 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 416ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:55.064 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 394ms - 表: dlp_data_202408.usb_device_opera_log_20240830
0805 09:02:55.065 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 395ms - 逻辑表: usb_device_opera_log
0805 09:02:55.065 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 395ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:55.442 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 376ms - 表: dlp_data_202408.usb_device_opera_log_20240831
0805 09:02:55.443 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 378ms - 逻辑表: usb_device_opera_log
0805 09:02:55.443 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 378ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:55.832 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 389ms - 表: dlp_data_202409.usb_device_opera_log_20240901
0805 09:02:55.833 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 390ms - 逻辑表: usb_device_opera_log
0805 09:02:55.833 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 390ms - 逻辑表: usb_device_opera_log，这是性能瓶颈！
0805 09:02:57.677 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 426ms - 表: dlp_data_202408.bt_file_log_20240801
0805 09:02:57.680 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 429ms - 逻辑表: bt_file_log
0805 09:02:57.681 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 429ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:02:58.061 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 380ms - 表: dlp_data_202408.bt_file_log_20240802
0805 09:02:58.063 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 382ms - 逻辑表: bt_file_log
0805 09:02:58.065 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 382ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:02:58.454 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 388ms - 表: dlp_data_202408.bt_file_log_20240803
0805 09:02:58.455 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 389ms - 逻辑表: bt_file_log
0805 09:02:58.456 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 389ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:02:58.898 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 442ms - 表: dlp_data_202408.bt_file_log_20240804
0805 09:02:58.899 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 443ms - 逻辑表: bt_file_log
0805 09:02:58.899 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 443ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:02:59.298 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 398ms - 表: dlp_data_202408.bt_file_log_20240805
0805 09:02:59.299 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 399ms - 逻辑表: bt_file_log
0805 09:02:59.299 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 399ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:02:59.697 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 398ms - 表: dlp_data_202408.bt_file_log_20240806
0805 09:02:59.698 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 399ms - 逻辑表: bt_file_log
0805 09:02:59.698 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 399ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:00.078 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 379ms - 表: dlp_data_202408.bt_file_log_20240807
0805 09:03:00.079 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 380ms - 逻辑表: bt_file_log
0805 09:03:00.079 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 380ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:00.458 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 377ms - 表: dlp_data_202408.bt_file_log_20240808
0805 09:03:00.459 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 379ms - 逻辑表: bt_file_log
0805 09:03:00.460 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 379ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:00.867 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 406ms - 表: dlp_data_202408.bt_file_log_20240809
0805 09:03:00.869 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 408ms - 逻辑表: bt_file_log
0805 09:03:00.870 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 408ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:01.261 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 390ms - 表: dlp_data_202408.bt_file_log_20240810
0805 09:03:01.263 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 393ms - 逻辑表: bt_file_log
0805 09:03:01.263 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 393ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:01.685 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 422ms - 表: dlp_data_202408.bt_file_log_20240811
0805 09:03:01.686 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 423ms - 逻辑表: bt_file_log
0805 09:03:01.686 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 423ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:02.084 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 398ms - 表: dlp_data_202408.bt_file_log_20240812
0805 09:03:02.085 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 399ms - 逻辑表: bt_file_log
0805 09:03:02.085 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 399ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:03.498 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 1413ms - 表: dlp_data_202408.bt_file_log_20240813
0805 09:03:03.499 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 1414ms - 逻辑表: bt_file_log
0805 09:03:03.499 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 1414ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:04.013 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 513ms - 表: dlp_data_202408.bt_file_log_20240814
0805 09:03:04.013 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 513ms - 逻辑表: bt_file_log
0805 09:03:04.014 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 513ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:04.393 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 378ms - 表: dlp_data_202408.bt_file_log_20240815
0805 09:03:04.394 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 378ms - 逻辑表: bt_file_log
0805 09:03:04.394 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 378ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:04.762 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 368ms - 表: dlp_data_202408.bt_file_log_20240816
0805 09:03:04.763 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 369ms - 逻辑表: bt_file_log
0805 09:03:04.763 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 369ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:05.155 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 391ms - 表: dlp_data_202408.bt_file_log_20240817
0805 09:03:05.155 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 392ms - 逻辑表: bt_file_log
0805 09:03:05.156 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 392ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:05.530 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 374ms - 表: dlp_data_202408.bt_file_log_20240818
0805 09:03:05.530 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 374ms - 逻辑表: bt_file_log
0805 09:03:05.531 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 374ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:05.903 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 372ms - 表: dlp_data_202408.bt_file_log_20240819
0805 09:03:05.904 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 373ms - 逻辑表: bt_file_log
0805 09:03:05.905 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 373ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:06.289 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 384ms - 表: dlp_data_202408.bt_file_log_20240820
0805 09:03:06.289 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 384ms - 逻辑表: bt_file_log
0805 09:03:06.290 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 384ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:06.676 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 386ms - 表: dlp_data_202408.bt_file_log_20240821
0805 09:03:06.677 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 387ms - 逻辑表: bt_file_log
0805 09:03:06.677 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 387ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:07.058 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 380ms - 表: dlp_data_202408.bt_file_log_20240822
0805 09:03:07.058 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 380ms - 逻辑表: bt_file_log
0805 09:03:07.059 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 380ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:07.444 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 385ms - 表: dlp_data_202408.bt_file_log_20240823
0805 09:03:07.445 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 386ms - 逻辑表: bt_file_log
0805 09:03:07.445 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 386ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:07.823 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 377ms - 表: dlp_data_202408.bt_file_log_20240824
0805 09:03:07.824 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 378ms - 逻辑表: bt_file_log
0805 09:03:07.824 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 378ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:08.188 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 363ms - 表: dlp_data_202408.bt_file_log_20240825
0805 09:03:08.190 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 365ms - 逻辑表: bt_file_log
0805 09:03:08.191 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 365ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:08.570 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 379ms - 表: dlp_data_202408.bt_file_log_20240826
0805 09:03:08.571 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 380ms - 逻辑表: bt_file_log
0805 09:03:08.572 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 380ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:08.954 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 382ms - 表: dlp_data_202408.bt_file_log_20240827
0805 09:03:08.955 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 383ms - 逻辑表: bt_file_log
0805 09:03:08.956 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 383ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:09.423 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 467ms - 表: dlp_data_202408.bt_file_log_20240828
0805 09:03:09.424 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 468ms - 逻辑表: bt_file_log
0805 09:03:09.424 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 468ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:09.817 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 392ms - 表: dlp_data_202408.bt_file_log_20240829
0805 09:03:09.819 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 394ms - 逻辑表: bt_file_log
0805 09:03:09.820 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 394ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:10.194 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 374ms - 表: dlp_data_202408.bt_file_log_20240830
0805 09:03:10.203 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 383ms - 逻辑表: bt_file_log
0805 09:03:10.204 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 383ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:10.665 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 460ms - 表: dlp_data_202408.bt_file_log_20240831
0805 09:03:10.667 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 462ms - 逻辑表: bt_file_log
0805 09:03:10.667 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 462ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:11.046 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 379ms - 表: dlp_data_202409.bt_file_log_20240901
0805 09:03:11.048 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 380ms - 逻辑表: bt_file_log
0805 09:03:11.048 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 380ms - 逻辑表: bt_file_log，这是性能瓶颈！
0805 09:03:20.772 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:03:23.050 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.a.ShardingAlgorithmHelper : [路由表计算] 未找到匹配的分片表，使用模板表: usb_device_opera_log
0805 09:03:23.146 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.a.ShardingAlgorithmHelper : [路由表计算] 分片表数量过多: 32，可能严重影响性能
0805 09:03:45.587 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 515ms - 表: dlp_data_202408.mtp_file_log_20240801
0805 09:03:45.588 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 516ms - 逻辑表: mtp_file_log
0805 09:03:45.589 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 516ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:45.989 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 400ms - 表: dlp_data_202408.mtp_file_log_20240802
0805 09:03:45.990 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 401ms - 逻辑表: mtp_file_log
0805 09:03:45.990 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 401ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:46.364 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 374ms - 表: dlp_data_202408.mtp_file_log_20240803
0805 09:03:46.366 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 376ms - 逻辑表: mtp_file_log
0805 09:03:46.366 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 376ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:46.823 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 456ms - 表: dlp_data_202408.mtp_file_log_20240804
0805 09:03:46.824 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 457ms - 逻辑表: mtp_file_log
0805 09:03:46.824 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 457ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:47.280 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 455ms - 表: dlp_data_202408.mtp_file_log_20240805
0805 09:03:47.281 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 456ms - 逻辑表: mtp_file_log
0805 09:03:47.281 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 456ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:47.666 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 385ms - 表: dlp_data_202408.mtp_file_log_20240806
0805 09:03:47.667 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 386ms - 逻辑表: mtp_file_log
0805 09:03:47.667 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 386ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:48.052 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 384ms - 表: dlp_data_202408.mtp_file_log_20240807
0805 09:03:48.054 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 386ms - 逻辑表: mtp_file_log
0805 09:03:48.054 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 386ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:48.438 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 383ms - 表: dlp_data_202408.mtp_file_log_20240808
0805 09:03:48.439 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 384ms - 逻辑表: mtp_file_log
0805 09:03:48.439 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 384ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:48.822 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 383ms - 表: dlp_data_202408.mtp_file_log_20240809
0805 09:03:48.823 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 384ms - 逻辑表: mtp_file_log
0805 09:03:48.823 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 384ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:49.215 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 392ms - 表: dlp_data_202408.mtp_file_log_20240810
0805 09:03:49.215 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 392ms - 逻辑表: mtp_file_log
0805 09:03:49.216 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 392ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:49.604 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 388ms - 表: dlp_data_202408.mtp_file_log_20240811
0805 09:03:49.604 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 388ms - 逻辑表: mtp_file_log
0805 09:03:49.604 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 388ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:49.970 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 365ms - 表: dlp_data_202408.mtp_file_log_20240812
0805 09:03:49.970 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 365ms - 逻辑表: mtp_file_log
0805 09:03:49.971 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 365ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:50.351 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 380ms - 表: dlp_data_202408.mtp_file_log_20240813
0805 09:03:50.352 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 381ms - 逻辑表: mtp_file_log
0805 09:03:50.352 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 381ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:50.733 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 378ms - 表: dlp_data_202408.mtp_file_log_20240814
0805 09:03:50.739 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 384ms - 逻辑表: mtp_file_log
0805 09:03:50.739 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 384ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:51.106 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 366ms - 表: dlp_data_202408.mtp_file_log_20240815
0805 09:03:51.106 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 366ms - 逻辑表: mtp_file_log
0805 09:03:51.106 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 366ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:51.475 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 368ms - 表: dlp_data_202408.mtp_file_log_20240816
0805 09:03:51.475 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 368ms - 逻辑表: mtp_file_log
0805 09:03:51.475 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 368ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:51.852 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 377ms - 表: dlp_data_202408.mtp_file_log_20240817
0805 09:03:51.852 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 377ms - 逻辑表: mtp_file_log
0805 09:03:51.852 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 377ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:52.225 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 372ms - 表: dlp_data_202408.mtp_file_log_20240818
0805 09:03:52.225 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 372ms - 逻辑表: mtp_file_log
0805 09:03:52.226 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 372ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:52.595 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 369ms - 表: dlp_data_202408.mtp_file_log_20240819
0805 09:03:52.595 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 369ms - 逻辑表: mtp_file_log
0805 09:03:52.596 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 369ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:53.021 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 425ms - 表: dlp_data_202408.mtp_file_log_20240820
0805 09:03:53.022 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 426ms - 逻辑表: mtp_file_log
0805 09:03:53.022 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 426ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:53.410 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 387ms - 表: dlp_data_202408.mtp_file_log_20240821
0805 09:03:53.411 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 388ms - 逻辑表: mtp_file_log
0805 09:03:53.411 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 388ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:53.820 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 408ms - 表: dlp_data_202408.mtp_file_log_20240822
0805 09:03:53.821 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 409ms - 逻辑表: mtp_file_log
0805 09:03:53.821 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 409ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:54.206 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 385ms - 表: dlp_data_202408.mtp_file_log_20240823
0805 09:03:54.207 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 386ms - 逻辑表: mtp_file_log
0805 09:03:54.207 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 386ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:54.656 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 449ms - 表: dlp_data_202408.mtp_file_log_20240824
0805 09:03:54.656 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 449ms - 逻辑表: mtp_file_log
0805 09:03:54.657 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 449ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:55.144 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 487ms - 表: dlp_data_202408.mtp_file_log_20240825
0805 09:03:55.146 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 489ms - 逻辑表: mtp_file_log
0805 09:03:55.147 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 489ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:55.633 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 486ms - 表: dlp_data_202408.mtp_file_log_20240826
0805 09:03:55.634 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 487ms - 逻辑表: mtp_file_log
0805 09:03:55.634 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 487ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:56.078 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 443ms - 表: dlp_data_202408.mtp_file_log_20240827
0805 09:03:56.078 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 443ms - 逻辑表: mtp_file_log
0805 09:03:56.079 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 443ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:56.517 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 438ms - 表: dlp_data_202408.mtp_file_log_20240828
0805 09:03:56.518 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 439ms - 逻辑表: mtp_file_log
0805 09:03:56.518 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 439ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:56.939 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 420ms - 表: dlp_data_202408.mtp_file_log_20240829
0805 09:03:56.940 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 421ms - 逻辑表: mtp_file_log
0805 09:03:56.940 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 421ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:57.317 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 377ms - 表: dlp_data_202408.mtp_file_log_20240830
0805 09:03:57.318 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 378ms - 逻辑表: mtp_file_log
0805 09:03:57.318 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 378ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:57.691 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 373ms - 表: dlp_data_202408.mtp_file_log_20240831
0805 09:03:57.691 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 373ms - 逻辑表: mtp_file_log
0805 09:03:57.692 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 373ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:58.071 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 379ms - 表: dlp_data_202409.mtp_file_log_20240901
0805 09:03:58.072 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 380ms - 逻辑表: mtp_file_log
0805 09:03:58.072 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 380ms - 逻辑表: mtp_file_log，这是性能瓶颈！
0805 09:03:59.810 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 463ms - 表: dlp_data_202408.browser_upload_log_20240801
0805 09:03:59.812 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 465ms - 逻辑表: browser_upload_log
0805 09:03:59.812 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 465ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:00.192 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 380ms - 表: dlp_data_202408.browser_upload_log_20240802
0805 09:04:00.192 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 380ms - 逻辑表: browser_upload_log
0805 09:04:00.193 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 380ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:00.576 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 383ms - 表: dlp_data_202408.browser_upload_log_20240803
0805 09:04:00.577 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 384ms - 逻辑表: browser_upload_log
0805 09:04:00.578 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 384ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:01.192 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 613ms - 表: dlp_data_202408.browser_upload_log_20240804
0805 09:04:01.195 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 616ms - 逻辑表: browser_upload_log
0805 09:04:01.196 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 616ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:01.596 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 399ms - 表: dlp_data_202408.browser_upload_log_20240805
0805 09:04:01.596 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 399ms - 逻辑表: browser_upload_log
0805 09:04:01.596 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 399ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:01.970 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 373ms - 表: dlp_data_202408.browser_upload_log_20240806
0805 09:04:01.971 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 374ms - 逻辑表: browser_upload_log
0805 09:04:01.971 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 374ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:02.921 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 949ms - 表: dlp_data_202408.browser_upload_log_20240807
0805 09:04:02.922 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 950ms - 逻辑表: browser_upload_log
0805 09:04:02.922 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 950ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:03.431 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 508ms - 表: dlp_data_202408.browser_upload_log_20240808
0805 09:04:03.431 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 508ms - 逻辑表: browser_upload_log
0805 09:04:03.432 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 508ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:03.848 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 416ms - 表: dlp_data_202408.browser_upload_log_20240809
0805 09:04:03.849 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 417ms - 逻辑表: browser_upload_log
0805 09:04:03.849 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 417ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:04.268 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 418ms - 表: dlp_data_202408.browser_upload_log_20240810
0805 09:04:04.269 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 419ms - 逻辑表: browser_upload_log
0805 09:04:04.270 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 419ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:04.672 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 402ms - 表: dlp_data_202408.browser_upload_log_20240811
0805 09:04:04.673 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 403ms - 逻辑表: browser_upload_log
0805 09:04:04.673 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 403ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:05.048 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 374ms - 表: dlp_data_202408.browser_upload_log_20240812
0805 09:04:05.048 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 374ms - 逻辑表: browser_upload_log
0805 09:04:05.049 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 374ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:05.465 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 416ms - 表: dlp_data_202408.browser_upload_log_20240813
0805 09:04:05.467 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 418ms - 逻辑表: browser_upload_log
0805 09:04:05.469 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 418ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:05.859 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 390ms - 表: dlp_data_202408.browser_upload_log_20240814
0805 09:04:05.861 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 391ms - 逻辑表: browser_upload_log
0805 09:04:05.861 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 391ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:06.256 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 394ms - 表: dlp_data_202408.browser_upload_log_20240815
0805 09:04:06.257 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 395ms - 逻辑表: browser_upload_log
0805 09:04:06.258 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 395ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:06.708 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 450ms - 表: dlp_data_202408.browser_upload_log_20240816
0805 09:04:06.709 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 451ms - 逻辑表: browser_upload_log
0805 09:04:06.709 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 451ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:07.100 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 390ms - 表: dlp_data_202408.browser_upload_log_20240817
0805 09:04:07.101 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 391ms - 逻辑表: browser_upload_log
0805 09:04:07.101 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 391ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:07.486 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 384ms - 表: dlp_data_202408.browser_upload_log_20240818
0805 09:04:07.486 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 384ms - 逻辑表: browser_upload_log
0805 09:04:07.487 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 384ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:07.909 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 422ms - 表: dlp_data_202408.browser_upload_log_20240819
0805 09:04:07.910 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 423ms - 逻辑表: browser_upload_log
0805 09:04:07.911 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 423ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:08.285 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 373ms - 表: dlp_data_202408.browser_upload_log_20240820
0805 09:04:08.287 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 376ms - 逻辑表: browser_upload_log
0805 09:04:08.288 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 376ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:08.672 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 384ms - 表: dlp_data_202408.browser_upload_log_20240821
0805 09:04:08.672 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 384ms - 逻辑表: browser_upload_log
0805 09:04:08.672 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 384ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:09.057 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 384ms - 表: dlp_data_202408.browser_upload_log_20240822
0805 09:04:09.058 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 385ms - 逻辑表: browser_upload_log
0805 09:04:09.058 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 385ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:09.433 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 375ms - 表: dlp_data_202408.browser_upload_log_20240823
0805 09:04:09.434 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 376ms - 逻辑表: browser_upload_log
0805 09:04:09.434 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 376ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:09.830 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 395ms - 表: dlp_data_202408.browser_upload_log_20240824
0805 09:04:09.830 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 395ms - 逻辑表: browser_upload_log
0805 09:04:09.831 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 395ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:10.208 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 377ms - 表: dlp_data_202408.browser_upload_log_20240825
0805 09:04:10.209 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 378ms - 逻辑表: browser_upload_log
0805 09:04:10.209 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 378ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:10.597 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 388ms - 表: dlp_data_202408.browser_upload_log_20240826
0805 09:04:10.598 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 389ms - 逻辑表: browser_upload_log
0805 09:04:10.599 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 389ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:11.017 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] 获取缺失列信息耗时: 417ms - 表: dlp_data_202408.browser_upload_log_20240827
0805 09:04:11.033 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时: 434ms - 逻辑表: browser_upload_log
0805 09:04:11.034 -ERROR 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.r.t.MyTableToken : [MyTableToken性能] toString方法耗时过长: 434ms - 逻辑表: browser_upload_log，这是性能瓶颈！
0805 09:04:12.694 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:04:16.085 - WARN 32328 [sync-scheduler-pool-t-1] o.m.l.Logger              : No MyBatis mapper was found in '[com.tipray.dlp.**.dao]' package. Please check your configuration.
0805 09:05:04.691 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:05:56.657 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:06:48.738 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:07:35.191 - WARN 32328 [pool-198-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 09:07:40.669 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:07:40.939 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.a.ShardingAlgorithmHelper : [路由表计算] 未找到匹配的分片表，使用模板表: enc_dec_files_log
0805 09:07:40.944 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.a.ShardingAlgorithmHelper : [路由表计算] 分片表数量过多: 32，可能严重影响性能
0805 09:07:55.509 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.a.ShardingAlgorithmHelper : [路由表计算] 未找到匹配的分片表，使用模板表: general_scan_violate_log
0805 09:07:55.513 - WARN 32328 [kJoinPool.commonPool-worker-15] c.t.d.m.s.s.a.ShardingAlgorithmHelper : [路由表计算] 分片表数量过多: 32，可能严重影响性能
0805 09:08:32.671 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:09:24.673 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:10:16.664 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:11:08.678 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:12:00.675 - WARN 32328 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:21:21.664 -ERROR 5396 [           main] c.t.d.a.u.PropertyUtil    : FileNotFound：E:\dlp_daily_work\Center\TRDLP\config.yml
0805 09:21:27.049 - WARN 5396 [           main] o.a.j.l.DirectJDKLog      : Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.28080.8022376885882552700] which is part of the web application [/dlp]
0805 09:21:29.206 -ERROR 5396 [           main] c.t.d.a.l.ContextDestroyListener : ====== contextInitialized
0805 09:21:29.639 -ERROR 5396 [           main] c.t.d.u.PropertyUtil      : FileNotFound：E:\dlp_daily_work\Center\TRDLP\config.yml
0805 09:21:30.536 - WARN 5396 [           main] c.t.d.m.u.LogUtil         : master db config: DBConfig ready, config=DBConfig(id=null, username=root, driverClassName=com.mysql.cj.jdbc.Driver, jdbcUrl=*********************************************************************************************************************************************************************, ip=null, port=null, dbType=1, database=null, schema=null, maxPoolSize=50, dbPassVer=null, baseDB=null)
0805 09:21:31.655 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect success,db=DBShardConfig(id=9001, rule=1, isMaster=false, bussId=[STRATEGY, BUSINESS])
0805 09:21:32.441 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.ext.dao.ExtDepartmentDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:32.498 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.ext.dao.ExtDepartmentDao.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateByIdMethod]
0805 09:21:32.546 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.ext.dao.ExtDeptUserDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:32.578 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.ext.dao.ExtDeptUserDao.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateByIdMethod]
0805 09:21:32.688 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : Can not find table primary key in Class: "com.tipray.dlp.ext.bean.ExtRelation".
0805 09:21:32.689 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : class com.tipray.dlp.ext.bean.ExtRelation ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
0805 09:21:32.694 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.ext.dao.ExtRelationDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:32.764 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.ext.dao.ExtUserDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:32.792 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.ext.dao.ExtUserDao.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateByIdMethod]
0805 09:21:33.301 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.OplogDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:33.343 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.OplogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:33.692 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.ServerDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:34.377 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.UserTerminalDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:34.549 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.FingerprintRelaDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:34.587 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.FingerprintRelaDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:35.293 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.ReportTemplateDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:35.473 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.SensitiveDetectScreenRcdLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:36.011 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.SysUserMultiAuthDao.insertBatch] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.InsertBatchMethod]
0805 09:21:36.900 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.ApprovalAccessDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:37.199 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.AssetPropUserDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:37.705 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.BurnLogDao.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
0805 09:21:37.719 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.BurnLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:37.964 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.ChatFileLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:38.809 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.CustomSoftAssetInfoDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:39.025 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.DecFileStatisticsDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:39.275 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.DenseTransLogDetailDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:39.979 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.DiskScanSelfCheckLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:40.036 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.DiskScanSelfCheckSensLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:40.293 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.EdmDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:40.300 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.EdmDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:40.393 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.EmailDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:40.401 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.EmailDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:40.422 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.EmailDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:40.445 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.EmailTemplateDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:40.450 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.EmailTemplateDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:40.473 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.EmailTemplateDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:40.533 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.EncOrDecLogDetailDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:40.784 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.FileFingerprintDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:40.792 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.FileFingerprintDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:40.960 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.FlowIPPortDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:40.967 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.FlowIPPortDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:40.978 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.FlowIPPortDao.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
0805 09:21:40.991 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.FlowIPPortDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:41.014 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.FlowProcessDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:41.021 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.FlowProcessDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:41.038 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.FlowProcessDao.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
0805 09:21:41.049 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.FlowProcessDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:41.292 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.GroupPolicyStrategyDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:41.300 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.GroupPolicyStrategyDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:41.382 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.IdentifierRuleDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:41.560 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.InterfaceAuthInfoDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:42.241 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.MailWhiteListReceiverDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:42.247 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.MailWhiteListReceiverDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:42.257 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.MailWhiteListReceiverDao.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
0805 09:21:42.270 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.MailWhiteListReceiverDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:42.293 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.MailWhiteListSenderDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:42.299 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.MailWhiteListSenderDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:42.311 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.MailWhiteListSenderDao.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
0805 09:21:42.322 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.MailWhiteListSenderDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:42.434 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.ModuleSubFilterDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:42.634 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.MstgDefDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:42.924 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.OutgoingMachineCodeDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:42.959 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.OutgoingProcessDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:42.972 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.OutgoingProcessDao.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
0805 09:21:42.987 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.OutgoingProcessDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:43.852 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.ReadPermissionDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:43.858 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.ReadPermissionDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:43.872 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.ReadPermissionDao.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
0805 09:21:43.883 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.ReadPermissionDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:44.012 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.RuleGroupDao.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
0805 09:21:44.024 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.RuleGroupDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:44.116 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.ScheduledTaskDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:44.123 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.ScheduledTaskDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:44.139 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.ScheduledTaskDao.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
0805 09:21:44.151 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.ScheduledTaskDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:44.181 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.ScreenMP4TaskDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:44.185 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.ScreenMP4TaskDao.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
0805 09:21:44.263 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.SecretLevelLogDetailDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:44.310 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.SecretLevelLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:44.525 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.SensitiveOpDetailLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:44.572 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.SensitiveOpLogDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:44.851 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.SoftwareAppInfoDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:44.889 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.SoftwareAssetAlarmSetupDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:44.895 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.SoftwareAssetAlarmSetupDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:44.939 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : Can not find table primary key in Class: "com.tipray.dlp.bean.SoftwareAuthorizedData".
0805 09:21:44.939 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : class com.tipray.dlp.bean.SoftwareAuthorizedData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
0805 09:21:44.942 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.SoftwareAuthorizedDataDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:44.945 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.SoftwareAuthorizedDataDao.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
0805 09:21:45.029 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.SoftwareExtInfoDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:45.036 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.SoftwareExtInfoDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:45.342 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.StgBaseConfigDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:46.298 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.TerminalGroupNickNameDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:46.542 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.UnregisteredModuleDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:47.113 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.VideoRecorderDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:47.197 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.VmlDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:47.203 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.dao.VmlDao.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.UpdateMethod]
0805 09:21:47.569 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.software.dao.ApprovalSoftwareSubmitLogDao.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
0805 09:21:47.620 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.software.dao.ApprovalSoftwareSubmitDao.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
0805 09:21:47.741 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.backup.dao.BackupDeleteRecordDao.insertBatch] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.InsertBatchMethod]
0805 09:21:47.991 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.backup.dao.BackupRestoreRecordDao.insertBatch] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.InsertBatchMethod]
0805 09:21:48.337 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.backup.dao.BackupDeleteStatusDao.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
0805 09:21:48.763 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : Can not find table primary key in Class: "com.tipray.dlp.sync.bean.FieldMapper".
0805 09:21:48.763 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : class com.tipray.dlp.sync.bean.FieldMapper ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
0805 09:21:48.809 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : Can not find table primary key in Class: "com.tipray.dlp.sync.bean.SyncGroupFilter".
0805 09:21:48.810 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : class com.tipray.dlp.sync.bean.SyncGroupFilter ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
0805 09:21:48.849 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : Can not find table primary key in Class: "com.tipray.dlp.sync.bean.SyncGroup".
0805 09:21:48.849 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : class com.tipray.dlp.sync.bean.SyncGroup ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
0805 09:21:48.872 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.sync.dao.SyncGroupDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:48.886 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : Can not find table primary key in Class: "com.tipray.dlp.sync.bean.SyncGroupTemp".
0805 09:21:48.886 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : class com.tipray.dlp.sync.bean.SyncGroupTemp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
0805 09:21:49.045 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : Can not find table primary key in Class: "com.tipray.dlp.sync.bean.SyncUser".
0805 09:21:49.045 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : class com.tipray.dlp.sync.bean.SyncUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
0805 09:21:49.077 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : [com.tipray.dlp.sync.dao.SyncUserDao.deleteByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.tipray.dlp.mybatis.injector.LogicDeleteMethod]
0805 09:21:49.102 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : Can not find table primary key in Class: "com.tipray.dlp.sync.bean.SyncUserTemp".
0805 09:21:49.102 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : class com.tipray.dlp.sync.bean.SyncUserTemp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
0805 09:21:52.807 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:21:57.787 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect success,db=DBShardConfig(id=9004, rule=1, isMaster=false, bussId=[STRATEGY, BUSINESS])
0805 09:22:15.318 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.d.MybatisShardingDataSourceBuilder : [Sharding DataSource Init] Success!
0805 09:23:07.148 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:23:59.161 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:24:01.552 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : Can not find table primary key in Class: "com.tipray.dlp.cloud.bean.CloudPreviewTemplate".
0805 09:24:01.552 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : class com.tipray.dlp.cloud.bean.CloudPreviewTemplate ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
0805 09:24:51.168 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:24:57.159 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : Can not find table primary key in Class: "com.tipray.dlp.bean.DocumentTrackLog".
0805 09:24:57.159 - WARN 5396 [           main] c.t.d.m.l.MyStdOutImpl    : class com.tipray.dlp.bean.DocumentTrackLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
0805 09:24:59.956 - WARN 5396 [           main] c.t.d.i.FileInitRunner    : copy dll(so) file from path(/dll/x64/libPath/*.dll) to path: C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\bin
0805 09:25:00.603 - WARN 5396 [           main] c.t.d.u.ServerLogUtil     : 获取当前进程信息命令：tasklist /fi "PID eq 5396"
0805 09:25:00.912 - WARN 5396 [           main] c.t.d.u.ServerLogUtil     : 当前进程名称：java.exe
0805 09:25:03.956 - WARN 5396 [           main] c.t.d.s.i.StgIssueServiceImpl : not add stg issue, because not get company name
0805 09:25:04.018 - WARN 5396 [           main] c.t.d.s.i.StgIssueServiceImpl : not add stg issue, because not get company name
0805 09:25:11.008 -ERROR 5396 [           main] c.t.d.u.CollectorUtil     : ccbgcollector.exe not exist!
0805 09:25:11.585 - WARN 5396 [           main] c.t.d.u.UsbTermUtil       : 【AutoUTermPkg】The LdUTerm directory or files not exists.
0805 09:25:11.605 -ERROR 5396 [           main] c.t.d.c.o.s.i.SyncOutFileScheduledServiceImpl : 修改同步外发文件的执行时间失败，系统不允许定时同步外发文件数据
0805 09:25:12.890 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner, EncodeFileInitRunner, GrantAuthInitRunner
0805 09:25:16.633 - WARN 5396 [socket-pool-t-3] c.t.d.u.EncodeFileUtil    : load Library Ldwebdf start
0805 09:25:16.699 - WARN 5396 [socket-pool-t-3] c.t.d.u.EncodeFileUtil    : load Library Ldwebdf success
0805 09:25:43.148 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:26:35.132 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:27:27.149 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:28:19.139 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:29:11.144 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:30:03.143 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:30:14.755 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 09:30:55.148 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:31:34.071 - WARN 5396 [sync-scheduler-pool-t-1] o.m.l.Logger              : No MyBatis mapper was found in '[com.tipray.dlp.**.dao]' package. Please check your configuration.
0805 09:31:43.946 -ERROR 5396 [http-nio-28080-exec-1] c.t.d.a.SyslogAspect      : admin invoke com.tipray.dlp.controller.StgIssueController.getNoticeSize[throw Exception]：
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
### The error may exist in com/tipray/dlp/dao/StgIssueDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT data_type, max(id) AS id, stg_type FROM stg_issue WHERE (data_type NOT IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND data_type NOT IN (?, ?)) GROUP BY data_type
### Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy149.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy369.selectList(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor256.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy371.selectList(Unknown Source)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.listToShowStgIssue(StgIssueServiceImpl.java:122)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.getNoticeSize(StgIssueServiceImpl.java:379)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at com.tipray.dlp.controller.StgIssueController.getNoticeSize(StgIssueController.java:70)
	at com.tipray.dlp.controller.StgIssueController$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.controller.StgIssueController$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.tipray.dlp.websocket.WSocketServletRequestFilter.doFilterInternal(WSocketServletRequestFilter.java:76)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.ExpiredSessionFilter.doFilterInternal(ExpiredSessionFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.JWTAuthenticationFilter.doFilterInternal(JWTAuthenticationFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:113)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:938)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:359)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor123.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy1675.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at sun.reflect.GeneratedMethodAccessor118.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:84)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1653.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.executeQuery(TRMybatisPlusInterceptor.java:181)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.interceptQuery(TRMybatisPlusInterceptor.java:141)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:72)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1652.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 166 common frames omitted
0805 09:31:43.997 -ERROR 5396 [http-nio-28080-exec-1] c.t.d.a.ControllerResponseAdvice : 
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
### The error may exist in com/tipray/dlp/dao/StgIssueDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT data_type, max(id) AS id, stg_type FROM stg_issue WHERE (data_type NOT IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND data_type NOT IN (?, ?)) GROUP BY data_type
### Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy149.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy369.selectList(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor256.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy371.selectList(Unknown Source)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.listToShowStgIssue(StgIssueServiceImpl.java:122)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.getNoticeSize(StgIssueServiceImpl.java:379)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at com.tipray.dlp.controller.StgIssueController.getNoticeSize(StgIssueController.java:70)
	at com.tipray.dlp.controller.StgIssueController$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.controller.StgIssueController$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.tipray.dlp.websocket.WSocketServletRequestFilter.doFilterInternal(WSocketServletRequestFilter.java:76)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.ExpiredSessionFilter.doFilterInternal(ExpiredSessionFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.JWTAuthenticationFilter.doFilterInternal(JWTAuthenticationFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:113)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:938)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:359)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor123.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy1675.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at sun.reflect.GeneratedMethodAccessor118.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:84)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1653.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.executeQuery(TRMybatisPlusInterceptor.java:181)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.interceptQuery(TRMybatisPlusInterceptor.java:141)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:72)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1652.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 166 common frames omitted
0805 09:31:47.152 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:32:39.147 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:33:31.167 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:34:23.148 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:35:14.824 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 09:35:15.153 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:36:07.149 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:36:59.129 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:37:43.946 - WARN 5396 [sync-scheduler-pool-t-1] o.m.l.Logger              : No MyBatis mapper was found in '[com.tipray.dlp.**.dao]' package. Please check your configuration.
0805 09:37:51.136 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:38:34.079 - WARN 5396 [sync-scheduler-pool-t-1] o.m.l.Logger              : No MyBatis mapper was found in '[com.tipray.dlp.**.dao]' package. Please check your configuration.
0805 09:38:43.160 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:39:29.270 - WARN 5396 [sync-scheduler-pool-t-1] o.m.l.Logger              : No MyBatis mapper was found in '[com.tipray.dlp.**.dao]' package. Please check your configuration.
0805 09:39:35.157 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:40:16.776 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 09:40:27.145 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:41:19.145 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:42:11.130 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:43:03.142 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:43:55.165 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:44:47.139 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:45:09.223 - WARN 5396 [sync-scheduler-pool-t-1] o.m.l.Logger              : No MyBatis mapper was found in '[com.tipray.dlp.**.dao]' package. Please check your configuration.
0805 09:45:16.779 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 09:45:39.164 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:46:31.123 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:47:23.146 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:48:15.133 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:49:07.142 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:49:20.752 - WARN 5396 [sync-scheduler-pool-t-1] o.m.l.Logger              : No MyBatis mapper was found in '[com.tipray.dlp.**.dao]' package. Please check your configuration.
0805 09:49:59.131 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:50:16.817 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 09:50:51.138 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:51:43.140 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:52:35.140 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:53:27.144 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:54:19.146 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:55:11.150 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:55:18.795 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 09:56:03.152 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:56:55.140 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:57:47.131 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:58:39.140 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 09:59:31.146 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:00:18.912 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 10:00:22.930 -ERROR 5396 [http-nio-28080-exec-4] c.t.d.a.SyslogAspect      : admin invoke com.tipray.dlp.controller.StgIssueController.getNoticeSize[throw Exception]：
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
### The error may exist in com/tipray/dlp/dao/StgIssueDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT data_type, max(id) AS id, stg_type FROM stg_issue WHERE (data_type NOT IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND data_type NOT IN (?, ?)) GROUP BY data_type
### Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy149.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy369.selectList(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor256.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy371.selectList(Unknown Source)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.listToShowStgIssue(StgIssueServiceImpl.java:122)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.getNoticeSize(StgIssueServiceImpl.java:379)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at com.tipray.dlp.controller.StgIssueController.getNoticeSize(StgIssueController.java:70)
	at com.tipray.dlp.controller.StgIssueController$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.controller.StgIssueController$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.tipray.dlp.websocket.WSocketServletRequestFilter.doFilterInternal(WSocketServletRequestFilter.java:76)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.ExpiredSessionFilter.doFilterInternal(ExpiredSessionFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.JWTAuthenticationFilter.doFilterInternal(JWTAuthenticationFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:113)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:938)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:359)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor123.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy1675.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at sun.reflect.GeneratedMethodAccessor118.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:84)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1653.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.executeQuery(TRMybatisPlusInterceptor.java:181)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.interceptQuery(TRMybatisPlusInterceptor.java:141)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:72)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1652.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 166 common frames omitted
0805 10:00:22.935 -ERROR 5396 [http-nio-28080-exec-4] c.t.d.a.ControllerResponseAdvice : 
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
### The error may exist in com/tipray/dlp/dao/StgIssueDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT data_type, max(id) AS id, stg_type FROM stg_issue WHERE (data_type NOT IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND data_type NOT IN (?, ?)) GROUP BY data_type
### Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy149.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy369.selectList(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor256.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy371.selectList(Unknown Source)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.listToShowStgIssue(StgIssueServiceImpl.java:122)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.getNoticeSize(StgIssueServiceImpl.java:379)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at com.tipray.dlp.controller.StgIssueController.getNoticeSize(StgIssueController.java:70)
	at com.tipray.dlp.controller.StgIssueController$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.controller.StgIssueController$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.tipray.dlp.websocket.WSocketServletRequestFilter.doFilterInternal(WSocketServletRequestFilter.java:76)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.ExpiredSessionFilter.doFilterInternal(ExpiredSessionFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.JWTAuthenticationFilter.doFilterInternal(JWTAuthenticationFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:113)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:938)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:359)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor123.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy1675.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at sun.reflect.GeneratedMethodAccessor118.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:84)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1653.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.executeQuery(TRMybatisPlusInterceptor.java:181)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.interceptQuery(TRMybatisPlusInterceptor.java:141)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:72)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1652.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 166 common frames omitted
0805 10:00:23.139 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:01:15.151 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:02:07.156 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:02:59.165 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:03:51.142 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:04:43.140 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:05:13.510 - WARN 5396 [sync-scheduler-pool-t-1] o.m.l.Logger              : No MyBatis mapper was found in '[com.tipray.dlp.**.dao]' package. Please check your configuration.
0805 10:05:20.780 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 10:05:23.128 -ERROR 5396 [http-nio-28080-exec-5] c.t.d.a.SyslogAspect      : admin invoke com.tipray.dlp.controller.StgIssueController.getNoticeSize[throw Exception]：
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
### The error may exist in com/tipray/dlp/dao/StgIssueDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT data_type, max(id) AS id, stg_type FROM stg_issue WHERE (data_type NOT IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND data_type NOT IN (?, ?)) GROUP BY data_type
### Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy149.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy369.selectList(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor256.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy371.selectList(Unknown Source)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.listToShowStgIssue(StgIssueServiceImpl.java:122)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.getNoticeSize(StgIssueServiceImpl.java:379)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at com.tipray.dlp.controller.StgIssueController.getNoticeSize(StgIssueController.java:70)
	at com.tipray.dlp.controller.StgIssueController$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.controller.StgIssueController$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.tipray.dlp.websocket.WSocketServletRequestFilter.doFilterInternal(WSocketServletRequestFilter.java:76)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.ExpiredSessionFilter.doFilterInternal(ExpiredSessionFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.JWTAuthenticationFilter.doFilterInternal(JWTAuthenticationFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:113)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:938)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:359)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor123.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy1675.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at sun.reflect.GeneratedMethodAccessor118.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:84)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1653.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.executeQuery(TRMybatisPlusInterceptor.java:181)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.interceptQuery(TRMybatisPlusInterceptor.java:141)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:72)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1652.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 166 common frames omitted
0805 10:05:23.130 -ERROR 5396 [http-nio-28080-exec-5] c.t.d.a.ControllerResponseAdvice : 
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
### The error may exist in com/tipray/dlp/dao/StgIssueDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT data_type, max(id) AS id, stg_type FROM stg_issue WHERE (data_type NOT IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND data_type NOT IN (?, ?)) GROUP BY data_type
### Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy149.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy369.selectList(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor256.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy371.selectList(Unknown Source)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.listToShowStgIssue(StgIssueServiceImpl.java:122)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.getNoticeSize(StgIssueServiceImpl.java:379)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at com.tipray.dlp.controller.StgIssueController.getNoticeSize(StgIssueController.java:70)
	at com.tipray.dlp.controller.StgIssueController$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.controller.StgIssueController$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.tipray.dlp.websocket.WSocketServletRequestFilter.doFilterInternal(WSocketServletRequestFilter.java:76)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.ExpiredSessionFilter.doFilterInternal(ExpiredSessionFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.JWTAuthenticationFilter.doFilterInternal(JWTAuthenticationFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:113)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:938)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:359)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor123.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy1675.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at sun.reflect.GeneratedMethodAccessor118.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:84)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1653.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.executeQuery(TRMybatisPlusInterceptor.java:181)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.interceptQuery(TRMybatisPlusInterceptor.java:141)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:72)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1652.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 166 common frames omitted
0805 10:05:35.163 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:05:49.726 -ERROR 5396 [http-nio-28080-exec-4] c.t.d.a.SyslogAspect      : admin invoke com.tipray.dlp.controller.StgIssueController.getNoticeSize[throw Exception]：
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
### The error may exist in com/tipray/dlp/dao/StgIssueDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT data_type, max(id) AS id, stg_type FROM stg_issue WHERE (data_type NOT IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND data_type NOT IN (?, ?)) GROUP BY data_type
### Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy149.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy369.selectList(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor256.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy371.selectList(Unknown Source)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.listToShowStgIssue(StgIssueServiceImpl.java:122)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.getNoticeSize(StgIssueServiceImpl.java:379)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at com.tipray.dlp.controller.StgIssueController.getNoticeSize(StgIssueController.java:70)
	at com.tipray.dlp.controller.StgIssueController$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.controller.StgIssueController$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.tipray.dlp.websocket.WSocketServletRequestFilter.doFilterInternal(WSocketServletRequestFilter.java:76)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.ExpiredSessionFilter.doFilterInternal(ExpiredSessionFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.JWTAuthenticationFilter.doFilterInternal(JWTAuthenticationFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:113)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:938)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:359)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor123.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy1675.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at sun.reflect.GeneratedMethodAccessor118.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:84)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1653.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.executeQuery(TRMybatisPlusInterceptor.java:181)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.interceptQuery(TRMybatisPlusInterceptor.java:141)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:72)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1652.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 166 common frames omitted
0805 10:05:49.728 -ERROR 5396 [http-nio-28080-exec-4] c.t.d.a.ControllerResponseAdvice : 
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
### The error may exist in com/tipray/dlp/dao/StgIssueDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT data_type, max(id) AS id, stg_type FROM stg_issue WHERE (data_type NOT IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND data_type NOT IN (?, ?)) GROUP BY data_type
### Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy149.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy369.selectList(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor256.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy371.selectList(Unknown Source)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.listToShowStgIssue(StgIssueServiceImpl.java:122)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.getNoticeSize(StgIssueServiceImpl.java:379)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at com.tipray.dlp.controller.StgIssueController.getNoticeSize(StgIssueController.java:70)
	at com.tipray.dlp.controller.StgIssueController$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.controller.StgIssueController$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.tipray.dlp.websocket.WSocketServletRequestFilter.doFilterInternal(WSocketServletRequestFilter.java:76)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.ExpiredSessionFilter.doFilterInternal(ExpiredSessionFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.JWTAuthenticationFilter.doFilterInternal(JWTAuthenticationFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:113)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:938)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:359)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor123.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy1675.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at sun.reflect.GeneratedMethodAccessor118.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:84)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1653.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.executeQuery(TRMybatisPlusInterceptor.java:181)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.interceptQuery(TRMybatisPlusInterceptor.java:141)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:72)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1652.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 166 common frames omitted
0805 10:06:27.159 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:07:19.154 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:08:11.153 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:09:03.174 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:09:55.139 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:10:22.816 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 10:10:47.158 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:11:39.140 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:12:31.139 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:13:23.148 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:14:15.160 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:15:07.153 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:15:22.859 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 10:15:59.135 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:16:51.157 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:17:43.126 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:18:35.136 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:19:27.165 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:20:19.153 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:20:23.062 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 10:21:11.126 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:22:03.170 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:22:55.154 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:23:47.156 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:24:39.141 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:25:24.832 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 10:25:31.150 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:26:23.137 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:27:15.149 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:28:07.158 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:28:59.139 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:29:51.155 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:30:24.837 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 10:30:43.142 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:31:35.176 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:32:27.156 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:33:19.141 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:34:11.154 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:35:03.134 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:35:26.826 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 10:35:55.135 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:36:47.148 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:37:39.139 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:38:31.160 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:39:23.150 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:40:15.152 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:40:28.794 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 10:41:07.122 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:41:59.156 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:42:51.152 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:43:43.171 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:44:35.146 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:45:27.137 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:45:28.827 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 10:46:19.147 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:47:11.150 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:48:03.128 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:48:55.131 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:49:47.143 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:50:30.817 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 10:50:39.152 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:51:31.147 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:52:23.137 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:53:15.145 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:54:07.139 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:54:59.151 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:55:32.829 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 10:55:51.157 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:56:43.140 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:57:35.155 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:58:27.136 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 10:59:19.140 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 11:00:10.857 - WARN 5396 [http-nio-28080-exec-4] c.t.d.c.UsbTermController : 获取U盘插件安装包版本，插件安装包不存在
0805 11:00:10.872 - WARN 5396 [http-nio-28080-exec-1] c.t.d.c.UsbTermController : 获取U盘插件安装包版本，插件安装包不存在
0805 11:00:11.135 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 11:00:32.887 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 11:01:03.154 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 11:01:55.155 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 11:02:11.821 -ERROR 5396 [http-nio-28080-exec-8] c.t.d.a.SyslogAspect      : admin invoke com.tipray.dlp.controller.StgIssueController.getNoticeSize[throw Exception]：
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
### The error may exist in com/tipray/dlp/dao/StgIssueDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT data_type, max(id) AS id, stg_type FROM stg_issue WHERE (data_type NOT IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND data_type NOT IN (?, ?)) GROUP BY data_type
### Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy149.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy369.selectList(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor256.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy371.selectList(Unknown Source)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.listToShowStgIssue(StgIssueServiceImpl.java:122)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.getNoticeSize(StgIssueServiceImpl.java:379)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at com.tipray.dlp.controller.StgIssueController.getNoticeSize(StgIssueController.java:70)
	at com.tipray.dlp.controller.StgIssueController$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.controller.StgIssueController$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.tipray.dlp.websocket.WSocketServletRequestFilter.doFilterInternal(WSocketServletRequestFilter.java:76)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.ExpiredSessionFilter.doFilterInternal(ExpiredSessionFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.JWTAuthenticationFilter.doFilterInternal(JWTAuthenticationFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:113)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:938)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:359)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor123.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy1675.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at sun.reflect.GeneratedMethodAccessor118.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:84)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1653.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.executeQuery(TRMybatisPlusInterceptor.java:181)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.interceptQuery(TRMybatisPlusInterceptor.java:141)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:72)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1652.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 166 common frames omitted
0805 11:02:11.825 -ERROR 5396 [http-nio-28080-exec-8] c.t.d.a.ControllerResponseAdvice : 
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
### The error may exist in com/tipray/dlp/dao/StgIssueDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT data_type, max(id) AS id, stg_type FROM stg_issue WHERE (data_type NOT IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND data_type NOT IN (?, ?)) GROUP BY data_type
### Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy149.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy369.selectList(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor256.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy371.selectList(Unknown Source)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.listToShowStgIssue(StgIssueServiceImpl.java:122)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.getNoticeSize(StgIssueServiceImpl.java:379)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at com.tipray.dlp.controller.StgIssueController.getNoticeSize(StgIssueController.java:70)
	at com.tipray.dlp.controller.StgIssueController$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.controller.StgIssueController$$EnhancerBySpringCGLIB$$1.getNoticeSize(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.tipray.dlp.websocket.WSocketServletRequestFilter.doFilterInternal(WSocketServletRequestFilter.java:76)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.ExpiredSessionFilter.doFilterInternal(ExpiredSessionFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.JWTAuthenticationFilter.doFilterInternal(JWTAuthenticationFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:113)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:938)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:359)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor123.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy1675.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at sun.reflect.GeneratedMethodAccessor118.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:84)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1653.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.executeQuery(TRMybatisPlusInterceptor.java:181)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.interceptQuery(TRMybatisPlusInterceptor.java:141)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:72)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1652.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 166 common frames omitted
0805 11:02:47.156 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 11:03:39.154 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 11:04:31.149 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 11:05:39.494 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 11:05:41.306 - WARN 5396 [pool-199-thread-1] c.t.d.i.InitRunnerSchedule : execute is not ready, class=CCBGLogConfigInitRunner
0805 11:06:47.662 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 11:07:39.149 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 11:08:24.163 -ERROR 5396 [http-nio-28080-exec-8] c.t.d.a.SyslogAspect      : admin invoke com.tipray.dlp.controller.StgIssueController.list({"page":1,"limit":20,"sortName":"id","sortOrder":"desc","total":0,"items":[],"nestedQuery":false,"current":1,"size":20,"offset":0,"records":[],"pages":0})[throw Exception]：
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
### The error may exist in com/tipray/dlp/dao/StgIssueDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT data_type, max(id) AS id, stg_type FROM stg_issue WHERE (data_type NOT IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND data_type NOT IN (?, ?)) GROUP BY data_type
### Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy149.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy369.selectList(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor256.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy371.selectList(Unknown Source)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.listToShowStgIssue(StgIssueServiceImpl.java:122)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.listStgIssueContent(StgIssueServiceImpl.java:113)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$EnhancerBySpringCGLIB$$1.listStgIssueContent(<generated>)
	at com.tipray.dlp.controller.StgIssueController.list(StgIssueController.java:53)
	at com.tipray.dlp.controller.StgIssueController$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.controller.StgIssueController$$EnhancerBySpringCGLIB$$1.list(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.tipray.dlp.websocket.WSocketServletRequestFilter.doFilterInternal(WSocketServletRequestFilter.java:76)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.ExpiredSessionFilter.doFilterInternal(ExpiredSessionFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.JWTAuthenticationFilter.doFilterInternal(JWTAuthenticationFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:113)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:938)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:359)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor123.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy1675.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at sun.reflect.GeneratedMethodAccessor118.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:84)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1653.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.executeQuery(TRMybatisPlusInterceptor.java:181)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.interceptQuery(TRMybatisPlusInterceptor.java:141)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:72)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1652.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 169 common frames omitted
0805 11:08:24.164 -ERROR 5396 [http-nio-28080-exec-8] c.t.d.a.ControllerResponseAdvice : 
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
### The error may exist in com/tipray/dlp/dao/StgIssueDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT data_type, max(id) AS id, stg_type FROM stg_issue WHERE (data_type NOT IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) AND data_type NOT IN (?, ?)) GROUP BY data_type
### Cause: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy149.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy369.selectList(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor256.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy371.selectList(Unknown Source)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.listToShowStgIssue(StgIssueServiceImpl.java:122)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl.listStgIssueContent(StgIssueServiceImpl.java:113)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.service.impl.StgIssueServiceImpl$$EnhancerBySpringCGLIB$$1.listStgIssueContent(<generated>)
	at com.tipray.dlp.controller.StgIssueController.list(StgIssueController.java:53)
	at com.tipray.dlp.controller.StgIssueController$$FastClassBySpringCGLIB$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.tipray.dlp.controller.StgIssueController$$EnhancerBySpringCGLIB$$1.list(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.tipray.dlp.websocket.WSocketServletRequestFilter.doFilterInternal(WSocketServletRequestFilter.java:76)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.ExpiredSessionFilter.doFilterInternal(ExpiredSessionFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.tipray.dlp.security.JWTAuthenticationFilter.doFilterInternal(JWTAuthenticationFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dlp_sysmgr.stg_issue.stg_type' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:113)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:938)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:359)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor123.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy1675.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at sun.reflect.GeneratedMethodAccessor118.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:84)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1653.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.executeQuery(TRMybatisPlusInterceptor.java:181)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.interceptQuery(TRMybatisPlusInterceptor.java:141)
	at com.tipray.dlp.mybatis.interceptor.TRMybatisPlusInterceptor.intercept(TRMybatisPlusInterceptor.java:72)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy1652.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 169 common frames omitted
0805 11:08:31.146 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 11:09:23.137 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
0805 11:10:15.161 - WARN 5396 [mybatis-pool-t-1] c.t.d.m.u.LogUtil         : DB connect error,db=DBShardConfig(id=9003, rule=1, isMaster=false, bussId=[]) 
 Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
