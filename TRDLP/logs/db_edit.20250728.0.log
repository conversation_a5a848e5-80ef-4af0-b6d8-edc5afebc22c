0728 10:28:18.550 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?,?,?))   #parameter: [0, null, 1, 2, 7, 8]
0728 10:28:19.112 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?))   #parameter: [3, null, 5, 6]
0728 10:28:19.121 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?,?))   #parameter: [7, null, 3, 4, 9]
0728 10:28:19.464 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"3b0516ee35e4c8faf4c526187fb32d20","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 10:28:19 CST 2025, 4]
0728 10:28:19.682 - WARN : #sql: INSERT INTO sync_field_mapper             SELECT * FROM sync_field_mapper_lib         WHERE source_type NOT IN (SELECT DISTINCT source_type FROM sync_field_mapper)   #parameter: []
0728 10:28:19.785 - WARN : #sql: INSERT INTO rel_user_role  (user_id, role_id, modify_ver, create_time, modify_time)         SELECT id,201,?,?,? FROM user u WHERE u.deleted = 0 and u.id NOT IN (SELECT DISTINCT r.user_id FROM rel_user_role r WHERE r.deleted = 0)   #parameter: [-1, Mon Jul 28 10:28:19 CST 2025, Mon Jul 28 10:28:19 CST 2025]
0728 10:28:19.801 - WARN : #sql: UPDATE rel_user_role SET modify_ver = (select modify_ver+1 from tb_change_info where tb_name='rel_user_role') WHERE modify_ver = -1; UPDATE tb_change_info SET last_modify_dev_id = 9003, modify_ver = modify_ver+1, modify_time = '2025-07-28 10:28:19' WHERE tb_name = 'rel_user_role';   #parameter: []
0728 11:07:58.024 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?,?,?))   #parameter: [0, null, 1, 2, 7, 8]
0728 11:07:58.212 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?))   #parameter: [3, null, 5, 6]
0728 11:07:58.223 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?,?))   #parameter: [7, null, 3, 4, 9]
0728 11:07:58.551 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"3b0516ee35e4c8faf4c526187fb32d20","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 11:07:58 CST 2025, 4]
0728 11:07:58.789 - WARN : #sql: INSERT INTO sync_field_mapper             SELECT * FROM sync_field_mapper_lib         WHERE source_type NOT IN (SELECT DISTINCT source_type FROM sync_field_mapper)   #parameter: []
0728 11:07:58.887 - WARN : #sql: INSERT INTO rel_user_role  (user_id, role_id, modify_ver, create_time, modify_time)         SELECT id,201,?,?,? FROM user u WHERE u.deleted = 0 and u.id NOT IN (SELECT DISTINCT r.user_id FROM rel_user_role r WHERE r.deleted = 0)   #parameter: [-1, Mon Jul 28 11:07:58 CST 2025, Mon Jul 28 11:07:58 CST 2025]
0728 11:07:58.897 - WARN : #sql: UPDATE rel_user_role SET modify_ver = (select modify_ver+1 from tb_change_info where tb_name='rel_user_role') WHERE modify_ver = -1; UPDATE tb_change_info SET last_modify_dev_id = 9003, modify_ver = modify_ver+1, modify_time = '2025-07-28 11:07:58' WHERE tb_name = 'rel_user_role';   #parameter: []
0728 11:36:40.179 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 11:36:40.436 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"3b0516ee35e4c8faf4c526187fb32d20","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 11:36:40 CST 2025, 4]
0728 11:40:46.930 - WARN : #sql: UPDATE sys_user  SET account=?, name=?, email=?, phone=?, password=?, md5=?, up_pwd_time=?, active=?, limit_code=?,      modify_time=?  WHERE id=?   #parameter: [test01, test01, , , b4061a2cafa15e509039eb3d8cd32d61, b4061a2cafa15e509039eb3d8cd32d61, Mon Jul 28 11:40:46 CST 2025, 1, 7, null, 9]
0728 11:40:46.957 - WARN : #sql: update sys_user         set valid_start_date = ?, valid_end_date = ?         where id in (9)   #parameter: [null, null]
0728 11:40:46.981 - WARN : #sql: DELETE FROM sys_user_role      WHERE  (user_id = ?)   #parameter: [9]
0728 11:40:47.000 - WARN : #sql: INSERT INTO sys_user_role (user_id,role_id) VALUES  (?,?) ;   #parameter: [9, 7]
0728 11:40:47.038 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 11:40:47.282 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 11:40:47 CST 2025, 4]
0728 11:41:27.408 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 11:41:27.892 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 11:41:27 CST 2025, 4]
0728 11:59:57.348 - WARN : #sql: delete from db_backup_notice_msg          WHERE  create_time < ?   #parameter: [Sat Jun 28 00:00:00 CST 2025]
0728 13:25:24.319 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 13:25:24.694 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 13:25:24 CST 2025, 4]
0728 13:27:24.065 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 13:27:24.373 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 13:27:24 CST 2025, 4]
0728 13:32:31.060 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?,?,?))   #parameter: [0, null, 1, 2, 7, 8]
0728 13:32:31.172 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?))   #parameter: [3, null, 5, 6]
0728 13:32:31.180 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?,?))   #parameter: [7, null, 3, 4, 9]
0728 13:32:31.504 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 13:32:31 CST 2025, 4]
0728 13:32:31.731 - WARN : #sql: INSERT INTO sync_field_mapper             SELECT * FROM sync_field_mapper_lib         WHERE source_type NOT IN (SELECT DISTINCT source_type FROM sync_field_mapper)   #parameter: []
0728 13:32:31.833 - WARN : #sql: INSERT INTO rel_user_role  (user_id, role_id, modify_ver, create_time, modify_time)         SELECT id,201,?,?,? FROM user u WHERE u.deleted = 0 and u.id NOT IN (SELECT DISTINCT r.user_id FROM rel_user_role r WHERE r.deleted = 0)   #parameter: [-1, Mon Jul 28 13:32:31 CST 2025, Mon Jul 28 13:32:31 CST 2025]
0728 13:32:31.847 - WARN : #sql: UPDATE rel_user_role SET modify_ver = (select modify_ver+1 from tb_change_info where tb_name='rel_user_role') WHERE modify_ver = -1; UPDATE tb_change_info SET last_modify_dev_id = 9003, modify_ver = modify_ver+1, modify_time = '2025-07-28 13:32:31' WHERE tb_name = 'rel_user_role';   #parameter: []
0728 13:34:10.484 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 13:34:10.789 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 13:34:10 CST 2025, 4]
0728 13:35:10.786 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 13:35:11.290 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 13:35:11 CST 2025, 4]
0728 13:35:54.832 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 13:35:55.127 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 13:35:55 CST 2025, 4]
0728 13:37:33.109 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 13:37:33.425 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 13:37:33 CST 2025, 4]
0728 13:37:53.108 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 13:37:53.632 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 13:37:53 CST 2025, 4]
0728 13:48:12.549 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 13:48:12.964 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 13:48:12 CST 2025, 4]
0728 13:49:09.623 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 13:49:09.983 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 13:49:09 CST 2025, 4]
0728 13:51:43.115 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 13:51:43.423 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 13:51:43 CST 2025, 4]
0728 13:52:17.780 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 13:52:18.121 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 13:52:18 CST 2025, 4]
0728 13:58:59.772 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 13:59:00.807 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 13:59:00 CST 2025, 4]
0728 14:00:23.693 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 14:00:24.213 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 14:00:24 CST 2025, 4]
0728 14:01:22.797 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 14:01:23.147 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 14:01:23 CST 2025, 4]
0728 14:07:38.967 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 14:07:39.403 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 14:07:39 CST 2025, 4]
0728 14:08:26.800 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 14:08:27.110 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 14:08:27 CST 2025, 4]
0728 14:09:50.337 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 14:09:50.658 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 14:09:50 CST 2025, 4]
0728 14:16:18.083 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?,?,?))   #parameter: [0, null, 1, 2, 7, 8]
0728 14:16:18.153 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?))   #parameter: [3, null, 5, 6]
0728 14:16:18.168 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?,?))   #parameter: [7, null, 3, 4, 9]
0728 14:16:18.515 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 14:16:18 CST 2025, 4]
0728 14:16:19.674 - WARN : #sql: INSERT INTO sync_field_mapper             SELECT * FROM sync_field_mapper_lib         WHERE source_type NOT IN (SELECT DISTINCT source_type FROM sync_field_mapper)   #parameter: []
0728 14:16:19.764 - WARN : #sql: INSERT INTO rel_user_role  (user_id, role_id, modify_ver, create_time, modify_time)         SELECT id,201,?,?,? FROM user u WHERE u.deleted = 0 and u.id NOT IN (SELECT DISTINCT r.user_id FROM rel_user_role r WHERE r.deleted = 0)   #parameter: [-1, Mon Jul 28 14:16:19 CST 2025, Mon Jul 28 14:16:19 CST 2025]
0728 14:16:19.777 - WARN : #sql: UPDATE rel_user_role SET modify_ver = (select modify_ver+1 from tb_change_info where tb_name='rel_user_role') WHERE modify_ver = -1; UPDATE tb_change_info SET last_modify_dev_id = 9003, modify_ver = modify_ver+1, modify_time = '2025-07-28 14:16:19' WHERE tb_name = 'rel_user_role';   #parameter: []
0728 14:18:58.591 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 14:18:58.910 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 14:18:58 CST 2025, 4]
0728 14:19:17.971 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 14:19:18.337 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 14:19:18 CST 2025, 4]
0728 14:21:12.592 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 14:21:13.015 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 14:21:13 CST 2025, 4]
0728 14:21:24.583 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?))   #parameter: [7, null, 9]
0728 14:21:24.999 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 14:21:24 CST 2025, 4]
0728 15:02:26.144 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?,?,?))   #parameter: [0, null, 1, 2, 7, 8]
0728 15:02:26.216 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?))   #parameter: [3, null, 5, 6]
0728 15:02:26.224 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?,?))   #parameter: [7, null, 3, 4, 9]
0728 15:02:26.837 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 15:02:26 CST 2025, 4]
0728 15:02:27.420 - WARN : #sql: INSERT INTO sync_field_mapper             SELECT * FROM sync_field_mapper_lib         WHERE source_type NOT IN (SELECT DISTINCT source_type FROM sync_field_mapper)   #parameter: []
0728 15:02:27.928 - WARN : #sql: INSERT INTO rel_user_role  (user_id, role_id, modify_ver, create_time, modify_time)         SELECT id,201,?,?,? FROM user u WHERE u.deleted = 0 and u.id NOT IN (SELECT DISTINCT r.user_id FROM rel_user_role r WHERE r.deleted = 0)   #parameter: [-1, Mon Jul 28 15:02:27 CST 2025, Mon Jul 28 15:02:27 CST 2025]
0728 15:02:28.270 - WARN : #sql: UPDATE rel_user_role SET modify_ver = (select modify_ver+1 from tb_change_info where tb_name='rel_user_role') WHERE modify_ver = -1; UPDATE tb_change_info SET last_modify_dev_id = 9003, modify_ver = modify_ver+1, modify_time = '2025-07-28 15:02:28' WHERE tb_name = 'rel_user_role';   #parameter: []
0728 15:35:56.837 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?,?,?))   #parameter: [0, null, 1, 2, 7, 8]
0728 15:35:56.946 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?))   #parameter: [3, null, 5, 6]
0728 15:35:56.962 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?,?))   #parameter: [7, null, 3, 4, 9]
0728 15:35:57.430 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"6ddde9019aa079e8b2f7f94d2b3c2e51","limitCode":3,"range":"0","account":"unlockAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":3,"range":"0","account":"xuzc"},{"password":"4ed65a6d0d2b00846cf89161092cd6af","limitCode":0,"range":"0","account":"chenrz"},{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"qrh"},{"password":"b4061a2cafa15e509039eb3d8cd32d61","limitCode":7,"range":"0","account":"test01"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 15:35:57 CST 2025, 4]
0728 15:35:57.735 - WARN : #sql: INSERT INTO sync_field_mapper             SELECT * FROM sync_field_mapper_lib         WHERE source_type NOT IN (SELECT DISTINCT source_type FROM sync_field_mapper)   #parameter: []
0728 15:35:57.822 - WARN : #sql: INSERT INTO rel_user_role  (user_id, role_id, modify_ver, create_time, modify_time)         SELECT id,201,?,?,? FROM user u WHERE u.deleted = 0 and u.id NOT IN (SELECT DISTINCT r.user_id FROM rel_user_role r WHERE r.deleted = 0)   #parameter: [-1, Mon Jul 28 15:35:57 CST 2025, Mon Jul 28 15:35:57 CST 2025]
0728 15:35:57.832 - WARN : #sql: UPDATE rel_user_role SET modify_ver = (select modify_ver+1 from tb_change_info where tb_name='rel_user_role') WHERE modify_ver = -1; UPDATE tb_change_info SET last_modify_dev_id = 9003, modify_ver = modify_ver+1, modify_time = '2025-07-28 15:35:57' WHERE tb_name = 'rel_user_role';   #parameter: []
0728 16:43:14.354 - WARN : #sql: UPDATE property  SET code=?, value=?, type=?, rule=?, remark=?, editable=?, parent_id=?, modify_time=?     WHERE (code = ?)   #parameter: [config.out.file.sync, 16:00:00, String, , 配置云平台外发文件同步时间。请输入HH:mm:ss这样的时间格式，如配置了02:00:00,则代表会在每天的凌晨2点自动同步一次云平台外发文件数据，配置了21：00：00就是在晚上9点自动同步一次, true, 0, Mon Jul 28 16:43:14 CST 2025, config.out.file.sync]
0728 16:43:14.672 - WARN : #sql: UPDATE property  SET code=?, value=?, type=?, rule=?, remark=?, editable=?, parent_id=?, modify_time=?     WHERE (code = ?)   #parameter: [enable.out.file.sync, false, boolean, , 是否启用定时同步云平台外发数据, true, 0, Mon Jul 28 16:43:14 CST 2025, enable.out.file.sync]
0728 16:43:15.676 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?))   #parameter: [0, null, 1, 2]
0728 16:43:15.690 - WARN : #sql: UPDATE sys_user  SET limit_code=?,     modify_time=?     WHERE (id IN (?,?,?,?,?,?,?))   #parameter: [7, null, 3, 4, 5, 6, 7, 8, 9]
0728 16:43:16.241 - WARN : #sql: UPDATE stg_def  SET name=?, stg_type_number=?, strategy_type=?,  strategy_info=?, strategy_def_type=?, object_type=?, object_info=?, active=?, os_type=?,  modify_time=?  WHERE id=?   #parameter: [管理员权限策略, 219, sysUserPermission, {"name":"管理员权限策略","id":4,"osType":15,"datas":[{"password":"d46d3ece94434457dcc6598e0c06086a","limitCode":0,"range":"0","account":"admin"},{"password":"9e288906c07c28d7302331f586155812","limitCode":0,"range":"0","account":"sysAdmin"},{"password":"14bb45b7dfeea3c235d6dc24302602eb","limitCode":7,"range":"0","account":"xuzc"}]}, 0, 3, {"objGroupIds":"0"}, 1, 15, Mon Jul 28 16:43:16 CST 2025, 4]
0728 16:43:16.614 - WARN : #sql: INSERT INTO sync_field_mapper             SELECT * FROM sync_field_mapper_lib         WHERE source_type NOT IN (SELECT DISTINCT source_type FROM sync_field_mapper)   #parameter: []
0728 16:43:16.764 - WARN : #sql: INSERT INTO rel_user_role  (user_id, role_id, modify_ver, create_time, modify_time)         SELECT id,201,?,?,? FROM user u WHERE u.deleted = 0 and u.id NOT IN (SELECT DISTINCT r.user_id FROM rel_user_role r WHERE r.deleted = 0)   #parameter: [-1, Mon Jul 28 16:43:16 CST 2025, Mon Jul 28 16:43:16 CST 2025]
0728 16:43:16.777 - WARN : #sql: UPDATE rel_user_role SET modify_ver = (select modify_ver+1 from tb_change_info where tb_name='rel_user_role') WHERE modify_ver = -1; UPDATE tb_change_info SET last_modify_dev_id = 9003, modify_ver = modify_ver+1, modify_time = '2025-07-28 16:43:16' WHERE tb_name = 'rel_user_role';   #parameter: []
0728 16:43:17.696 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstBluetooth}}, 蓝牙外传文件, zh_CN, 0, 0, 614]
0728 16:43:17.703 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay512}}, 网盘上传文件, zh_CN, 0, 0, 615]
0728 16:43:17.708 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.serverMemoryUsage}}, 服务器内存占用告警, zh_CN, 0, 0, 616]
0728 16:43:17.718 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.patchInstallationStrategy}}, 补丁策略-安装策略, zh_CN, 0, 0, 617]
0728 16:43:17.723 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.DeviceInfo}}, 设备信息, zh_CN, 0, 0, 618]
0728 16:43:17.731 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.iconRefreshDir}}, 加密图标辅助刷新设置, zh_CN, 0, 0, 619]
0728 16:43:17.737 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.autoLabelingStrategy}}, 标签基础库, zh_CN, 0, 0, 620]
0728 16:43:17.746 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.signName}}, 征兆名称, zh_CN, 0, 0, 621]
0728 16:43:17.756 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numFTP}}, FTP文件上传, zh_CN, 0, 0, 1914]
0728 16:43:17.766 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.magneticDiskAlarmConfig}}, 磁盘总体占用告警设置, zh_CN, 0, 0, 622]
0728 16:43:17.772 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.hardDriveTime}}, 您的硬盘{{DiskName}}已使用{{Hour}}小时，已超过设置的阈值({{Number}})小时, zh_CN, 0, 0, 623]
0728 16:43:17.780 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.systemSlot}}, 系统插槽, zh_CN, 0, 0, 624]
0728 16:43:17.786 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.specifications}}, 规格, zh_CN, 0, 0, 625]
0728 16:43:17.792 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay4}}, 解密文件, zh_CN, 0, 0, 626]
0728 16:43:17.797 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mstscControl}}, 远程桌面管控, zh_CN, 0, 0, 627]
0728 16:43:17.802 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.socketIdentifier}}, Socket标志, zh_CN, 0, 0, 628]
0728 16:43:17.807 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.specialProcess}}, 内置特殊进程, zh_CN, 0, 0, 629]
0728 16:43:17.812 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.DownFlow}}, 下载流量, zh_CN, 0, 0, 1915]
0728 16:43:17.816 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Count}}, 次数, zh_CN, 0, 0, 630]
0728 16:43:17.820 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.outgoingTemplateStrategy}}, 直接外发模板设置, zh_CN, 0, 0, 631]
0728 16:43:17.825 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.maximumClockFrequency}}, 最大时钟频率, zh_CN, 0, 0, 632]
0728 16:43:17.830 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.webpagePasteAudit}}, 网页粘贴网址过滤, zh_CN, 0, 0, 633]
0728 16:43:17.833 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstBurnDownload}}, 光盘下载文件涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 634]
0728 16:43:17.838 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.specialSuffixStrategy}}, 特殊文件后缀, zh_CN, 0, 0, 635]
0728 16:43:17.843 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.imFileStrategy}}, 通讯工具管控设置, zh_CN, 0, 0, 636]
0728 16:43:17.847 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.level.level3}}, 重要, zh_CN, 0, 0, 637]
0728 16:43:17.851 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.emailAttachFileStrategy}}, 邮件附件限制, zh_CN, 0, 0, 638]
0728 16:43:17.856 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{label.grade.3}}, 核心数据, zh_CN, 0, 0, 639]
0728 16:43:17.862 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processCollectRuleStrategy}}, 程序收集, zh_CN, 0, 0, 640]
0728 16:43:17.867 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.OffLineNum}}, 离线数, zh_CN, 0, 0, 641]
0728 16:43:17.873 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.horizontalResolution}}, 水平分辨率, zh_CN, 0, 0, 642]
0728 16:43:17.879 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Tip}}, 提示, zh_CN, 0, 0, 643]
0728 16:43:17.886 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.scanTask}}, 敏感内容扫描任务, zh_CN, 0, 0, 644]
0728 16:43:17.893 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.terminalMenuStrategy}}, 终端菜单管理, zh_CN, 0, 0, 645]
0728 16:43:17.900 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mailSenderStrategy}}, 邮件发件人白名单, zh_CN, 0, 0, 646]
0728 16:43:17.905 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.LabelName}}, 标签内容, zh_CN, 0, 0, 647]
0728 16:43:17.911 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstNetDiskDownload}}, 网盘下载文件, zh_CN, 0, 0, 648]
0728 16:43:17.915 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.videoStrategy}}, 屏幕录像设置, zh_CN, 0, 0, 649]
0728 16:43:17.919 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstImFile}}, 即时通讯发送文件, zh_CN, 0, 0, 650]
0728 16:43:17.922 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.applicationSoftware}}, 应用软件, zh_CN, 0, 0, 651]
0728 16:43:17.926 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.hotkey}}, 热键设置, zh_CN, 0, 0, 652]
0728 16:43:17.932 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.httpWhiteListStrategy}}, 服务器白名单设置, zh_CN, 0, 0, 653]
0728 16:43:17.937 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.networkAdapter}}, 网卡, zh_CN, 0, 0, 654]
0728 16:43:17.943 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numNetDiskUpload}}, 网盘上传文件, zh_CN, 0, 0, 1916]
0728 16:43:17.947 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.timeDimension}}, 时间维度, zh_CN, 0, 0, 655]
0728 16:43:17.950 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.blueToothConfig}}, 蓝牙文件管控, zh_CN, 0, 0, 656]
0728 16:43:17.956 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstSaveAs}}, USB另存为文件, zh_CN, 0, 0, 657]
0728 16:43:17.960 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.offline}}, 您的终端与服务器断开{{OffLineNum}}分钟，处于离线状态，{{Interval}}分钟后将会锁屏，请尽快与服务器连接，或与管理员联系, zh_CN, 0, 0, 658]
0728 16:43:17.964 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.emailAttachFile}}, 您正在违规通过邮件外传附件 \r\n 外传附件名：{{alarmData}} 收件人：{{mailReceiver}} 发件人：{{mailSender}}, zh_CN, 0, 0, 659]
0728 16:43:17.970 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstWebDownloadFile}}, 从网页下载到本地的文件涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 660]
0728 16:43:17.974 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.serverLibrary}}, 服务器信息库, zh_CN, 0, 0, 661]
0728 16:43:17.978 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.CMDData}}, 命令行数据, zh_CN, 0, 0, 662]
0728 16:43:17.983 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.dataWidth}}, 数据宽度, zh_CN, 0, 0, 663]
0728 16:43:17.987 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Address}}, 地址, zh_CN, 0, 0, 664]
0728 16:43:17.991 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.normalCount}}, 一般次数, zh_CN, 0, 0, 665]
0728 16:43:17.995 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstLocShare}}, 本地共享文件, zh_CN, 0, 0, 666]
0728 16:43:18.000 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.deviceName}}, 设备名称, zh_CN, 0, 0, 667]
0728 16:43:18.003 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.level.level4}}, 紧急, zh_CN, 0, 0, 668]
0728 16:43:18.007 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.print}}, 您触发违规打印事件，打印文件路径：{{FilePath}},打印进程名:{{ProcessName}}, zh_CN, 0, 0, 669]
0728 16:43:18.011 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstIm}}, 即时通信消息涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；, zh_CN, 0, 0, 670]
0728 16:43:18.015 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstAISendMsg}}, AI传输文本内容, zh_CN, 0, 0, 1953]
0728 16:43:18.019 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.hardDriveTimes}}, 硬盘使用次数, zh_CN, 0, 0, 671]
0728 16:43:18.024 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{label.grade.2}}, 重要数据, zh_CN, 0, 0, 672]
0728 16:43:18.028 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numWebFile}}, 发送到网页的文件涉及违规，违规详情如下：文件大小或外传文件累计数量违规；文件路径：{{FilePath}}, zh_CN, 0, 0, 1917]
0728 16:43:18.033 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.serverBackupConfigStg}}, 服务器存储配置, zh_CN, 0, 0, 673]
0728 16:43:18.037 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.wifiBlock}}, 您违规连接WiFi：{{wifiSsid}}，MAC地址：{{wifiBssid}}, zh_CN, 0, 0, 674]
0728 16:43:18.041 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.terminalConfig}}, 终端设置, zh_CN, 0, 0, 675]
0728 16:43:18.046 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.companyName}}, 公司名称, zh_CN, 0, 0, 676]
0728 16:43:18.050 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.enterpriseMailConfig}}, 邮件白名单设置——SSL企业邮箱配置, zh_CN, 0, 0, 677]
0728 16:43:18.054 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.bytesPerSector}}, 每扇区字节数, zh_CN, 0, 0, 678]
0728 16:43:18.059 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numNetDiskUpload}}, 您的网盘上传文件内容涉及违规，违规详情如下：文件大小或外传文件累计数量违规；文件路径：{{FilePath}}, zh_CN, 0, 0, 1918]
0728 16:43:18.064 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.diskUsage}}, 磁盘余量, zh_CN, 0, 0, 679]
0728 16:43:18.068 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.USBSendOutFile}}, USB文件外传告警, zh_CN, 0, 0, 680]
0728 16:43:18.072 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.deviceID}}, 设备ID, zh_CN, 0, 0, 681]
0728 16:43:18.078 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.adbFileSuffixLibrary}}, ADB文件后缀库, zh_CN, 0, 0, 682]
0728 16:43:18.083 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.lastStartupTime}}, 最后启动时间, zh_CN, 0, 0, 683]
0728 16:43:18.088 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.newName}}, 新名称, zh_CN, 0, 0, 684]
0728 16:43:18.094 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstMail}}, 发送的邮件内容涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}, zh_CN, 0, 0, 685]
0728 16:43:18.102 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.softAsset}}, 软件资产变更报警, zh_CN, 0, 0, 686]
0728 16:43:18.109 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstNetDiskUpload}}, 您的网盘上传文件内容涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 687]
0728 16:43:18.115 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.cpuAlarmConfig}}, CPU占用告警设置, zh_CN, 0, 0, 688]
0728 16:43:18.120 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.ipAndMacBind}}, IP/MAC绑定, zh_CN, 0, 0, 689]
0728 16:43:18.128 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.diskScan}}, 全盘扫描, zh_CN, 0, 0, 690]
0728 16:43:18.133 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.descript}}, 描述, zh_CN, 0, 0, 691]
0728 16:43:18.138 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numWebFile}}, 网页上传文件, zh_CN, 0, 0, 1919]
0728 16:43:18.142 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelMailFile}}, 邮件附件内容, zh_CN, 0, 0, 692]
0728 16:43:18.146 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.webFlowStrategy}}, 流量限制, zh_CN, 0, 0, 693]
0728 16:43:18.149 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.browserFileDownloadStrategy}}, 网页下载文件管控, zh_CN, 0, 0, 694]
0728 16:43:18.152 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelBurn}}, 光盘刻录的文件涉及违规，违规详情如下：标签内容{{LabelName}}违规 或 “文档等级”高于指定级别{{LabelLevel}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 695]
0728 16:43:18.155 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstMailFile}}, 邮件附件内容, zh_CN, 0, 0, 696]
0728 16:43:18.160 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.otherDevice}}, 其它设备, zh_CN, 0, 0, 697]
0728 16:43:18.164 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processFileMd5}}, 受控程序策略库——进程防伪冒策略, zh_CN, 0, 0, 698]
0728 16:43:18.167 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.slot}}, 插槽, zh_CN, 0, 0, 699]
0728 16:43:18.169 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.workMode}}, 工作模式切换中，禁止运行进程：{{ProcessName}}, zh_CN, 0, 0, 700]
0728 16:43:18.173 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.decToolLimitStg}}, 高危软件限制策略, zh_CN, 0, 0, 701]
0728 16:43:18.176 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.totalCylinders}}, 总柱面数, zh_CN, 0, 0, 702]
0728 16:43:18.179 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mailWhiteListStgConfig}}, 邮件白名单设置——系统设置, zh_CN, 0, 0, 703]
0728 16:43:18.181 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.sensitiveFileOutSendConfig}}, 敏感文件外传参数配置, zh_CN, 0, 0, 704]
0728 16:43:18.184 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processStgChild}}, 受控程序策略库——子进程策略, zh_CN, 0, 0, 705]
0728 16:43:18.187 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.AISendFile}}, AI上传文件管控, zh_CN, 0, 0, 1954]
0728 16:43:18.191 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numRemoteDesktop}}, 远程桌面发送的文件涉及违规，违规详情如下：文件大小或外传文件累计数量违规；文件路径：{{FilePath}}, zh_CN, 0, 0, 1920]
0728 16:43:18.196 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstAISendFile}}, AI上传文件, zh_CN, 0, 0, 1955]
0728 16:43:18.202 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.netInterface}}, 您的网络接口{{InterfaceKey}}违规接入了{{InterfaceDeviceType}}设备{{InterfaceDeviceKey}}, zh_CN, 0, 0, 706]
0728 16:43:18.208 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.defaultMsgParamSetup}}, 终端默认弹窗参数, zh_CN, 0, 0, 707]
0728 16:43:18.214 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processLibrary}}, 进程信息库, zh_CN, 0, 0, 708]
0728 16:43:18.219 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.label}}, 卷标, zh_CN, 0, 0, 709]
0728 16:43:18.223 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.install}}, 您触发{{Type}}事件，{{Type}}路径：{{FilePath}}, zh_CN, 0, 0, 710]
0728 16:43:18.231 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.netInterfaceLimitStrategy}}, 网络接口管控, zh_CN, 0, 0, 711]
0728 16:43:18.238 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.bluetooth}}, 蓝牙文件管控, zh_CN, 0, 0, 712]
0728 16:43:18.244 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.seriesNumber}}, 系列号, zh_CN, 0, 0, 713]
0728 16:43:18.249 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.patchAutoInstallStrategy}}, 补丁策略-自动安装策略, zh_CN, 0, 0, 714]
0728 16:43:18.254 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.hardware}}, 硬件, zh_CN, 0, 0, 715]
0728 16:43:18.259 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.type}}, 类型, zh_CN, 0, 0, 716]
0728 16:43:18.263 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.period}}, 周期, zh_CN, 0, 0, 717]
0728 16:43:18.268 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.assetAlarmSetup}}, 资产变更报警设置, zh_CN, 0, 0, 718]
0728 16:43:18.275 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.RemotePort}}, 远程端口, zh_CN, 0, 0, 719]
0728 16:43:18.280 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstBluetooth}}, 蓝牙外传文件, zh_CN, 0, 0, 720]
0728 16:43:18.286 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstIm}}, 即时通讯文本内容, zh_CN, 0, 0, 721]
0728 16:43:18.291 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.serverProgramDiskUsage}}, {{devType}}【{{devId}}】盘符（{{disk}}）剩余可用空间为{{value}},低于设置的阈值{{threshold}}GB, zh_CN, 0, 0, 722]
0728 16:43:18.296 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.operatorConfig}}, 加密参数设置, zh_CN, 0, 0, 723]
0728 16:43:18.300 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.tracksPerColumn}}, 每柱磁道数, zh_CN, 0, 0, 724]
0728 16:43:18.304 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.diskType}}, 磁盘类型, zh_CN, 0, 0, 725]
0728 16:43:18.307 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.print}}, 打印权限控制, zh_CN, 0, 0, 726]
0728 16:43:18.311 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.AppVersion}}, APP版本, zh_CN, 0, 0, 727]
0728 16:43:18.313 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numMailFile}}, 发送的邮件附件涉及违规，违规详情如下：文件大小或外传文件累计数量违规；文件路径：{{FilePath}}, zh_CN, 0, 0, 1921]
0728 16:43:18.317 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.usbDevice}}, USB设备库, zh_CN, 0, 0, 728]
0728 16:43:18.319 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.appVerLimit}}, 程序版本限制, zh_CN, 0, 0, 729]
0728 16:43:18.321 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstPrint}}, 文件打印内容, zh_CN, 0, 0, 730]
0728 16:43:18.325 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.servicePatch}}, 服务补丁, zh_CN, 0, 0, 731]
0728 16:43:18.327 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.timeInfo}}, 时间段基础数据, zh_CN, 0, 0, 732]
0728 16:43:18.329 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.httpWhiteList}}, 服务器白名单, zh_CN, 0, 0, 733]
0728 16:43:18.332 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.signatureData}}, 特征码库, zh_CN, 0, 0, 734]
0728 16:43:18.335 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.assetDB}}, 软硬件资产采集项信息, zh_CN, 0, 0, 735]
0728 16:43:18.338 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.translucentEncStrategy}}, 半透明加密, zh_CN, 0, 0, 736]
0728 16:43:18.342 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.currentClockFrequency}}, 当前时钟频率, zh_CN, 0, 0, 737]
0728 16:43:18.346 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.forumURLFilter}}, 论坛发帖网址过滤, zh_CN, 0, 0, 738]
0728 16:43:18.349 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelRemoteDesktop}}, 远程桌面发送的文件涉及违规，违规详情如下：标签内容{{LabelName}}违规 或 “文档等级”高于指定级别{{LabelLevel}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 739]
0728 16:43:18.352 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.dimension}}, 尺寸, zh_CN, 0, 0, 740]
0728 16:43:18.355 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.keyboardName}}, 键盘名称, zh_CN, 0, 0, 741]
0728 16:43:18.359 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.keyboard}}, 键盘, zh_CN, 0, 0, 742]
0728 16:43:18.362 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.appReportDefault}}, 应用程序运行时间内置表, zh_CN, 0, 0, 743]
0728 16:43:18.365 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.mstscAddress}}, 远程连接地址, zh_CN, 0, 0, 744]
0728 16:43:18.367 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.date}}, 日期, zh_CN, 0, 0, 745]
0728 16:43:18.370 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstCloud}}, 云传输, zh_CN, 0, 0, 746]
0728 16:43:18.373 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processInjectDefault}}, 受控注入默认设置, zh_CN, 0, 0, 747]
0728 16:43:18.377 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.label}}, 当前外传文件：{{FilePath}}涉及违规, zh_CN, 0, 0, 748]
0728 16:43:18.380 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.softLimit}}, 您打开一个违规软件{{FilePath}}{{descript}}, zh_CN, 0, 0, 749]
0728 16:43:18.382 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.frequency}}, 频率, zh_CN, 0, 0, 750]
0728 16:43:18.384 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.LogType}}, 日志类型, zh_CN, 0, 0, 751]
0728 16:43:18.386 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.emailAddr}}, 邮箱地址, zh_CN, 0, 0, 752]
0728 16:43:18.388 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.labelPermissionControlStrategy}}, 带标签文档外传管控, zh_CN, 0, 0, 753]
0728 16:43:18.391 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.groupInfo}}, 部门信息, zh_CN, 0, 0, 754]
0728 16:43:18.395 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.IDeviceKey}}, 设备, zh_CN, 0, 0, 755]
0728 16:43:18.399 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.computer}}, 计算机, zh_CN, 0, 0, 756]
0728 16:43:18.403 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.computerEnergySaving}}, 计算机节能设置, zh_CN, 0, 0, 757]
0728 16:43:18.405 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.driveLetter}}, 盘符, zh_CN, 0, 0, 758]
0728 16:43:18.409 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.violationRespRuleExtConfig}}, 违规响应规则配置扩展表, zh_CN, 0, 0, 759]
0728 16:43:18.412 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstFtp}}, FTP上传文件, zh_CN, 0, 0, 760]
0728 16:43:18.415 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.shareConfig}}, 共享文件管控, zh_CN, 0, 0, 761]
0728 16:43:18.417 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Hour}}, 小时, zh_CN, 0, 0, 762]
0728 16:43:18.419 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.mailReceiverWhiteList}}, 邮件白名单违规告警, zh_CN, 0, 0, 763]
0728 16:43:18.422 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstPrint}}, 打印文件的内容涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 764]
0728 16:43:18.425 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstBurnDownload}}, 光盘下载文件, zh_CN, 0, 0, 765]
0728 16:43:18.428 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.diskScanSelfCheck}}, 敏感文件自检策略, zh_CN, 0, 0, 766]
0728 16:43:18.431 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.summary}}, 摘要, zh_CN, 0, 0, 767]
0728 16:43:18.434 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.serverMemoryUsage}}, {{devType}}【{{devId}}】内存使用率为{{value}}%,已超过设置的阈值{{threshold}}%, zh_CN, 0, 0, 768]
0728 16:43:18.436 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.appInfoMd5}}, 受控程序策略库——应用程序限制防伪冒指纹表, zh_CN, 0, 0, 769]
0728 16:43:18.439 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.effectiveFuncConfig}}, 敏感检测高级配置, zh_CN, 0, 0, 770]
0728 16:43:18.442 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{enc.denseInfo.secret}}, 秘密文件, zh_CN, 0, 0, 771]
0728 16:43:18.445 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.defaultGatewayIPv6Address}}, 默认网关IPv6地址, zh_CN, 0, 0, 772]
0728 16:43:18.448 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.softRunning}}, 必须运行软件{{FilePath}}, zh_CN, 0, 0, 773]
0728 16:43:18.450 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.dataDisclosureStg}}, 内容检测策略, zh_CN, 0, 0, 774]
0728 16:43:18.455 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.sysUserPermission}}, 管理员权限, zh_CN, 0, 0, 775]
0728 16:43:18.459 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstCopy}}, USB复制文件, zh_CN, 0, 0, 776]
0728 16:43:18.462 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstPrint}}, 文件打印内容, zh_CN, 0, 0, 777]
0728 16:43:18.465 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.custom}}, 自定义, zh_CN, 0, 0, 778]
0728 16:43:18.467 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processStgLibrary}}, 受控程序策略库, zh_CN, 0, 0, 779]
0728 16:43:18.470 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{enc.denseInfo.public}}, 公开文件, zh_CN, 0, 0, 780]
0728 16:43:18.474 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.software}}, 软件, zh_CN, 0, 0, 781]
0728 16:43:18.477 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processConfigStg}}, 透明加密高级配置, zh_CN, 0, 0, 782]
0728 16:43:18.479 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.netIsolation}}, 网络隔离设置, zh_CN, 0, 0, 783]
0728 16:43:18.482 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numBurn}}, 光盘刻录的文件涉及违规，违规详情如下：文件大小或外传文件累计数量违规；文件路径：{{FilePath}}, zh_CN, 0, 0, 1922]
0728 16:43:18.484 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.title}}, 标题, zh_CN, 0, 0, 784]
0728 16:43:18.486 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.UPSPort}}, UPS端口, zh_CN, 0, 0, 785]
0728 16:43:18.489 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstABDUpload}}, ADB外传文件, zh_CN, 0, 0, 786]
0728 16:43:18.491 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.hardDriveTime}}, 硬盘使用时间, zh_CN, 0, 0, 787]
0728 16:43:18.493 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.mode}}, 模式, zh_CN, 0, 0, 788]
0728 16:43:18.496 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.emailRecordLimit}}, 邮件记录限制, zh_CN, 0, 0, 789]
0728 16:43:18.499 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Optype}}, 操作类型, zh_CN, 0, 0, 790]
0728 16:43:18.503 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelBluetooth}}, 蓝牙传输的文件涉及违规，违规详情如下：标签内容{{LabelName}}违规 或 “文档等级”高于指定级别{{LabelLevel}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 791]
0728 16:43:18.506 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.appBlockStrategy}}, 程序访问限制, zh_CN, 0, 0, 792]
0728 16:43:18.508 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{enc.denseInfo.topSecret}}, 绝密文件, zh_CN, 0, 0, 793]
0728 16:43:18.510 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.importantCount}}, 重要次数, zh_CN, 0, 0, 794]
0728 16:43:18.512 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.denseInfo}}, 密级基础设置, zh_CN, 0, 0, 795]
0728 16:43:18.514 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.screenWaterMarkStrategy}}, 屏幕水印设置, zh_CN, 0, 0, 796]
0728 16:43:18.517 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.applySecurityAccess}}, 应用安全接入服务器, zh_CN, 0, 0, 797]
0728 16:43:18.519 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstWebDownloadFile}}, 网页下载文件, zh_CN, 0, 0, 798]
0728 16:43:18.522 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay8}}, 手动添加水印, zh_CN, 0, 0, 799]
0728 16:43:18.524 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.softwareDownloadStrategy}}, 软件上架, zh_CN, 0, 0, 800]
0728 16:43:18.526 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstNetDiskUpload}}, 网盘上传文件, zh_CN, 0, 0, 801]
0728 16:43:18.528 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstCopy}}, USB复制文件, zh_CN, 0, 0, 802]
0728 16:43:18.531 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.store}}, 违规使用了：{{DeviceName}}，设备编码：{{usbcode}}，品牌：{{vendor}}，容量：{{capacity}}, zh_CN, 0, 0, 803]
0728 16:43:18.534 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.asset}}, {{AssetType}}资产信息{{LogType}}，详情如下:{{DetailInfo}}, zh_CN, 0, 0, 804]
0728 16:43:18.537 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstMailFile}}, 发送的邮件附件涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 805]
0728 16:43:18.540 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstMTP}}, MTP外传文件, zh_CN, 0, 0, 806]
0728 16:43:18.542 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.fileSystem}}, 文件系统, zh_CN, 0, 0, 807]
0728 16:43:18.545 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.softRequiredInstallStrategy}}, 必须安装软件, zh_CN, 0, 0, 808]
0728 16:43:18.548 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay128}}, 邮件外传文件, zh_CN, 0, 0, 809]
0728 16:43:18.552 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstBurnDownload}}, 光盘下载文件, zh_CN, 0, 0, 810]
0728 16:43:18.554 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mailTable}}, 内置邮箱关键字, zh_CN, 0, 0, 811]
0728 16:43:18.557 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.SoftwareName}}, 软件名称, zh_CN, 0, 0, 1972]
0728 16:43:18.559 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelBluetooth}}, 蓝牙外传文件, zh_CN, 0, 0, 812]
0728 16:43:18.561 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.patchCheckStrategy}}, 补丁策略-参数配置, zh_CN, 0, 0, 813]
0728 16:43:18.565 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.deviceInfo}}, 其他设备限制基础数据, zh_CN, 0, 0, 814]
0728 16:43:18.568 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.softwareAssetAlarmSetup}}, 软件资产变更报警设置, zh_CN, 0, 0, 815]
0728 16:43:18.571 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstCut}}, 剪切到移动存储设备的内容涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 816]
0728 16:43:18.573 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.totalSectors}}, 总扇区数, zh_CN, 0, 0, 817]
0728 16:43:18.576 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sysDiskAlarmConfig}}, 系统盘占用告警设置, zh_CN, 0, 0, 818]
0728 16:43:18.578 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.personalizePolicy}}, 计算机个性化, zh_CN, 0, 0, 819]
0728 16:43:18.582 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.exceptStrategy}}, 敏感内容检测配置——例外设置, zh_CN, 0, 0, 820]
0728 16:43:18.585 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processStgConfig}}, 受控程序策略库——高级配置, zh_CN, 0, 0, 821]
0728 16:43:18.587 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.globalConfig}}, 全局配置下发表, zh_CN, 0, 0, 822]
0728 16:43:18.589 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstLocShare}}, 操作本地共享文件涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 823]
0728 16:43:18.591 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.browserFileStrategy}}, 网页上传文件管控, zh_CN, 0, 0, 824]
0728 16:43:18.594 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.name}}, 名称, zh_CN, 0, 0, 825]
0728 16:43:18.598 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.diskBackupSpace}}, 备份磁盘空间告警, zh_CN, 0, 0, 826]
0728 16:43:18.601 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstRemoteUploadTool}}, 远程工具上传的文件涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 1923]
0728 16:43:18.603 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstPost}}, 论坛发帖文本内容, zh_CN, 0, 0, 827]
0728 16:43:18.605 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.softRequiredRunStrategy}}, 必须运行软件, zh_CN, 0, 0, 828]
0728 16:43:18.607 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstImDownload}}, 即时通讯下载文件, zh_CN, 0, 0, 829]
0728 16:43:18.609 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.enDeFileDetail}}, 内置加密文件监视过滤, zh_CN, 0, 0, 830]
0728 16:43:18.612 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstAISendMsg}}, AI传输文本内容, zh_CN, 0, 0, 1956]
0728 16:43:18.615 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.AISendFile}}, 您触发违规AI文件上传事件，进程名:{{ProcessName}}，文件路径：{{FilePath}}, zh_CN, 0, 0, 1957]
0728 16:43:18.618 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstRemoteUploadTool}}, 远程工具上传文件, zh_CN, 0, 0, 1958]
0728 16:43:18.620 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mailCarbonCopyStrategy}}, 邮件抄送, zh_CN, 0, 0, 831]
0728 16:43:18.623 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.AIName}}, AI名称, zh_CN, 0, 0, 2062]
0728 16:43:18.625 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.otherDeviceLimit}}, 设备使用控制, zh_CN, 0, 0, 832]
0728 16:43:18.629 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstWebFile}}, 发送到网页的文件涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 833]
0728 16:43:18.632 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstMemoryUsed}}, 您的内存使用率为{{Function}}%已超过设置的阈值{{number}}%, zh_CN, 0, 0, 834]
0728 16:43:18.634 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.memory}}, 内存, zh_CN, 0, 0, 835]
0728 16:43:18.637 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.label}}, 标签外传权限控制, zh_CN, 0, 0, 836]
0728 16:43:18.640 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numABDUpload}}, ADB上传文件, zh_CN, 0, 0, 1924]
0728 16:43:18.644 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstSmartEnc}}, 智能加密, zh_CN, 0, 0, 837]
0728 16:43:18.649 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay64}}, 通讯工具外传文件, zh_CN, 0, 0, 838]
0728 16:43:18.652 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.threshold}}, 阈值, zh_CN, 0, 0, 839]
0728 16:43:18.654 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.diskPartition}}, 磁盘分区, zh_CN, 0, 0, 840]
0728 16:43:18.656 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.termScan}}, 终端安全检测, zh_CN, 0, 0, 841]
0728 16:43:18.659 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numUsbCopy}}, 复制到移动存储设备的内容涉及违规，违规详情如下：文件大小或外传文件累计数量违规；文件路径：{{FilePath}}, zh_CN, 0, 0, 1925]
0728 16:43:18.662 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mailLibrary}}, 邮箱信息库, zh_CN, 0, 0, 842]
0728 16:43:18.665 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.ADBController}}, 您违规使用ADB调试工具传输文件：{{FileName}}, zh_CN, 0, 0, 843]
0728 16:43:18.668 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.operatingSystem}}, 操作系统, zh_CN, 0, 0, 844]
0728 16:43:18.669 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.mailReceiver}}, 邮箱接收人, zh_CN, 0, 0, 845]
0728 16:43:18.672 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.softInstalled}}, 必须安装软件{{FilePath}}, zh_CN, 0, 0, 846]
0728 16:43:18.674 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.appVer}}, {{FilePath}}，{{Type}}, zh_CN, 0, 0, 847]
0728 16:43:18.677 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.usbInterfaceLimitStrategy}}, USB接口管控, zh_CN, 0, 0, 848]
0728 16:43:18.680 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.EnDeFileScan}}, 加密文件监视过滤, zh_CN, 0, 0, 849]
0728 16:43:18.682 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.usbInterface}}, USB接口管控, zh_CN, 0, 0, 850]
0728 16:43:18.684 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.softWareCharge}}, 软件版权管控, zh_CN, 0, 0, 1973]
0728 16:43:18.686 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.level.level0}}, 通知, zh_CN, 0, 0, 851]
0728 16:43:18.688 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.netIsolation}}, 违规访问网络，网络地址:{{RemoteIP}}:{{RemotePort}}, zh_CN, 0, 0, 852]
0728 16:43:18.691 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.macAlarmConfig}}, MAC地址变更告警设置, zh_CN, 0, 0, 853]
0728 16:43:18.693 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.appReportSet}}, 程序运行时长统计设置, zh_CN, 0, 0, 854]
0728 16:43:18.696 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.lastUsedTime}}, 最后使用时间, zh_CN, 0, 0, 855]
0728 16:43:18.698 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.pKeyInfo}}, 企业密钥, zh_CN, 0, 0, 856]
0728 16:43:18.701 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.competitorInfo}}, 竞品信息, zh_CN, 0, 0, 857]
0728 16:43:18.703 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.numberOfLogicalProcesses}}, 逻辑处理个数, zh_CN, 0, 0, 858]
0728 16:43:18.705 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.softwareBlacklist}}, 软件黑名单, zh_CN, 0, 0, 859]
0728 16:43:18.709 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.otherDeviceLimit}}, 设备使用控制, zh_CN, 0, 0, 860]
0728 16:43:18.711 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.AssetType}}, 资产类型, zh_CN, 0, 0, 861]
0728 16:43:18.713 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.level}}, 等级, zh_CN, 0, 0, 862]
0728 16:43:18.716 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstScan}}, 全盘扫描, zh_CN, 0, 0, 863]
0728 16:43:18.719 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.USBSendOutFile}}, 违规使用USB外传文件，文件路径：{{alarmData}}, zh_CN, 0, 0, 864]
0728 16:43:18.721 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstNetDiskUpload}}, 网盘上传文件, zh_CN, 0, 0, 865]
0728 16:43:18.724 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.serverProgramDiskUsage}}, 服务器程序所在磁盘空间不足告警, zh_CN, 0, 0, 866]
0728 16:43:18.726 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processMonitor}}, 程序动作监控, zh_CN, 0, 0, 867]
0728 16:43:18.729 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstNetShare}}, 远程共享文件外传, zh_CN, 0, 0, 868]
0728 16:43:18.731 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.dripDataDisclosureStg}}, 零星检测策略, zh_CN, 0, 0, 869]
0728 16:43:18.734 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstTelnet}}, telnet传输, zh_CN, 0, 0, 870]
0728 16:43:18.736 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.antivirusSoftware}}, 杀毒软件, zh_CN, 0, 0, 871]
0728 16:43:18.739 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstWebPaste}}, 网页粘贴文本内容, zh_CN, 0, 0, 872]
0728 16:43:18.742 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.appBlock}}, 程序访问限制, zh_CN, 0, 0, 873]
0728 16:43:18.745 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.strategyName}}, 策略名称, zh_CN, 0, 0, 874]
0728 16:43:18.748 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstImFile}}, 发送的即时通信文件涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 875]
0728 16:43:18.750 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numMailFile}}, 邮件附件内容, zh_CN, 0, 0, 1926]
0728 16:43:18.753 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.outgoingScreenWaterMark}}, 外发屏幕水印, zh_CN, 0, 0, 876]
0728 16:43:18.755 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.USBName}}, USB名称, zh_CN, 0, 0, 877]
0728 16:43:18.757 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.installationDate}}, 安装日期, zh_CN, 0, 0, 878]
0728 16:43:18.759 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.iPv6Address}}, IPv6地址, zh_CN, 0, 0, 879]
0728 16:43:18.761 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.IDeviceType}}, 设备类型, zh_CN, 0, 0, 880]
0728 16:43:18.764 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.emailAttachFile}}, 邮件附件限制, zh_CN, 0, 0, 881]
0728 16:43:18.766 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.level.level1}}, 警告, zh_CN, 0, 0, 882]
0728 16:43:18.768 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.driverStrategy}}, 存储设备使用控制, zh_CN, 0, 0, 883]
0728 16:43:18.772 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstCut}}, USB剪切文件, zh_CN, 0, 0, 884]
0728 16:43:18.774 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{label.grade.1}}, 一般数据, zh_CN, 0, 0, 885]
0728 16:43:18.776 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.alarmData}}, 告警数据, zh_CN, 0, 0, 886]
0728 16:43:18.778 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.emailContent}}, 邮件内容限制, zh_CN, 0, 0, 887]
0728 16:43:18.780 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.oldName}}, 旧名称, zh_CN, 0, 0, 888]
0728 16:43:18.782 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.batchDecLimit}}, 您的解密文件次数已达到每{{period}}允许的上限{{number}}个, zh_CN, 0, 0, 889]
0728 16:43:18.784 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numFTP}}, FTP上传文件内容涉及违规，违规详情如下：文件大小或外传文件累计数量违规；文件路径：{{FilePath}}, zh_CN, 0, 0, 1927]
0728 16:43:18.787 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.webUpload}}, 您触发违规网页文件上传事件，进程名:{{ProcessName}}，文件路径：{{FilePath}}, zh_CN, 0, 0, 890]
0728 16:43:18.790 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.telnetCommControl}}, Telnet通讯管控, zh_CN, 0, 0, 891]
0728 16:43:18.792 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstBatchEnc}}, 批量加解密, zh_CN, 0, 0, 892]
0728 16:43:18.795 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.logFilter}}, 计算机Windows日志设置, zh_CN, 0, 0, 893]
0728 16:43:18.798 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.hardAsset}}, 硬件资产变更报警, zh_CN, 0, 0, 894]
0728 16:43:18.800 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.chatSendFile}}, 聊天文件外传管控, zh_CN, 0, 0, 895]
0728 16:43:18.803 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.adbLimitStrategy}}, ADB管控, zh_CN, 0, 0, 896]
0728 16:43:18.806 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstBurn}}, 光盘刻录的文件涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 897]
0728 16:43:18.808 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.remoteDesktop}}, 您违规使用远程桌面连接工具，远程连接地址：{{address}}，外传文件路径：{{filePath}}, zh_CN, 0, 0, 898]
0728 16:43:18.810 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.outgoingConfig}}, 外发高级配置, zh_CN, 0, 0, 899]
0728 16:43:18.812 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstMTP}}, MTP外传文件, zh_CN, 0, 0, 900]
0728 16:43:18.815 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.subnetMask}}, 子网掩码, zh_CN, 0, 0, 901]
0728 16:43:18.818 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.AISendFileMsg}}, 您在使用AI交互时触发了AI发送内容限制策略，违规内容为：\r\n关键字：{{KeyWord}}\r\nAI名称：{{AIName}}\r\n进程名：{{ProcessName}}, zh_CN, 0, 0, 2063]
0728 16:43:18.821 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.SCSIController}}, SCSI控制器, zh_CN, 0, 0, 902]
0728 16:43:18.823 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.fileGateConfig}}, 旧服务器白名单配置表, zh_CN, 0, 0, 903]
0728 16:43:18.826 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.qrLoginStrategy}}, 终端扫描登录策略, zh_CN, 0, 0, 904]
0728 16:43:18.829 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstWebDownloadFile}}, 网页下载文件, zh_CN, 0, 0, 905]
0728 16:43:18.833 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.termSecurityDetectionStrategy}}, 终端安全检测, zh_CN, 0, 0, 906]
0728 16:43:18.836 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.diskUsageRate}}, 磁盘占用率, zh_CN, 0, 0, 907]
0728 16:43:18.838 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{label.grade.0}}, 无等级, zh_CN, 0, 0, 908]
0728 16:43:18.840 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.hardAsset}}, {{AssetType}}资产信息{{LogType}}，详情如下:{{DetailInfo}}, zh_CN, 0, 0, 909]
0728 16:43:18.842 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.processorID}}, 处理器ID, zh_CN, 0, 0, 910]
0728 16:43:18.844 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.diskBackupSpace}}, 当前备份目录所在磁盘剩余空间均不足{{DiskFreeSpace}}GB，请及时清理，以免无法备份文件, zh_CN, 0, 0, 911]
0728 16:43:18.847 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstMacChanged}}, MAC改变,消失的MAC为{{sub}},新增的MAC为{{Add}}, zh_CN, 0, 0, 912]
0728 16:43:18.850 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.softWareCharge}}, 您违规安装未授权软件{{SoftwareName}}请联系管理员授权, zh_CN, 0, 0, 2064]
0728 16:43:18.852 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.MTP}}, MTP管控, zh_CN, 0, 0, 913]
0728 16:43:18.854 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.refreshFrequency}}, 刷新频率, zh_CN, 0, 0, 914]
0728 16:43:18.856 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.DetailInfo}}, 详细信息, zh_CN, 0, 0, 915]
0728 16:43:18.858 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.usbcode}}, USB编号, zh_CN, 0, 0, 916]
0728 16:43:18.861 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.wifiBssid}}, WiFi物理地址, zh_CN, 0, 0, 917]
0728 16:43:18.863 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numMTP}}, MTP外传文件, zh_CN, 0, 0, 1928]
0728 16:43:18.866 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.freeSize}}, 空闲大小, zh_CN, 0, 0, 918]
0728 16:43:18.868 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.emailKeywordBlockStrategy}}, 邮件内容限制, zh_CN, 0, 0, 919]
0728 16:43:18.869 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.documentTrackGlobal}}, 文档追踪——高级配置, zh_CN, 0, 0, 920]
0728 16:43:18.871 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.webUpload}}, 网页上传文件管控, zh_CN, 0, 0, 921]
0728 16:43:18.874 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.tapeDrive}}, 磁带驱动器, zh_CN, 0, 0, 922]
0728 16:43:18.877 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.strategyHttpWhiteList}}, {{ProcessName}}访问白名单网站（{{ServerName}}）失败！原因：同一个浏览器打开了不同加密策略的白名单网站, zh_CN, 0, 0, 923]
0728 16:43:18.880 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.modeOfOperation}}, 工作模式, zh_CN, 0, 0, 924]
0728 16:43:18.882 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.level.level2}}, 次要, zh_CN, 0, 0, 925]
0728 16:43:18.884 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.processHttpWhiteList}}, {{ProcessName}}访问白名单网站（{{ServerName}}）失败！原因：非特殊进程访问白名单网站，可反馈给管理员处理。, zh_CN, 0, 0, 926]
0728 16:43:18.886 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.appLogConfig}}, 程序使用记录设置, zh_CN, 0, 0, 927]
0728 16:43:18.888 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstLocShare}}, 本地共享文件, zh_CN, 0, 0, 928]
0728 16:43:18.890 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.alarmTemplateParam}}, 告警消息模板参数, zh_CN, 0, 0, 929]
0728 16:43:18.894 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstSaveAs}}, 另存到移动存储设备的内容涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 930]
0728 16:43:18.897 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.signReportPush}}, {{date}} {{timeDimension}} {{signName}}征兆情况：<br> 严重违规等级次数：{{seriousCount}}次, <br> 重要违规等级次数：{{importantCount}}次, <br> 一般违规等级次数：{{normalCount}}次, zh_CN, 0, 0, 931]
0728 16:43:18.899 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numNetShare}}, 远程共享文件外传, zh_CN, 0, 0, 1929]
0728 16:43:18.901 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.seamlessReplace}}, 兼容其他产品设置, zh_CN, 0, 0, 932]
0728 16:43:18.903 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelUsbCopy}}, USB复制文件, zh_CN, 0, 0, 933]
0728 16:43:18.905 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay32}}, 文件拷贝至共享目录, zh_CN, 0, 0, 934]
0728 16:43:18.907 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.softwareLimitStrategy}}, 软件黑白名单, zh_CN, 0, 0, 935]
0728 16:43:18.910 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.formatStore}}, U盘违规格式化, zh_CN, 0, 0, 936]
0728 16:43:18.912 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.netInterface}}, 网络接口管控, zh_CN, 0, 0, 937]
0728 16:43:18.914 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.releaseDate}}, 发布日期, zh_CN, 0, 0, 938]
0728 16:43:18.916 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.usbFileConfig}}, USB外传文件设置, zh_CN, 0, 0, 939]
0728 16:43:18.919 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstDrip}}, 零星检测, zh_CN, 0, 0, 940]
0728 16:43:18.921 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstFtp}}, FTP上传文件内容文件涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 941]
0728 16:43:18.923 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.systemCatalog}}, 系统目录, zh_CN, 0, 0, 942]
0728 16:43:18.927 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelWebFile}}, 发送到网页的文件涉及违规，违规详情如下：标签内容{{LabelName}}违规 或 “文档等级”高于指定级别{{LabelLevel}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 943]
0728 16:43:18.932 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstCut}}, USB剪切文件, zh_CN, 0, 0, 944]
0728 16:43:18.935 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.store}}, 存储设备使用控制, zh_CN, 0, 0, 945]
0728 16:43:18.939 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.asset}}, 资产变更报警, zh_CN, 0, 0, 946]
0728 16:43:18.943 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.uninstallStrategy}}, 软件安装/卸载限制——卸载限制, zh_CN, 0, 0, 947]
0728 16:43:18.945 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mobileOpenSuffixStrategy}}, 移动端后缀配置, zh_CN, 0, 0, 948]
0728 16:43:18.948 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.computerNameAlarmConfig}}, 计算机名称变更告警设置, zh_CN, 0, 0, 949]
0728 16:43:18.950 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.1394ControlCard}}, 1394控制卡, zh_CN, 0, 0, 950]
0728 16:43:18.952 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.softWareTaskStrategy}}, 软件卸载, zh_CN, 0, 0, 951]
0728 16:43:18.954 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.softLimit}}, 软件黑白名单, zh_CN, 0, 0, 952]
0728 16:43:18.956 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.sensitiveBackupConfig}}, 敏感内容文件备份配置, zh_CN, 0, 0, 953]
0728 16:43:18.959 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.wifiSsid}}, WiFi名称, zh_CN, 0, 0, 954]
0728 16:43:18.961 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.LabelLevel}}, 标签等级, zh_CN, 0, 0, 955]
0728 16:43:18.963 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstCopy}}, 复制到移动存储设备的内容涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 956]
0728 16:43:18.966 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelNetShare}}, 远程共享文件外传, zh_CN, 0, 0, 957]
0728 16:43:18.969 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstMailFile}}, 邮件附件内容, zh_CN, 0, 0, 958]
0728 16:43:18.996 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstWebPaste}}, 网页粘贴文本内容, zh_CN, 0, 0, 959]
0728 16:43:19.001 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.urlStrategy}}, 网页浏览限制, zh_CN, 0, 0, 960]
0728 16:43:19.004 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.mailSenderWhiteList}}, 您发送的邮件附件已被解密，接收解密附件的邮箱地址为：\r\n{{emailAddr}}附件名称：{{attachment}}, zh_CN, 0, 0, 961]
0728 16:43:19.007 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.serverAnyDiskUsage}}, {{devType}}【{{devId}}】盘符（{{disk}}）剩余可用空间为{{value}},低于设置的阈值{{threshold}}GB, zh_CN, 0, 0, 962]
0728 16:43:19.009 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Type}}, 类型, zh_CN, 0, 0, 963]
0728 16:43:19.011 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.keyTable}}, 内置论坛网站协议, zh_CN, 0, 0, 964]
0728 16:43:19.013 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.registeredUser}}, 注册用户, zh_CN, 0, 0, 965]
0728 16:43:19.015 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.fileFilterSetup}}, 文件监视过滤, zh_CN, 0, 0, 966]
0728 16:43:19.019 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.size}}, 大小, zh_CN, 0, 0, 967]
0728 16:43:19.022 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numBurn}}, 光盘刻录文件, zh_CN, 0, 0, 1930]
0728 16:43:19.025 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.appBlock}}, {{FilePath}}，应用程序限制, zh_CN, 0, 0, 968]
0728 16:43:19.029 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.WinTitle}}, 窗口标题, zh_CN, 0, 0, 969]
0728 16:43:19.031 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.softRunning}}, 必须运行软件, zh_CN, 0, 0, 970]
0728 16:43:19.034 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.DNSServer}}, DNS服务器, zh_CN, 0, 0, 971]
0728 16:43:19.037 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstWebFile}}, 网页上传文件, zh_CN, 0, 0, 972]
0728 16:43:19.039 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.serialNumber}}, 序列号, zh_CN, 0, 0, 973]
0728 16:43:19.042 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.userDense}}, 操作员密级设置, zh_CN, 0, 0, 974]
0728 16:43:19.044 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.modem}}, 调制解调器, zh_CN, 0, 0, 975]
0728 16:43:19.046 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.seriousCount}}, 严重次数, zh_CN, 0, 0, 976]
0728 16:43:19.048 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelUsbCut}}, 剪切到移动存储设备的内容涉及违规，违规详情如下：标签内容{{LabelName}}违规 或 “文档等级”高于指定级别{{LabelLevel}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 977]
0728 16:43:19.050 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.workMode}}, 工作模式设置, zh_CN, 0, 0, 978]
0728 16:43:19.052 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelImFile}}, 发送的即时通信文件涉及违规，违规详情如下：标签内容{{LabelName}}违规 或 “文档等级”高于指定级别{{LabelLevel}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 979]
0728 16:43:19.055 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.devName}}, 服务器名称, zh_CN, 0, 0, 980]
0728 16:43:19.057 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.netShareFile}}, 违规使用网络共享外传文件，文件路径：{{alarmData}}, zh_CN, 0, 0, 981]
0728 16:43:19.059 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.verticalResolution}}, 垂直分辨率, zh_CN, 0, 0, 982]
0728 16:43:19.061 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.autoPatchStrategy}}, 自动补丁策略, zh_CN, 0, 0, 983]
0728 16:43:19.064 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.remoteUploadTool}}, 您触发违规远程工具文件上传事件，进程名:{{ProcessName}}，文件路径：{{FilePath}}, zh_CN, 0, 0, 1931]
0728 16:43:19.067 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.sysUser}}, 管理员信息, zh_CN, 0, 0, 984]
0728 16:43:19.069 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelFTP}}, FTP上传文件, zh_CN, 0, 0, 1888]
0728 16:43:19.072 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.formatStore}}, 违规使用了专用U盘：{{DeviceName}} ，序列号：{{usbcode}} ， 品牌：{{vendor}}，容量：{{capacity}}, zh_CN, 0, 0, 985]
0728 16:43:19.076 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.MACAddress}}, MAC地址, zh_CN, 0, 0, 986]
0728 16:43:19.080 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.forumPostingRecordLimit}}, 论坛发帖记录限制, zh_CN, 0, 0, 987]
0728 16:43:19.083 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.bus}}, 总线, zh_CN, 0, 0, 988]
0728 16:43:19.085 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.motherboard}}, 主板, zh_CN, 0, 0, 989]
0728 16:43:19.087 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstWebPrint}}, 网页打印, zh_CN, 0, 0, 990]
0728 16:43:19.089 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelFTP}}, FTP上传文件内容涉及违规，违规详情如下：标签内容{{LabelName}}违规 或 “文档等级”高于指定级别{{LabelLevel}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 1889]
0728 16:43:19.092 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.serverDataDiskUsage}}, {{devType}}【{{devId}}】盘符（{{disk}}）剩余可用空间为{{value}},低于设置的阈值{{threshold}}GB, zh_CN, 0, 0, 991]
0728 16:43:19.097 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.iPv4Address}}, IPv4地址, zh_CN, 0, 0, 992]
0728 16:43:19.100 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.DeviceInstanceId}}, 实例路径, zh_CN, 0, 0, 993]
0728 16:43:19.102 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.RemoteIP}}, 远程IP, zh_CN, 0, 0, 994]
0728 16:43:19.105 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.outgoingProcess}}, 软件白名单, zh_CN, 0, 0, 995]
0728 16:43:19.108 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numBluetooth}}, 蓝牙外传文件, zh_CN, 0, 0, 1932]
0728 16:43:19.111 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstBurn}}, 光盘刻录文件, zh_CN, 0, 0, 996]
0728 16:43:19.114 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.serviceName}}, 服务名, zh_CN, 0, 0, 997]
0728 16:43:19.117 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.groupPolicyFunction}}, 组策略功能信息, zh_CN, 0, 0, 998]
0728 16:43:19.119 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.backUpConfig}}, 加密文件备份设置, zh_CN, 0, 0, 999]
0728 16:43:19.122 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.FilePath}}, 文件路径, zh_CN, 0, 0, 1000]
0728 16:43:19.125 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.appLogOption}}, 程序使用监视高级配置, zh_CN, 0, 0, 1001]
0728 16:43:19.129 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.wifiBlockStrategy}}, WiFi连接限制, zh_CN, 0, 0, 1002]
0728 16:43:19.133 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.decToolLimitInfo}}, 高危软件库, zh_CN, 0, 0, 1003]
0728 16:43:19.135 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstSaveAs}}, USB另存为文件, zh_CN, 0, 0, 1004]
0728 16:43:19.138 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processStgNet}}, 受控程序策略库——网络限制策略, zh_CN, 0, 0, 1005]
0728 16:43:19.140 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.floppyDrive}}, 软驱, zh_CN, 0, 0, 1006]
0728 16:43:19.145 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.serverDataDiskUsage}}, 服务器数据磁盘空间不足告警, zh_CN, 0, 0, 1007]
0728 16:43:19.148 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.AISendFileMsg}}, AI发送内容限制, zh_CN, 0, 0, 2065]
0728 16:43:19.150 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstImDownload}}, 下载的即时通信文件涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 1008]
0728 16:43:19.152 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstRemoteUploadTool}}, 远程工具上传文件, zh_CN, 0, 0, 1933]
0728 16:43:19.154 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.signReportPush}}, 征兆告警推送, zh_CN, 0, 0, 1009]
0728 16:43:19.156 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.telnetControl}}, telnet管控, zh_CN, 0, 0, 1010]
0728 16:43:19.159 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelMTP}}, MTP外传文件, zh_CN, 0, 0, 1011]
0728 16:43:19.163 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.active}}, 是否激活, zh_CN, 0, 0, 1934]
0728 16:43:19.165 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.IKey}}, 违规类型, zh_CN, 0, 0, 1012]
0728 16:43:19.167 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.numberOfPartitions}}, 分区数, zh_CN, 0, 0, 1013]
0728 16:43:19.170 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.installStrategy}}, 软件安装/卸载限制——安装限制, zh_CN, 0, 0, 1014]
0728 16:43:19.173 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.alarmActionParam}}, 告警消息模板参数, zh_CN, 0, 0, 1015]
0728 16:43:19.177 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.graphicsCard}}, 显卡, zh_CN, 0, 0, 1016]
0728 16:43:19.180 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.role}}, 管理员角色, zh_CN, 0, 0, 1017]
0728 16:43:19.183 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstDrip}}, 零星检测, zh_CN, 0, 0, 1018]
0728 16:43:19.185 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{enc.denseInfo.confidential}}, 机密文件, zh_CN, 0, 0, 1019]
0728 16:43:19.188 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.DiskFreeSpace}}, 磁盘剩余空间, zh_CN, 0, 0, 1020]
0728 16:43:19.191 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelMTP}}, MTP外传的文件涉及违规，违规详情如下：标签内容{{LabelName}}违规 或 “文档等级”高于指定级别{{LabelLevel}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 1021]
0728 16:43:19.194 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.softAsset}}, {{AssetType}}资产信息{{LogType}}，详情如下:{{DetailInfo}}, zh_CN, 0, 0, 1022]
0728 16:43:19.197 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.sensitiveFileLossType}}, 允许的外传途径, zh_CN, 0, 0, 1023]
0728 16:43:19.200 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.userExtInfo}}, 操作员扩展数据, zh_CN, 0, 0, 1024]
0728 16:43:19.202 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay256}}, 网页上传文件, zh_CN, 0, 0, 1025]
0728 16:43:19.205 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.capacity}}, 容量, zh_CN, 0, 0, 1026]
0728 16:43:19.208 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.interfaceType}}, 接口类型, zh_CN, 0, 0, 1027]
0728 16:43:19.212 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstWeb}}, 访问网页文本内容, zh_CN, 0, 0, 1028]
0728 16:43:19.214 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.USBDevice}}, USB设备, zh_CN, 0, 0, 1029]
0728 16:43:19.216 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.media}}, 介质, zh_CN, 0, 0, 1030]
0728 16:43:19.219 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.ftpTool}}, FTP工具传输, zh_CN, 0, 0, 1031]
0728 16:43:19.222 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.remoteUploadTool}}, 远程工具上传文件, zh_CN, 0, 0, 1935]
0728 16:43:19.224 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.lossTypeParam}}, 敏感泄露类型, zh_CN, 0, 0, 1032]
0728 16:43:19.227 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.contentScan}}, 敏感内容扫描策略, zh_CN, 0, 0, 1033]
0728 16:43:19.229 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.appVersionLimitStrategy}}, 程序版本限制, zh_CN, 0, 0, 1034]
0728 16:43:19.232 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.chatSendFile}}, 您触发违规聊天文件外传事件，进程名:{{ProcessName}}，文件路径：{{FilePath}}, zh_CN, 0, 0, 1035]
0728 16:43:19.234 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numBluetooth}}, 蓝牙传输的文件涉及违规，违规详情如下：文件大小或外传文件累计数量违规；文件路径：{{FilePath}}, zh_CN, 0, 0, 1936]
0728 16:43:19.236 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numMTP}}, MTP外传的文件涉及违规，违规详情如下：文件大小或外传文件累计数量违规；文件路径：{{FilePath}}, zh_CN, 0, 0, 1937]
0728 16:43:19.240 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelRemoteDesktop}}, 远程桌面文件外传, zh_CN, 0, 0, 1036]
0728 16:43:19.243 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstIm}}, 即时通讯发送文本内容, zh_CN, 0, 0, 1037]
0728 16:43:19.245 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.netDisk}}, 网盘传输备份限制, zh_CN, 0, 0, 1038]
0728 16:43:19.247 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.bluetooth}}, 违规通过蓝牙传输文件，文件路径：{{FilePath}}, zh_CN, 0, 0, 1039]
0728 16:43:19.249 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.ftpTool}}, 您违规使用FTP工具：{{ProcessName}}，{{Optype}}文件，文件路径：{{FilePath}}, zh_CN, 0, 0, 1040]
0728 16:43:19.250 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.softProcessMd5}}, 受控程序策略库——应用程序安装卸载防伪冒指纹表, zh_CN, 0, 0, 1041]
0728 16:43:19.253 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelBurn}}, 光盘刻录文件, zh_CN, 0, 0, 1042]
0728 16:43:19.256 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstMagneticDiskUsed}}, 您的所有磁盘使用率为{{Function}}%已超过设置的阈值{{number}}%, zh_CN, 0, 0, 1043]
0728 16:43:19.258 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.shareStgConfig}}, 敏感内容检测配置——共享管控设置, zh_CN, 0, 0, 1044]
0728 16:43:19.261 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.path}}, 路径, zh_CN, 0, 0, 1045]
0728 16:43:19.263 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.outgoingPrintWaterMark}}, 外发打印水印, zh_CN, 0, 0, 1046]
0728 16:43:19.265 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processInject}}, 受控注入设置, zh_CN, 0, 0, 1047]
0728 16:43:19.268 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstMail}}, 邮件传输文本内容, zh_CN, 0, 0, 1048]
0728 16:43:19.271 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.burnConfigStrategy}}, 刻录机使用管控, zh_CN, 0, 0, 1049]
0728 16:43:19.273 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.chatWhiteList}}, 通讯账号库列表, zh_CN, 0, 0, 1050]
0728 16:43:19.275 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.vendor}}, 供应商, zh_CN, 0, 0, 1051]
0728 16:43:19.277 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.flowThresholdLimit}}, {{ProcessName}}当前使用已超过阈值流量，其中上传流量：{{UpFlow}} MB；下载流量：{{DownFlow}} MB；合计：{{TotalFlow}} MB, zh_CN, 0, 0, 1938]
0728 16:43:19.279 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.burnCD}}, 刻录机使用管控, zh_CN, 0, 0, 1052]
0728 16:43:19.281 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstBurn}}, 光盘刻录文件, zh_CN, 0, 0, 1053]
0728 16:43:19.283 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.cpuUsage}}, CPU使用率, zh_CN, 0, 0, 1054]
0728 16:43:19.286 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstNetDiskDownload}}, 网盘下载文件, zh_CN, 0, 0, 1055]
0728 16:43:19.288 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.diskSize}}, 磁盘大小, zh_CN, 0, 0, 1056]
0728 16:43:19.290 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.webPortStrategy}}, 网络端口限制, zh_CN, 0, 0, 1057]
0728 16:43:19.292 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.alarmTypeParam}}, 告警来源参数, zh_CN, 0, 0, 1058]
0728 16:43:19.294 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.landLabelingStrategy}}, 落地加标签, zh_CN, 0, 0, 1059]
0728 16:43:19.297 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.UPSDevice}}, UPS设备, zh_CN, 0, 0, 1060]
0728 16:43:19.298 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.winTitle}}, 您打开一个违规窗口标题“{{WinTitle}}”，包含关键字“{{KeyWord}}”, zh_CN, 0, 0, 1061]
0728 16:43:19.303 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.ADBController}}, ADB管控, zh_CN, 0, 0, 1062]
0728 16:43:19.306 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.maximumTransferRate}}, 最大传输率, zh_CN, 0, 0, 1063]
0728 16:43:19.308 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.PCMCIAController}}, PCMCIA控制器, zh_CN, 0, 0, 1064]
0728 16:43:19.310 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.telnetControl}}, 您违规使用telnet通讯工具，进程名:{{ProcessName}}，服务器地址:{{Address}} 命令: {{CMDData}}, zh_CN, 0, 0, 1065]
0728 16:43:19.313 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.IType}}, 接口, zh_CN, 0, 0, 1066]
0728 16:43:19.316 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.backupRule}}, 备份过滤规则, zh_CN, 0, 0, 1067]
0728 16:43:19.319 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.FileName}}, 文件名称, zh_CN, 0, 0, 1068]
0728 16:43:19.322 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstImFile}}, 即时通讯发送文件, zh_CN, 0, 0, 1069]
0728 16:43:19.326 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.webpageBrowseAudit}}, 网页浏览网址过滤, zh_CN, 0, 0, 1070]
0728 16:43:19.329 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.readPermission}}, 阅读权限设置, zh_CN, 0, 0, 1071]
0728 16:43:19.333 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.groupPolicyStrategy}}, 组策略配置, zh_CN, 0, 0, 1072]
0728 16:43:19.337 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.winTitle}}, 窗口标题限制, zh_CN, 0, 0, 1073]
0728 16:43:19.339 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstMTP}}, MTP外传的文件涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 1074]
0728 16:43:19.342 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelWebFile}}, 网页上传文件, zh_CN, 0, 0, 1075]
0728 16:43:19.345 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstImDownload}}, 即时通讯下载文件, zh_CN, 0, 0, 1076]
0728 16:43:19.349 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstRemoteDesktop}}, 远程桌面发送的文件涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 1077]
0728 16:43:19.351 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.netCard}}, 网卡接口, zh_CN, 0, 0, 1078]
0728 16:43:19.354 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.imToolStrategy}}, 通讯工具白名单, zh_CN, 0, 0, 1079]
0728 16:43:19.356 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstNetShare}}, 访问/修改网络共享时涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 1080]
0728 16:43:19.358 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.sysAlarmConfig}}, 计算机告警设置, zh_CN, 0, 0, 1081]
0728 16:43:19.360 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstNetShare}}, 远程共享文件外传, zh_CN, 0, 0, 1082]
0728 16:43:19.364 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.soundCard}}, 声卡, zh_CN, 0, 0, 1083]
0728 16:43:19.366 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.workModeProcessMd5}}, 工作模式切换进程防伪冒, zh_CN, 0, 0, 1084]
0728 16:43:19.368 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.serverCpuUsage}}, 服务器CPU占用告警, zh_CN, 0, 0, 1085]
0728 16:43:19.370 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.computerType}}, 计算机类型, zh_CN, 0, 0, 1939]
0728 16:43:19.372 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstImReceive}}, 即时通讯接收文本内容, zh_CN, 0, 0, 1086]
0728 16:43:19.374 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.sysWebPortStrategy}}, 内置端口限制策略, zh_CN, 0, 0, 1087]
0728 16:43:19.376 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.totalHeads}}, 总磁头数, zh_CN, 0, 0, 1088]
0728 16:43:19.380 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstNetDiskDownload}}, 您的网盘下载文件内容涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 1089]
0728 16:43:19.382 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.sub}}, 减去, zh_CN, 0, 0, 1090]
0728 16:43:19.384 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstRemoteDesktop}}, 远程桌面外传文件, zh_CN, 0, 0, 1091]
0728 16:43:19.386 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.netShareFile}}, 网络共享文件外传告警, zh_CN, 0, 0, 1092]
0728 16:43:19.388 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mobileWPSConfig}}, 移动终端WPS配置, zh_CN, 0, 0, 1093]
0728 16:43:19.390 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.directOutgingWhiteList}}, 直接外发程序白名单, zh_CN, 0, 0, 1094]
0728 16:43:19.393 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.flowThresholdLimit}}, 流量阈值限制, zh_CN, 0, 0, 1940]
0728 16:43:19.396 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.winTitleBlockStrategy}}, 窗口标题限制, zh_CN, 0, 0, 1095]
0728 16:43:19.399 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.manufacturer}}, 制造商, zh_CN, 0, 0, 1096]
0728 16:43:19.401 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.emailContent}}, 您发送的邮件触发了邮件内容限制策略，违规内容为：\r\n关键字：\r\n{{KeyWord}}收件人：{{mailReceiver}}发件人：{{mailSender}}, zh_CN, 0, 0, 1097]
0728 16:43:19.403 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.effectiveFunction}}, 敏感外传检测配置, zh_CN, 0, 0, 1098]
0728 16:43:19.405 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.organization}}, 组织, zh_CN, 0, 0, 1099]
0728 16:43:19.408 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.printerSet}}, 打印权限控制, zh_CN, 0, 0, 1100]
0728 16:43:19.411 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.RuleName}}, 规则名称, zh_CN, 0, 0, 1101]
0728 16:43:19.413 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelNetShare}}, 访问/修改网络共享时涉及违规，违规详情如下：标签内容{{LabelName}}违规 或 “文档等级”高于指定级别{{LabelLevel}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 1102]
0728 16:43:19.416 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.installationTime}}, 安装时间, zh_CN, 0, 0, 1103]
0728 16:43:19.418 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.encryptSpecialPath}}, 特殊目录设置, zh_CN, 0, 0, 1104]
0728 16:43:19.420 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.memoryAlarmConfig}}, 内存占用告警设置, zh_CN, 0, 0, 1105]
0728 16:43:19.421 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numUsbCut}}, 剪切到移动存储设备的内容涉及违规，违规详情如下：文件大小或外传文件累计数量违规；文件路径：{{FilePath}}, zh_CN, 0, 0, 1941]
0728 16:43:19.423 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.workMode}}, 工作模式切换, zh_CN, 0, 0, 1106]
0728 16:43:19.426 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.CPU}}, CPU, zh_CN, 0, 0, 1107]
0728 16:43:19.429 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.autoGetIP}}, 自动获取IP, zh_CN, 0, 0, 1108]
0728 16:43:19.431 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstFtp}}, FTP上传文件, zh_CN, 0, 0, 1109]
0728 16:43:19.433 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numRemoteDesktop}}, 远程桌面外传文件, zh_CN, 0, 0, 1942]
0728 16:43:19.435 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstSysDiskUsed}}, 您的系统盘使用率为{{Function}}%已超过设置的阈值{{number}}%, zh_CN, 0, 0, 1110]
0728 16:43:19.437 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.usage}}, 使用情况, zh_CN, 0, 0, 1111]
0728 16:43:19.438 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.url}}, 网址基础数据, zh_CN, 0, 0, 1112]
0728 16:43:19.441 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.webPostStrategy}}, 网页发送内容限制, zh_CN, 0, 0, 1113]
0728 16:43:19.443 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.sysBaseConfig}}, 计算机设置, zh_CN, 0, 0, 1114]
0728 16:43:19.445 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.TotalFlow}}, 总流量, zh_CN, 0, 0, 1943]
0728 16:43:19.447 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.batchEncOrDec}}, 批量加解密, zh_CN, 0, 0, 1115]
0728 16:43:19.449 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numImFile}}, 即时通讯发送文件, zh_CN, 0, 0, 1944]
0728 16:43:19.458 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.terminalUpgrade}}, 终端在线升级, zh_CN, 0, 0, 1116]
0728 16:43:19.463 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.totalSize}}, 总大小, zh_CN, 0, 0, 1117]
0728 16:43:19.465 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.bitsPerPixel}}, 每像素位数, zh_CN, 0, 0, 1118]
0728 16:43:19.467 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.sectorsPerTrack}}, 每磁道扇区数, zh_CN, 0, 0, 1119]
0728 16:43:19.469 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.hardwareAssetAlarmSetup}}, 硬件资产变更报警设置, zh_CN, 0, 0, 1120]
0728 16:43:19.471 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.serialPortDevice}}, 串口设备, zh_CN, 0, 0, 1121]
0728 16:43:19.474 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.termScan}}, 终端安全检测结果风险等级为{{level}} 共扫描{{Number}}项，其中成功{{SucNumber}}项 失败{{ErrNumber}}项, zh_CN, 0, 0, 1122]
0728 16:43:19.477 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{enc.denseInfo.internal}}, 内部资料文件, zh_CN, 0, 0, 1123]
0728 16:43:19.479 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelUsbCopy}}, 复制到移动存储设备的内容涉及违规，违规详情如下：标签内容{{LabelName}}违规 或 “文档等级”高于指定级别{{LabelLevel}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 1124]
0728 16:43:19.481 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.ftpControllerConfig}}, FTP文件管理, zh_CN, 0, 0, 1125]
0728 16:43:19.483 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstWeb}}, 访问网页文本内容, zh_CN, 0, 0, 1126]
0728 16:43:19.485 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.otherDeviceLimit}}, 您的设备被禁止使用，设备名：{{DeviceName}}，设备实例路径：{{DeviceInstanceId}}, zh_CN, 0, 0, 1127]
0728 16:43:19.486 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mailWhiteListStrategy}}, 邮件白名单设置——收件人白名单, zh_CN, 0, 0, 1128]
0728 16:43:19.489 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.KeyWord}}, 关键词, zh_CN, 0, 0, 1129]
0728 16:43:19.492 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.ipAlarmConfig}}, IP地址变更告警设置, zh_CN, 0, 0, 1130]
0728 16:43:19.494 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.totalTracks}}, 总磁道数, zh_CN, 0, 0, 1131]
0728 16:43:19.497 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.devType}}, 服务器类型, zh_CN, 0, 0, 1132]
0728 16:43:19.499 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.driverVersion}}, 驱动版本, zh_CN, 0, 0, 1133]
0728 16:43:19.502 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstABDUpload}}, ADB传输文件内容涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 1134]
0728 16:43:19.504 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Function}}, 功能, zh_CN, 0, 0, 1135]
0728 16:43:19.508 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.version}}, 版本, zh_CN, 0, 0, 1136]
0728 16:43:19.511 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.ServerName}}, 服务器名称, zh_CN, 0, 0, 1137]
0728 16:43:19.514 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.webSite}}, 联网验证地址, zh_CN, 0, 0, 1138]
0728 16:43:19.517 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.infraredDevice}}, 红外线设备, zh_CN, 0, 0, 1139]
0728 16:43:19.520 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.i18nDict}}, 国际化字典, zh_CN, 0, 0, 1140]
0728 16:43:19.522 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.wifiCollectStrategy}}, WiFi信息收集, zh_CN, 0, 0, 1141]
0728 16:43:19.525 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.batchDecLimit}}, 解密数量限制, zh_CN, 0, 0, 1142]
0728 16:43:19.527 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstIpChanged}}, IP改变,消失的IP为{{sub}},新增的IP为{{Add}}, zh_CN, 0, 0, 1143]
0728 16:43:19.529 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.AIModel}}, 您违规使用AI模型工具，违规内容为：\r\nAI名称：{{AIName}}\r\n进程名：{{ProcessName}}, zh_CN, 0, 0, 2066]
0728 16:43:19.531 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.timelyBackupStg}}, 即时备份, zh_CN, 0, 0, 1144]
0728 16:43:19.533 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.opticalDiskDrive}}, 光盘驱动器, zh_CN, 0, 0, 1145]
0728 16:43:19.536 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay1}}, 下载文件, zh_CN, 0, 0, 1146]
0728 16:43:19.539 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.quantity}}, 数量, zh_CN, 0, 0, 1147]
0728 16:43:19.541 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstPost}}, 论坛发帖文本内容, zh_CN, 0, 0, 1148]
0728 16:43:19.543 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.relGroupUser}}, 操作员所属部门信息, zh_CN, 0, 0, 1149]
0728 16:43:19.545 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.description}}, 描述, zh_CN, 0, 0, 1150]
0728 16:43:19.547 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numNetShare}}, 访问/修改网络共享时涉及违规，违规详情如下：文件大小或外传文件累计数量违规；文件路径：{{FilePath}}, zh_CN, 0, 0, 1945]
0728 16:43:19.549 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.remoteDesktop}}, 远程桌面管控, zh_CN, 0, 0, 1151]
0728 16:43:19.551 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelImFile}}, 即时通讯发送文件, zh_CN, 0, 0, 1152]
0728 16:43:19.554 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.productionDayDate}}, 生产日期, zh_CN, 0, 0, 1153]
0728 16:43:19.556 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.waterMarkStrategy}}, 打印水印设置, zh_CN, 0, 0, 1154]
0728 16:43:19.559 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.appVer}}, 软件版本限制, zh_CN, 0, 0, 1155]
0728 16:43:19.562 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.screenshotStrategy}}, 截屏设置, zh_CN, 0, 0, 1156]
0728 16:43:19.564 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.specialPathStrategy}}, 软件安装/卸载限制——特殊目录放行, zh_CN, 0, 0, 1157]
0728 16:43:19.566 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.mailSender}}, 邮箱发送人, zh_CN, 0, 0, 1158]
0728 16:43:19.570 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.fullDiskScanBackupStg}}, 全盘扫描备份, zh_CN, 0, 0, 1159]
0728 16:43:19.572 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.mailReceiverWhiteList}}, 您发送的邮件附件已被解密，接收解密附件的邮箱地址为：\r\n{{emailAddr}}附件名称：{{attachment}}, zh_CN, 0, 0, 1160]
0728 16:43:19.575 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Number}}, 数字, zh_CN, 0, 0, 1161]
0728 16:43:19.577 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstABDUpload}}, ADB外传文件, zh_CN, 0, 0, 1162]
0728 16:43:19.579 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.developer}}, 开发商, zh_CN, 0, 0, 1163]
0728 16:43:19.582 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.outFilePath}}, 外传文件路径, zh_CN, 0, 0, 1164]
0728 16:43:19.585 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mobileWPSConfigStrategy}}, 移动终端WPS配置策略, zh_CN, 0, 0, 1165]
0728 16:43:19.588 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.deviceMark}}, 设备标志, zh_CN, 0, 0, 1166]
0728 16:43:19.591 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processCollectRule}}, 进程指纹收集规则, zh_CN, 0, 0, 1167]
0728 16:43:19.593 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Interval}}, 间隔, zh_CN, 0, 0, 1168]
0728 16:43:19.596 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.hardDiskDrive}}, 硬盘驱动器, zh_CN, 0, 0, 1169]
0728 16:43:19.599 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numABDUpload}}, ADB传输文件内容涉及违规，违规详情如下：文件大小或外传文件累计数量违规；文件路径：{{FilePath}}, zh_CN, 0, 0, 1946]
0728 16:43:19.603 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.systemPatch}}, 系统补丁, zh_CN, 0, 0, 1170]
0728 16:43:19.606 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.addressWidth}}, 地址宽度, zh_CN, 0, 0, 1171]
0728 16:43:19.609 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelMailFile}}, 发送的邮件附件涉及违规，违规详情如下：标签内容{{LabelName}}违规 或 “文档等级”高于指定级别{{LabelLevel}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 1172]
0728 16:43:19.611 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.cache}}, 高速缓存, zh_CN, 0, 0, 1173]
0728 16:43:19.614 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.usbPort}}, USB接口, zh_CN, 0, 0, 1174]
0728 16:43:19.618 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstMail}}, 邮件传输文本内容, zh_CN, 0, 0, 1175]
0728 16:43:19.621 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay16}}, 文件拷贝至移动盘, zh_CN, 0, 0, 1176]
0728 16:43:19.623 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.alarmTemplate}}, 告警消息模板, zh_CN, 0, 0, 1177]
0728 16:43:19.625 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstAISendFile}}, AI上传文件, zh_CN, 0, 0, 1959]
0728 16:43:19.627 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.serverCpuUsage}}, {{devType}}【{{devId}}】CPU使用率为{{value}}%,已超过设置的阈值{{threshold}}%, zh_CN, 0, 0, 1178]
0728 16:43:19.629 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.assetProp}}, 软硬件资产属性信息表, zh_CN, 0, 0, 1179]
0728 16:43:19.632 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay2}}, 打开文档, zh_CN, 0, 0, 1180]
0728 16:43:19.634 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.stgFileData}}, 策略关联文件数据, zh_CN, 0, 0, 1181]
0728 16:43:19.636 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.documentTrack}}, 文档追踪, zh_CN, 0, 0, 1182]
0728 16:43:19.638 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.display}}, 显示器, zh_CN, 0, 0, 1183]
0728 16:43:19.640 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.clipboardStrategy}}, 剪切板控制, zh_CN, 0, 0, 1184]
0728 16:43:19.642 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.violationResponseRule}}, 违规响应规则, zh_CN, 0, 0, 1185]
0728 16:43:19.644 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstWeb}}, 浏览的网页信息涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}, zh_CN, 0, 0, 1186]
0728 16:43:19.648 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.unconventionalSensitive}}, 全盘扫描内容检测策略, zh_CN, 0, 0, 1187]
0728 16:43:19.650 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mobileTerminalUpgradeStrategy}}, 移动终端升级, zh_CN, 0, 0, 1188]
0728 16:43:19.652 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.localDNSHostName}}, 本地DNS主机名, zh_CN, 0, 0, 1189]
0728 16:43:19.654 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.baseFunction}}, 终端基础功能模块, zh_CN, 0, 0, 1190]
0728 16:43:19.656 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstWebFile}}, 网页上传文件, zh_CN, 0, 0, 1191]
0728 16:43:19.658 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.officeWaterMark}}, Office文档水印设置, zh_CN, 0, 0, 1192]
0728 16:43:19.660 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.blindWatermark}}, 图片盲水印, zh_CN, 0, 0, 1193]
0728 16:43:19.663 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstWebPaste}}, 粘贴到网页的内容涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}, zh_CN, 0, 0, 1194]
0728 16:43:19.666 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.DiskName}}, 磁盘名称, zh_CN, 0, 0, 1195]
0728 16:43:19.668 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.VersionRange}}, 版本范围, zh_CN, 0, 0, 1196]
0728 16:43:19.671 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.outgoingTemplate}}, 直接外发模板库, zh_CN, 0, 0, 1197]
0728 16:43:19.673 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.defaultGatewayIPv4Address}}, 默认网关IPv4地址, zh_CN, 0, 0, 1198]
0728 16:43:19.675 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Add}}, 添加, zh_CN, 0, 0, 1199]
0728 16:43:19.678 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.webBrowseURLFilter}}, 网页浏览网址过滤策略, zh_CN, 0, 0, 1200]
0728 16:43:19.681 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.diskPartition}}, 磁盘分区, zh_CN, 0, 0, 1201]
0728 16:43:19.683 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.mailSenderWhiteList}}, 邮件白名单违规告警, zh_CN, 0, 0, 1202]
0728 16:43:19.686 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.appVerLimit}}, {{FilePath}},不支持产品版本为<{{AppVersion}}>的应用程序。\r\n限制以下版本：{{VersionRange}}, zh_CN, 0, 0, 1203]
0728 16:43:19.688 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processStrategy}}, 透明加密, zh_CN, 0, 0, 1204]
0728 16:43:19.691 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.lanSegment}}, 局域网网段设置基础数据, zh_CN, 0, 0, 1205]
0728 16:43:19.693 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.UpFlow}}, 上传流量, zh_CN, 0, 0, 1947]
0728 16:43:19.697 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstCpuUsed}}, 您的cpu使用率为{{Function}}%已超过设置的阈值{{number}}%, zh_CN, 0, 0, 1206]
0728 16:43:19.699 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.appOpenSuffixStrategy}}, 移动终端文档阅读设置, zh_CN, 0, 0, 1207]
0728 16:43:19.702 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.softBlack}}, 您打开一个违规程序{{FilePath}}, zh_CN, 0, 0, 1208]
0728 16:43:19.704 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.memoryUsage}}, 内存使用率, zh_CN, 0, 0, 1209]
0728 16:43:19.706 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numUsbCopy}}, USB复制文件, zh_CN, 0, 0, 1948]
0728 16:43:19.709 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.usbInterface}}, 您的USB接口{{InterfaceKey}}违规接入了{{InterfaceDeviceType}}设备{{InterfaceDeviceKey}}, zh_CN, 0, 0, 1210]
0728 16:43:19.711 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstAISendMsg}}, 发送的AI文本内容涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}, zh_CN, 0, 0, 1960]
0728 16:43:19.713 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.BIOS}}, BIOS, zh_CN, 0, 0, 1211]
0728 16:43:19.715 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstAISendFile}}, 发送到AI的文件涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 1961]
0728 16:43:19.717 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.osProcess}}, 内置系统进程, zh_CN, 0, 0, 1212]
0728 16:43:19.719 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.AIModel}}, AI模型限制, zh_CN, 0, 0, 2067]
0728 16:43:19.721 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.ProcessName}}, 进程名称, zh_CN, 0, 0, 1213]
0728 16:43:19.723 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstPost}}, 发帖内容涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}, zh_CN, 0, 0, 1214]
0728 16:43:19.725 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.DeviceName}}, 设备名称, zh_CN, 0, 0, 1215]
0728 16:43:19.728 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.burnCD}}, 您违规使用光驱刻录机：{{DeviceInfo}}，刻录文件：{{FilePath}}, zh_CN, 0, 0, 1216]
0728 16:43:19.730 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.wifiBlock}}, WiFi连接限制, zh_CN, 0, 0, 1217]
0728 16:43:19.732 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.denseSet}}, 密级图标及参数设置, zh_CN, 0, 0, 1218]
0728 16:43:19.734 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.netCtrlStrategy}}, 网络隔离设置, zh_CN, 0, 0, 1219]
0728 16:43:19.736 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.install}}, 软件安装/卸载限制, zh_CN, 0, 0, 1220]
0728 16:43:19.738 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.commonConfig}}, 安装包限制基础数据表——常用配置表, zh_CN, 0, 0, 1221]
0728 16:43:19.740 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.alarmBaseStrategy}}, 基础策略——告警配置表, zh_CN, 0, 0, 1222]
0728 16:43:19.744 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numUsbCut}}, USB剪切文件, zh_CN, 0, 0, 1949]
0728 16:43:19.746 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstRemoteDesktop}}, 远程桌面外传文件, zh_CN, 0, 0, 1223]
0728 16:43:19.748 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelUsbCut}}, USB剪切文件, zh_CN, 0, 0, 1224]
0728 16:43:19.751 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.attachment}}, 附件, zh_CN, 0, 0, 1225]
0728 16:43:19.753 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.MTP}}, 违规通过MTP外传文件，文件路径：{{FilePath}}, zh_CN, 0, 0, 1226]
0728 16:43:19.755 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.softBlack}}, 高危软件限制, zh_CN, 0, 0, 1227]
0728 16:43:19.758 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.outgoingCodeWhiteList}}, 外发机器码白名单, zh_CN, 0, 0, 1228]
0728 16:43:19.761 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.serverAnyDiskUsage}}, 服务器系统磁盘空间不足告警, zh_CN, 0, 0, 1229]
0728 16:43:19.763 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.hardDriveTimes}}, 您的硬盘{{DiskName}}已使用{{Count}}次，已超过设置的阈值({{Number}})次, zh_CN, 0, 0, 1230]
0728 16:43:19.766 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.user}}, 操作员信息, zh_CN, 0, 0, 1231]
0728 16:43:19.768 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstBluetooth}}, 蓝牙传输的文件涉及违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}；文件路径：{{FilePath}}, zh_CN, 0, 0, 1232]
0728 16:43:19.769 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mtpConfig}}, MTP管控, zh_CN, 0, 0, 1233]
0728 16:43:19.771 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.smartEncStrategy}}, 智能加密, zh_CN, 0, 0, 1234]
0728 16:43:19.775 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.productModel}}, 产品型号, zh_CN, 0, 0, 1235]
0728 16:43:19.777 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.offlineLockScreen}}, 离线锁屏, zh_CN, 0, 0, 1236]
0728 16:43:19.779 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.dataDisclosureStrategy}}, 敏感内容策略, zh_CN, 0, 0, 1237]
0728 16:43:19.781 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numImFile}}, 发送的即时通信文件涉及违规，违规详情如下：文件大小或外传文件累计数量违规；文件路径：{{FilePath}}, zh_CN, 0, 0, 1950]
0728 16:43:19.783 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.diskScanSelfCheckSensitive}}, 敏感文件自检内容检测策略, zh_CN, 0, 0, 1238]
0728 16:43:19.786 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.offline}}, 离线锁屏告警, zh_CN, 0, 0, 1239]
0728 16:43:19.787 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstDrip}}, 涉及零星检测违规，违规详情如下：策略名称：{{strategyName}}；规则名称：{{RuleName}}, zh_CN, 0, 0, 1240]
0728 16:43:19.790 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.shortOfflineStrategy}}, 短期离线策略设置, zh_CN, 0, 0, 1241]
0728 16:43:19.792 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.httpWhiteListProcessFilter}}, 服务器白名单——进程过滤, zh_CN, 0, 0, 1242]
0728 16:43:19.794 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstComputerNameChanged}}, 计算机名已修改，原名称： {{oldName}}  现名称： {{newName}}, zh_CN, 0, 0, 1243]
0728 16:43:19.797 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.softInstalled}}, 必须安装软件, zh_CN, 0, 0, 1244]
0728 16:43:19.799 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.manualLabelStrategy}}, 手动加标签, zh_CN, 0, 0, 1245]
0728 16:43:19.801 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.fileDistribute}}, 文件分发任务, zh_CN, 0, 0, 1246]
0728 16:43:19.816 - WARN : #sql: UPDATE property  SET code=?, value=?, type=?, rule=?,  editable=?, parent_id=?, modify_time=?     WHERE (code = ?)   #parameter: [i18n.zh_CN.version, 20250720001100, string, , false, 0, Mon Jul 28 16:43:19 CST 2025, i18n.zh_CN.version]
0728 16:43:19.846 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstBluetooth}}, 藍牙外傳檔案, zh_TW, 0, 0, 1247]
0728 16:43:19.849 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay512}}, 網盤上傳檔案, zh_TW, 0, 0, 1248]
0728 16:43:19.851 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.serverMemoryUsage}}, 服務器內存佔用告警, zh_TW, 0, 0, 1249]
0728 16:43:19.853 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.patchInstallationStrategy}}, 補丁策略-安裝策略, zh_TW, 0, 0, 1250]
0728 16:43:19.855 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.DeviceInfo}}, 裝置資訊, zh_TW, 0, 0, 1251]
0728 16:43:19.857 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.iconRefreshDir}}, 加密圖示輔助重新整理設定, zh_TW, 0, 0, 1252]
0728 16:43:19.859 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.autoLabelingStrategy}}, 標籤基礎庫, zh_TW, 0, 0, 1253]
0728 16:43:19.861 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.signName}}, 徵兆名稱, zh_TW, 0, 0, 1254]
0728 16:43:19.864 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numFTP}}, FTP檔案上傳, zh_TW, 0, 0, 2015]
0728 16:43:19.867 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.magneticDiskAlarmConfig}}, 磁碟總體佔用, zh_TW, 0, 0, 1255]
0728 16:43:19.870 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.hardDriveTime}}, 您的硬碟{{DiskName}}已使用{{Hour}}小時，已超過設定的閾值({{Number}})小時, zh_TW, 0, 0, 1256]
0728 16:43:19.872 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.systemSlot}}, 系統插槽, zh_TW, 0, 0, 1257]
0728 16:43:19.875 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.specifications}}, 規格, zh_TW, 0, 0, 1258]
0728 16:43:19.878 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay4}}, 解密檔案, zh_TW, 0, 0, 1259]
0728 16:43:19.880 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mstscControl}}, 遠端桌面管控, zh_TW, 0, 0, 1260]
0728 16:43:19.883 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.socketIdentifier}}, Socket標誌, zh_TW, 0, 0, 1261]
0728 16:43:19.886 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.specialProcess}}, 預設特殊程式, zh_TW, 0, 0, 1262]
0728 16:43:19.888 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.DownFlow}}, 下載流量, zh_TW, 0, 0, 1967]
0728 16:43:19.891 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Count}}, 次數, zh_TW, 0, 0, 1263]
0728 16:43:19.893 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.outgoingTemplateStrategy}}, 直接外發模板設定, zh_TW, 0, 0, 1264]
0728 16:43:19.896 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.maximumClockFrequency}}, 最大時鐘頻率, zh_TW, 0, 0, 1265]
0728 16:43:19.899 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.webpagePasteAudit}}, 網頁粘貼網址過濾, zh_TW, 0, 0, 1266]
0728 16:43:19.903 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstBurnDownload}}, 光碟下載檔案涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1267]
0728 16:43:19.905 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.specialSuffixStrategy}}, 特殊檔案後綴, zh_TW, 0, 0, 1268]
0728 16:43:19.907 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.imFileStrategy}}, 通訊工具管控設定, zh_TW, 0, 0, 1269]
0728 16:43:19.909 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.level.level3}}, 重要, zh_TW, 0, 0, 1270]
0728 16:43:19.911 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.emailAttachFileStrategy}}, 郵件附件限制, zh_TW, 0, 0, 1271]
0728 16:43:19.913 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{label.grade.3}}, 核心數據, zh_TW, 0, 0, 1272]
0728 16:43:19.916 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processCollectRuleStrategy}}, 程式收集, zh_TW, 0, 0, 1273]
0728 16:43:19.918 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.OffLineNum}}, 離線數, zh_TW, 0, 0, 1274]
0728 16:43:19.920 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.horizontalResolution}}, 水平解析度, zh_TW, 0, 0, 1275]
0728 16:43:19.923 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Tip}}, 提示, zh_TW, 0, 0, 1276]
0728 16:43:19.925 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.scanTask}}, 敏感內容掃描任務, zh_TW, 0, 0, 1277]
0728 16:43:19.928 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.terminalMenuStrategy}}, 終端選單管理, zh_TW, 0, 0, 1278]
0728 16:43:19.932 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mailSenderStrategy}}, 郵件發件人白名單, zh_TW, 0, 0, 1279]
0728 16:43:19.934 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.LabelName}}, 標籤內容, zh_TW, 0, 0, 1280]
0728 16:43:19.936 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstNetDiskDownload}}, 網盤下載檔案, zh_TW, 0, 0, 1281]
0728 16:43:19.938 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.videoStrategy}}, 螢幕錄影設定, zh_TW, 0, 0, 1282]
0728 16:43:19.940 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstImFile}}, 即時通訊傳送檔案, zh_TW, 0, 0, 1283]
0728 16:43:19.943 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.applicationSoftware}}, 應用軟體, zh_TW, 0, 0, 1284]
0728 16:43:19.945 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.hotkey}}, 熱鍵設定, zh_TW, 0, 0, 1285]
0728 16:43:19.949 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.httpWhiteListStrategy}}, 伺服器白名單設定, zh_TW, 0, 0, 1286]
0728 16:43:19.951 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.networkAdapter}}, 網路介面卡, zh_TW, 0, 0, 1287]
0728 16:43:19.953 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numNetDiskUpload}}, 網盤上傳檔案, zh_TW, 0, 0, 2016]
0728 16:43:19.955 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.timeDimension}}, 時間維度, zh_TW, 0, 0, 1288]
0728 16:43:19.957 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.blueToothConfig}}, 藍牙檔案管控, zh_TW, 0, 0, 1289]
0728 16:43:19.959 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstSaveAs}}, USB另存為檔案, zh_TW, 0, 0, 1290]
0728 16:43:19.961 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.offline}}, 您的終端與伺服器斷開{{OffLineNum}}分鐘，處於離線狀態，{{Interval}}分鐘後將會鎖屏，請盡快與伺服器連接，或與管理員聯系, zh_TW, 0, 0, 1291]
0728 16:43:19.965 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.emailAttachFile}}, 您正在違規通過郵件外傳附件 \r\n 外傳附件名：{{alarmData}} 收件人：{{mailReceiver}} 發件人：{{mailSender}}, zh_TW, 0, 0, 1292]
0728 16:43:19.967 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstWebDownloadFile}}, 從網頁下載到本地的檔案涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1293]
0728 16:43:19.969 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.serverLibrary}}, 伺服器資訊庫, zh_TW, 0, 0, 1294]
0728 16:43:19.971 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.CMDData}}, 命令行數據, zh_TW, 0, 0, 1295]
0728 16:43:19.973 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.dataWidth}}, 資料寬度, zh_TW, 0, 0, 1296]
0728 16:43:19.976 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Address}}, 地址, zh_TW, 0, 0, 1297]
0728 16:43:19.979 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.normalCount}}, 一般次數, zh_TW, 0, 0, 1298]
0728 16:43:19.982 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstLocShare}}, 本地共用檔案, zh_TW, 0, 0, 1299]
0728 16:43:19.985 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.deviceName}}, 裝置名稱, zh_TW, 0, 0, 1300]
0728 16:43:19.988 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.level.level4}}, 緊急, zh_TW, 0, 0, 1301]
0728 16:43:19.991 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.print}}, 您觸發違規列印事件，列印檔案路徑：{{FilePath}},列印程式名:{{ProcessName}}, zh_TW, 0, 0, 1302]
0728 16:43:19.994 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstIm}}, 即時通信消息涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；, zh_TW, 0, 0, 1303]
0728 16:43:19.997 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstAISendMsg}}, AI傳輸文字內容, zh_TW, 0, 0, 2017]
0728 16:43:19.999 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.hardDriveTimes}}, 硬碟使用次數, zh_TW, 0, 0, 1304]
0728 16:43:20.001 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{label.grade.2}}, 重要數據, zh_TW, 0, 0, 1305]
0728 16:43:20.003 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numWebFile}}, 傳送到網頁的檔案涉及違規，違規詳情如下：檔案大小或外傳檔案累計數量違規；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 2018]
0728 16:43:20.005 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.serverBackupConfigStg}}, 伺服器儲存配置, zh_TW, 0, 0, 1306]
0728 16:43:20.007 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.wifiBlock}}, 您違規連接WiFi：{{wifiSsid}}，MAC位址：{{wifiBssid}}, zh_TW, 0, 0, 1307]
0728 16:43:20.010 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.terminalConfig}}, 終端設定, zh_TW, 0, 0, 1308]
0728 16:43:20.012 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.companyName}}, 公司名稱, zh_TW, 0, 0, 1309]
0728 16:43:20.014 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.enterpriseMailConfig}}, 郵件白名單設定——SSL企業郵箱設定, zh_TW, 0, 0, 1310]
0728 16:43:20.016 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.bytesPerSector}}, 每扇區字節數, zh_TW, 0, 0, 1311]
0728 16:43:20.018 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numNetDiskUpload}}, 您的網盤上傳檔案內容涉及違規，違規詳情如下：檔案大小或外傳檔案累計數量違規；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 2019]
0728 16:43:20.020 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.diskUsage}}, 磁碟餘量, zh_TW, 0, 0, 1312]
0728 16:43:20.022 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.USBSendOutFile}}, USB檔案外傳告警, zh_TW, 0, 0, 1313]
0728 16:43:20.025 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.deviceID}}, 裝置ID, zh_TW, 0, 0, 1314]
0728 16:43:20.027 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.adbFileSuffixLibrary}}, ADB檔案後綴庫, zh_TW, 0, 0, 1315]
0728 16:43:20.030 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.lastStartupTime}}, 最後啟動時間, zh_TW, 0, 0, 1316]
0728 16:43:20.032 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.newName}}, 新名稱, zh_TW, 0, 0, 1317]
0728 16:43:20.034 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstMail}}, 傳送的郵件內容涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}, zh_TW, 0, 0, 1318]
0728 16:43:20.037 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.softAsset}}, 軟體資產變更, zh_TW, 0, 0, 1319]
0728 16:43:20.039 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstNetDiskUpload}}, 您的網盤上傳檔案內容涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1320]
0728 16:43:20.041 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.cpuAlarmConfig}}, CPU佔用, zh_TW, 0, 0, 1321]
0728 16:43:20.044 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.ipAndMacBind}}, IP/MAC繫結, zh_TW, 0, 0, 1322]
0728 16:43:20.046 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.diskScan}}, 全碟掃描, zh_TW, 0, 0, 1323]
0728 16:43:20.048 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.descript}}, 描述, zh_TW, 0, 0, 1324]
0728 16:43:20.050 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numWebFile}}, 網頁上傳檔案, zh_TW, 0, 0, 2020]
0728 16:43:20.052 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelMailFile}}, 郵件附件內容, zh_TW, 0, 0, 1890]
0728 16:43:20.054 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.webFlowStrategy}}, 流量限制, zh_TW, 0, 0, 1325]
0728 16:43:20.056 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.browserFileDownloadStrategy}}, 網頁下載檔案管控, zh_TW, 0, 0, 1326]
0728 16:43:20.059 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelBurn}}, 光碟燒錄的檔案涉及違規，違規詳情如下：標籤內容{{LabelName}}違規 或 「檔案等級」高於指定級別{{LabelLevel}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1891]
0728 16:43:20.062 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstMailFile}}, 郵件附件內容, zh_TW, 0, 0, 1327]
0728 16:43:20.065 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.otherDevice}}, 其它裝置, zh_TW, 0, 0, 1328]
0728 16:43:20.067 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processFileMd5}}, 受控程式策略庫——程式防偽冒策略, zh_TW, 0, 0, 1329]
0728 16:43:20.069 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.slot}}, 插槽, zh_TW, 0, 0, 1330]
0728 16:43:20.071 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.workMode}}, 工作模式切換中，禁止執行程式：{{ProcessName}}, zh_TW, 0, 0, 1331]
0728 16:43:20.074 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.decToolLimitStg}}, 高危軟體限制策略, zh_TW, 0, 0, 1332]
0728 16:43:20.076 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.totalCylinders}}, 總柱面數, zh_TW, 0, 0, 1333]
0728 16:43:20.078 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mailWhiteListStgConfig}}, 郵件白名單設定——系統設定, zh_TW, 0, 0, 1334]
0728 16:43:20.080 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.sensitiveFileOutSendConfig}}, 敏感檔案外傳引數設定, zh_TW, 0, 0, 1335]
0728 16:43:20.082 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processStgChild}}, 受控程式策略庫——子程式策略, zh_TW, 0, 0, 1336]
0728 16:43:20.084 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.AISendFile}}, AI上傳檔案管控, zh_TW, 0, 0, 2021]
0728 16:43:20.086 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numRemoteDesktop}}, 遠端桌面傳送的檔案涉及違規，違規詳情如下：檔案大小或外傳檔案累計數量違規；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 2022]
0728 16:43:20.089 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstAISendFile}}, AI上傳檔案, zh_TW, 0, 0, 2023]
0728 16:43:20.091 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.netInterface}}, 您的網絡接口{{InterfaceKey}}違規接入了{{InterfaceDeviceType}}設備{{InterfaceDeviceKey}}, zh_TW, 0, 0, 1337]
0728 16:43:20.093 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.defaultMsgParamSetup}}, 終端預設彈窗引數, zh_TW, 0, 0, 1338]
0728 16:43:20.096 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processLibrary}}, 程式資訊庫, zh_TW, 0, 0, 1339]
0728 16:43:20.098 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.label}}, 卷標, zh_TW, 0, 0, 1340]
0728 16:43:20.100 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.install}}, 您觸發{{Type}}事件，{{Type}}路徑：{{FilePath}}, zh_TW, 0, 0, 1341]
0728 16:43:20.102 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.netInterfaceLimitStrategy}}, 網絡接口管控, zh_TW, 0, 0, 1342]
0728 16:43:20.105 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.bluetooth}}, 藍牙檔案管控, zh_TW, 0, 0, 1343]
0728 16:43:20.107 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.seriesNumber}}, 系列號, zh_TW, 0, 0, 1344]
0728 16:43:20.109 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.patchAutoInstallStrategy}}, 補丁策略-自動安裝策略, zh_TW, 0, 0, 1345]
0728 16:43:20.111 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.hardware}}, 硬體, zh_TW, 0, 0, 1346]
0728 16:43:20.113 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.type}}, 型別, zh_TW, 0, 0, 1347]
0728 16:43:20.115 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.period}}, 周期, zh_TW, 0, 0, 1348]
0728 16:43:20.118 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.assetAlarmSetup}}, 資產變更報警設定, zh_TW, 0, 0, 1349]
0728 16:43:20.120 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.RemotePort}}, 遠端連接埠, zh_TW, 0, 0, 1350]
0728 16:43:20.124 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstBluetooth}}, 藍牙外傳檔案, zh_TW, 0, 0, 1351]
0728 16:43:20.126 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstIm}}, 即時通訊文字內容, zh_TW, 0, 0, 1352]
0728 16:43:20.129 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.serverProgramDiskUsage}}, {{devType}}【{{devId}}】磁碟機代號（{{disk}}）剩餘可用空間為{{value}},低於設定的閾值{{threshold}}GB, zh_TW, 0, 0, 1353]
0728 16:43:20.131 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.operatorConfig}}, 加密引數設定, zh_TW, 0, 0, 1354]
0728 16:43:20.133 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.tracksPerColumn}}, 每柱磁軌數, zh_TW, 0, 0, 1355]
0728 16:43:20.136 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.diskType}}, 磁碟型別, zh_TW, 0, 0, 1356]
0728 16:43:20.139 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.print}}, 列印許可權控制, zh_TW, 0, 0, 1357]
0728 16:43:20.142 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.AppVersion}}, APP版本, zh_TW, 0, 0, 1358]
0728 16:43:20.144 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numMailFile}}, 傳送的郵件附件涉及違規，違規詳情如下：檔案大小或外傳檔案累計數量違規；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 2024]
0728 16:43:20.146 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.usbDevice}}, USB裝置庫, zh_TW, 0, 0, 1359]
0728 16:43:20.148 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.appVerLimit}}, 程式版本限制, zh_TW, 0, 0, 1360]
0728 16:43:20.150 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstPrint}}, 檔案列印内容, zh_TW, 0, 0, 1361]
0728 16:43:20.152 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.servicePatch}}, 服務補丁, zh_TW, 0, 0, 1362]
0728 16:43:20.154 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.timeInfo}}, 時間段基礎資料, zh_TW, 0, 0, 1363]
0728 16:43:20.156 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.httpWhiteList}}, 服務器白名單, zh_TW, 0, 0, 1364]
0728 16:43:20.158 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.signatureData}}, 特征碼庫, zh_TW, 0, 0, 1365]
0728 16:43:20.160 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.assetDB}}, 軟硬體資產採集項資訊, zh_TW, 0, 0, 1366]
0728 16:43:20.162 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.translucentEncStrategy}}, 半透明加密, zh_TW, 0, 0, 1367]
0728 16:43:20.165 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.currentClockFrequency}}, 當前時鐘頻率, zh_TW, 0, 0, 1368]
0728 16:43:20.168 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.forumURLFilter}}, 論壇發文網址過濾, zh_TW, 0, 0, 1369]
0728 16:43:20.170 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelRemoteDesktop}}, 遠端桌面傳送的檔案涉及違規，違規詳情如下：標籤內容{{LabelName}}違規 或 「檔案等級」高於指定級別{{LabelLevel}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1892]
0728 16:43:20.172 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.dimension}}, 尺寸, zh_TW, 0, 0, 1370]
0728 16:43:20.174 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.keyboardName}}, 鍵盤名稱, zh_TW, 0, 0, 1371]
0728 16:43:20.176 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.keyboard}}, 鍵盤, zh_TW, 0, 0, 1372]
0728 16:43:20.178 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.appReportDefault}}, 應用程式執行時間預設表, zh_TW, 0, 0, 1373]
0728 16:43:20.180 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.mstscAddress}}, 遠端連線地址, zh_TW, 0, 0, 1374]
0728 16:43:20.182 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.date}}, 日期, zh_TW, 0, 0, 1375]
0728 16:43:20.184 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstCloud}}, 雲傳輸, zh_TW, 0, 0, 1376]
0728 16:43:20.186 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processInjectDefault}}, 受控註入預設設定, zh_TW, 0, 0, 1377]
0728 16:43:20.188 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.label}}, 當前外傳檔案：{{FilePath}}涉及違規, zh_TW, 0, 0, 1378]
0728 16:43:20.190 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.softLimit}}, 您開啟一個違規軟體{{FilePath}}{{descript}}, zh_TW, 0, 0, 1379]
0728 16:43:20.192 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.frequency}}, 頻率, zh_TW, 0, 0, 1380]
0728 16:43:20.194 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.LogType}}, 日誌型別, zh_TW, 0, 0, 1381]
0728 16:43:20.197 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.emailAddr}}, 郵箱位址, zh_TW, 0, 0, 1382]
0728 16:43:20.200 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.labelPermissionControlStrategy}}, 帶標籤檔案外傳管控, zh_TW, 0, 0, 1383]
0728 16:43:20.201 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.groupInfo}}, 部門資訊, zh_TW, 0, 0, 1384]
0728 16:43:20.203 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.IDeviceKey}}, 設備, zh_TW, 0, 0, 1385]
0728 16:43:20.206 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.computer}}, 電腦, zh_TW, 0, 0, 1386]
0728 16:43:20.207 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.computerEnergySaving}}, 電腦節能設定, zh_TW, 0, 0, 1387]
0728 16:43:20.209 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.driveLetter}}, 盤符, zh_TW, 0, 0, 1388]
0728 16:43:20.211 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.violationRespRuleExtConfig}}, 違規響應規則設定擴展表, zh_TW, 0, 0, 1389]
0728 16:43:20.214 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstFtp}}, FTP上傳檔案, zh_TW, 0, 0, 1390]
0728 16:43:20.216 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.shareConfig}}, 共用檔案管控, zh_TW, 0, 0, 1391]
0728 16:43:20.218 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Hour}}, 小時, zh_TW, 0, 0, 1392]
0728 16:43:20.220 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.mailReceiverWhiteList}}, 郵件白名單違規告警, zh_TW, 0, 0, 1393]
0728 16:43:20.222 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstPrint}}, 列印檔案的內容涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1394]
0728 16:43:20.224 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstBurnDownload}}, 光碟下載檔案, zh_TW, 0, 0, 1395]
0728 16:43:20.226 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.diskScanSelfCheck}}, 敏感檔案自檢策略, zh_TW, 0, 0, 1396]
0728 16:43:20.229 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.summary}}, 摘要, zh_TW, 0, 0, 1397]
0728 16:43:20.231 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.serverMemoryUsage}}, {{devType}}【{{devId}}】內存使用率為{{value}}%,已超過設置的閾值{{threshold}}%, zh_TW, 0, 0, 1398]
0728 16:43:20.233 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.appInfoMd5}}, 受控程式策略庫——應用程式限制防偽冒指紋表, zh_TW, 0, 0, 1399]
0728 16:43:20.235 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.effectiveFuncConfig}}, 敏感檢測進階設定, zh_TW, 0, 0, 1400]
0728 16:43:20.237 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{enc.denseInfo.secret}}, 秘密檔案, zh_TW, 0, 0, 1401]
0728 16:43:20.239 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.defaultGatewayIPv6Address}}, 預設閘道IPv6位址, zh_TW, 0, 0, 1402]
0728 16:43:20.242 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.softRunning}}, 必須執行軟體{{FilePath}}, zh_TW, 0, 0, 1403]
0728 16:43:20.243 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.dataDisclosureStg}}, 內容檢測策略, zh_TW, 0, 0, 1404]
0728 16:43:20.247 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.sysUserPermission}}, 管理員權限, zh_TW, 0, 0, 1405]
0728 16:43:20.249 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstCopy}}, USB複製檔案, zh_TW, 0, 0, 1406]
0728 16:43:20.251 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstPrint}}, 檔案列印內容, zh_TW, 0, 0, 1407]
0728 16:43:20.253 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.custom}}, 自定義, zh_TW, 0, 0, 1408]
0728 16:43:20.255 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processStgLibrary}}, 受控程式策略庫, zh_TW, 0, 0, 1409]
0728 16:43:20.257 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{enc.denseInfo.public}}, 公開密級, zh_TW, 0, 0, 1410]
0728 16:43:20.259 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.software}}, 軟體, zh_TW, 0, 0, 1411]
0728 16:43:20.262 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processConfigStg}}, 透明加密進階設定, zh_TW, 0, 0, 1412]
0728 16:43:20.264 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.netIsolation}}, 網路隔離設定, zh_TW, 0, 0, 1413]
0728 16:43:20.266 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numBurn}}, 光碟燒錄的檔案涉及違規，違規詳情如下：檔案大小或外傳檔案累計數量違規；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 2025]
0728 16:43:20.268 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.title}}, 標題, zh_TW, 0, 0, 1414]
0728 16:43:20.270 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.UPSPort}}, UPS連接埠, zh_TW, 0, 0, 1415]
0728 16:43:20.272 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstABDUpload}}, ADB外傳檔案, zh_TW, 0, 0, 1416]
0728 16:43:20.274 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.hardDriveTime}}, 硬碟使用時間, zh_TW, 0, 0, 1417]
0728 16:43:20.276 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.mode}}, 模式, zh_TW, 0, 0, 1418]
0728 16:43:20.280 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.emailRecordLimit}}, 郵件記錄限制, zh_TW, 0, 0, 1419]
0728 16:43:20.282 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Optype}}, 操作型別, zh_TW, 0, 0, 1420]
0728 16:43:20.284 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelBluetooth}}, 藍牙傳輸的檔案涉及違規，違規詳情如下：標籤內容{{LabelName}}違規 或 「檔案等級」高於指定級別{{LabelLevel}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1893]
0728 16:43:20.286 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.appBlockStrategy}}, 程式瀏覽限制, zh_TW, 0, 0, 1421]
0728 16:43:20.289 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{enc.denseInfo.topSecret}}, 絕密檔案, zh_TW, 0, 0, 1422]
0728 16:43:20.291 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.importantCount}}, 重要次數, zh_TW, 0, 0, 1423]
0728 16:43:20.294 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.denseInfo}}, 密級基礎設定, zh_TW, 0, 0, 1424]
0728 16:43:20.296 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.screenWaterMarkStrategy}}, 螢幕浮水印設定, zh_TW, 0, 0, 1425]
0728 16:43:20.299 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.applySecurityAccess}}, 應用安全接入伺服器, zh_TW, 0, 0, 1426]
0728 16:43:20.301 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstWebDownloadFile}}, 網頁下載檔案, zh_TW, 0, 0, 1427]
0728 16:43:20.303 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay8}}, 手動添加水印, zh_TW, 0, 0, 1428]
0728 16:43:20.305 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.softwareDownloadStrategy}}, 軟體上架, zh_TW, 0, 0, 1429]
0728 16:43:20.307 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstNetDiskUpload}}, 網盤上傳檔案, zh_TW, 0, 0, 1430]
0728 16:43:20.312 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstCopy}}, USB複製檔案, zh_TW, 0, 0, 1431]
0728 16:43:20.314 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.store}}, 違規使用了：{{DeviceName}}，設備編碼：{{usbcode}}，品牌：{{vendor}}，容量：{{capacity}}, zh_TW, 0, 0, 1432]
0728 16:43:20.316 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.asset}}, {{AssetType}}資產資訊{{LogType}}，詳情如下:{{DetailInfo}}, zh_TW, 0, 0, 1433]
0728 16:43:20.318 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstMailFile}}, 傳送的郵件附件涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1434]
0728 16:43:20.320 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstMTP}}, MTP外傳檔案, zh_TW, 0, 0, 1435]
0728 16:43:20.322 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.fileSystem}}, 檔案系統, zh_TW, 0, 0, 1436]
0728 16:43:20.325 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.softRequiredInstallStrategy}}, 必須安裝軟體, zh_TW, 0, 0, 1437]
0728 16:43:20.328 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay128}}, 郵件外傳檔案, zh_TW, 0, 0, 1438]
0728 16:43:20.331 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstBurnDownload}}, 光碟下載檔案, zh_TW, 0, 0, 1439]
0728 16:43:20.333 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mailTable}}, 預設郵箱關鍵字, zh_TW, 0, 0, 1440]
0728 16:43:20.335 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.SoftwareName}}, 軟體名稱, zh_TW, 0, 0, 2026]
0728 16:43:20.337 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelBluetooth}}, 藍牙外傳檔案, zh_TW, 0, 0, 1894]
0728 16:43:20.339 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.patchCheckStrategy}}, 補丁策略-引數設定, zh_TW, 0, 0, 1441]
0728 16:43:20.342 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.deviceInfo}}, 其他裝置限制基礎資料, zh_TW, 0, 0, 1442]
0728 16:43:20.344 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.softwareAssetAlarmSetup}}, 軟體資產變更報警設定, zh_TW, 0, 0, 1443]
0728 16:43:20.346 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstCut}}, 剪下到移動儲存裝置的內容涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1444]
0728 16:43:20.349 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.totalSectors}}, 總扇區數, zh_TW, 0, 0, 1445]
0728 16:43:20.351 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sysDiskAlarmConfig}}, 系統碟佔用, zh_TW, 0, 0, 1446]
0728 16:43:20.352 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.personalizePolicy}}, 電腦個人化, zh_TW, 0, 0, 1447]
0728 16:43:20.354 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.exceptStrategy}}, 敏感內容檢測設定-例外設定, zh_TW, 0, 0, 1448]
0728 16:43:20.358 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processStgConfig}}, 受控程式策略庫——進階設定, zh_TW, 0, 0, 1449]
0728 16:43:20.362 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.globalConfig}}, 全局設定下發表, zh_TW, 0, 0, 1450]
0728 16:43:20.365 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstLocShare}}, 操作本地共用檔案涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1451]
0728 16:43:20.367 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.browserFileStrategy}}, 網頁上傳檔案管控, zh_TW, 0, 0, 1452]
0728 16:43:20.369 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.name}}, 名稱, zh_TW, 0, 0, 1453]
0728 16:43:20.374 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.diskBackupSpace}}, 備份磁碟空間告警, zh_TW, 0, 0, 1454]
0728 16:43:20.377 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstRemoteUploadTool}}, 遠端工具上傳的檔案涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 2027]
0728 16:43:20.380 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstPost}}, 論壇發文文字內容, zh_TW, 0, 0, 1455]
0728 16:43:20.383 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.softRequiredRunStrategy}}, 必須執行軟體, zh_TW, 0, 0, 1456]
0728 16:43:20.386 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstImDownload}}, 即時通訊下載檔案, zh_TW, 0, 0, 1457]
0728 16:43:20.388 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.enDeFileDetail}}, 預設加密檔案監視過濾, zh_TW, 0, 0, 1458]
0728 16:43:20.391 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstAISendMsg}}, AI傳輸文字內容, zh_TW, 0, 0, 2028]
0728 16:43:20.393 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.AISendFile}}, 您觸發違規AI檔案上傳事件，程序名:{{ProcessName}}，檔案路徑：{{FilePath}}, zh_TW, 0, 0, 2029]
0728 16:43:20.396 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstRemoteUploadTool}}, 遠端工具上傳檔案, zh_TW, 0, 0, 2030]
0728 16:43:20.398 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mailCarbonCopyStrategy}}, 郵件抄送, zh_TW, 0, 0, 1459]
0728 16:43:20.400 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.AIName}}, AI名稱, zh_TW, 0, 0, 2068]
0728 16:43:20.403 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.otherDeviceLimit}}, 裝置使用控制, zh_TW, 0, 0, 1460]
0728 16:43:20.405 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstWebFile}}, 傳送到網頁的檔案涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1461]
0728 16:43:20.407 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstMemoryUsed}}, 您的記憶體使用率為{{Function}}%已超過設定的閾值{{number}}%, zh_TW, 0, 0, 1462]
0728 16:43:20.409 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.memory}}, 記憶體, zh_TW, 0, 0, 1463]
0728 16:43:20.411 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.label}}, 標籤外傳權限控製, zh_TW, 0, 0, 1464]
0728 16:43:20.413 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numABDUpload}}, ADB上傳檔案, zh_TW, 0, 0, 2031]
0728 16:43:20.415 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstSmartEnc}}, 智能加密, zh_TW, 0, 0, 1465]
0728 16:43:20.417 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay64}}, 通訊工具外傳檔案, zh_TW, 0, 0, 1466]
0728 16:43:20.419 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.threshold}}, 閾值, zh_TW, 0, 0, 1467]
0728 16:43:20.421 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.diskPartition}}, 磁碟分區, zh_TW, 0, 0, 1468]
0728 16:43:20.423 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.termScan}}, 終端安全檢測, zh_TW, 0, 0, 1469]
0728 16:43:20.425 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numUsbCopy}}, 複製到移動儲存裝置的內容涉及違規，違規詳情如下：檔案大小或外傳檔案累計數量違規；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 2032]
0728 16:43:20.427 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mailLibrary}}, 郵箱資訊庫, zh_TW, 0, 0, 1470]
0728 16:43:20.430 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.ADBController}}, 您違規使用ADB除錯工具傳輸檔案：{{FileName}}, zh_TW, 0, 0, 1471]
0728 16:43:20.432 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.operatingSystem}}, 作業系統, zh_TW, 0, 0, 1472]
0728 16:43:20.435 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.mailReceiver}}, 郵箱接收人, zh_TW, 0, 0, 1473]
0728 16:43:20.438 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.softInstalled}}, 必須安裝軟體{{FilePath}}, zh_TW, 0, 0, 1474]
0728 16:43:20.440 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.appVer}}, {{FilePath}}，{{Type}}, zh_TW, 0, 0, 1475]
0728 16:43:20.442 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.usbInterfaceLimitStrategy}}, USB接口管控, zh_TW, 0, 0, 1476]
0728 16:43:20.444 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.EnDeFileScan}}, 加密檔案監視過濾, zh_TW, 0, 0, 1477]
0728 16:43:20.446 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.usbInterface}}, USB接口管控, zh_TW, 0, 0, 1478]
0728 16:43:20.447 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.softWareCharge}}, 軟體版權管控, zh_TW, 0, 0, 2033]
0728 16:43:20.450 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.level.level0}}, 通知, zh_TW, 0, 0, 1479]
0728 16:43:20.452 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.netIsolation}}, 違規瀏覽網路，網路位址:{{RemoteIP}}:{{RemotePort}}, zh_TW, 0, 0, 1480]
0728 16:43:20.454 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.macAlarmConfig}}, MAC位址變更, zh_TW, 0, 0, 1481]
0728 16:43:20.456 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.appReportSet}}, 程式執行時長統計設定, zh_TW, 0, 0, 1482]
0728 16:43:20.458 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.lastUsedTime}}, 最後使用時間, zh_TW, 0, 0, 1483]
0728 16:43:20.460 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.pKeyInfo}}, 企業密鑰, zh_TW, 0, 0, 1484]
0728 16:43:20.462 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.competitorInfo}}, 競品資訊, zh_TW, 0, 0, 1485]
0728 16:43:20.464 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.numberOfLogicalProcesses}}, 邏輯處理個數, zh_TW, 0, 0, 1486]
0728 16:43:20.468 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.softwareBlacklist}}, 軟件黑名單程式, zh_TW, 0, 0, 1487]
0728 16:43:20.470 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.otherDeviceLimit}}, 裝置使用控制, zh_TW, 0, 0, 1488]
0728 16:43:20.472 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.AssetType}}, 資產型別, zh_TW, 0, 0, 1489]
0728 16:43:20.474 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.level}}, 等級, zh_TW, 0, 0, 1490]
0728 16:43:20.476 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstScan}}, 全碟掃描, zh_TW, 0, 0, 1491]
0728 16:43:20.478 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.USBSendOutFile}}, 違規使用USB外傳檔案，檔案路徑：{{alarmData}}, zh_TW, 0, 0, 1492]
0728 16:43:20.480 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstNetDiskUpload}}, 網盤上傳檔案, zh_TW, 0, 0, 1493]
0728 16:43:20.482 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.serverProgramDiskUsage}}, 伺服器程式所在磁碟空間不足告警, zh_TW, 0, 0, 1494]
0728 16:43:20.484 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processMonitor}}, 程式動作監控, zh_TW, 0, 0, 1495]
0728 16:43:20.486 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstNetShare}}, 遠端共用檔案外傳, zh_TW, 0, 0, 1496]
0728 16:43:20.488 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.dripDataDisclosureStg}}, 零星檢測策略, zh_TW, 0, 0, 1497]
0728 16:43:20.491 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstTelnet}}, telnet傳輸, zh_TW, 0, 0, 1498]
0728 16:43:20.493 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.antivirusSoftware}}, 防毒軟體, zh_TW, 0, 0, 1499]
0728 16:43:20.495 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstWebPaste}}, 網頁貼上文字內容, zh_TW, 0, 0, 1500]
0728 16:43:20.498 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.appBlock}}, 程式瀏覽限制, zh_TW, 0, 0, 1501]
0728 16:43:20.501 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.strategyName}}, 策略名稱, zh_TW, 0, 0, 1502]
0728 16:43:20.503 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstImFile}}, 傳送的即時通信檔案涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1503]
0728 16:43:20.505 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numMailFile}}, 郵件附件內容, zh_TW, 0, 0, 2034]
0728 16:43:20.507 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.outgoingScreenWaterMark}}, 外發螢幕浮水印, zh_TW, 0, 0, 1504]
0728 16:43:20.508 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.USBName}}, USB名稱, zh_TW, 0, 0, 1505]
0728 16:43:20.510 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.installationDate}}, 安裝日期, zh_TW, 0, 0, 1506]
0728 16:43:20.512 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.iPv6Address}}, IPv6位址, zh_TW, 0, 0, 1507]
0728 16:43:20.514 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.IDeviceType}}, 設備類型, zh_TW, 0, 0, 1508]
0728 16:43:20.516 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.emailAttachFile}}, 郵件附件限制, zh_TW, 0, 0, 1509]
0728 16:43:20.519 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.level.level1}}, 警告, zh_TW, 0, 0, 1510]
0728 16:43:20.521 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.driverStrategy}}, 儲存裝置使用控制, zh_TW, 0, 0, 1511]
0728 16:43:20.522 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstCut}}, USB剪下檔案, zh_TW, 0, 0, 1512]
0728 16:43:20.524 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{label.grade.1}}, 一般數據, zh_TW, 0, 0, 1513]
0728 16:43:20.526 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.alarmData}}, 告警資料, zh_TW, 0, 0, 1514]
0728 16:43:20.530 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.emailContent}}, 郵件內容限制, zh_TW, 0, 0, 1515]
0728 16:43:20.532 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.oldName}}, 舊名稱, zh_TW, 0, 0, 1516]
0728 16:43:20.534 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.batchDecLimit}}, 您的解密檔案次數已達到每{{period}}允許的上限{{number}}個, zh_TW, 0, 0, 1517]
0728 16:43:20.536 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numFTP}}, FTP上傳檔案內容涉及違規，違規詳情如下：檔案大小或外傳檔案累計數量違規；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 2035]
0728 16:43:20.537 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.webUpload}}, 您觸發違規網頁檔案上傳事件，程式名:{{ProcessName}}，檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1518]
0728 16:43:20.540 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.telnetCommControl}}, Telnet通訊管控, zh_TW, 0, 0, 1519]
0728 16:43:20.543 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstBatchEnc}}, 批次加解密, zh_TW, 0, 0, 1520]
0728 16:43:20.546 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.logFilter}}, 電腦Windows日誌設定, zh_TW, 0, 0, 1521]
0728 16:43:20.551 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.hardAsset}}, 硬體資產變更, zh_TW, 0, 0, 1522]
0728 16:43:20.553 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.chatSendFile}}, 聊天檔案外傳管控, zh_TW, 0, 0, 1523]
0728 16:43:20.555 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.adbLimitStrategy}}, ADB管控, zh_TW, 0, 0, 1524]
0728 16:43:20.557 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstBurn}}, 光碟燒錄的檔案涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1525]
0728 16:43:20.560 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.remoteDesktop}}, 您違規使用遠端桌面連線工具，遠端連線地址：{{address}}，外傳檔案路徑：{{filePath}}, zh_TW, 0, 0, 1526]
0728 16:43:20.563 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.outgoingConfig}}, 外發進階設定, zh_TW, 0, 0, 1527]
0728 16:43:20.566 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstMTP}}, MTP外傳檔案, zh_TW, 0, 0, 1528]
0728 16:43:20.569 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.subnetMask}}, 子網路遮罩, zh_TW, 0, 0, 1529]
0728 16:43:20.572 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.AISendFileMsg}}, 您在使用AI互動時觸發了AI傳送內容限制策略，違規內容為：\r\n關鍵字：{{KeyWord}}\r\nAI名稱：{{AIName}}\r\n程序名：{{ProcessName}}, zh_TW, 0, 0, 2069]
0728 16:43:20.574 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.SCSIController}}, SCSI控制器, zh_TW, 0, 0, 1530]
0728 16:43:20.578 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.fileGateConfig}}, 舊伺服器白名單設定表, zh_TW, 0, 0, 1531]
0728 16:43:20.580 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.qrLoginStrategy}}, 終端掃描登入策略, zh_TW, 0, 0, 1532]
0728 16:43:20.583 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstWebDownloadFile}}, 網頁下載檔案, zh_TW, 0, 0, 1533]
0728 16:43:20.585 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.termSecurityDetectionStrategy}}, 終端安全檢測, zh_TW, 0, 0, 1534]
0728 16:43:20.587 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.diskUsageRate}}, 磁碟使用比, zh_TW, 0, 0, 1535]
0728 16:43:20.589 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{label.grade.0}}, 無等級, zh_TW, 0, 0, 1536]
0728 16:43:20.592 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.hardAsset}}, {{AssetType}}資產資訊{{LogType}}，詳情如下:{{DetailInfo}}, zh_TW, 0, 0, 1537]
0728 16:43:20.595 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.processorID}}, 處理器ID, zh_TW, 0, 0, 1538]
0728 16:43:20.598 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.diskBackupSpace}}, 當前備份目錄所在磁碟剩餘空間均不足{{DiskFreeSpace}}GB，請及時清理，以免無法備份檔案, zh_TW, 0, 0, 1539]
0728 16:43:20.600 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstMacChanged}}, MAC改變,消失的MAC為{{sub}},新增的MAC為{{Add}}, zh_TW, 0, 0, 1540]
0728 16:43:20.602 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.softWareCharge}}, 您違規安裝未授權軟體{{SoftwareName}}請聯絡管理員授權, zh_TW, 0, 0, 2070]
0728 16:43:20.604 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.MTP}}, MTP管控, zh_TW, 0, 0, 1541]
0728 16:43:20.605 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.refreshFrequency}}, 重新整理頻率, zh_TW, 0, 0, 1542]
0728 16:43:20.608 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.DetailInfo}}, 詳細資訊, zh_TW, 0, 0, 1543]
0728 16:43:20.612 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.usbcode}}, USB編號, zh_TW, 0, 0, 1544]
0728 16:43:20.614 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.wifiBssid}}, WiFi實體位址, zh_TW, 0, 0, 1545]
0728 16:43:20.617 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numMTP}}, MTP外傳檔案, zh_TW, 0, 0, 2036]
0728 16:43:20.619 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.freeSize}}, 空閑大小, zh_TW, 0, 0, 1546]
0728 16:43:20.621 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.emailKeywordBlockStrategy}}, 郵件內容限制, zh_TW, 0, 0, 1547]
0728 16:43:20.623 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.documentTrackGlobal}}, 檔案追蹤-進階設定, zh_TW, 0, 0, 1548]
0728 16:43:20.626 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.webUpload}}, 網頁上傳檔案管控, zh_TW, 0, 0, 1549]
0728 16:43:20.630 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.tapeDrive}}, 磁帶磁碟機, zh_TW, 0, 0, 1550]
0728 16:43:20.633 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.strategyHttpWhiteList}}, {{ProcessName}}訪問白名單網站（{{ServerName}}）失敗！原因：同一個瀏覽器打開了不同加密策略的白名單網站, zh_TW, 0, 0, 1551]
0728 16:43:20.635 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.modeOfOperation}}, 工作模式, zh_TW, 0, 0, 1552]
0728 16:43:20.637 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.level.level2}}, 次要, zh_TW, 0, 0, 1553]
0728 16:43:20.639 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.processHttpWhiteList}}, {{ProcessName}}訪問白名單網站（{{ServerName}}）失敗！原因：非特殊進程訪問白名單網站，可反饋給管理員處理。, zh_TW, 0, 0, 1554]
0728 16:43:20.644 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.appLogConfig}}, 程式使用記錄設定, zh_TW, 0, 0, 1555]
0728 16:43:20.648 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstLocShare}}, 本地共用檔案, zh_TW, 0, 0, 1556]
0728 16:43:20.650 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.alarmTemplateParam}}, 告警消息模板引數, zh_TW, 0, 0, 1557]
0728 16:43:20.652 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstSaveAs}}, 另存到移動儲存裝置的內容涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1558]
0728 16:43:20.654 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.signReportPush}}, {{date}} {{timeDimension}} {{signName}}徵兆情況：<br> 嚴重違規等級次數：{{seriousCount}}次, <br> 重要違規等級次數：{{importantCount}}次, <br > 一般違規等級次數：{{normalCount}}次, zh_TW, 0, 0, 1559]
0728 16:43:20.657 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numNetShare}}, 遠端共享檔案外傳, zh_TW, 0, 0, 2037]
0728 16:43:20.659 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.seamlessReplace}}, 兼容其他產品設定, zh_TW, 0, 0, 1560]
0728 16:43:20.661 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelUsbCopy}}, USB複製檔案, zh_TW, 0, 0, 1895]
0728 16:43:20.664 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay32}}, 檔案拷貝至共享目錄, zh_TW, 0, 0, 1561]
0728 16:43:20.666 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.softwareLimitStrategy}}, 軟體黑白名單, zh_TW, 0, 0, 1562]
0728 16:43:20.668 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.formatStore}}, 隨身碟違規格式化, zh_TW, 0, 0, 1563]
0728 16:43:20.670 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.netInterface}}, 網絡接口管控, zh_TW, 0, 0, 1564]
0728 16:43:20.673 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.releaseDate}}, 發布日期, zh_TW, 0, 0, 1565]
0728 16:43:20.676 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.usbFileConfig}}, USB外傳檔案設定, zh_TW, 0, 0, 1566]
0728 16:43:20.678 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstDrip}}, 零星檢測, zh_TW, 0, 0, 1567]
0728 16:43:20.680 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstFtp}}, FTP上傳檔案內容檔案涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1568]
0728 16:43:20.682 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.systemCatalog}}, 系統目錄, zh_TW, 0, 0, 1569]
0728 16:43:20.684 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelWebFile}}, 傳送到網頁的檔案涉及違規，違規詳情如下：標籤內容{{LabelName}}違規 或 「檔案等級」高於指定級別{{LabelLevel}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1896]
0728 16:43:20.686 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstCut}}, USB剪下檔案, zh_TW, 0, 0, 1570]
0728 16:43:20.689 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.store}}, 儲存裝置使用控制, zh_TW, 0, 0, 1571]
0728 16:43:20.691 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.asset}}, 資產變更, zh_TW, 0, 0, 1572]
0728 16:43:20.693 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.uninstallStrategy}}, 軟體安裝/解除安裝限制-解除安裝限制, zh_TW, 0, 0, 1573]
0728 16:43:20.696 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mobileOpenSuffixStrategy}}, 移動端後綴設定, zh_TW, 0, 0, 1574]
0728 16:43:20.698 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.computerNameAlarmConfig}}, 電腦名稱變更, zh_TW, 0, 0, 1575]
0728 16:43:20.700 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.1394ControlCard}}, 1394控制卡, zh_TW, 0, 0, 1576]
0728 16:43:20.702 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.softWareTaskStrategy}}, 軟體解除安裝, zh_TW, 0, 0, 1577]
0728 16:43:20.705 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.softLimit}}, 軟體黑白名單, zh_TW, 0, 0, 1578]
0728 16:43:20.708 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.sensitiveBackupConfig}}, 敏感內容檔案備份設定, zh_TW, 0, 0, 1579]
0728 16:43:20.711 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.wifiSsid}}, WiFi名稱, zh_TW, 0, 0, 1580]
0728 16:43:20.714 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.LabelLevel}}, 標籤等級, zh_TW, 0, 0, 1581]
0728 16:43:20.717 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstCopy}}, 複製到移動儲存裝置的內容涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1582]
0728 16:43:20.720 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelNetShare}}, 遠端共享檔案外傳, zh_TW, 0, 0, 1897]
0728 16:43:20.722 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstMailFile}}, 郵件外傳管控, zh_TW, 0, 0, 1583]
0728 16:43:20.725 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstWebPaste}}, 網頁貼上文字內容, zh_TW, 0, 0, 1584]
0728 16:43:20.728 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.urlStrategy}}, 網頁瀏覽限制, zh_TW, 0, 0, 1585]
0728 16:43:20.731 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.mailSenderWhiteList}}, 您傳送的郵件附件已被解密，接收解密附件的郵箱位址為：\r\n{{emailAddr}}附件名稱：{{attachment}}, zh_TW, 0, 0, 1586]
0728 16:43:20.734 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.serverAnyDiskUsage}}, {{devType}}【{{devId}}】磁碟機代號（{{disk}}）剩餘可用空間為{{value}},低於設定的閾值{{threshold}}GB, zh_TW, 0, 0, 1587]
0728 16:43:20.737 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Type}}, 型別, zh_TW, 0, 0, 1588]
0728 16:43:20.740 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.keyTable}}, 預設論壇網站合約, zh_TW, 0, 0, 1589]
0728 16:43:20.742 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.registeredUser}}, 註冊使用者, zh_TW, 0, 0, 1590]
0728 16:43:20.744 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.fileFilterSetup}}, 檔案監視過濾, zh_TW, 0, 0, 1591]
0728 16:43:20.746 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.size}}, 大小, zh_TW, 0, 0, 1592]
0728 16:43:20.749 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numBurn}}, 光碟燒錄檔案, zh_TW, 0, 0, 2038]
0728 16:43:20.751 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.appBlock}}, {{FilePath}}，應用程式限制, zh_TW, 0, 0, 1593]
0728 16:43:20.754 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.WinTitle}}, 視窗標題, zh_TW, 0, 0, 1594]
0728 16:43:20.756 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.softRunning}}, 必須執行軟體, zh_TW, 0, 0, 1595]
0728 16:43:20.758 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.DNSServer}}, DNS伺服器, zh_TW, 0, 0, 1596]
0728 16:43:20.760 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstWebFile}}, 網頁上傳檔案, zh_TW, 0, 0, 1597]
0728 16:43:20.762 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.serialNumber}}, 序列號, zh_TW, 0, 0, 1598]
0728 16:43:20.765 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.userDense}}, 操作員密級設定, zh_TW, 0, 0, 1599]
0728 16:43:20.768 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.modem}}, 數據機, zh_TW, 0, 0, 1600]
0728 16:43:20.771 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.seriousCount}}, 嚴重次數, zh_TW, 0, 0, 1601]
0728 16:43:20.774 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelUsbCut}}, 剪切到移動儲存裝置的內容涉及違規，違規詳情如下：標籤內容{{LabelName}}違規 或 「檔案等級」高於指定級別{{LabelLevel}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1898]
0728 16:43:20.777 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.workMode}}, 工作模式設定, zh_TW, 0, 0, 1602]
0728 16:43:20.779 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelImFile}}, 傳送的即時通信檔案涉及違規，違規詳情如下：標籤內容{{LabelName}}違規 或 「檔案等級」高於指定級別{{LabelLevel}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1899]
0728 16:43:20.781 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.devName}}, 伺服器名稱, zh_TW, 0, 0, 1603]
0728 16:43:20.783 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.netShareFile}}, 違規使用網路共用外傳檔案，檔案路徑：{{alarmData}}, zh_TW, 0, 0, 1604]
0728 16:43:20.786 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.verticalResolution}}, 垂直解析度, zh_TW, 0, 0, 1605]
0728 16:43:20.788 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.autoPatchStrategy}}, 自動補丁策略, zh_TW, 0, 0, 1606]
0728 16:43:20.790 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.remoteUploadTool}}, 您觸發違規遠端工具檔案上傳事件，程序名:{{ProcessName}}，檔案路徑：{{FilePath}}, zh_TW, 0, 0, 2039]
0728 16:43:20.792 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.sysUser}}, 管理員資訊, zh_TW, 0, 0, 1607]
0728 16:43:20.795 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelFTP}}, FTP上傳檔案, zh_TW, 0, 0, 1900]
0728 16:43:20.798 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.formatStore}}, 違規使用了專用隨身碟：{{DeviceName}} ，序列號：{{usbcode}} ， 品牌：{{vendor}}，容量：{{capacity}}, zh_TW, 0, 0, 1608]
0728 16:43:20.802 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.MACAddress}}, MAC位址, zh_TW, 0, 0, 1609]
0728 16:43:20.804 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.forumPostingRecordLimit}}, 論壇發帖記錄限制, zh_TW, 0, 0, 1610]
0728 16:43:20.807 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.bus}}, 匯流排, zh_TW, 0, 0, 1611]
0728 16:43:20.810 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.motherboard}}, 主機板, zh_TW, 0, 0, 1612]
0728 16:43:20.813 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstWebPrint}}, 網頁列印, zh_TW, 0, 0, 1613]
0728 16:43:20.817 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelFTP}}, FTP上傳檔案內容涉及違規，違規詳情如下：標籤內容{{LabelName}}違規 或 「檔案等級」高於指定級別{{LabelLevel}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1901]
0728 16:43:20.820 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.serverDataDiskUsage}}, {{devType}}【{{devId}}】磁碟機代號（{{disk}}）剩餘可用空間為{{value}},低於設定的閾值{{threshold}}GB, zh_TW, 0, 0, 1614]
0728 16:43:20.823 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.iPv4Address}}, IPv4位址, zh_TW, 0, 0, 1615]
0728 16:43:20.826 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.DeviceInstanceId}}, 實例路徑, zh_TW, 0, 0, 1616]
0728 16:43:20.830 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.RemoteIP}}, 遠端IP, zh_TW, 0, 0, 1617]
0728 16:43:20.834 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.outgoingProcess}}, 軟體白名單, zh_TW, 0, 0, 1618]
0728 16:43:20.837 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numBluetooth}}, 藍芽外傳檔案, zh_TW, 0, 0, 2040]
0728 16:43:20.840 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstBurn}}, 光碟燒錄檔案, zh_TW, 0, 0, 1619]
0728 16:43:20.843 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.serviceName}}, 服務名, zh_TW, 0, 0, 1620]
0728 16:43:20.848 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.groupPolicyFunction}}, 組策略功能資訊, zh_TW, 0, 0, 1621]
0728 16:43:20.851 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.backUpConfig}}, 加密檔案備份設定, zh_TW, 0, 0, 1622]
0728 16:43:20.854 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.FilePath}}, 檔案路徑, zh_TW, 0, 0, 1623]
0728 16:43:20.857 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.appLogOption}}, 程式使用監視進階設定, zh_TW, 0, 0, 1624]
0728 16:43:20.859 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.wifiBlockStrategy}}, WiFi連接限制, zh_TW, 0, 0, 1625]
0728 16:43:20.863 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.decToolLimitInfo}}, 高危軟件庫, zh_TW, 0, 0, 1626]
0728 16:43:20.866 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstSaveAs}}, USB另存為檔案, zh_TW, 0, 0, 1627]
0728 16:43:20.869 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processStgNet}}, 受控程式策略庫——網路限制策略, zh_TW, 0, 0, 1628]
0728 16:43:20.871 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.floppyDrive}}, 軟碟機, zh_TW, 0, 0, 1629]
0728 16:43:20.874 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.serverDataDiskUsage}}, 服務器數據磁盤空間不足告警, zh_TW, 0, 0, 1630]
0728 16:43:20.877 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.AISendFileMsg}}, AI傳送內容限制, zh_TW, 0, 0, 2071]
0728 16:43:20.880 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstImDownload}}, 下載的即時通信檔案涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1631]
0728 16:43:20.883 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstRemoteUploadTool}}, 遠端工具上傳檔案, zh_TW, 0, 0, 2041]
0728 16:43:20.886 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.signReportPush}}, 徵兆警報推播, zh_TW, 0, 0, 1632]
0728 16:43:20.889 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.telnetControl}}, telnet管控, zh_TW, 0, 0, 1633]
0728 16:43:20.893 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelMTP}}, MTP外傳檔案, zh_TW, 0, 0, 1902]
0728 16:43:20.897 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.active}}, 是否啟用, zh_TW, 0, 0, 1951]
0728 16:43:20.901 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.IKey}}, 違規類型, zh_TW, 0, 0, 1634]
0728 16:43:20.904 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.numberOfPartitions}}, 分區數, zh_TW, 0, 0, 1635]
0728 16:43:20.906 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.installStrategy}}, 軟體安裝/解除安裝限制-安裝限制, zh_TW, 0, 0, 1636]
0728 16:43:20.911 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.alarmActionParam}}, 告警消息模板引數, zh_TW, 0, 0, 1637]
0728 16:43:20.913 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.graphicsCard}}, 顯示卡, zh_TW, 0, 0, 1638]
0728 16:43:20.916 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.role}}, 管理員角色, zh_TW, 0, 0, 1639]
0728 16:43:20.918 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstDrip}}, 零星檢測, zh_TW, 0, 0, 1640]
0728 16:43:20.921 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{enc.denseInfo.confidential}}, 機密檔案, zh_TW, 0, 0, 1641]
0728 16:43:20.925 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.DiskFreeSpace}}, 磁碟剩餘空間, zh_TW, 0, 0, 1642]
0728 16:43:20.929 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelMTP}}, MTP外傳的檔案涉及違規，違規詳情如下：標籤內容{{LabelName}}違規 或 「檔案等級」高於指定級別{{LabelLevel}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1903]
0728 16:43:20.936 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.softAsset}}, {{AssetType}}資產資訊{{LogType}}，詳情如下:{{DetailInfo}}, zh_TW, 0, 0, 1643]
0728 16:43:20.940 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.sensitiveFileLossType}}, 允許的外傳途徑, zh_TW, 0, 0, 1644]
0728 16:43:20.943 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.userExtInfo}}, 操作員擴展資料, zh_TW, 0, 0, 1645]
0728 16:43:20.946 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay256}}, 網頁上傳檔案, zh_TW, 0, 0, 1646]
0728 16:43:20.949 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.capacity}}, 容量, zh_TW, 0, 0, 1647]
0728 16:43:20.952 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.interfaceType}}, 介面型別, zh_TW, 0, 0, 1648]
0728 16:43:20.954 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstWeb}}, 瀏覽網頁文字內容, zh_TW, 0, 0, 1649]
0728 16:43:20.957 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.USBDevice}}, USB裝置, zh_TW, 0, 0, 1650]
0728 16:43:20.960 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.media}}, 介質, zh_TW, 0, 0, 1651]
0728 16:43:20.963 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.ftpTool}}, FTP工具傳輸, zh_TW, 0, 0, 1652]
0728 16:43:20.965 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.remoteUploadTool}}, 遠端工具上傳檔案, zh_TW, 0, 0, 2042]
0728 16:43:20.967 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.lossTypeParam}}, 敏感泄露型別, zh_TW, 0, 0, 1653]
0728 16:43:20.970 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.contentScan}}, 敏感內容掃描策略, zh_TW, 0, 0, 1654]
0728 16:43:20.974 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.appVersionLimitStrategy}}, 程式版本限制, zh_TW, 0, 0, 1655]
0728 16:43:20.977 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.chatSendFile}}, 您觸發違規聊天檔案外傳事件，程式名:{{ProcessName}}，檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1656]
0728 16:43:20.979 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numBluetooth}}, 藍芽傳輸的檔案涉及違規，違規詳情如下：檔案大小或外傳檔案累計數量違規；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 2043]
0728 16:43:20.982 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numMTP}}, MTP外傳的檔案涉及違規，違規詳情如下：檔案大小或外傳檔案累計數量違規；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 2044]
0728 16:43:20.984 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelRemoteDesktop}}, 遠端桌面檔案外傳, zh_TW, 0, 0, 1904]
0728 16:43:20.987 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstIm}}, 即時通訊傳送文字內容, zh_TW, 0, 0, 1657]
0728 16:43:20.989 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.netDisk}}, 網盤傳輸備份限制, zh_TW, 0, 0, 1658]
0728 16:43:20.991 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.bluetooth}}, 違規通過藍牙傳輸檔案，檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1659]
0728 16:43:20.993 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.ftpTool}}, 您違規使用FTP工具：{{ProcessName}}，{{Optype}}檔案，檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1660]
0728 16:43:20.996 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.softProcessMd5}}, 受控程式策略庫——應用程式安裝解除安裝防偽冒指紋表, zh_TW, 0, 0, 1661]
0728 16:43:20.999 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelBurn}}, 光碟燒錄檔案, zh_TW, 0, 0, 1905]
0728 16:43:21.001 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstMagneticDiskUsed}}, 您的所有磁碟使用率為{{Function}}%已超過設定的閾值{{number}}%, zh_TW, 0, 0, 1662]
0728 16:43:21.005 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.shareStgConfig}}, 敏感內容檢測設定-共用管控設定, zh_TW, 0, 0, 1663]
0728 16:43:21.008 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.path}}, 路徑, zh_TW, 0, 0, 1664]
0728 16:43:21.011 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.outgoingPrintWaterMark}}, 外發列印浮水印, zh_TW, 0, 0, 1665]
0728 16:43:21.014 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processInject}}, 受控註入設定, zh_TW, 0, 0, 1666]
0728 16:43:21.017 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstMail}}, 郵件傳輸文字內容, zh_TW, 0, 0, 1667]
0728 16:43:21.022 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.burnConfigStrategy}}, 燒錄機使用管控, zh_TW, 0, 0, 1668]
0728 16:43:21.025 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.chatWhiteList}}, 通訊賬號庫列表, zh_TW, 0, 0, 1669]
0728 16:43:21.027 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.vendor}}, 供應商, zh_TW, 0, 0, 1670]
0728 16:43:21.029 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.flowThresholdLimit}}, {{ProcessName}}當前使用已超過閾值流量，其中上傳流量：{{UpFlow}} MB；下載流量：{{DownFlow}} MB；合計：{{TotalFlow}} MB, zh_TW, 0, 0, 1968]
0728 16:43:21.032 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.burnCD}}, 燒錄使用管控, zh_TW, 0, 0, 1671]
0728 16:43:21.034 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstBurn}}, 光碟燒錄檔案, zh_TW, 0, 0, 1672]
0728 16:43:21.037 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.cpuUsage}}, CPU使用率, zh_TW, 0, 0, 1673]
0728 16:43:21.039 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstNetDiskDownload}}, 網盤下載檔案, zh_TW, 0, 0, 1674]
0728 16:43:21.041 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.diskSize}}, 磁碟大小, zh_TW, 0, 0, 1675]
0728 16:43:21.044 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.webPortStrategy}}, 網路連接埠限制, zh_TW, 0, 0, 1676]
0728 16:43:21.046 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.alarmTypeParam}}, 告警來源引數, zh_TW, 0, 0, 1677]
0728 16:43:21.048 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.landLabelingStrategy}}, 落地加標籤, zh_TW, 0, 0, 1678]
0728 16:43:21.050 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.UPSDevice}}, UPS裝置, zh_TW, 0, 0, 1679]
0728 16:43:21.053 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.winTitle}}, 您開啟一個違規視窗標題“{{WinTitle}}”，包含關鍵字“{{KeyWord}}”, zh_TW, 0, 0, 1680]
0728 16:43:21.055 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.ADBController}}, ADB管控, zh_TW, 0, 0, 1681]
0728 16:43:21.057 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.maximumTransferRate}}, 最大傳輸率, zh_TW, 0, 0, 1682]
0728 16:43:21.059 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.PCMCIAController}}, PCMCIA控制器, zh_TW, 0, 0, 1683]
0728 16:43:21.062 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.telnetControl}}, 您違規使用telnet通訊工具，進程名:{{ProcessName}}，伺服器位址:{{Address}} 命令: {{CMDData}}, zh_TW, 0, 0, 1684]
0728 16:43:21.064 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.IType}}, 接口, zh_TW, 0, 0, 1685]
0728 16:43:21.067 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.backupRule}}, 備份過濾規則, zh_TW, 0, 0, 1686]
0728 16:43:21.069 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.FileName}}, 檔案名稱, zh_TW, 0, 0, 1687]
0728 16:43:21.072 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstImFile}}, 即時通訊傳送檔案, zh_TW, 0, 0, 1688]
0728 16:43:21.074 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.webpageBrowseAudit}}, 網頁瀏覽網址過濾, zh_TW, 0, 0, 1689]
0728 16:43:21.076 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.readPermission}}, 閱讀權限設定, zh_TW, 0, 0, 1690]
0728 16:43:21.078 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.groupPolicyStrategy}}, 組策略設定, zh_TW, 0, 0, 1691]
0728 16:43:21.080 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.winTitle}}, 視窗標題限制, zh_TW, 0, 0, 1692]
0728 16:43:21.082 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstMTP}}, MTP外傳的檔案涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1693]
0728 16:43:21.084 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelWebFile}}, 網頁上傳檔案, zh_TW, 0, 0, 1906]
0728 16:43:21.086 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstImDownload}}, 即時通訊下載檔案, zh_TW, 0, 0, 1694]
0728 16:43:21.088 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstRemoteDesktop}}, 遠端桌面傳送的檔案涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1695]
0728 16:43:21.089 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.netCard}}, 網卡接口, zh_TW, 0, 0, 1696]
0728 16:43:21.092 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.imToolStrategy}}, 通訊工具白名單, zh_TW, 0, 0, 1697]
0728 16:43:21.094 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstNetShare}}, 瀏覽/修改網路共用時涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1698]
0728 16:43:21.096 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.sysAlarmConfig}}, 電腦告警設定, zh_TW, 0, 0, 1699]
0728 16:43:21.100 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstNetShare}}, 遠端共用檔案外傳, zh_TW, 0, 0, 1700]
0728 16:43:21.102 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.soundCard}}, 音效卡, zh_TW, 0, 0, 1701]
0728 16:43:21.104 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.workModeProcessMd5}}, 工作模式切換程式防偽冒, zh_TW, 0, 0, 1702]
0728 16:43:21.106 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.serverCpuUsage}}, 服務器CPU佔用告警, zh_TW, 0, 0, 1703]
0728 16:43:21.108 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.computerType}}, 計算機型別, zh_TW, 0, 0, 1952]
0728 16:43:21.110 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstImReceive}}, 即時通訊接收文字內容, zh_TW, 0, 0, 1704]
0728 16:43:21.112 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.sysWebPortStrategy}}, 預設連接埠限制策略, zh_TW, 0, 0, 1705]
0728 16:43:21.115 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.totalHeads}}, 總磁頭數, zh_TW, 0, 0, 1706]
0728 16:43:21.117 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstNetDiskDownload}}, 您的網盤下載檔案內容涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1707]
0728 16:43:21.119 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.sub}}, 減去, zh_TW, 0, 0, 1708]
0728 16:43:21.121 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstRemoteDesktop}}, 遠端桌面外傳檔案, zh_TW, 0, 0, 1709]
0728 16:43:21.123 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.netShareFile}}, 網路共用檔案外傳告警, zh_TW, 0, 0, 1710]
0728 16:43:21.125 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mobileWPSConfig}}, 移動終端WPS設定, zh_TW, 0, 0, 1711]
0728 16:43:21.127 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.directOutgingWhiteList}}, 直接外發程式白名單, zh_TW, 0, 0, 1712]
0728 16:43:21.129 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.flowThresholdLimit}}, 流量閾值限制, zh_TW, 0, 0, 1969]
0728 16:43:21.132 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.winTitleBlockStrategy}}, 視窗標題限制, zh_TW, 0, 0, 1713]
0728 16:43:21.134 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.manufacturer}}, 製造商, zh_TW, 0, 0, 1714]
0728 16:43:21.136 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.emailContent}}, 您傳送的郵件觸發了郵件內容限制策略，違規內容為：\r\n關鍵字：\r\n{{KeyWord}}收件人：{{mailReceiver}}發件人：{{mailSender}}, zh_TW, 0, 0, 1715]
0728 16:43:21.138 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.effectiveFunction}}, 敏感外傳檢測設定, zh_TW, 0, 0, 1716]
0728 16:43:21.140 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.organization}}, 組織, zh_TW, 0, 0, 1717]
0728 16:43:21.143 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.printerSet}}, 列印許可權控制, zh_TW, 0, 0, 1718]
0728 16:43:21.146 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.RuleName}}, 規則名稱, zh_TW, 0, 0, 1719]
0728 16:43:21.149 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelNetShare}}, 訪問/修改網絡共享時涉及違規，違規詳情如下：標籤內容{{LabelName}}違規 或 「檔案等級」高於指定級別{{LabelLevel}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1907]
0728 16:43:21.152 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.installationTime}}, 安裝時間, zh_TW, 0, 0, 1720]
0728 16:43:21.154 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.encryptSpecialPath}}, 特殊目錄設定, zh_TW, 0, 0, 1721]
0728 16:43:21.156 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.memoryAlarmConfig}}, 記憶體佔用, zh_TW, 0, 0, 1722]
0728 16:43:21.159 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numUsbCut}}, 剪下到移動儲存裝置的內容涉及違規，違規詳情如下：檔案大小或外傳檔案累計數量違規；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 2045]
0728 16:43:21.161 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.workMode}}, 工作模式切換, zh_TW, 0, 0, 1723]
0728 16:43:21.164 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.CPU}}, CPU, zh_TW, 0, 0, 1724]
0728 16:43:21.166 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.autoGetIP}}, 自動獲取IP, zh_TW, 0, 0, 1725]
0728 16:43:21.168 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstFtp}}, FTP上傳檔案, zh_TW, 0, 0, 1726]
0728 16:43:21.170 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numRemoteDesktop}}, 遠端桌面外傳檔案, zh_TW, 0, 0, 2046]
0728 16:43:21.172 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstSysDiskUsed}}, 您的系統碟使用率為{{Function}}%已超過設定的閾值{{number}}%, zh_TW, 0, 0, 1727]
0728 16:43:21.174 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.usage}}, 使用情況, zh_TW, 0, 0, 1728]
0728 16:43:21.178 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.url}}, 網址基礎資料, zh_TW, 0, 0, 1729]
0728 16:43:21.180 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.webPostStrategy}}, 網頁傳送內容限制, zh_TW, 0, 0, 1730]
0728 16:43:21.182 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.sysBaseConfig}}, 電腦設定, zh_TW, 0, 0, 1731]
0728 16:43:21.184 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.TotalFlow}}, 總流量, zh_TW, 0, 0, 1970]
0728 16:43:21.186 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.batchEncOrDec}}, 批次加解密, zh_TW, 0, 0, 1732]
0728 16:43:21.187 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numImFile}}, 即時通訊傳送檔案, zh_TW, 0, 0, 2047]
0728 16:43:21.190 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.terminalUpgrade}}, 終端線上升級, zh_TW, 0, 0, 1733]
0728 16:43:21.192 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.totalSize}}, 總大小, zh_TW, 0, 0, 1734]
0728 16:43:21.194 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.bitsPerPixel}}, 每像素位數, zh_TW, 0, 0, 1735]
0728 16:43:21.197 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.sectorsPerTrack}}, 每磁軌扇區數, zh_TW, 0, 0, 1736]
0728 16:43:21.199 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.hardwareAssetAlarmSetup}}, 硬體資產變更報警設定, zh_TW, 0, 0, 1737]
0728 16:43:21.201 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.serialPortDevice}}, 序列埠裝置, zh_TW, 0, 0, 1738]
0728 16:43:21.203 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.termScan}}, 終端安全檢測結果風險等級爲{{level}} 共掃描{{Number}}項，其中成功{{SucNumber}}項 失敗{{ErrNumber}}項, zh_TW, 0, 0, 1739]
0728 16:43:21.205 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{enc.denseInfo.internal}}, 內部資料檔案, zh_TW, 0, 0, 1740]
0728 16:43:21.208 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelUsbCopy}}, 復製到移動儲存裝置的內容涉及違規，違規詳情如下：標籤內容{{LabelName}}違規 或 「檔案等級」高於指定級別{{LabelLevel}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1908]
0728 16:43:21.210 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.ftpControllerConfig}}, FTP檔案管理, zh_TW, 0, 0, 1741]
0728 16:43:21.212 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstWeb}}, 瀏覽網頁文字內容, zh_TW, 0, 0, 1742]
0728 16:43:21.214 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.otherDeviceLimit}}, 您的設備被禁止使用，裝置名：{{DeviceName}}，裝置實例路徑：{{DeviceInstanceId}}, zh_TW, 0, 0, 1743]
0728 16:43:21.216 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mailWhiteListStrategy}}, 郵件白名單設定-收件人白名單, zh_TW, 0, 0, 1744]
0728 16:43:21.218 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.KeyWord}}, 關鍵詞, zh_TW, 0, 0, 1745]
0728 16:43:21.220 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.ipAlarmConfig}}, IP位址變更, zh_TW, 0, 0, 1746]
0728 16:43:21.223 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.totalTracks}}, 總磁軌數, zh_TW, 0, 0, 1747]
0728 16:43:21.225 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.devType}}, 伺服器型別, zh_TW, 0, 0, 1748]
0728 16:43:21.227 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.driverVersion}}, 驅動版本, zh_TW, 0, 0, 1749]
0728 16:43:21.229 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstABDUpload}}, ADB傳輸檔案內容涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1750]
0728 16:43:21.232 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Function}}, 功能, zh_TW, 0, 0, 1751]
0728 16:43:21.234 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.version}}, 版本, zh_TW, 0, 0, 1752]
0728 16:43:21.236 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.ServerName}}, 服務器名稱, zh_TW, 0, 0, 1753]
0728 16:43:21.239 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.webSite}}, 聯網驗證位址, zh_TW, 0, 0, 1754]
0728 16:43:21.241 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.infraredDevice}}, 紅外線裝置, zh_TW, 0, 0, 1755]
0728 16:43:21.243 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.i18nDict}}, 國際化字典, zh_TW, 0, 0, 1756]
0728 16:43:21.245 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.wifiCollectStrategy}}, WiFi資訊收集, zh_TW, 0, 0, 1757]
0728 16:43:21.247 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.batchDecLimit}}, 解密數量限制, zh_TW, 0, 0, 1758]
0728 16:43:21.249 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstIpChanged}}, IP改變,消失的IP為{{sub}},新增的IP為{{Add}}, zh_TW, 0, 0, 1759]
0728 16:43:21.251 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.AIModel}}, 您違規使用AI模型工具，違規內容為：\r\nAI名稱：{{AIName}}\r\n程序名：{{ProcessName}}, zh_TW, 0, 0, 2072]
0728 16:43:21.253 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.timelyBackupStg}}, 即時備份, zh_TW, 0, 0, 1760]
0728 16:43:21.256 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.opticalDiskDrive}}, 光碟磁碟機, zh_TW, 0, 0, 1761]
0728 16:43:21.258 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay1}}, 下載檔案, zh_TW, 0, 0, 1762]
0728 16:43:21.260 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.quantity}}, 數量, zh_TW, 0, 0, 1763]
0728 16:43:21.262 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstPost}}, 論壇發文文字內容, zh_TW, 0, 0, 1764]
0728 16:43:21.265 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.relGroupUser}}, 操作員所屬部門資訊, zh_TW, 0, 0, 1765]
0728 16:43:21.267 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.description}}, 描述, zh_TW, 0, 0, 1766]
0728 16:43:21.270 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numNetShare}}, 訪問/修改網路共享時涉及違規，違規詳情如下：檔案大小或外傳檔案累計數量違規；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 2048]
0728 16:43:21.273 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.remoteDesktop}}, 遠端桌面管控, zh_TW, 0, 0, 1767]
0728 16:43:21.275 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelImFile}}, 即時通訊傳送檔案, zh_TW, 0, 0, 1909]
0728 16:43:21.277 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.productionDayDate}}, 生產日期, zh_TW, 0, 0, 1768]
0728 16:43:21.279 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.waterMarkStrategy}}, 列印浮水印設定, zh_TW, 0, 0, 1769]
0728 16:43:21.281 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.appVer}}, 軟體版本限制, zh_TW, 0, 0, 1770]
0728 16:43:21.282 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.screenshotStrategy}}, 截取螢幕設定, zh_TW, 0, 0, 1771]
0728 16:43:21.285 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.specialPathStrategy}}, 軟體安裝/解除安裝限制-特殊目錄放行, zh_TW, 0, 0, 1772]
0728 16:43:21.287 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.mailSender}}, 郵箱傳送人, zh_TW, 0, 0, 1773]
0728 16:43:21.289 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.fullDiskScanBackupStg}}, 全盤掃描備份, zh_TW, 0, 0, 1774]
0728 16:43:21.291 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.mailReceiverWhiteList}}, 您傳送的郵件附件已被解密，接收解密附件的郵箱位址為：\r\n{{emailAddr}}附件名稱：{{attachment}}, zh_TW, 0, 0, 1775]
0728 16:43:21.293 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Number}}, 數字, zh_TW, 0, 0, 1776]
0728 16:43:21.296 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.sstABDUpload}}, ADB外傳檔案, zh_TW, 0, 0, 1777]
0728 16:43:21.298 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.developer}}, 開發商, zh_TW, 0, 0, 1778]
0728 16:43:21.301 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.outFilePath}}, 外傳檔案路徑, zh_TW, 0, 0, 1779]
0728 16:43:21.304 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mobileWPSConfigStrategy}}, 移動終端WPS設定策略, zh_TW, 0, 0, 1780]
0728 16:43:21.306 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.deviceMark}}, 裝置標誌, zh_TW, 0, 0, 1781]
0728 16:43:21.308 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processCollectRule}}, 程式指紋收集規則, zh_TW, 0, 0, 1782]
0728 16:43:21.310 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Interval}}, 間隔, zh_TW, 0, 0, 1783]
0728 16:43:21.312 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.hardDiskDrive}}, 硬碟磁碟機, zh_TW, 0, 0, 1784]
0728 16:43:21.315 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numABDUpload}}, ADB傳輸檔案內容涉及違規，違規詳情如下：檔案大小或外傳檔案累計數量違規；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 2049]
0728 16:43:21.317 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.systemPatch}}, 系統更新, zh_TW, 0, 0, 1785]
0728 16:43:21.319 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.addressWidth}}, 位址寬度, zh_TW, 0, 0, 1786]
0728 16:43:21.321 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.labelMailFile}}, 傳送的郵件附件涉及違規，違規詳情如下：標籤內容{{LabelName}}違規 或 「檔案等級」高於指定級別{{LabelLevel}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1910]
0728 16:43:21.323 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.cache}}, 高速緩存, zh_TW, 0, 0, 1787]
0728 16:43:21.325 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.usbPort}}, USB接口, zh_TW, 0, 0, 1788]
0728 16:43:21.327 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstMail}}, 郵件傳輸文字內容, zh_TW, 0, 0, 1789]
0728 16:43:21.330 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay16}}, 檔案拷貝至移動盤, zh_TW, 0, 0, 1790]
0728 16:43:21.332 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.alarmTemplate}}, 告警消息模板, zh_TW, 0, 0, 1791]
0728 16:43:21.334 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstAISendFile}}, AI上傳檔案, zh_TW, 0, 0, 2050]
0728 16:43:21.336 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.serverCpuUsage}}, {{devType}}【{{devId}}】CPU使用率為{{value}}%,已超過設置的閾值{{threshold}}%, zh_TW, 0, 0, 1792]
0728 16:43:21.338 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.assetProp}}, 軟硬體資產內容資訊表, zh_TW, 0, 0, 1793]
0728 16:43:21.341 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{officeWaterMark.officeMarkWay2}}, 打開檔案, zh_TW, 0, 0, 1794]
0728 16:43:21.343 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.stgFileData}}, 策略關聯檔案資料, zh_TW, 0, 0, 1795]
0728 16:43:21.345 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.documentTrack}}, 檔案追蹤, zh_TW, 0, 0, 1796]
0728 16:43:21.349 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.display}}, 顯示器, zh_TW, 0, 0, 1797]
0728 16:43:21.351 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.clipboardStrategy}}, 剪下簿控制, zh_TW, 0, 0, 1798]
0728 16:43:21.353 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.violationResponseRule}}, 違規響應規則, zh_TW, 0, 0, 1799]
0728 16:43:21.355 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstWeb}}, 瀏覽的網頁資訊涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}, zh_TW, 0, 0, 1800]
0728 16:43:21.358 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.unconventionalSensitive}}, 全碟掃描內容檢測策略, zh_TW, 0, 0, 1801]
0728 16:43:21.360 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mobileTerminalUpgradeStrategy}}, 移動終端升級, zh_TW, 0, 0, 1802]
0728 16:43:21.363 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.localDNSHostName}}, 本地DNS主機名, zh_TW, 0, 0, 1803]
0728 16:43:21.365 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.baseFunction}}, 終端基礎功能模組, zh_TW, 0, 0, 1804]
0728 16:43:21.367 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstWebFile}}, 網頁上傳檔案, zh_TW, 0, 0, 1805]
0728 16:43:21.369 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.officeWaterMark}}, Office檔案浮水印設定, zh_TW, 0, 0, 1806]
0728 16:43:21.371 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.blindWatermark}}, 圖片盲浮水印, zh_TW, 0, 0, 1807]
0728 16:43:21.373 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstWebPaste}}, 貼上到網頁的內容涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}, zh_TW, 0, 0, 1808]
0728 16:43:21.376 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.DiskName}}, 磁碟名稱, zh_TW, 0, 0, 1809]
0728 16:43:21.380 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.VersionRange}}, 版本範圍, zh_TW, 0, 0, 1810]
0728 16:43:21.382 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.outgoingTemplate}}, 直接外發模板庫, zh_TW, 0, 0, 1811]
0728 16:43:21.384 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.defaultGatewayIPv4Address}}, 預設閘道IPv4位址, zh_TW, 0, 0, 1812]
0728 16:43:21.388 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.Add}}, 添加, zh_TW, 0, 0, 1813]
0728 16:43:21.397 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.webBrowseURLFilter}}, 網頁瀏覽網址過濾策略, zh_TW, 0, 0, 1814]
0728 16:43:21.401 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.diskPartition}}, 磁碟分割槽, zh_TW, 0, 0, 1815]
0728 16:43:21.405 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.mailSenderWhiteList}}, 郵件白名單違規告警, zh_TW, 0, 0, 1816]
0728 16:43:21.408 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.appVerLimit}}, {{FilePath}},不支援產品版本為<{{AppVersion}}>的應用程式。\r\n限制以下版本：{{VersionRange}}, zh_TW, 0, 0, 1817]
0728 16:43:21.411 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.processStrategy}}, 透明加密, zh_TW, 0, 0, 1818]
0728 16:43:21.415 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.lanSegment}}, 區域網路網段設定基礎資料, zh_TW, 0, 0, 1819]
0728 16:43:21.418 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.UpFlow}}, 上傳流量, zh_TW, 0, 0, 1971]
0728 16:43:21.422 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstCpuUsed}}, 您的cpu使用率為{{Function}}%已超過設定的閾值{{number}}%, zh_TW, 0, 0, 1820]
0728 16:43:21.426 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.appOpenSuffixStrategy}}, 移動終端檔案閱讀設定, zh_TW, 0, 0, 1821]
0728 16:43:21.429 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.softBlack}}, 您打開一個違規程式{{FilePath}}, zh_TW, 0, 0, 1822]
0728 16:43:21.433 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.memoryUsage}}, 記憶體使用率, zh_TW, 0, 0, 1823]
0728 16:43:21.437 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numUsbCopy}}, USB複製檔案, zh_TW, 0, 0, 2051]
0728 16:43:21.440 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.usbInterface}}, 您的USB接口{{InterfaceKey}}違規接入了{{InterfaceDeviceType}}設備{{InterfaceDeviceKey}}, zh_TW, 0, 0, 1824]
0728 16:43:21.444 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstAISendMsg}}, 傳送的AI文字內容涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}, zh_TW, 0, 0, 2052]
0728 16:43:21.448 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.BIOS}}, BIOS, zh_TW, 0, 0, 1825]
0728 16:43:21.451 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstAISendFile}}, 傳送到AI的檔案涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 2053]
0728 16:43:21.455 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.osProcess}}, 預設系統程式, zh_TW, 0, 0, 1826]
0728 16:43:21.459 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.AIModel}}, AI模型限制, zh_TW, 0, 0, 2073]
0728 16:43:21.462 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.ProcessName}}, 程式名稱, zh_TW, 0, 0, 1827]
0728 16:43:21.466 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstPost}}, 發文內容涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}, zh_TW, 0, 0, 1828]
0728 16:43:21.468 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.DeviceName}}, 裝置名稱, zh_TW, 0, 0, 1829]
0728 16:43:21.470 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.burnCD}}, 您違規使用光碟機燒錄機：{{DeviceInfo}}，燒錄檔案：{{FilePath}}, zh_TW, 0, 0, 1830]
0728 16:43:21.475 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.wifiBlock}}, WiFi連接限制, zh_TW, 0, 0, 1831]
0728 16:43:21.478 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.denseSet}}, 密級圖示及引數設定, zh_TW, 0, 0, 1832]
0728 16:43:21.481 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.netCtrlStrategy}}, 網路隔離設定, zh_TW, 0, 0, 1833]
0728 16:43:21.483 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.install}}, 軟體安裝/解除安裝限制, zh_TW, 0, 0, 1834]
0728 16:43:21.486 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.commonConfig}}, 安裝包限制基礎資料表-常用設定表, zh_TW, 0, 0, 1835]
0728 16:43:21.490 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.alarmBaseStrategy}}, 基礎策略-告警設定表, zh_TW, 0, 0, 1836]
0728 16:43:21.492 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.numUsbCut}}, USB剪下檔案, zh_TW, 0, 0, 2054]
0728 16:43:21.496 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.lossType.sstRemoteDesktop}}, 遠端桌面外傳檔案, zh_TW, 0, 0, 1837]
0728 16:43:21.500 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.labelUsbCut}}, USB剪下檔案, zh_TW, 0, 0, 1911]
0728 16:43:21.504 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.param.attachment}}, 附件, zh_TW, 0, 0, 1838]
0728 16:43:21.508 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.MTP}}, 違規通過MTP外傳檔案，檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1839]
0728 16:43:21.511 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.softBlack}}, 高危軟體限制, zh_TW, 0, 0, 1840]
0728 16:43:21.515 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.outgoingCodeWhiteList}}, 外發機器碼白名單, zh_TW, 0, 0, 1841]
0728 16:43:21.518 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.serverAnyDiskUsage}}, 伺服器系統磁碟空間不足告警, zh_TW, 0, 0, 1842]
0728 16:43:21.521 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.hardDriveTimes}}, 您的硬碟{{DiskName}}已使用{{Count}}次，已超過設定的閾值({{Number}})次, zh_TW, 0, 0, 1843]
0728 16:43:21.524 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.user}}, 操作員資訊, zh_TW, 0, 0, 1844]
0728 16:43:21.526 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstBluetooth}}, 藍牙傳輸的檔案涉及違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 1845]
0728 16:43:21.530 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.mtpConfig}}, MTP管控, zh_TW, 0, 0, 1846]
0728 16:43:21.532 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.smartEncStrategy}}, 智能加密, zh_TW, 0, 0, 1847]
0728 16:43:21.536 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{assets.productModel}}, 產品型號, zh_TW, 0, 0, 1848]
0728 16:43:21.540 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.offlineLockScreen}}, 離線鎖屏, zh_TW, 0, 0, 1849]
0728 16:43:21.543 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.dataDisclosureStrategy}}, 敏感內容策略, zh_TW, 0, 0, 1850]
0728 16:43:21.547 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.numImFile}}, 傳送的即時通訊檔案涉及違規，違規詳情如下：檔案大小或外傳檔案累計數量違規；檔案路徑：{{FilePath}}, zh_TW, 0, 0, 2055]
0728 16:43:21.550 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.diskScanSelfCheckSensitive}}, 敏感檔案自檢內容檢測策略, zh_TW, 0, 0, 1851]
0728 16:43:21.554 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.offline}}, 離線鎖屏告警, zh_TW, 0, 0, 1852]
0728 16:43:21.557 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstDrip}}, 涉及零星檢測違規，違規詳情如下：策略名稱：{{strategyName}}；規則名稱：{{RuleName}}, zh_TW, 0, 0, 1853]
0728 16:43:21.560 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.shortOfflineStrategy}}, 短期離線策略設定, zh_TW, 0, 0, 1854]
0728 16:43:21.563 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.httpWhiteListProcessFilter}}, 伺服器白名單——程式過濾, zh_TW, 0, 0, 1855]
0728 16:43:21.568 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.template.sstComputerNameChanged}}, 電腦名已修改，原名稱： {{oldName}}  現名稱： {{newName}}, zh_TW, 0, 0, 1856]
0728 16:43:21.572 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{alarm.type.softInstalled}}, 必須安裝軟體, zh_TW, 0, 0, 1857]
0728 16:43:21.575 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.manualLabelStrategy}}, 手動加標籤, zh_TW, 0, 0, 1858]
0728 16:43:21.579 - WARN : #sql: UPDATE i18n_dict  SET dict_key=?, dict_value=?, lng=?, custom=?, deleted=?  WHERE id=?   #parameter: [{{stgBaseConfig.fileDistribute}}, 檔案分發任務, zh_TW, 0, 0, 1859]
0728 16:43:21.615 - WARN : #sql: UPDATE property  SET code=?, value=?, type=?, rule=?,  editable=?, parent_id=?, modify_time=?     WHERE (code = ?)   #parameter: [i18n.zh_TW.version, 20250720001100, string, , false, 0, Mon Jul 28 16:43:21 CST 2025, i18n.zh_TW.version]
