0805 09:25:02.214 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1790/856252671@6fd24580
0805 09:25:02.346 - INFO [      main] : send service supplier apply: add config size=4
0805 09:25:02.347 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0805 09:25:02.401 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0805 09:25:02.402 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0805 09:25:02.402 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
