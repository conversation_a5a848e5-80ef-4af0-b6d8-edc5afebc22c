0804 09:40:34.143 - INFO [pool-11-thread-1] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1638/1185119147@6ebc11d9
0804 09:40:34.254 - INFO [pool-11-thread-1] : send service supplier apply: add config size=4
0804 09:40:34.255 - INFO [pool-11-thread-1] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 09:40:34.280 - INFO [pool-11-thread-1] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 09:40:34.281 - INFO [pool-11-thread-1] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 09:40:34.281 - INFO [pool-11-thread-1] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
0804 10:02:57.666 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1782/1450075694@4f3d0407
0804 10:02:57.782 - INFO [      main] : send service supplier apply: add config size=4
0804 10:02:57.783 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 10:02:57.799 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 10:02:57.800 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 10:02:57.800 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
0804 11:45:23.521 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1780/611214681@77b21dc3
0804 11:45:23.632 - INFO [      main] : send service supplier apply: add config size=4
0804 11:45:23.632 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 11:45:23.648 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 11:45:23.648 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 11:45:23.648 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
0804 13:33:28.734 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1748/490285375@3c663620
0804 13:33:29.026 - INFO [      main] : send service supplier apply: add config size=4
0804 13:33:29.026 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 13:33:29.208 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 13:33:29.208 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 13:33:29.208 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
0804 13:43:55.915 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1778/1916409354@540c464
0804 13:43:56.036 - INFO [      main] : send service supplier apply: add config size=4
0804 13:43:56.037 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 13:43:56.052 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 13:43:56.052 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 13:43:56.052 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
0804 13:52:50.960 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1705/1934331115@583453e9
0804 13:52:51.015 - INFO [      main] : send service supplier apply: add config size=4
0804 13:52:51.015 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 13:52:51.026 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 13:52:51.026 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 13:52:51.026 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
0804 14:01:05.298 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1791/290899513@6a9aca88
0804 14:01:05.418 - INFO [      main] : send service supplier apply: add config size=4
0804 14:01:05.418 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 14:01:05.437 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 14:01:05.437 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 14:01:05.437 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
0804 14:35:19.028 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1792/1697758242@12090240
0804 14:35:19.160 - INFO [      main] : send service supplier apply: add config size=4
0804 14:35:19.160 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 14:35:19.180 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 14:35:19.181 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 14:35:19.181 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
0804 14:41:19.951 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1795/951253103@67d5ed38
0804 14:41:20.112 - INFO [      main] : send service supplier apply: add config size=4
0804 14:41:20.113 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 14:41:20.146 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 14:41:20.146 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 14:41:20.146 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
0804 14:45:53.078 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1793/1986857915@ebdbe08
0804 14:45:53.198 - INFO [      main] : send service supplier apply: add config size=4
0804 14:45:53.198 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 14:45:53.215 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 14:45:53.215 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 14:45:53.216 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
0804 14:53:58.522 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1717/1569909921@3f9ca3f4
0804 14:53:58.586 - INFO [      main] : send service supplier apply: add config size=4
0804 14:53:58.587 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 14:53:58.597 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 14:53:58.598 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 14:53:58.598 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
0804 14:58:57.823 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1793/882135099@6f1672f6
0804 14:58:57.945 - INFO [      main] : send service supplier apply: add config size=4
0804 14:58:57.946 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 14:58:57.963 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 14:58:57.963 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 14:58:57.964 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
0804 15:43:53.971 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1823/734093216@6d2c5eca
0804 15:43:54.102 - INFO [      main] : send service supplier apply: add config size=4
0804 15:43:54.102 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 15:43:54.121 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 15:43:54.121 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 15:43:54.122 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
0804 15:50:13.584 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1812/1211442413@71fbe971
0804 15:50:13.715 - INFO [      main] : send service supplier apply: add config size=4
0804 15:50:13.716 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 15:50:13.734 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 15:50:13.734 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 15:50:13.734 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
0804 16:01:40.787 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1796/1258639355@1b2b488d
0804 16:01:41.070 - INFO [      main] : send service supplier apply: add config size=4
0804 16:01:41.070 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 16:01:41.104 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 16:01:41.105 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 16:01:41.105 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
0804 16:31:32.504 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1793/1570680236@9e51691
0804 16:31:32.623 - INFO [      main] : send service supplier apply: add config size=4
0804 16:31:32.623 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 16:31:32.640 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 16:31:32.640 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 16:31:32.640 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
0804 16:47:26.405 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1792/985089646@37401140
0804 16:47:26.546 - INFO [      main] : send service supplier apply: add config size=4
0804 16:47:26.547 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 16:47:26.569 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 16:47:26.569 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 16:47:26.570 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
0804 17:04:28.255 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1792/1241451939@5c5ef8f5
0804 17:04:28.385 - INFO [      main] : send service supplier apply: add config size=4
0804 17:04:28.385 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 17:04:28.407 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 17:04:28.408 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 17:04:28.408 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
0804 17:39:19.077 - INFO [      main] : set send service supplier: supplier=com.tipray.dlp.init.FtpInitRunner$$Lambda$1792/1475922031@2951e193
0804 17:39:19.339 - INFO [      main] : send service supplier apply: add config size=4
0804 17:39:19.339 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3001, refDevId=null, osType=1, ipPorts=[ftp://************:20182], user=server12345, secret=true)]
0804 17:39:19.359 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3002, refDevId=null, osType=1, ipPorts=[ftp://************:20186], user=server12345, secret=true)]
0804 17:39:19.359 - INFO [      main] : Ftp add config=[FtpConfig(devType=11, devId=3003, refDevId=null, osType=1, ipPorts=[ftp://fe80::3fff:69d6:b50f:26a2:20182, ftp://***************:20182], user=server12345, secret=true)]
0804 17:39:19.359 - INFO [      main] : Ftp add config=[FtpConfig(devType=172, devId=6401, refDevId=null, osType=1, ipPorts=[ftp://127.0.0.1:20187], user=server12345, secret=true)]
