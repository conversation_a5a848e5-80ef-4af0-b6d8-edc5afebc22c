<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dlp</artifactId>
        <groupId>com.tipray</groupId>
        <version>3.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <description>审批中间件：负责与审批服务器进行同步数据、更新策略等业务</description>

    <artifactId>tipray-trwfe-dlp</artifactId>
    <!--	<packaging>war</packaging>-->
    <packaging>jar</packaging>
    <properties>
        <skipTests>true</skipTests>
        <!--maven.build.timestamp保存了maven编译时间戳-->
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <!--本地包-->
        <dependency>
            <groupId>com.tipray.cloud</groupId>
            <artifactId>tp-cloud-api</artifactId>
            <version>0.1</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/src/main/resources/libs/tp-cloud-api-0.1.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-logging</artifactId>
            <version>1.0</version>
        </dependency>

        <!--dlp工具包-->
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-tools</artifactId>
            <version>1.0</version>
        </dependency>

        <!--dlp生效策略计算-->
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-effectiveStg</artifactId>
            <version>1.0</version>
        </dependency>

        <!--dlp生效模块计算-->
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-register</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <!--以war打包的话，要加上这段-->
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <!--				<exclusion>-->
                <!--					<artifactId>spring-boot-starter-tomcat</artifactId>-->
                <!--					<groupId>org.springframework.boot</groupId>-->
                <!--				</exclusion>-->
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-yaml</artifactId>
        </dependency>

        <!--socket-->
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-communication</artifactId>
            <version>1.0</version>
        </dependency>

        <!--RC4-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-buffer</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>protobuf-java</artifactId>
                    <groupId>com.google.protobuf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.5.0</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.mybatis.spring.boot</groupId>-->
<!--            <artifactId>mybatis-spring-boot-starter</artifactId>-->
<!--            <version>2.1.3</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>2.0</version>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>

        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>5.13.0</version>
        </dependency>

        <!-- 分页工具 -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>5.1.10</version>
        </dependency>

        <!-- Base64编码解码 -->
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
        </dependency>
        <!--解析exe数字签名信息-->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk18on</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.baomidou/mybatis-plus-generator -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.5.4</version>
        </dependency>
        <!--以war打包的话，要加上这段-->
        <!--		<dependency>-->
        <!--			<groupId>javax.servlet</groupId>-->
        <!--			<artifactId>javax.servlet-api</artifactId>-->
        <!--			<version>3.1.0</version>-->
        <!--			<scope>provided</scope>-->
        <!--		</dependency>-->

        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
            <version>6.4.6</version>
        </dependency>
        <!--<dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-modules-base</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>-->
        <!--<dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-modules-base</artifactId>
            <version>1.0</version>
        </dependency>-->
    </dependencies>

    <build>
        <finalName>dlp-trwfe</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>

