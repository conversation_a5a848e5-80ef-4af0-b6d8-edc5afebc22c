<?xml version="1.0" encoding="UTF-8" ?>
<!-- 日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出 -->
<!-- scan:当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true -->
<!-- scanPeriod:设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。 -->
<!-- debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 -->
<configuration scan="false" debug="false">
    <contextName>dlp</contextName>
    <!-- 定义全局变量 -->
    <property name="log.pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} -%5p ${PID:-} [%15.30t] %-25.90C{15} : %m%n"/>
    <property name="log.dir" value="${catalina.home}/TRDLP/logs"/>
    <property name="log.file.number" value="10"/>
    <property name="log.file.size" value="20MB"/>
    <property name="log.file.total.size" value="2GB"/>
    <property name="log.file.suffix" value=".%d{yyyyMMdd}.%i.log"/>
    <property name="log.additivity" value="false"/>

    <!--输出到控制台-->
    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <!--此日志appender是为开发使用，只配置最底级别，控制台输出的日志级别是大于或等于此级别的日志信息-->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>info</level>
        </filter>
        <encoder>
            <Pattern>${log.pattern}</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/info.log</file>
        <!-- 时间滚动输出 level为 DEBUG、info、warn等 日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>NEUTRAL</onMismatch>
        </filter>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <!--日志文件输出格式-->
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 归档日志命名规则 -->
            <fileNamePattern>${log.dir}/info${log.file.suffix}</fileNamePattern>
            <!--单个文件大小，超过则生成新文件-->
            <maxFileSize>${log.file.size}</maxFileSize>
            <!--日志文件保留天数-->
            <maxHistory>${log.file.number}</maxHistory>
            <!--日志文件总大小-->
            <totalSizeCap>${log.file.total.size}</totalSizeCap>
        </rollingPolicy>
    </appender>
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/error.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>NEUTRAL</onMismatch>
        </filter>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/error${log.file.suffix}</fileNamePattern>
            <maxFileSize>${log.file.size}</maxFileSize>
            <maxHistory>${log.file.number}</maxHistory>
            <totalSizeCap>${log.file.total.size}</totalSizeCap>
        </rollingPolicy>
    </appender>
    <appender name="HTTP_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/http.log</file>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/http${log.file.suffix}</fileNamePattern>
            <maxFileSize>${log.file.size}</maxFileSize>
            <maxHistory>${log.file.number}</maxHistory>
            <totalSizeCap>${log.file.total.size}</totalSizeCap>
        </rollingPolicy>
    </appender>
    <appender name="SOCKET_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/socket.log</file>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/socket${log.file.suffix}</fileNamePattern>
            <maxFileSize>${log.file.size}</maxFileSize>
            <maxHistory>${log.file.number}</maxHistory>
            <totalSizeCap>${log.file.total.size}</totalSizeCap>
        </rollingPolicy>
    </appender>
    <appender name="VNC_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/vnc.log</file>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/vnc${log.file.suffix}</fileNamePattern>
            <maxFileSize>${log.file.size}</maxFileSize>
            <maxHistory>${log.file.number}</maxHistory>
            <totalSizeCap>${log.file.total.size}</totalSizeCap>
        </rollingPolicy>
    </appender>
    <appender name="DB_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/db.log</file>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/db${log.file.suffix}</fileNamePattern>
            <maxFileSize>${log.file.size}</maxFileSize>
            <maxHistory>${log.file.number}</maxHistory>
            <totalSizeCap>${log.file.total.size}</totalSizeCap>
        </rollingPolicy>
    </appender>

    <root level="INFO">
        <appender-ref ref="stdout"/>
        <appender-ref ref="INFO_FILE"/>
        <appender-ref ref="ERROR_FILE"/>
    </root>
    <logger name="HTTP_LOG" level="WARN">
        <additive>${log.additivity}</additive>
        <appender-ref ref="HTTP_FILE"/>
    </logger>
    <logger name="SOCKET_LOG" level="INFO">
        <additive>${log.additivity}</additive>
        <appender-ref ref="SOCKET_FILE"/>
    </logger>
    <logger name="com.tipray.cloud.admin.mapper" level="WARN">
        <additive>${log.additivity}</additive>
        <appender-ref ref="DB_FILE"/>
    </logger>
</configuration>
