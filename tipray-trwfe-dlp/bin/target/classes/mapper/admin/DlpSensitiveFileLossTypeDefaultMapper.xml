<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.DlpSensitiveFileLossTypeDefaultMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.DlpSensitiveFileLossTypeDefault">
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="alarm_type" property="alarmType"  jdbcType="VARCHAR" />
    <result column="alarm_desc" property="alarmDesc"  jdbcType="VARCHAR" />
    <result column="alarm_desc_key" property="alarmDescKey"  jdbcType="VARCHAR" />
    <result column="deleted" property="deleted" jdbcType="INTEGER" />
    <result column="modify_ver" property="modifyVer" jdbcType="BIGINT" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    SELECT  id, alarm_type, alarm_desc,
    alarm_desc_key, deleted, modify_ver
    FROM sensitive_file_loss_type_default
    <where>
      <if test="modifyVer != null">
        and modify_ver > #{modifyVer}
      </if>
    </where>
  </select>

  <select id="allList" resultMap="BaseResultMap">
    SELECT  id, alarm_type, alarm_desc,
    alarm_desc_key, deleted, modify_ver
    FROM sensitive_file_loss_type_default
    WHERE deleted = 0
  </select>

</mapper>
