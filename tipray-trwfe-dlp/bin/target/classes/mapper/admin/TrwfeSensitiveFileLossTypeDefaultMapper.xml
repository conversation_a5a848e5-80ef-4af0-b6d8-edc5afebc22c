<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeSensitiveFileLossTypeDefaultMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.TrwfeSensitiveFileLossTypeDefault">
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="alarm_type" property="alarmType"  jdbcType="VARCHAR" />
    <result column="alarm_desc" property="alarmDesc"  jdbcType="VARCHAR" />
    <result column="alarm_desc_key" property="alarmDescKey"  jdbcType="VARCHAR" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    SELECT id, alarm_type, alarm_desc,alarm_desc_key
    FROM ext_sensitive_way
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into
    ext_sensitive_way(id, alarm_type, alarm_desc,alarm_desc_key)
    values
    <foreach collection="addList" item="item" index="index" separator=",">
      (
        #{item.id,jdbcType=VARCHAR},#{item.alarmType,jdbcType=VARCHAR},
        #{item.alarmDesc,jdbcType=VARCHAR},#{item.alarmDescKey,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List">
    update ext_sensitive_way
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="alarm_type = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.alarmType!=null">
            when id = #{item.id} then #{item.alarmType}
          </if>
        </foreach>
      </trim>

      <trim prefix="alarm_desc = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.alarmDesc != null">
            when id = #{item.id} then #{item.alarmDesc}
          </if>
        </foreach>
      </trim>

      <trim prefix="alarm_desc_key = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.alarmDescKey != null">
            when id = #{item.id} then #{item.alarmDescKey}
          </if>
        </foreach>
      </trim>
    </trim>
    where
    id in
    <foreach collection="updateList" index="index" item="item"
             separator="," open="(" close=")">
      #{item.id}
    </foreach>
  </update>

  <delete id="batchDelete" parameterType="java.util.List">
    delete from ext_sensitive_way
    where id in
    <foreach  item="item" collection="deleteIds" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

</mapper>
