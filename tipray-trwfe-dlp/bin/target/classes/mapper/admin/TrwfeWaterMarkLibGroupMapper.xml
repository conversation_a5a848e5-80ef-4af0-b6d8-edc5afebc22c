<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeWaterMarkLibGroupMapper" >

  <resultMap type="com.tipray.cloud.admin.entity.WaterMarkLibGroup" id="resultMap">
    <result property="id" column="id"/>
    <result property="name" column="name"/>
    <result property="parentId" column="parent_id"/>
  </resultMap>

  <select id="list" resultMap="resultMap">
        select id, name, parent_id from water_mark_lib_group
  </select>

  <select id="selectByPrimaryKeys" resultType="java.lang.String">
    select id
    from water_mark_lib_group
    where id  in
      <foreach  item="item" collection="ids" index="index"  open="(" separator="," close=")">
        #{item}
      </foreach>
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into
    water_mark_lib_group( id, name, parent_id )
    values
    <foreach collection="addList" item="item" index="index" separator=",">
      (
        #{item.id,jdbcType=VARCHAR},#{item.name,jdbcType=VARCHAR},#{item.parentId,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List">
    update water_mark_lib_group
    <trim prefix="set" suffixOverrides=",">

      <trim prefix="name = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.name!=null">
            when id = #{item.id} then #{item.name}
          </if>
        </foreach>
      </trim>


      <trim prefix="parent_id = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.parentId != null">
            when id = #{item.id} then #{item.parentId}
          </if>
        </foreach>
      </trim>

    </trim>

    where
    id in
    <foreach collection="updateList" index="index" item="item"
             separator="," open="(" close=")">
      #{item.id}
    </foreach>
  </update>

  <delete id="batchDelete" parameterType="java.util.List">
    delete from water_mark_lib_group
    where id in
    <foreach  item="item" collection="deleteIds" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

</mapper>
