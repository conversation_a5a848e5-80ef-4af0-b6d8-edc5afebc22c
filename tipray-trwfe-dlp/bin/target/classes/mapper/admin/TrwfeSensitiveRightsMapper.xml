<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeSensitiveRightsMapper" >

  <select id="list" resultType="com.tipray.cloud.admin.entity.TrwfeSensitiveRights">
    SELECT  user_id , dept_id , is_right
    FROM ext_sensitive_right
  </select>


  <insert id="batchInsert" parameterType="java.util.List">
    insert into
    ext_sensitive_right (user_id , dept_id , is_right )
    values
    <foreach collection="addList" item="item" index="index" separator=",">
      (
        #{item.userId,jdbcType=VARCHAR}, #{item.deptId,jdbcType=VARCHAR},
        #{item.isRight,jdbcType=INTEGER}
      )
    </foreach>
  </insert>


  <delete id="deleteAll">
    delete from ext_sensitive_right
  </delete>

</mapper>
