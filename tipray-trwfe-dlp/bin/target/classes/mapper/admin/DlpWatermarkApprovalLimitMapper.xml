<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.DlpWatermarkApprovalLimitMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.DlpWatermarkApprovalLimit">
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="type" property="type"  jdbcType="INTEGER" />
    <result column="type_desc" property="typeDesc"  jdbcType="VARCHAR" />
    <result column="type_desc_key" property="typeDescKey"  jdbcType="VARCHAR" />
    <result column="deleted" property="deleted" jdbcType="INTEGER" />
    <result column="modify_ver" property="modifyVer" jdbcType="BIGINT" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    SELECT  id, type, type_desc,
    type_desc_key, deleted, modify_ver
    FROM watermark_approval_limit
    <where>
      <if test="modifyVer != null">
        and modify_ver > #{modifyVer}
      </if>
    </where>
  </select>

  <select id="allList" resultMap="BaseResultMap">
    SELECT  id, type, type_desc,
            type_desc_key, deleted, modify_ver
    FROM watermark_approval_limit
    WHERE deleted = 0
  </select>

</mapper>
