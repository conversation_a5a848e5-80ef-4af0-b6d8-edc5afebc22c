<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.DlpUnregisteredModuleMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.DlpUnregisteredModule">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <result column="module_no" property="moduleNo"  jdbcType="BIGINT" />
    <result column="ciphertext" property="ciphertext"  jdbcType="VARCHAR" />
    <result column="file_guid" property="fileGuid" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectByModuleNo" resultMap="BaseResultMap">
    select id, module_no, file_guid
    from unregistered_module
    where module_no  = 60001
  </select>

</mapper>
