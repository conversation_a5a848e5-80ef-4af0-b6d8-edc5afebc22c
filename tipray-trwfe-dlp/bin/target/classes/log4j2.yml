# 共有8个级别，按照从低到高为：ALL < TRACE < DEBUG < INFO < WARN < ERROR < FATAL < OFF。
# Configuration后面的status，用于设置log4j2自身内部的信息输出，可以不设置，当设置成trace时，你会看到log4j2内部各种详细输出
# monitorInterval：Log4j能够自动检测修改配置 文件和重新配置本身，设置间隔秒数
Configuration:
   status: WARN
   monitorInterval: 60000
  
   Properties: # 定义全局变量
      Property: # 缺省配置（用于开发环境）。其他环境需要在VM参数中指定，如下：
         - name: log.pattern
           value: "%d{yyyy-MM-dd HH:mm:ss.SSS} -%5p ${PID:-} [%15.15t] %-30.30C{1.} : %m%n"
         - name: log.dir
           value: ${sys:catalina.home}/TRDLP/logs
           
   Appenders:
      Console: #输出到控制台
         name: stdout
         target: SYSTEM_OUT
         PatternLayout:
            pattern: ${log.pattern}
            
      RollingFile:
         - name: INFO_FILE
           fileName: ${log.dir}/info.log
           filePattern: ${log.dir}/info.log%d{yyyyMMdd}
           PatternLayout:
              pattern: ${log.pattern}
           Filters:
              ThresholdFilter:
                 - level: WARN
                   onMatch: DENY
                   onMismatch: NEUTRAL
                 - level: DEBUG
                   onMatch: ACCEPT
                   onMismatch: DENY
           Policies:
              TimeBasedTriggeringPolicy: #按天分类
                 modulate: true
                 interval: 1
           DefaultRolloverStrategy:   #文件最多保存个数
              max: 5
         - name: ERROR_FILE
           fileName: ${log.dir}/error.log
           filePattern: ${log.dir}/error.log%d{yyyyMMdd}
           PatternLayout:
              pattern: ${log.pattern}
           Filters:
              ThresholdFilter:
                 -  level: WARN
                    onMatch: ACCEPT
                    onMismatch: DENY
           Policies:
              TimeBasedTriggeringPolicy: #按天分类
                 modulate: true
                 interval: 1
           DefaultRolloverStrategy:   #文件最多保存个数
              max: 5
         - name: HTTP_FILE
           fileName: ${log.dir}/http.log
           filePattern: ${log.dir}/http.log%d{yyyyMMdd}
           PatternLayout:
              pattern: ${log.pattern}
           Policies:
              TimeBasedTriggeringPolicy: #按天分类
                 modulate: true
                 interval: 1
           DefaultRolloverStrategy:   #文件最多保存个数
              max: 5
         - name: SOCKET_FILE
           fileName: ${log.dir}/socket.log
           filePattern: ${log.dir}/socket.log%d{yyyyMMdd}
           PatternLayout:
              pattern: ${log.pattern}
           Policies:
              TimeBasedTriggeringPolicy: #按天分类
                 modulate: true
                 interval: 1
           DefaultRolloverStrategy:   #文件最多保存个数
              max: 5
         - name: DB_FILE
           fileName: ${log.dir}/db.log
           filePattern: ${log.dir}/db.log%d{yyyyMMdd}
           PatternLayout:
             pattern: ${log.pattern}
           Policies:
             TimeBasedTriggeringPolicy: #按天分类
               modulate: true
               interval: 1
           DefaultRolloverStrategy:   #文件最多保存个数
             max: 5
#      JDBC:
#         - name: DATABASE
#           tableName: oplog
#           ConnectionFactory:
#             class: com.tipray.dlp.util.DBUtil
#             method: getDataSource
#           Column:
#             - name: createTime
#               pattern: "%d{yyyy-MM-dd hh:mm:ss}"


   Loggers:
      Root:
         level: INFO
         AppenderRef:
            - ref: stdout
            - ref: INFO_FILE
            - ref: ERROR_FILE
      Logger:
         - name: HTTP_LOG
           level: DEBUG
           additivity: false
           AppenderRef:
             - ref: HTTP_FILE
         - name: SOCKET_LOG
           level: DEBUG
           additivity: true
           AppenderRef:
             - ref: SOCKET_FILE

#         - name: DB_LOG
#           level: DEBUG
#           additivity: false
#           AppenderRef:
#             - ref: DATABASE
         
         # 数据库日志输出,可以查看执行SQL
         - name: com.tipray.cloud.admin.mapper
           level: WARN
           additivity: true
           AppenderRef:
             - ref: DB_FILE
             
          
          
