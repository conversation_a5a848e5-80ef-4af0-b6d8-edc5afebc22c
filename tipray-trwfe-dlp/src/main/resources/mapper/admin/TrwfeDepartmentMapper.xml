<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeDepartmentMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.TrwfeDepartment">
    <id column="ID_" property="id" jdbcType="VARCHAR"/>
    <result column="NAME_" property="name"  jdbcType="VARCHAR" />
    <result column="PARENT_ID_" property="parentId"  jdbcType="VARCHAR" />
    <result column="DIRECTOR_" property="director"  jdbcType="VARCHAR" />
    <result column="BRANCH_LEADER_" property="branchLeader"  jdbcType="VARCHAR" />
    <result column="SORT_NUM_" property="sortNum"  jdbcType="BIGINT" />
    <result column="COMMON_INHERIT_" property="commonInherit"  jdbcType="VARCHAR" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    SELECT  ID_ , NAME_ , PARENT_ID_
    FROM act_id_department
    where ID_ != 0
  </select>

  <sql id="Base_Column_List">
    ID_ , NAME_ , PARENT_ID_
  </sql>

  <select id="selectByPrimaryKeys" resultType="java.lang.String">
    select ID_
    from act_id_department
    where ID_  in
    <foreach  item="item" collection="ids" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into
    act_id_department(ID_ ,REV_, NAME_ , PARENT_ID_, DIRECTOR_, BRANCH_LEADER_, SORT_NUM_,COMMON_INHERIT_)
    values
    <foreach collection="addList" item="item" index="index" separator=",">
      (
      #{item.id,jdbcType=VARCHAR} , 1, #{item.name,jdbcType=VARCHAR} ,#{item.parentId,jdbcType=VARCHAR},
      #{item.director,jdbcType=VARCHAR},#{item.branchLeader,jdbcType=VARCHAR},#{item.sortNum,jdbcType=BIGINT},#{item.commonInherit,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List">
    update act_id_department
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="NAME_ = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.name!=null">
            when ID_ = #{item.id} then #{item.name}
          </if>
        </foreach>
      </trim>

      <trim prefix="PARENT_ID_ = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.parentId != null">
            when ID_ = #{item.id} then #{item.parentId}
          </if>
        </foreach>
      </trim>

      <trim prefix="DIRECTOR_ = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          when ID_ = #{item.id} then #{item.director}
        </foreach>
      </trim>

      <trim prefix="BRANCH_LEADER_ = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          when ID_ = #{item.id} then #{item.branchLeader}
        </foreach>
      </trim>

      <trim prefix="SORT_NUM_ = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          when ID_ = #{item.id} then #{item.sortNum}
        </foreach>
      </trim>

    </trim>

    where
    ID_ in
    <foreach collection="updateList" index="index" item="item"
             separator="," open="(" close=")">
      #{item.id}
    </foreach>
  </update>

  <delete id="batchDelete" parameterType="java.util.List">
    delete from act_id_department
    where ID_ in
    <foreach  item="item" collection="deleteIds" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

</mapper>
