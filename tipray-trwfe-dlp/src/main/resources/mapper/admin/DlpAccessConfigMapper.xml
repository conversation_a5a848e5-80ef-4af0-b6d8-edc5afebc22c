<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.cloud.admin.mapper.DlpAccessConfigMapper">
    <resultMap id="resultMap" type="com.tipray.cloud.admin.entity.AccessConfig">
        <result column="access_mode" property="accessMode" jdbcType="INTEGER" />
        <result column="group_id" property="groupId" jdbcType="BIGINT" />
        <result column="lowest_version" property="lowestVersion" jdbcType="VARCHAR" />
    </resultMap>

    <select id="list" parameterType="java.lang.String" resultMap="resultMap">
		select access_mode, group_id, lowest_version from access_config where pc_or_mobile = 2
	</select>


</mapper>
