<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeGroupMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.TrwfeGroup">
    <id column="ID_" property="id" jdbcType="VARCHAR"/>
    <result column="NAME_" property="name"  jdbcType="VARCHAR" />
    <result column="REV_" property="rev"  jdbcType="VARCHAR" />
    <result column="TYPE_" property="type"  jdbcType="VARCHAR" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    SELECT  ID_ , NAME_ , TYPE_
    FROM act_id_group
    where ID_ not in ('cashier','chairman','deptLeader','employee','financeDirector','hrLeader','manager','vicemanager')
  </select>

  <sql id="Base_Column_List">
    ID_ , NAME_ , TYPE_
  </sql>

  <select id="selectByPrimaryKeys" resultType="java.lang.String">
    select ID_
    from act_id_group
    where ID_  in
    <foreach  item="item" collection="ids" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into
    act_id_group(ID_ ,REV_, NAME_ , TYPE_)
    values
    <foreach collection="addList" item="item" index="index" separator=",">
      (
      #{item.id,jdbcType=VARCHAR} , #{item.rev,jdbcType=INTEGER}, #{item.name,jdbcType=VARCHAR} ,#{item.type,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List">
    update act_id_group
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="REV_ = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.rev!=null">
            when ID_ = #{item.id} then #{item.rev}
          </if>
        </foreach>
      </trim>

      <trim prefix="NAME_ = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.name!=null">
            when ID_ = #{item.id} then #{item.name}
          </if>
        </foreach>
      </trim>
      <trim prefix="TYPE_ = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.name!=null">
            when ID_ = #{item.id} then #{item.type}
          </if>
        </foreach>
      </trim>
    </trim>
    where
    ID_ in
    <foreach collection="updateList" index="index" item="item"
             separator="," open="(" close=")">
      #{item.id}
    </foreach>
  </update>

  <delete id="batchDelete" parameterType="java.util.List">
    delete from act_id_group
    where ID_ in
    <foreach  item="item" collection="deleteIds" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

</mapper>
