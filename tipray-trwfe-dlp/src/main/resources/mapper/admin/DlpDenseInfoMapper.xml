<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.DlpDenseInfoMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.DlpDenseInfo">
    <result column="encrypt_level" property="encryptLevel"  jdbcType="INTEGER" />
    <result column="dense_name" property="denseName"  jdbcType="VARCHAR" />
    <result column="dense_name_key" property="denseNameKey"  jdbcType="VARCHAR" />
    <result column="deleted" property="deleted" jdbcType="INTEGER" />
    <result column="modify_ver" property="modifyVer" jdbcType="BIGINT" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    SELECT  encrypt_level, dense_name, dense_name_key, deleted, modify_ver
    FROM dense_info
    <where>
      <if test="modifyVer != null">
        and modify_ver > #{modifyVer}
      </if>
    </where>
  </select>

</mapper>
