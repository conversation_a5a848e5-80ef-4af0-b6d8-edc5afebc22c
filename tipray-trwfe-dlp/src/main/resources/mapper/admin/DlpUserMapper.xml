<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.DlpUserMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.DlpUser">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <result column="account" property="account"  jdbcType="VARCHAR" />
    <result column="name" property="name"  jdbcType="VARCHAR" />
    <result column="password" property="password"  jdbcType="VARCHAR" />
    <result column="email" property="email"  jdbcType="VARCHAR" />
    <result column="phone" property="phone"  jdbcType="VARCHAR" />
    <result column="flag" property="flag"  jdbcType="INTEGER"/>
    <result column="source" property="source"  jdbcType="INTEGER"/>
    <result column="active" property="active"  jdbcType="INTEGER"/>
    <result column="sid" property="sid"  jdbcType="VARCHAR" />
    <result column="deleted" property="deleted" jdbcType="INTEGER" />
    <result column="modify_ver" property="modifyVer" jdbcType="BIGINT" />
    <result column="director_id" property="directorId" jdbcType="BIGINT" />
    <result column="valid_start_date" property="validStartDate" />
    <result column="valid_end_date" property="validEndDate" jdbcType="BIGINT" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    SELECT  id, account, name, password,
      email, phone, sid, source, flag, active, deleted, modify_ver, director_id, valid_start_date, valid_end_date
    FROM "user"
    <where>
      <if test="modifyVer != null">
        and modify_ver > #{modifyVer}
      </if>
    </where>
  </select>

  <select id="allUser" resultMap="BaseResultMap">
    SELECT  id, account, name,
      password,
      email, phone, sid, source, flag, active, deleted, modify_ver, director_id, valid_start_date, valid_end_date
    FROM "user"
    WHERE deleted = 0
  </select>

  <select id="selectByAccount" resultMap="BaseResultMap">
     select id, account, name,
      password,
      email, phone, sid, source, flag, active, deleted, modify_ver, director_id, valid_start_date, valid_end_date
    from "user"
    where account = #{account} and active = 1 and deleted = 0
  </select>

  <select id="selectByPhone" resultMap="BaseResultMap">
     select id, account, name,
       password,
       email, phone, sid, source, flag, active, deleted, modify_ver, director_id, valid_start_date, valid_end_date
    from "user"
    where phone = #{phone} and active = 1 and deleted = 0
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select id, account, name,
      password,
      email, phone, sid, source, flag, active, deleted, modify_ver, director_id, valid_start_date, valid_end_date
    from  "user"
    where id = #{id,jdbcType=INTEGER} and deleted = 0
  </select>

</mapper>
