<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.DlpDepartmentUserMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.DlpDepartmentUser">
    <id column="user_id" property="userId" jdbcType="BIGINT"/>
    <result column="group_id" property="departmentId"  jdbcType="BIGINT" />
    <result column="deleted" property="deleted" jdbcType="INTEGER" />
    <result column="modify_ver" property="modifyVer" jdbcType="BIGINT" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    SELECT  gu.user_id , gu.group_id, CASE  WHEN u.deleted=1 OR g.deleted=1 THEN 1 ELSE  gu.deleted END AS deleted, gu.modify_ver
    FROM rel_group_user gu JOIN "user" u ON u.id = gu.user_id JOIN group_info g ON gu.group_id = g.id
    <where>
      <if test="modifyVer != null">
        and gu.modify_ver > #{modifyVer}
      </if>
    </where>
  </select>

  <select id="allDeptUser" resultMap="BaseResultMap">
    SELECT  gu.user_id , gu.group_id, gu.deleted, gu.modify_ver
    FROM rel_group_user gu join "user" u on u.id = gu.user_id JOIN group_info g ON gu.group_id = g.id
    where gu.deleted = 0 and u.deleted = 0 and g.deleted = 0
  </select>

</mapper>
