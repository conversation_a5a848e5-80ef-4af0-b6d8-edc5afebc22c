<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.DlpDepartmentMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.DlpDepartment">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <result column="name" property="name"  jdbcType="VARCHAR" />
    <result column="parent_id" property="parentId"  jdbcType="INTEGER" />
    <result column="deleted" property="deleted" jdbcType="INTEGER" />
    <result column="modify_ver" property="modifyVer" jdbcType="BIGINT" />
    <result column="manager" property="manager" jdbcType="BIGINT" />
    <result column="branch_leader" property="branchLeader" jdbcType="BIGINT" />
    <result column="sort_num" property="sortNum" jdbcType="BIGINT" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    SELECT  id, name, parent_id, deleted, modify_ver,sort_num,
    (SELECT GROUP_CONCAT(rgp.user_id SEPARATOR ',') FROM rel_group_position rgp WHERE g.id = rgp.group_id and TYPE = 1 and deleted = 0) manager ,
    (SELECT GROUP_CONCAT(rgp.user_id SEPARATOR ',') FROM rel_group_position rgp WHERE g.id = rgp.group_id and TYPE = 2 and deleted = 0) branch_leader
    FROM group_info g
    <where>
      <if test="modifyVer != null">
        and modify_ver > #{modifyVer}
      </if>
    </where>
  </select>


  <select id="allDept" resultMap="BaseResultMap">
    SELECT  id, name, parent_id, deleted, modify_ver,sort_num,
            (SELECT GROUP_CONCAT(rgp.user_id SEPARATOR ',') FROM rel_group_position rgp WHERE g.id = rgp.group_id and TYPE = 1 and deleted = 0) manager ,
            (SELECT GROUP_CONCAT(rgp.user_id SEPARATOR ',') FROM rel_group_position rgp WHERE g.id = rgp.group_id and TYPE = 2 and deleted = 0) branch_leader
    FROM group_info g
    where deleted = 0
  </select>

  <select id="selectByUserId" resultMap="BaseResultMap">
    select id, name, parent_id, deleted, modify_ver,sort_num,
           (SELECT GROUP_CONCAT(rgp.user_id SEPARATOR ',') FROM rel_group_position rgp WHERE g.id = rgp.group_id and TYPE = 1 and deleted = 0) manager ,
           (SELECT GROUP_CONCAT(rgp.user_id SEPARATOR ',') FROM rel_group_position rgp WHERE g.id = rgp.group_id and TYPE = 2 and deleted = 0) branch_leader
    from group_info g
    where id  = (select group_id from rel_group_user where user_id = #{userId} and deleted = 0)
  </select>

  <select id="selectByTermId" resultMap="BaseResultMap">
    select id, name, parent_id, deleted, modify_ver,sort_num,
           (SELECT GROUP_CONCAT(rgp.user_id SEPARATOR ',') FROM rel_group_position rgp WHERE g.id = rgp.group_id and TYPE = 1 and deleted = 0) manager ,
           (SELECT GROUP_CONCAT(rgp.user_id SEPARATOR ',') FROM rel_group_position rgp WHERE g.id = rgp.group_id and TYPE = 2 and deleted = 0) branch_leader
    from group_info g
    where id  = (select group_id from rel_group_terminal where term_id = #{termId} and deleted = 0)
  </select>

  <select id="selectById" resultMap="BaseResultMap">
    select id, name, parent_id, deleted, modify_ver,sort_num,
           (SELECT GROUP_CONCAT(rgp.user_id SEPARATOR ',') FROM rel_group_position rgp WHERE g.id = rgp.group_id and TYPE = 1 and deleted = 0) manager ,
           (SELECT GROUP_CONCAT(rgp.user_id SEPARATOR ',') FROM rel_group_position rgp WHERE g.id = rgp.group_id and TYPE = 2 and deleted = 0) branch_leader
    from group_info g
    where id  = #{id}
  </select>

</mapper>
