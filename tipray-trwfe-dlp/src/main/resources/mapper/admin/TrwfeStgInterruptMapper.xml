<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeStgInterruptMapper">

    <resultMap id="StgDefMap" type="com.tipray.cloud.admin.entity.StgInterrupt">
        <result property="id" column="id"/>
        <result property="strategyTypeNumber" column="stg_type_number"/>
        <result property="objectType" column="object_type"/>
        <result property="objectId" column="object_id"/>
    </resultMap>


    <select id="listByStrategyType" resultMap="StgDefMap">
      select
      id, stg_type_number, object_type, object_id
      from stg_obj_interrupt
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into stg_obj_interrupt(id, stg_type_number, object_type, object_id)
        values
        <foreach collection="addList" item="item" index="index" separator=",">
            (
            #{item.id}, #{item.strategyTypeNumber}, #{item.objectType}, #{item.objectId}
            )
        </foreach>
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        delete from stg_obj_interrupt
        where
        <foreach collection="deleteList" item="item" index="index" separator="or">
            (
            id = #{item.id,jdbcType=BIGINT}
            )
        </foreach>
    </delete>



</mapper>
