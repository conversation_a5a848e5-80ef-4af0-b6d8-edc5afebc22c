<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.cloud.admin.mapper.DlpTerminalMapper">

	<resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.DLPTerminal">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="name" property="name"  jdbcType="VARCHAR" />
		<result column="version" property="version"  jdbcType="VARCHAR" />
		<result column="use_type" property="useType" jdbcType="INTEGER" />
	</resultMap>

	<select id="getByGuid" parameterType="java.lang.String" resultMap="BaseResultMap">
		select id,name,version,use_type from terminal where guid = #{value} and deleted = 0
	</select>

	<select id="getSpecifyAppByTerminalId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
		select specify_app from terminal_ext_mobile where term_id = #{termId}
	</select>

</mapper>
