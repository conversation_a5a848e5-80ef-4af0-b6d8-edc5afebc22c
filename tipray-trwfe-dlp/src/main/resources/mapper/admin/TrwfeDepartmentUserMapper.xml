<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeDepartmentUserMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.TrwfeDepartmentUser">
    <id column="USER_ID_" property="userId" jdbcType="VARCHAR"/>
    <result column="DEPT_ID_" property="departmentId"  jdbcType="VARCHAR" />
  </resultMap>


  <sql id="Base_Column_List">
    USER_ID_ , DEPT_ID_
  </sql>

  <select id="list" resultMap="BaseResultMap">
    SELECT  USER_ID_ , DEPT_ID_
    FROM act_id_userdept
     WHERE USER_ID_ not in ('admin','sysadmin','logadmin','secadmin')
  </select>

  <select id="selectByUserIds" resultType="java.lang.String">
    select USER_ID_
    from act_id_userdept
    where USER_ID_  in
    <foreach  item="item" collection="userIds" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into
    act_id_userdept (USER_ID_ , DEPT_ID_)
    values
    <foreach collection="addList" item="item" index="index" separator=",">
      (
        #{item.userId,jdbcType=VARCHAR} ,#{item.departmentId,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List">
    update act_id_userdept
    <trim prefix="set" suffixOverrides=",">

      <trim prefix="DEPT_ID_ = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
            when USER_ID_ = #{item.userId} then #{item.departmentId}
        </foreach>
      </trim>

    </trim>

    where
    USER_ID_ in
    <foreach collection="updateList" index="index" item="item"
             separator="," open="(" close=")">
      #{item.userId}
    </foreach>
  </update>

  <delete id="batchDelete" parameterType="java.util.List">
    delete from act_id_userdept
    where USER_ID_ in
    <foreach  item="item" collection="deleteIds" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

</mapper>