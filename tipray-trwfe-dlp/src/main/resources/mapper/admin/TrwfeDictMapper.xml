<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeDictMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.TrwfeDict">
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="lng" property="lng"  jdbcType="VARCHAR" />
    <result column="dict_key" property="dictKey"  jdbcType="VARCHAR" />
    <result column="dict_value" property="dictValue"  jdbcType="VARCHAR" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    SELECT id, lng, dict_key,dict_value
    FROM ext_dlp_i18n
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into
    ext_dlp_i18n(id, lng, dict_key,dict_value)
    values
    <foreach collection="addList" item="item" index="index" separator=",">
      (
        #{item.id,jdbcType=VARCHAR}, #{item.lng,jdbcType=VARCHAR},
        #{item.dictKey,jdbcType=VARCHAR},#{item.dictValue,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List">
    update ext_dlp_i18n
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="lng = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.lng!=null">
            when id = #{item.id} then #{item.lng}
          </if>
        </foreach>
      </trim>

      <trim prefix="dict_key = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.dictKey != null">
            when id = #{item.id} then #{item.dictKey}
          </if>
        </foreach>
      </trim>

      <trim prefix="dict_value = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.dictValue != null">
            when id = #{item.id} then #{item.dictValue}
          </if>
        </foreach>
      </trim>
    </trim>
    where
    id in
    <foreach collection="updateList" index="index" item="item"
             separator="," open="(" close=")">
      #{item.id}
    </foreach>
  </update>

  <delete id="batchDelete" parameterType="java.util.List">
    delete from ext_dlp_i18n
    where id in
    <foreach  item="item" collection="deleteIds" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

</mapper>
