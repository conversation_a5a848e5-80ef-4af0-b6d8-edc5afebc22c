<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.cloud.admin.mapper.DlpStgGroupMapper">

    <resultMap id="StgDefMap" type="com.tipray.cloud.admin.entity.StgGroup">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="objectType" column="object_type"/>
        <result property="groupType" column="group_type"/>
        <result property="info" column="info"/>
        <result property="active" column="active"/>
    </resultMap>


    <select id="list" parameterType="java.lang.String" resultMap="StgDefMap">
        select
            id, object_type, group_type, info, active
        from stg_group
        where object_type = 2 and active = 1 and group_type = 1 order by id asc
    </select>

</mapper>
