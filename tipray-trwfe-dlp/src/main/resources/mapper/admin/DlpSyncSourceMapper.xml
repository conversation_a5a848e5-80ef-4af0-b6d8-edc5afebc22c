<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.DlpSyncSourceMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.DlpSyncSource">
    <id column="sync_id" property="syncId" jdbcType="BIGINT"/>
    <result column="source_type" property="sourceType"  jdbcType="INTEGER" />
    <result column="name" property="name"  jdbcType="VARCHAR" />
    <result column="config" property="config"  jdbcType="VARCHAR" />
    <result column="login_able" property="loginAble"  jdbcType="INTEGER" />
<!--    <result column="active" property="active" jdbcType="INTEGER" />-->
    <result column="deleted" property="deleted" jdbcType="INTEGER" />
    <result column="modify_ver" property="modifyVer" jdbcType="BIGINT" />
  </resultMap>

  <select id="isTableExist" parameterType="string" resultType="int">
    SELECT COUNT(*) FROM information_schema.TABLES WHERE table_name = 'sync_source'
  </select>

  <select id="list" resultMap="BaseResultMap">
    SELECT  sync_id, source_type, name,  config , login_able , deleted, modify_ver
    FROM sync_source
    <where>
    <if test="modifyVer != null">
      and modify_ver > #{modifyVer}
    </if>
    </where>
  </select>


  <select id="allSyncSource" resultMap="BaseResultMap">
    SELECT  sync_id, source_type, name,  config , login_able , deleted, modify_ver
    FROM sync_source
  </select>

</mapper>
