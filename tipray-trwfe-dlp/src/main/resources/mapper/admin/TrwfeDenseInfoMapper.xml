<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeDenseInfoMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.TrwfeDenseInfo">
    <result column="levelId" property="encryptLevel"  jdbcType="INTEGER" />
    <result column="levelName" property="denseName"  jdbcType="VARCHAR" />
    <result column="levelNameKey" property="levelNameKey"  jdbcType="VARCHAR" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    SELECT  levelId , levelName, levelNameKey
    FROM ext_ld_secret_level
  </select>

  <select id="selectByPrimaryKeys" resultType="java.lang.Integer">
    select levelId
    from ext_ld_secret_level
    where levelId  in
    <foreach  item="item" collection="ids" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into
    ext_ld_secret_level(levelId , levelName, levelNameKey)
    values
    <foreach collection="addList" item="item" index="index" separator=",">
      (
      #{item.encryptLevel,jdbcType=INTEGER}, #{item.denseName,jdbcType=VARCHAR}, #{item.levelNameKey,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List">
    update ext_ld_secret_level
    <trim prefix="set" suffixOverrides=",">

      <trim prefix="levelName = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          when levelId = #{item.encryptLevel} then #{item.denseName}
        </foreach>
      </trim>
      <trim prefix="levelNameKey = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          when levelId = #{item.encryptLevel} then #{item.levelNameKey}
        </foreach>
      </trim>

    </trim>

    where
    levelId in
    <foreach collection="updateList" index="index" item="item"
             separator="," open="(" close=")">
      #{item.encryptLevel}
    </foreach>
  </update>

  <delete id="batchDelete" parameterType="java.util.List">
    delete from ext_ld_secret_level
    where levelId in
    <foreach  item="item" collection="deleteIds" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

</mapper>
