<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeAuthoriseMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.TrwfeAuthorise">
    <id column="ta_id" property="taId" />
    <result column="ta_import_time" property="taImportTime" />
    <result column="ta_total_times" property="taTotalTimes" />
    <result column="ta_left_times" property="taLeftTimes"  />
    <result column="ta_offline_times" property="taOfflineTimes" />
    <result column="ta_suffix" property="taSuffix" />
    <result column="ta_deadline" property="taDeadline" />
    <result column="ta_guid" property="taGuid"  />
    <result column="ta_checksum" property="taChecksum" />
    <result column="ta_left_checksum" property="taLeftChecksum" />
    <result column="ta_license_key" property="taLicenseKey" />
    <result column="ta_status" property="taStatus" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    SELECT  ta_id,ta_import_time,ta_total_times,ta_left_times,ta_offline_times,
    ta_suffix,ta_deadline,ta_guid,
    ta_checksum,ta_left_checksum,ta_license_key,ta_status
    FROM ext_tbs_authorise
  </select>


  <insert id="insert" parameterType="com.tipray.cloud.admin.entity.TrwfeAuthorise">
    insert into ext_tbs_authorise
     (ta_import_time,ta_total_times,ta_left_times,ta_offline_times,
    ta_suffix,ta_deadline,ta_guid,
    ta_checksum,ta_left_checksum,ta_license_key,ta_status)
    values ( #{taImportTime,jdbcType=TIMESTAMP},#{taTotalTimes,jdbcType=INTEGER},#{taLeftTimes,jdbcType=INTEGER},#{taOfflineTimes,jdbcType=INTEGER},
    #{taSuffix,jdbcType=VARCHAR},#{taDeadline,jdbcType=TIMESTAMP},#{taGuid,jdbcType=VARCHAR},
    #{taChecksum,jdbcType=VARCHAR},#{taLeftChecksum,jdbcType=VARCHAR},#{taLicenseKey,jdbcType=VARCHAR},#{taStatus,jdbcType=INTEGER})
  </insert>

  <select id="isTableExist" parameterType="string" resultType="int">
     SELECT COUNT(*) FROM information_schema.TABLES WHERE table_name = 'ext_tbs_authorise'
  </select>

  <update id="updateStatus" parameterType="com.tipray.cloud.admin.entity.TrwfeAuthorise">
    update ext_tbs_authorise
    set ta_status = #{taStatus}
    where ta_guid  = #{taGuid}
  </update>

</mapper>
