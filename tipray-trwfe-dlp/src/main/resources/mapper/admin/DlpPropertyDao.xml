<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.cloud.admin.mapper.DlpPropertyMapper">

    <resultMap type="com.tipray.cloud.admin.entity.DlpProperty" id="resultMap">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId"/>
        <result column="code" property="code"/>
        <result column="value" property="value"/>
        <result column="type" property="type"/>
        <result column="rule" property="rule"/>
        <result column="remark" property="remark"/>
        <result column="editable" property="editable"/>
    </resultMap>

    <select id="getByKey" resultMap="resultMap">
        select
          code, value
        from property
        where code = #{key}
    </select>

</mapper>
