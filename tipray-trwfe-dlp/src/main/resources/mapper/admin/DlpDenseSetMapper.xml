<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.DlpDenseSetMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.DlpDenseSet">
    <result column="visible_count" property="visibleCount"  jdbcType="INTEGER" />
    <result column="deleted" property="deleted" jdbcType="INTEGER" />
    <result column="modify_ver" property="modifyVer" jdbcType="BIGINT" />
  </resultMap>

  <select id="get" resultMap="BaseResultMap">
    SELECT visible_count, deleted, modify_ver
    FROM dense_set
    <where>
      <if test="modifyVer != null">
        and modify_ver > #{modifyVer}
      </if>
    </where>
  </select>

</mapper>