<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.cloud.admin.mapper.DlpGlobalConfigMapper">

    <resultMap type="com.tipray.cloud.admin.entity.DlpGlobalConfig" id="GlobalConfigMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="key" column="config_key" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="type" column="config_type" jdbcType="VARCHAR"/>
        <result property="value" column="config_value" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="INTEGER" />
        <result column="modify_ver" property="modifyVer" jdbcType="BIGINT" />
    </resultMap>

    <select id="isGlobalConfigTableExist" resultType="int">
        select count(*) from information_schema.TABLES where table_name = 'global_config'
    </select>

    <select id="getByKey" resultMap="GlobalConfigMap">
        select
          id, config_key, remark, config_type, config_value, modify_ver, deleted
        from global_config
        where config_key = #{key}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="GlobalConfigMap">
        select
        id, config_key, remark, config_type, config_value, modify_ver, deleted
        from global_config
    </select>
    
</mapper>
