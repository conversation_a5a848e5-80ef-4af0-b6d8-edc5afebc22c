<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.cloud.admin.mapper.DlpStgInterruptMapper">

    <resultMap id="StgDefMap" type="com.tipray.cloud.admin.entity.StgInterrupt">
        <result property="id" column="id"/>
        <result property="strategyTypeNumber" column="stg_type_number"/>
        <result property="objectType" column="object_type"/>
        <result property="objectId" column="object_id"/>
    </resultMap>


    <select id="listByStrategyType" resultMap="StgDefMap">
      select
      id, stg_type_number, object_type, object_id
      from stg_obj_interrupt
    <where>
        object_type in (2,4)
        <if test="strategyTypeNumber != null and strategyTypeNumber != ''">
            and stg_type_number in (${strategyTypeNumber})
        </if>
    </where>
    </select>


</mapper>
