<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.cloud.admin.mapper.DlpStgTerminalMapper">

    <resultMap type="com.tipray.cloud.admin.entity.DlpStgObject" id="StgTerminalMap">
        <result property="id" column="id"/>
        <result property="objectId" column="term_id"/>
        <result property="stgTypeNum" column="stg_type_number"/>
        <result property="stgDefId" column="stg_def_id"/>
        <result property="sourceType" column="source_type"/>
        <result property="sourceGroupId" column="source_group_id"/>
        <result property="sourcePkgId" column="source_pkg_id"/>
    </resultMap>

    <select id="listByTermIdAndNumber" resultMap="StgTerminalMap">
        select id, term_id,stg_type_number, stg_def_id, source_type, source_group_id, source_pkg_id
        from stg_terminal
        where term_id = #{termId} and stg_type_number in
        <foreach  item="item" collection="typeNumbers" index="index"  open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>
