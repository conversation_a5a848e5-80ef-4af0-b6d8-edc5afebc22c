<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeGroupUserMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.TrwfeGroupUser">
    <id column="USER_ID_" property="userId" jdbcType="VARCHAR"/>
    <result column="GROUP_ID_" property="groupId"  jdbcType="VARCHAR" />
  </resultMap>


  <sql id="Base_Column_List">
    USER_ID_ , GROUP_ID_
  </sql>

  <select id="list" resultMap="BaseResultMap">
    SELECT  USER_ID_ , GROUP_ID_
    FROM act_id_membership
    where USER_ID_ not in ('admin','sysadmin','logadmin','secadmin')
  </select>

  <select id="selectByUserIds" resultType="java.lang.String">
    select USER_ID_
    from act_id_membership
    where USER_ID_  in
    <foreach  item="item" collection="userIds" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into
    act_id_membership (USER_ID_ , GROUP_ID_)
    values
    <foreach collection="addList" item="item" index="index" separator=",">
      (
      #{item.userId,jdbcType=VARCHAR} ,#{item.groupId,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <delete id="batchDelete" parameterType="java.util.List">
    delete from act_id_membership
    where USER_ID_ in
    <foreach  item="item" collection="deleteTrwfeUserGroups" index="index"  open="(" separator="," close=")">
      #{item.userId}
    </foreach>
    and GROUP_ID_ in
    <foreach  item="item" collection="deleteTrwfeUserGroups" index="index"  open="(" separator="," close=")">
      #{item.groupId}
    </foreach>
  </delete>
  <delete id="batchDeleteByuserIds" parameterType="java.util.List">
    delete from act_id_membership
    where USER_ID_ in
    <foreach  item="item" collection="userIds" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

  <delete id="deleteAll" parameterType="java.util.List">
    delete from act_id_membership
    where USER_ID_ not in ('admin','sysadmin','logadmin','secadmin')
  </delete>

</mapper>
