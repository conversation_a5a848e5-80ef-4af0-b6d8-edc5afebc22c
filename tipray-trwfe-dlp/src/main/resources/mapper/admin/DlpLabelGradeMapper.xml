<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.DlpLabelGradeMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.DlpLabelGrade">
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="grade" property="grade"  jdbcType="INTEGER" />
    <result column="content" property="content"  jdbcType="VARCHAR" />
    <result column="lang_key" property="langKey"  jdbcType="VARCHAR" />
    <result column="deleted" property="deleted" jdbcType="INTEGER" />
    <result column="modify_ver" property="modifyVer" jdbcType="BIGINT" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    SELECT  id, grade, content,
    lang_key, deleted, modify_ver
    FROM label_grade_library
    <where>
      <if test="modifyVer != null">
        and modify_ver > #{modifyVer}
      </if>
    </where>
  </select>

  <select id="allList" resultMap="BaseResultMap">
    SELECT  id, grade, content,
    lang_key, deleted, modify_ver
    FROM label_grade_library
    WHERE deleted = 0
  </select>

</mapper>
