<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.cloud.admin.mapper.DlpBackupServerMapper">

    <resultMap type="com.tipray.cloud.admin.entity.DlpBackupServer" id="resultMap">
        <result property="devId" column="dev_id"/>
        <result property="name" column="name"/>
        <result property="devType" column="dev_type"/>
        <result property="bussType" column="buss_type"/>
        <result property="accessPwd" column="access_pwd"/>
        <result property="intranetIp" column="intranet_ip"/>
        <result property="internetIp" column="internet_ip"/>
        <result property="intranetIpv6" column="intranet_ipv6"/>
        <result property="internetIpv6" column="internet_ipv6"/>
        <result property="intranetPort" column="intranet_port"/>
        <result property="internetPort" column="internet_port"/>
        <result property="userName" column="username"/>
        <result property="password" column="password"/>
        <result property="mainPath" column="main_path"/>
        <result property="uploadSpeedLimit" column="upload_speed_limit"/>
        <result property="downSpeedLimit" column="down_speed_limit"/>
        <result property="maxOccurs" column="max_occurs"/>
        <result property="diskSpaceLimit" column="disk_space_limit"/>
        <result property="diskShortageAlert" column="disk_shortage_alert"/>
        <result property="minDataPort" column="min_data_port"/>
        <result property="maxDataPort" column="max_data_port"/>
        <result property="enableInternet" column="enable_internet"/>
        <result property="enableForceRetIP" column="enable_force_ret_ip"/>
    </resultMap>

    <select id="allOriBackUp" resultMap="resultMap">
        select dseb.* ,ds.name, ds.dev_type, ds.access_pwd, ds.intranet_ip, ds.internet_ip, ds.intranet_port, ds.internet_port
        from dev_server_ext_backup dseb
        left join dev_server ds on dseb.dev_id = ds.dev_id
    </select>

    <select id="isRelServerDeviceBackupTableExist" resultType="int">
        select count(*) from information_schema.TABLES where table_name = 'rel_data_server_device'
    </select>

    <select id="allBackUp" resultMap="resultMap">
        select dseb.* ,ds.name, ds.dev_type, ds.access_pwd, ds.intranet_ip, ds.internet_ip,ds.intranet_ipv6, ds.internet_ipv6, ds.intranet_port, ds.internet_port
        from dev_server_ext_backup dseb
        left join dev_server ds on dseb.dev_id = ds.dev_id
        where dseb.dev_id in (select device_id from rel_data_server_device where server_id = 6001)
    </select>

    <select id="allFileServer" resultMap="resultMap">
        select dseb.* ,ds.name, ds.dev_type, ds.access_pwd, ds.intranet_ip, ds.internet_ip, ds.intranet_ipv6, ds.internet_ipv6,ds.intranet_port, ds.internet_port
        from dev_server_ext_backup dseb
        left join dev_server ds on dseb.dev_id = ds.dev_id
        where ds.dev_type = 11
    </select>

    <select id="getTrwfeDevId" resultType="java.lang.Long">
        select dev_id
        from rel_backup_server_buss
        where dev_id in (${devIds}) and buss_id = 13 and enabled = 1
    </select>

</mapper>
