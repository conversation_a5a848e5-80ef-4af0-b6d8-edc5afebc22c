<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeSyncSourceMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.TrwfeSyncSource">
    <id column="sync_id" property="syncId" jdbcType="BIGINT"/>
    <result column="source_type" property="sourceType"  jdbcType="INTEGER" />
    <result column="source_name" property="sourceName"  jdbcType="VARCHAR" />
    <result column="source_config" property="sourceConfig"  jdbcType="VARCHAR" />
    <result column="login_able" property="loginAble"  jdbcType="INTEGER" />
    <!--    <result column="active" property="active" jdbcType="INTEGER" />-->
    <result column="deleted" property="deleted" jdbcType="INTEGER" />
    <!--    <result column="modify_ver" property="modifyVer" jdbcType="BIGINT" />-->
  </resultMap>

  <select id="isTableExist" parameterType="string" resultType="int">
     SELECT COUNT(*) FROM information_schema.TABLES WHERE table_name = 'ext_sync_source'
  </select>

  <select id="list" resultMap="BaseResultMap">
    SELECT  sync_id, source_type, source_name,  source_config , login_able , deleted
    FROM ext_sync_source
  </select>

  <insert id="batchReplace" parameterType="java.util.List">
    replace into
    ext_sync_source(sync_id, source_type, source_name,  source_config , login_able , deleted)
    values
    <foreach collection="replaceList" item="item" index="index" separator=",">
      (
      #{item.syncId,jdbcType=INTEGER} ,#{item.sourceType,jdbcType=INTEGER} ,
      #{item.sourceName,jdbcType=VARCHAR} ,#{item.sourceConfig,jdbcType=VARCHAR} ,
      #{item.loginAble,jdbcType=INTEGER} ,
      #{item.deleted,jdbcType=INTEGER}
      )
    </foreach>
  </insert>


  <insert id="insert" parameterType="com.tipray.cloud.admin.entity.TrwfeSyncSource">
    insert into ext_sync_source(sync_id, source_type, source_name,  source_config , login_able , deleted)
    values (
        #{syncId,jdbcType=INTEGER} ,#{sourceType,jdbcType=INTEGER} ,
        #{sourceName,jdbcType=VARCHAR} ,#{sourceConfig,jdbcType=VARCHAR} ,
        #{loginAble,jdbcType=INTEGER} ,
        #{deleted,jdbcType=INTEGER}
      )
  </insert>

  <update id="update" parameterType="com.tipray.cloud.admin.entity.TrwfeSyncSource">
    update ext_sync_source
    set source_type = #{sourceType,jdbcType=INTEGER} ,
    source_name = #{sourceName,jdbcType=VARCHAR},source_config = #{sourceConfig,jdbcType=VARCHAR} ,
    login_able = #{loginAble,jdbcType=INTEGER} ,deleted = #{deleted,jdbcType=INTEGER}
    where sync_id = #{syncId,jdbcType=INTEGER}
  </update>

</mapper>
