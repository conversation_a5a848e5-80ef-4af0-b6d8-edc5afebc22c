<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.cloud.admin.mapper.DlpMobileWPSConfigMapper">
    <resultMap type="com.tipray.cloud.admin.entity.DlpMobileWPSConfig" id="resultMap">
        <id column="id" property="id" />
        <result property="useWPS" column="use_wps"/>
        <result property="wpsOpenType" column="wps_open_type"/>
    </resultMap>

    <select id="getMobileWPSConfig" resultMap="resultMap">
        select * from mobile_wps_config
    </select>

</mapper>
