<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.cloud.admin.mapper.DlpAuthoriseMapper">

    <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.DlpAuthorise">
        <result property="id" column="id"/>
        <result property="guid" column="guid"/>
        <result property="deadline" column="deadline"/>
        <result property="totalTimes" column="total_times"/>
        <result property="leftTimes" column="left_times"/>
        <result property="authContent" column="auth_content"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>


    <select id="list" resultMap="BaseResultMap">
        SELECT  id, guid, deadline,total_times, left_times, auth_content, create_time, modify_time
        FROM charge_plug_auth
    </select>

    <select id="isTableExist" parameterType="string" resultType="int">
      SELECT COUNT(*) FROM information_schema.TABLES WHERE table_name = 'charge_plug_auth'
    </select>

</mapper>
