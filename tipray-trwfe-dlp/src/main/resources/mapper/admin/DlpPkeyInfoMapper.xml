<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.cloud.admin.mapper.DlpPkeyInfoMapper">

    <resultMap type="com.tipray.cloud.admin.entity.DlpPkeyInfo" id="PkeyInfoMap">
        <result property="recId" column="key_id"/>
        <result property="pkey" column="pkey"/>
        <result property="flag" column="flag"/>
        <result column="deleted" property="deleted" jdbcType="INTEGER" />
        <result column="modify_ver" property="modifyVer" jdbcType="BIGINT" />
    </resultMap>


    <select id="list" resultMap="PkeyInfoMap">
        SELECT key_id, pkey, flag, deleted, modify_ver
        FROM pkey_info
        where modify_ver > #{modifyVer}
    </select>

    <select id="allPKeyInfo" resultMap="PkeyInfoMap">
        SELECT  key_id, pkey, flag, deleted, modify_ver
        FROM pkey_info
        where deleted = 0
    </select>

</mapper>