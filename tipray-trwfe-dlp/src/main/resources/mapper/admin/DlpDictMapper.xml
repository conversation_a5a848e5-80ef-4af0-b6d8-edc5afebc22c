<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.DlpDictMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.DlpDict">
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="lng" property="lng"  jdbcType="VARCHAR" />
    <result column="dict_key" property="dictKey"  jdbcType="VARCHAR" />
    <result column="dict_value" property="dictValue"  jdbcType="VARCHAR" />
    <result column="deleted" property="deleted" jdbcType="INTEGER" />
    <result column="modify_ver" property="modifyVer" jdbcType="BIGINT" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    SELECT  id, lng, dict_key,dict_value, deleted, modify_ver
    FROM i18n_dict
    <where>
      dict_key like CONCAT('%','alarm.lossType','%') or dict_key like CONCAT('%','enc.denseInfo','%')
      or dict_key like CONCAT('%','officeWaterMark.officeMarkWay','%')
      or dict_key like CONCAT('%','label.grade','%')
      <if test="modifyVer != null">
        and modify_ver > #{modifyVer}
      </if>
    </where>
  </select>

  <select id="allList" resultMap="BaseResultMap">
    SELECT  id, lng, dict_key,dict_value, deleted, modify_ver
    FROM i18n_dict
    WHERE deleted = 0 and (dict_key like CONCAT('%','alarm.lossType','%')
      or dict_key like CONCAT('%','enc.denseInfo','%')
      or dict_key like CONCAT('%','officeWaterMark.officeMarkWay','%')
      or dict_key like CONCAT('%','label.grade','%'))
  </select>

</mapper>
