<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.cloud.admin.mapper.DlpAdDomainMapper">
    <resultMap id="resultMap" type="com.tipray.cloud.admin.entity.AdDomain">
        <result column="id" property="id"/>
        <result column="host" property="host"/>
        <result column="account" property="account"/>
        <result column="password" property="password"/>
    </resultMap>

    <select id="list" parameterType="java.lang.String" resultMap="resultMap">
		select * from ad_domain
	</select>

    <select id="isDomainLoginAuth"  resultType="java.lang.Integer">
		select opt_val from sys_options WHERE opt_id = 1024
	</select>

    <select id="autoRecover"  resultType="java.lang.String">
		select opt_str_val from sys_options WHERE opt_id = 2005
	</select>

    <select id="getEncType"  resultType="java.lang.Integer">
		select opt_val from sys_options WHERE opt_id = 1101
	</select>

</mapper>
