<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeAdDomainMapper" >

  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.AdDomain">
    <result column="serverId" property="id"/>
    <result column="serverAddr" property="host"/>
    <result column="adminAccount" property="account"/>
    <result column="adminPwd" property="password"/>
  </resultMap>

  <update id="updateAdDomainAuth" parameterType="java.lang.String">
    update ext_config
    set config_value = #{isAdDomainAuth}
    where config_key = 'ld.ad.validate'
  </update>

  <select id="list" resultMap="BaseResultMap">
    SELECT serverId, serverAddr , adminAccount, adminPwd
    FROM ext_ad_server_info
  </select>

  <select id="selectByPrimaryKeys" resultType="java.lang.Integer">
    select serverId
    from ext_ad_server_info
    where serverId in
    <foreach  item="item" collection="ids" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into
    ext_ad_server_info(serverId, serverAddr , adminAccount, adminPwd)
    values
    <foreach collection="addList" item="item" index="index" separator=",">
      (
      #{item.id,jdbcType=INTEGER}, #{item.host,jdbcType=VARCHAR},
      #{item.account,jdbcType=VARCHAR}, #{item.password,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List">
    update ext_ad_server_info
    <trim prefix="set" suffixOverrides=",">

      <trim prefix="serverAddr = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.host!=null">
          when serverId = #{item.id} then #{item.host}
          </if>
        </foreach>
      </trim>

      <trim prefix="adminAccount = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.account!=null">
            when serverId = #{item.id} then #{item.account}
          </if>
        </foreach>
      </trim>

      <trim prefix="adminPwd = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.password!=null">
            when serverId = #{item.id} then #{item.password}
          </if>
        </foreach>
      </trim>

    </trim>

    where
    serverId in
    <foreach collection="updateList" index="index" item="item"
             separator="," open="(" close=")">
      #{item.id}
    </foreach>
  </update>

  <delete id="batchDelete" parameterType="java.util.List">
    delete from ext_ad_server_info
    where id in
    <foreach  item="item" collection="deleteIds" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

  <delete id="clear">
    delete from ext_ad_server_info
  </delete>

</mapper>
