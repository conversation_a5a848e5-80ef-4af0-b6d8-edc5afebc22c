<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.cloud.admin.mapper.DlpWaterMarkLibDao">

    <resultMap type="com.tipray.cloud.admin.entity.WaterMarkLib" id="resultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="groupId" column="group_id"/>
        <result property="info" column="info"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <select id="list" resultMap="resultMap">
        select id,type, name, info, remark, group_id from water_mark_lib
    </select>

</mapper>
