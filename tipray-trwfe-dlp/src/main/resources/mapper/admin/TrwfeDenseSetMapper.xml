<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeDenseSetMapper" >

  <select id="get" resultType="java.lang.String">
    SELECT config_value
    FROM ext_config
    WHERE config_key = 'ld.max.secret.level'
  </select>


  <insert id="insert" parameterType="java.lang.String">
    insert into ext_config
     (config_key, config_desc, config_type, config_value)
    values ( 'ld.max.secret.level', null, 'integer', #{visibleCount,jdbcType=VARCHAR})
  </insert>

  <update id="update" parameterType="java.lang.String">
    update ext_config
    set config_value = #{visibleCount,jdbcType=VARCHAR}
    where config_key = 'ld.max.secret.level'
  </update>

</mapper>