<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeUserRightsMapper" >

  <select id="list" resultType="com.tipray.cloud.admin.entity.TrwfeUserRights">
    SELECT  userId , deptId , deptIdRight, userIdRight, readType
    FROM ext_user_rights
  </select>


  <insert id="batchInsert" parameterType="java.util.List">
    insert into
    ext_user_rights (userId, deptId, deptIdRight, userIdRight,  readType)
    values
    <foreach collection="addList" item="item" index="index" separator=",">
      (
      #{item.userId,jdbcType=BIGINT}, #{item.deptId,jdbcType=BIGINT},
      #{item.deptIdRight,jdbcType=BIGINT}, #{item.userIdRight,jdbcType=BIGINT},#{item.readType,jdbcType=INTEGER}
      )
    </foreach>
  </insert>


  <delete id="batchDelete" parameterType="java.util.List">
    delete from ext_user_rights
    where
    <foreach collection="deleteList" item="item" index="index" separator="or">
      (
      userId = #{item.userId,jdbcType=BIGINT} and deptId = #{item.deptId,jdbcType=BIGINT} and
      deptIdRight = #{item.deptIdRight,jdbcType=BIGINT} and userIdRight = #{item.userIdRight,jdbcType=BIGINT} and
      readType = #{item.readType,jdbcType=INTEGER}
      )
    </foreach>
  </delete>

</mapper>