<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.DlpUserRoleMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.DlpUserRole">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <result column="name" property="name"  jdbcType="VARCHAR" />
    <result column="deleted" property="deleted" jdbcType="INTEGER" />
    <result column="modify_ver" property="modifyVer" jdbcType="BIGINT" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    SELECT  id, name, deleted, modify_ver
    FROM user_role
    <where>
    <if test="modifyVer != null">
      and modify_ver > #{modifyVer}
    </if>
    </where>
  </select>


  <select id="allRole" resultMap="BaseResultMap">
    SELECT  id, name, deleted, modify_ver
    FROM user_role
    where deleted = 0
  </select>

  <select id="selectByUserId" resultMap="BaseResultMap">
    select id, name, parent_id, deleted, modify_ver
    from user_role
    where id  = (select group_id from rel_group_user where user_id = #{userId} and deleted = 0)
  </select>

  <select id="selectById" resultMap="BaseResultMap">
    select id, name, deleted, modify_ver
    from user_role
    where id  = #{id}
  </select>

</mapper>
