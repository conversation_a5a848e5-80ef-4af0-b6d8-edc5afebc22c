<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfePepKeyMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.TrwfePepKey">
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result property="recId" column="rec_id"/>
    <result property="pkey" column="pkey"/>
    <result property="flag" column="flag"/>
    <result property="keyType" column="keyType"/>
    <result property="encType" column="encType"/>
  </resultMap>

  <delete id="delete">
    delete from ext_pep_key
  </delete>

  <select id="mainEncType" resultType="java.lang.Integer">
    SELECT  encType
    FROM ext_pep_key where keyType = 0
  </select>

  <insert id="insertSelective" parameterType="com.tipray.cloud.admin.entity.TrwfePepKey" >
    insert into ext_pep_key
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="recId != null" >
        recId,
      </if>
      <if test="pkey != null" >
        pkey,
      </if>
      <if test="flag != null" >
        flag,
      </if>
      <if test="keyType != null" >
        keyType,
      </if>
      <if test="encType != null" >
        encType,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="recId != null" >
        #{recId,jdbcType=INTEGER},
      </if>
      <if test="pkey != null" >
        #{pkey,jdbcType=VARCHAR},
      </if>
      <if test="flag != null" >
        #{flag,jdbcType=INTEGER},
      </if>
      <if test="keyType != null" >
        #{keyType,jdbcType=VARCHAR},
      </if>
      <if test="encType != null" >
        #{encType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>
