<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeStgDefMapper">

    <resultMap id="StgDefMap" type="com.tipray.cloud.admin.entity.StgDef">
        <result property="id" column="id"/>
        <result property="strategyType" column="strategy_type"/>
        <result property="strategyInfo" column="strategy_info"/>
        <result property="objectType" column="object_type"/>
        <result property="objectId" column="object_id"/>
        <result property="objectInfo" column="object_info"/>
    </resultMap>


    <select id="listByStrategyType" parameterType="java.lang.String" resultMap="StgDefMap">
        select
            id, strategy_type, strategy_info, object_type, object_id, object_info
        from stg_def
        where strategy_type = #{strategyType}
    </select>

    <delete id="deleteByStrategyType"  parameterType="java.lang.String">
        delete from stg_def where strategy_type = #{strategyType}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into
        stg_def(id, strategy_type, strategy_info, object_type, object_id, object_info)
        values
        <foreach collection="addList" item="item" index="index" separator=",">
            (
            #{item.id}, #{item.strategyType},#{item.strategyInfo},#{item.objectType}, #{item.objectId}, #{item.objectInfo}
            )
        </foreach>
    </insert>


</mapper>
