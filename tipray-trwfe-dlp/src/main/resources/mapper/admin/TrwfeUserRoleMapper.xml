<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeUserRoleMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.TrwfeUserRole">
    <id column="userId" property="userId" jdbcType="VARCHAR"/>
    <result column="roleId" property="roleId"  jdbcType="VARCHAR" />
  </resultMap>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into
    ext_user_role (userId , roleId)
    values
    <foreach collection="addUserIds" item="item" index="index" separator=",">
      (
        #{item,jdbcType=VARCHAR} ,'role_user'
      )
    </foreach>
  </insert>

  <delete id="batchDelete" parameterType="java.lang.String">
    delete from ext_user_role
    where userId in
    <foreach  item="item" collection="deleteUserIds" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

</mapper>