<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeDescIdMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.TrwfeDescId">
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="dlpId" property="dlpId"  jdbcType="INTEGER" />
    <result column="descId" property="descId"  jdbcType="INTEGER" />
    <result column="descValue" property="descValue"  jdbcType="VARCHAR" />
    <result column="descKey" property="descKey"  jdbcType="VARCHAR" />
    <result column="funType" property="funType"  jdbcType="INTEGER" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap" parameterType="java.lang.Integer">
    SELECT id, dlpId, descId, descValue, descKey,funType
    FROM ext_dlp_desc_id where funType = #{funType}
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into
    ext_dlp_desc_id(id,dlpId, descId, descValue,descKey,funType)
    values
    <foreach collection="addList" item="item" index="index" separator=",">
      (
        #{item.id,jdbcType=INTEGER},#{item.dlpId,jdbcType=INTEGER},#{item.descId,jdbcType=INTEGER},
        #{item.descValue,jdbcType=VARCHAR},#{item.descKey,jdbcType=VARCHAR},#{item.funType,jdbcType=INTEGER}
      )
    </foreach>
  </insert>

  <update id="batchUpdate">
    update ext_dlp_desc_id
    <trim prefix="set" suffixOverrides=",">

      <trim prefix="descId = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.descId != null">
            when dlpId = #{item.dlpId} then #{item.descId}
          </if>
        </foreach>
      </trim>
      <trim prefix="descKey = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.descKey != null">
            when dlpId = #{item.dlpId} then #{item.descKey}
          </if>
        </foreach>
      </trim>
      <trim prefix="descValue = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.descValue != null">
            when dlpId = #{item.dlpId} then #{item.descValue}
          </if>
        </foreach>
      </trim>
    </trim>
    where
    dlpId in
    <foreach collection="updateList" index="index" item="item"
             separator="," open="(" close=")">
      #{item.dlpId}
    </foreach>
    and funType = #{funType}
  </update>

  <delete id="batchDelete">
    delete from ext_dlp_desc_id
    where dlpId in
    <foreach  item="item" collection="deleteDlpIds" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
    and funType = #{funType}
  </delete>

</mapper>
