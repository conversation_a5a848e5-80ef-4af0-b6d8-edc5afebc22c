<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeExtUserMapper" >

  <select id="list" resultType="com.tipray.cloud.admin.entity.TrwfeExtUser">
    SELECT  userId , secretLevel, applyValid
    FROM ext_id_user
    WHERE userId not in ('admin','sysadmin','logadmin','secadmin')
  </select>


  <select id="selectByPrimaryKeys" resultType="java.lang.String">
    select userId
    from ext_id_user
    where userId  in
    <foreach  item="item" collection="ids" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <!--<insert id="batchInsertByDenseLevel" parameterType="java.util.List">
    insert into
    ext_id_user( userId , secretLevel, applyValid, userType, autoLogin, pwdNullable)
    values
    <foreach collection="addList" item="item" index="index" separator=",">
      (
      #{item.userId,jdbcType=VARCHAR}, #{item.secretLevel,jdbcType=INTEGER}, #{item.applyValid,jdbcType=INTEGER},
      '1',1,0
      )
    </foreach>
  </insert>-->

  <insert id="batchInsertByUser" parameterType="java.util.List">
    insert into
    ext_id_user( userId , secretLevel, applyValid, userType, autoLogin, pwdNullable, active, wxId, validStartTime, validEndTime)
    values
    <foreach collection="addList" item="item" index="index" separator=",">
      (
      #{item.userId,jdbcType=VARCHAR}, 0, 0,
      #{item.userType,jdbcType=VARCHAR}, #{item.autoLogin,jdbcType=INTEGER}, #{item.pwdNullable,jdbcType=INTEGER},
      #{item.active,jdbcType=INTEGER}, #{item.wxId,jdbcType=VARCHAR}, #{item.validStartTime,jdbcType=TIMESTAMP}, #{item.validEndTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List">
    update ext_id_user

    <trim prefix="set" suffixOverrides=",">

      <trim prefix="secretLevel = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.secretLevel!=null">
            when userId = #{item.userId} then #{item.secretLevel}
          </if>
        </foreach>
      </trim>

      <trim prefix="applyValid = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.applyValid!=null">
            when userId = #{item.userId} then #{item.applyValid}
          </if>
        </foreach>
      </trim>

      <trim prefix="userType = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.userType!=null">
            when userId = #{item.userId} then #{item.userType}
          </if>
        </foreach>
      </trim>

      <trim prefix="autoLogin = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.autoLogin!=null">
            when userId = #{item.userId} then #{item.autoLogin}
          </if>
        </foreach>
      </trim>

      <trim prefix="pwdNullable = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.pwdNullable!=null">
            when userId = #{item.userId} then #{item.pwdNullable}
          </if>
        </foreach>
      </trim>

      <trim prefix="active = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.active!=null">
            when userId = #{item.userId} then #{item.active}
          </if>
        </foreach>
      </trim>

      <trim prefix="wxId = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.wxId!=null">
            when userId = #{item.userId} then #{item.wxId}
          </if>
        </foreach>
      </trim>

      <trim prefix="validStartTime = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
            when userId = #{item.userId} then #{item.validStartTime}
        </foreach>
      </trim>

      <trim prefix="validEndTime = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
            when userId = #{item.userId} then #{item.validEndTime}
        </foreach>
      </trim>

    </trim>

    where
    userId in
    <foreach collection="updateList" index="index" item="item"
             separator="," open="(" close=")">
      #{item.userId}
    </foreach>
  </update>

  <delete id="batchDelete" parameterType="java.util.List">
    delete from ext_id_user
    where userId in
    <foreach  item="item" collection="deleteIds" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

</mapper>
