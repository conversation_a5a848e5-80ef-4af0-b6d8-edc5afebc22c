<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.DlpRelUserRoleMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.DlpRelUserRole">
    <id column="user_id" property="userId" jdbcType="BIGINT"/>
    <result column="role_id" property="roleId"  jdbcType="BIGINT" />
    <result column="deleted" property="deleted" jdbcType="INTEGER" />
    <result column="modify_ver" property="modifyVer" jdbcType="BIGINT" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    SELECT  gu.user_id, gu.role_id, gu.deleted, gu.modify_ver
    FROM rel_user_role gu
    <where>
      <if test="modifyVer != null">
        and gu.modify_ver > #{modifyVer}
      </if>
    </where>
  </select>

  <select id="allRelRoleUser" resultMap="BaseResultMap">
    SELECT  gu.user_id, gu.role_id, gu.deleted, gu.modify_ver
    FROM rel_user_role gu
    where gu.deleted = 0
  </select>

</mapper>
