<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeSyncVerMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.TrwfeSyncVer">
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="table_name" property="tableName"  jdbcType="VARCHAR" />
    <result column="modify_ver" property="modifyVer"  jdbcType="BIGINT" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    SELECT *
    FROM dlp_trwfe_sync_ver
  </select>

  <update id="updateSelective" parameterType="com.tipray.cloud.admin.entity.TrwfeSyncVer">
    update dlp_trwfe_sync_ver
    set modify_ver = #{modifyVer}
    where table_name = #{tableName}
  </update>
</mapper>
