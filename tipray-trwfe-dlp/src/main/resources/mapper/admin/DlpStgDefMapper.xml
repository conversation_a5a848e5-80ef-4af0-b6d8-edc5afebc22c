<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.cloud.admin.mapper.DlpStgDefMapper">

    <resultMap id="StgDefMap" type="com.tipray.cloud.admin.entity.StgDef">
        <result property="id" column="id"/>
        <result property="strategyType" column="strategy_type"/>
        <result property="strategyInfo" column="strategy_info"/>
        <result property="objectType" column="object_type"/>
        <result property="objectId" column="object_id"/>
        <result property="objectInfo" column="object_info"/>
        <result property="strategyDefType" column="strategy_def_type"/>
    </resultMap>


    <select id="listByStrategyType" parameterType="java.lang.String" resultMap="StgDefMap">
        select
            id, strategy_type, strategy_info, object_type, object_id, object_info, strategy_def_type
        from stg_def
        where strategy_type = #{strategyType,jdbcType=VARCHAR} and (object_type in (2,4) or object_type is null) and active = 1
        order by id asc
    </select>


    <select id="strategyInfoById" parameterType="java.lang.Long" resultType="java.lang.String">
        select strategy_info
        from stg_def
        where id = #{id}
    </select>

</mapper>
