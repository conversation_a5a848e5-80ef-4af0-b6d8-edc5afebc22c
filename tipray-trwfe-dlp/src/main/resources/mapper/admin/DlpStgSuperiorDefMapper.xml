<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.cloud.admin.mapper.DlpStgSuperiorDefMapper">

    <select id="listStgIdBySuperiorStgDefId" resultType="java.lang.Long">
        select stg_def_id from stg_superior_def where superior_stg_def_id = #{superiorStgDefId}
        <if test="stgTypeNum != null and stgTypeNum != 0">
            and stg_type_number = #{stgTypeNum}
        </if>
    </select>

</mapper>
