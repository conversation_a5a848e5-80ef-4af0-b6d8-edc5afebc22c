<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tipray.cloud.admin.mapper.DlpStgUserMapper">

    <resultMap type="com.tipray.cloud.admin.entity.DlpStgObject" id="StgUserMap">
        <result property="id" column="id"/>
        <result property="objectId" column="user_id"/>
        <result property="stgTypeNum" column="stg_type_number"/>
        <result property="stgDefId" column="stg_def_id"/>
        <result property="sourceType" column="source_type"/>
        <result property="sourceGroupId" column="source_group_id"/>
        <result property="sourcePkgId" column="source_pkg_id"/>
    </resultMap>

    <select id="listByUserIdAndNumber" resultMap="StgUserMap">
        select id, user_id,stg_type_number, stg_def_id, source_type, source_group_id, source_pkg_id
        from stg_user
        where user_id = #{userId} and stg_type_number in
        <foreach  item="item" collection="typeNumbers" index="index"  open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>
