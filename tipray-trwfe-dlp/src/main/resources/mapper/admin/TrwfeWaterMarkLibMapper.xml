<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeWaterMarkLibMapper" >

  <resultMap type="com.tipray.cloud.admin.entity.WaterMarkLib" id="resultMap">
    <result property="id" column="id"/>
    <result property="name" column="name"/>
    <result property="type" column="type"/>
    <result property="groupId" column="group_id"/>
    <result property="info" column="info"/>
    <result property="remark" column="remark"/>
  </resultMap>

  <select id="list" resultMap="resultMap">
        select id,type, name, info, remark, group_id from water_mark_lib
  </select>

  <select id="selectByPrimaryKeys" resultType="java.lang.String">
    select id
    from water_mark_lib
    where id  in
      <foreach  item="item" collection="ids" index="index"  open="(" separator="," close=")">
        #{item}
      </foreach>
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into
    water_mark_lib(id,type, name, info, remark, group_id)
    values
    <foreach collection="addList" item="item" index="index" separator=",">
      (
        #{item.id,jdbcType=VARCHAR},#{item.type,jdbcType=VARCHAR} ,#{item.name,jdbcType=VARCHAR},
        #{item.info,jdbcType=VARCHAR},#{item.remark,jdbcType=VARCHAR},#{item.groupId,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List">
    update water_mark_lib
    <trim prefix="set" suffixOverrides=",">

      <trim prefix="type = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.type!=null">
            when id = #{item.id} then #{item.type}
          </if>
        </foreach>
      </trim>

      <trim prefix="name = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.name!=null">
            when id = #{item.id} then #{item.name}
          </if>
        </foreach>
      </trim>

      <trim prefix="info = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.info != null">
            when id = #{item.id} then #{item.info}
          </if>
        </foreach>
      </trim>

      <trim prefix="remark = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.remark!=null">
            when id = #{item.id} then #{item.remark}
          </if>
        </foreach>
      </trim>

      <trim prefix="group_id = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.groupId != null">
            when id = #{item.id} then #{item.groupId}
          </if>
        </foreach>
      </trim>

    </trim>

    where
    id in
    <foreach collection="updateList" index="index" item="item"
             separator="," open="(" close=")">
      #{item.id}
    </foreach>
  </update>

  <delete id="batchDelete" parameterType="java.util.List">
    delete from water_mark_lib
    where id in
    <foreach  item="item" collection="deleteIds" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

</mapper>
