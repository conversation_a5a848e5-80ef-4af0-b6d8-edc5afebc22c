<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeConfigMapper" >

  <select id="getTrwfeVersion" resultType="java.lang.String">
    SELECT config_value
    FROM ext_config
    WHERE config_key = 'system.version'
  </select>

  <select id="getTrwfeDisplayVersion" resultType="java.lang.String">
    SELECT config_value
    FROM ext_config
    WHERE config_key = 'system.display.version'
  </select>


  <select id="getOfflineTime" resultType="java.lang.String">
    SELECT config_value
    FROM ext_config
    WHERE config_key = 'ld.longest.offline.period'
  </select>

  <select id="getOperatorDisplayMode" resultType="java.lang.String">
    SELECT config_value
    FROM ext_config
    WHERE config_key = 'operatorDisplayMode'
  </select>

  <select id="getMobileDownloadMaxSize" resultType="java.lang.String">
    SELECT config_value
    FROM ext_config
    WHERE config_key = 'approval.mobile.download.max.size'
  </select>

  <select id="getCloseFourthEnc" resultType="java.lang.Boolean">
    SELECT config_value
    FROM ext_config
    WHERE config_key = 'close.fourth.encryption'
  </select>

  <update id="updateOperatorDisplayMode" parameterType="java.lang.String">
    update ext_config
    set config_value = #{operatorDisplayMode,jdbcType=VARCHAR}
    where config_key = 'operatorDisplayMode'
  </update>

  <update id="updateLockSwitch" parameterType="java.lang.String">
    update ext_config
    set config_value = #{lockSwitch,jdbcType=VARCHAR}
    where config_key = 'login.account.lock.switch'
  </update>

  <update id="updateLockDay" parameterType="java.lang.Integer">
    update ext_config
    set config_value = #{lockDay,jdbcType=INTEGER}
    where config_key = 'login.fail.lock.day'
  </update>

  <update id="updateCodeNumber" parameterType="java.lang.Integer">
    update ext_config
    set config_value = #{codeNumber,jdbcType=INTEGER}
    where config_key = 'login.fail.code.num'
  </update>

  <update id="updateFailNumber" parameterType="java.lang.Integer">
    update ext_config
    set config_value = #{failNumber,jdbcType=INTEGER}
    where config_key = 'login.fail.num'
  </update>

  <update id="updateLockMinute" parameterType="java.lang.Integer">
    update ext_config
    set config_value = #{lockMinute,jdbcType=INTEGER}
    where config_key = 'login.account.lock.minute'
  </update>

  <update id="updateProductNumber" parameterType="java.lang.String">
    update ext_config
    set config_value = #{productNumber,jdbcType=VARCHAR}
    where config_key = 'system.user.number'
  </update>

  <update id="closeFourthEnc" parameterType="java.lang.String">
    update ext_config
    set config_value = #{closeFourthEnc,jdbcType=VARCHAR}
    where config_key = 'close.fourth.encryption'
  </update>

</mapper>
