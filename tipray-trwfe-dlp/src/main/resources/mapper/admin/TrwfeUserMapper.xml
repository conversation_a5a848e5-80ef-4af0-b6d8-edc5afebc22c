<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tipray.cloud.admin.mapper.TrwfeUserMapper" >
  <resultMap id="BaseResultMap" type="com.tipray.cloud.admin.entity.TrwfeUser">
    <id column="ID_" property="id" jdbcType="VARCHAR"/>
    <result column="LAST_" property="account"  jdbcType="VARCHAR" />
    <result column="FIRST_" property="name"  jdbcType="VARCHAR" />
    <result column="PWD_" property="password"  jdbcType="VARCHAR" />
    <result column="EMAIL_" property="email"  jdbcType="VARCHAR" />
    <result column="MOBILE_" property="phone"  jdbcType="VARCHAR" />
    <result column="DIRECT_LEADER_" property="directLeader"  jdbcType="VARCHAR" />
    <result column="COMMON_INHERIT_" property="commonInherit"  jdbcType="VARCHAR" />
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    SELECT  ID_ , LAST_ , FIRST_ , PWD_ , EMAIL_, MOBILE_, DIRECT_LEADER_
    FROM act_id_user
    WHERE ID_ not in ('admin','sysadmin','logadmin','secadmin')
  </select>

  <update id="updatePassword" parameterType="com.tipray.cloud.admin.entity.TrwfeUser">
    update act_id_user
    set PWD_ = #{password}
    where ID_  = #{id}
  </update>

  <select id="selectByPrimaryKeys" resultType="java.lang.String">
    select ID_
    from act_id_user
    where ID_  in
    <foreach  item="item" collection="ids" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into
    act_id_user(ID_ , REV_, LAST_ , FIRST_ , PWD_ , EMAIL_, MOBILE_, DIRECT_LEADER_,COMMON_INHERIT_)
    values
    <foreach collection="addList" item="item" index="index" separator=",">
      (
      #{item.id,jdbcType=VARCHAR}, 1 , #{item.account,jdbcType=VARCHAR},
      #{item.name,jdbcType=VARCHAR},#{item.password,jdbcType=VARCHAR}, #{item.email,jdbcType=VARCHAR}, #{item.phone,jdbcType=VARCHAR},
      #{item.directLeader,jdbcType=VARCHAR},#{item.commonInherit,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List">
    update act_id_user
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="LAST_ = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.account!=null">
            when ID_ = #{item.id} then #{item.account}
          </if>
        </foreach>
      </trim>

      <trim prefix="FIRST_ = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.name != null">
            when ID_ = #{item.id} then #{item.name}
          </if>
        </foreach>
      </trim>

      <trim prefix="PWD_ = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.password != null">
            when ID_ = #{item.id} then #{item.password}
          </if>
        </foreach>
      </trim>

      <trim prefix="EMAIL_ = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.email != null">
            when ID_ = #{item.id} then #{item.email}
          </if>
        </foreach>
      </trim>

      <trim prefix="MOBILE_ = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.phone != null">
            when ID_ = #{item.id} then #{item.phone}
          </if>
        </foreach>
      </trim>
      <trim prefix="DIRECT_LEADER_ = case" suffix="end,">
        <foreach collection="updateList" item="item" index="index">
          <if test="item.directLeader != null">
            when ID_ = #{item.id} then #{item.directLeader}
          </if>
        </foreach>
      </trim>
    </trim>

    where
    ID_ in
    <foreach collection="updateList" index="index" item="item"
             separator="," open="(" close=")">
      #{item.id}
    </foreach>
  </update>

  <delete id="batchDelete" parameterType="java.util.List">
    delete from act_id_user
    where ID_ in
    <foreach  item="item" collection="deleteIds" index="index"  open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

</mapper>
