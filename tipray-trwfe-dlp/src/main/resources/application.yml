server:
  port: 12356
#server:
#  port: 12356
#  tongweb:
#    license:
#      type: file
#      path: ${license.path}
#    uri-encoding: utf-8
#    max-thread: 800

#  servlet:
#    path: /


spring:
  profiles:
    active: prod
  application:
    name: dlp-trwfe
  main:
    allow-bean-definition-overriding: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null
  datasource:
    dynamic:
      lazy: true
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候会抛出异常,不启动则使用默认数据源.
      datasource:
        master:
          url: jdbc:mysql://${database.ip}:${database.port}/dlp_sysmgr?useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&serverTimezone=Asia/Shanghai&allowMultiQueries=true
          driver-class-name: com.mysql.cj.jdbc.Driver
          #          url: jdbc:kingbase8://${database.ip}:${database.port}/dlp3?currentSchema=dlp_sysmgr
          #          driver-class-name: com.kingbase8.Driver
          username: ${database.username}
          password: ${database.password}
        slave:
          url: jdbc:mysql://${trwfeDatabase.ip}:${trwfeDatabase.port}/activiti?useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&serverTimezone=Asia/Shanghai&allowMultiQueries=true
          driver-class-name: com.mysql.cj.jdbc.Driver
          #          url: ************************************************************************************
          #          url: ******************************************************************************************
          #          driver-class-name: com.kingbase8.Driver
          username: ${trwfeDatabase.username}
          password: ${trwfeDatabase.password}

mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml,classpath*:sqlmapper/*.xml
  type-aliases-package: com.tipray.cloud.admin.entity,;com.tipray.dlp.effective.bean,;com.tipray.dlp.bean
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  config: ${catalina.home}/logback.xml
#打包时注释
#  config: classpath:logback.xml
#  level2DB: false

#打包时注释
#database:
#  ip: *************
#  port: 23306
#  username: root
#  password: AQIB9PT/sN92T2k2IuUqOiNFbXSg3uOgGgEJ0yvCbamaBAab
trwfeDatabase:
  ip: localhost
  password: AQIB9GaCCpwK0befNRkyPlDpLn8=
  port: 3506
  username: trwfe
  dbType: 2

#license:
#  path: "F:/中间件本地测试2/license.dat"

socket:
  service:
    accessPwd: AQIB9Huh9U3SMO5FXmbiP+sV3rxPkhZrKpOGGJdSwEVDAsws
    ip: 127.0.0.1
    port: 20180
  device:
    locID: 6001
    locType: 9
    destID: 1
