<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dlp</artifactId>
        <groupId>com.tipray</groupId>
        <version>3.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>tipray-dlp-core</artifactId>
    <packaging>war</packaging>
    <properties>
        <skipTests>true</skipTests>
        <dlpVersion>5.02</dlpVersion>
        <!--maven.build.timestamp保存了maven编译时间戳-->
        <timestamp>${maven.build.timestamp}</timestamp>
        <!--指定时间格式-->
        <maven.build.timestamp.format>yyyy-MM-dd HH:mm:ss</maven.build.timestamp.format>
    </properties>

    <dependencies>
        <!--当需要给炫几提供版本时，加载此模块-->
        <!--<dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-api-xuanji</artifactId>
            <version>1.0</version>
        </dependency>-->
        <!--当需要给第三方提供接口时，加载此模块-->
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-api-ext</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-modules-all</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-modules-report</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-modules-sync</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-tools</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-communication</artifactId>
            <version>1.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-mybatis</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tipray</groupId>
            <artifactId>tipray-commons-logging</artifactId>
            <version>1.0</version>
        </dependency>

        <!-- 开发使用的包，用在编译和测试-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>springloaded</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- 若项目启动无法正常加载yml内容，可屏蔽该引入-->
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-devtools</artifactId>-->
<!--            <optional>true</optional>-->
<!--            <scope>provided</scope>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.taobao.arthas</groupId>
            <artifactId>arthas-spring-boot-starter</artifactId>
            <version>4.0.5</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-yaml</artifactId>
        </dependency>

        <!--springboot 容器 支持开启 ：前面对默认tomcat执行了exclusion， tomcat指定8.5-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>net.sf.ucanaccess</groupId>
            <artifactId>ucanaccess</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- RESTFull API 接口文档-->
        <dependency>
            <groupId>com.github.shalousun</groupId>
            <artifactId>smart-doc</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.antlr</groupId>
                    <artifactId>antlr4-runtime</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.github.yedaxia</groupId>
            <artifactId>japidocs</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <filtering>true</filtering>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>sqls/**</exclude>
                </excludes>
            </resource>
            <resource>
                <filtering>false</filtering>
                <directory>src/main/resources-non-filtered</directory>
                <excludes>
                    <!--<exclude>tool/**</exclude>-->
                    <exclude>tool/ccbgcollector/**</exclude>
                    <exclude>tool/readme.txt</exclude>
<!--                    <exclude>*.keystore</exclude>-->
                </excludes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <!-- system scope 的jar包由 maven-war-plugin 打入lib目录 -->
                    <includeSystemScope>false</includeSystemScope>
                    <excludeDevtools>true</excludeDevtools>
                    <excludeGroupIds>
                        <!-- logback 相关jar包内容打入 tipray-commons-logging 模块以兼容springboot，不重复打入lib目录 -->
                        ch.qos.logback,
                        <!-- 项目运行不需要 lombok 组件-->
                        org.projectlombok,
                        <!-- 以下组件由 servlet 容器提供-->
                        javax.servlet,
                        org.apache.tomcat.embed
                    </excludeGroupIds>
                    <excludes>
                        <exclude>
                            <groupId>org.springframework</groupId>
                            <artifactId>springloaded</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <!--20240718指定版本为3.1.0，原因：3.2.0版本打包tipray-dlp-core失败 -->
                <version>3.1.0</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>txt</nonFilteredFileExtension>
                        <nonFilteredFileExtension>keystore</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                        <nonFilteredFileExtension>doc</nonFilteredFileExtension>
                        <nonFilteredFileExtension>docx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>et</nonFilteredFileExtension>
                        <nonFilteredFileExtension>dll</nonFilteredFileExtension>
                        <nonFilteredFileExtension>so</nonFilteredFileExtension>
                        <nonFilteredFileExtension>exe</nonFilteredFileExtension>
                        <nonFilteredFileExtension>cab</nonFilteredFileExtension>
                        <nonFilteredFileExtension>keystore</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.tipray</groupId>
                <artifactId>tipray-commons-plugin</artifactId>
                <version>1.0</version>
                <configuration>
                    <baseDir>${project.basedir}</baseDir>
                    <outputDir>${project.build.directory}</outputDir>
                    <webSourceDir>${project.basedir}/src/main/resources/websource</webSourceDir>
                </configuration>
                <executions>
                    <execution>
                        <phase>test</phase>
                        <goals>
                            <!-- 根据update的sql脚本，生成增量更新的sql脚本-->
                            <!--<goal>sqlCompare</goal>-->
                            <!-- 根据webSourceDir中的资源文件生成对应的web资源包：产品名称、版权等-->
                            <goal>webResource</goal>
                            <!-- 根据后端的多语言资源文件生成对应的langResource.js-->
                            <goal>i18nResource</goal>
                            <!-- 对指定的类进行加密处理-->
                            <goal>classEncrypt</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.2.3</version>
                <configuration>
                    <webResources>
                        <!--解决人大金仓在tongweb下因缺少驱动无法连接数据库
                         原因：依赖范围<scope>system</scope>只会打入lib-provided目录，tongweb只认lib，故无法识别到该驱动
                         解决方案：引入commons-mybatis的libs目录底下的jar包后生产到WEB-INF/lib-->
                        <resource>
                            <!-- 源jar包所在位置， ${project.basedir} 表示当前目录的路径 -->
                            <directory>${project.basedir}/../tipray-commons/tipray-commons-mybatis/src/main/resources/libs</directory>
                            <!-- 打包后的目标文件夹，也就是你打包后的war包或者jar包的目录  -->
                            <targetPath>WEB-INF/lib</targetPath>
                            <filtering>false</filtering>
                            <includes>
                                <!-- 把所有的jar包都打进去 -->
                                <include>**/*.jar</include>
                            </includes>
                        </resource>
                        <resource>
                            <!-- 源jar包所在位置， ${project.basedir} 表示当前目录的路径 -->
                            <directory>${project.basedir}/../tipray-modules/tipray-modules-cloud/lib</directory>
                            <!-- 打包后的目标文件夹，也就是你打包后的war包或者jar包的目录  -->
                            <targetPath>WEB-INF/lib</targetPath>
                            <filtering>false</filtering>
                            <includes>
                                <!-- 把所有的jar包都打进去 -->
                                <include>**/*.jar</include>
                            </includes>
                        </resource>
                    </webResources>
                    <packagingExcludes>
                        WEB-INF/classes/static/**,
                        WEB-INF/classes/websource/**,
                        <!-- logback 相关jar包内容打入 tipray-commons-logging 模块以兼容springboot，不重复打入lib目录 -->
                        WEB-INF/lib/logback-*.jar
                    </packagingExcludes>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
